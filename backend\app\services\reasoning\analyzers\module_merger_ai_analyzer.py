#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块合并AI分析器
基于多个模块的分析结果进行AI智能整合，识别架构组件和功能模块
"""
import logging
from typing import Any, Dict, List, Optional, Tuple
from pathlib import Path

from langchain.chains import <PERSON><PERSON>hain
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser
from langchain_openai import ChatOpenAI
from ....utils.retry import retry_async

from ...ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser
from .module_merger_models import AIAnalysisResult
from .module_models import ModuleAnalysis

logger = logging.getLogger(__name__)


@AgentManager.register("ModuleMergerAIAnalyzerAgent")
class ModuleMergerAIAnalyzer(BaseAgent):
    """
    模块合并AI分析器
    基于多个模块分析结果进行AI智能整合和洞察生成
    """

    def __init__(self):
        """初始化模块合并AI分析器"""
        super().__init__()
        self.name = "ModuleMergerAIAnalyzer"
        self.chain = None

    async def _initialize(self) -> None:
        """初始化分析器特定资源"""
        logger.info("初始化模块合并AI分析器特定资源")

    async def _create_chain(self) -> None:
        """初始化处理链"""
        try:
            # AI整合分析提示模板
            ai_analysis_template = """
            # 模块合并AI智能分析

            作为专业的软件架构分析专家，请基于多个模块的分析结果进行AI智能整合。

            ## 分析输入
            项目名称: {{ project_name }}
            分析范围: {{ analysis_scope }}

            ## 模块分析结果
            {% for module_name, analysis in module_analyses.items() %}
            ### {{ module_name }}
            - **路径**: {{ analysis.module_path }}
            - **目的**: {{ analysis.purpose }}
            - **角色**: {{ analysis.role }}
            - **层级**: {{ analysis.layer }}
            - **职责**: {{ analysis.responsibilities | join(', ') }}
            {% if analysis.features %}
            - **功能特性**:
            {% for feature in analysis.features %}
              - {{ feature.name }}: {{ feature.description }}
                - 重要性: {{ feature.importance }}/5.0
                {% if feature.use_cases %}
                - 使用场景: {{ feature.use_cases | join(', ') }}
                {% endif %}
            {% endfor %}
            {% else %}
            - **功能特性**: 无
            {% endif %}
            - **依赖数量**: {{ analysis.dependency_count }}
            {% endfor %}

            ## AI智能整合任务

            请基于上述模块分析结果，进行以下AI智能整合：

            ### 1. 架构组件识别 (architectural_components)
            识别和提取项目中的核心架构组件：
            - name: 组件名称（如"用户管理组件"、"数据访问组件"）
            - type: 组件类型（controller/service/model/util/middleware等）
            - layer: 架构层级（根据模块层级推导）
            - related_modules: 相关模块路径列表

            ### 2. 功能模块整合 (functional_modules)
            整合识别功能模块：
            - name: 功能模块名称（如"用户认证模块"、"数据处理模块"）
            - purpose: 功能模块主要目的
            - features: 从各模块功能特性中整合的核心功能
            - related_modules: 相关模块路径列表

            ### 3. AI分析洞察 (ai_insights)
            提供AI智能洞察：
            - architectural_patterns: 识别的架构模式（如MVC、分层架构、微服务等）
            - design_principles: 遵循的设计原则（如单一职责、开闭原则等）
            - code_smells: 发现的代码异味（如循环依赖、职责过多等）
            - refactoring_suggestions: 重构建议

            ### 4. AI分析发现 (ai_findings)
            生成AI发现列表：
            - title: 发现标题
            - description: 详细描述
            - severity: 严重程度（info/warning/critical）
            - related_paths: 相关路径
            - suggestion: 改进建议

            ### 5. AI改进建议 (ai_recommendations)
            提供字符串列表形式的改进建议

            ## 分析原则
            1. **基于事实**: 所有分析必须基于提供的模块分析结果
            2. **避免臆想**: 不要添加不存在于输入数据中的信息
            3. **智能整合**: 识别模块间的关联和模式
            4. **架构视角**: 从整体架构角度进行分析
            5. **实用导向**: 提供可操作的洞察和建议

            {{ format_instructions }}
            """

            # 创建解析器
            ai_parser = AnalysisOutputParser(pydantic_object=AIAnalysisResult)
            parser = OutputFixingParser.from_llm(parser=ai_parser, llm=self.llm)

            # 获取格式指令
            ai_format_instructions = ai_parser.get_format_instructions()

            # 创建提示模板
            ai_prompt = PromptTemplate.from_template(
                ai_analysis_template,
                template_format="jinja2"
            ).partial(format_instructions=ai_format_instructions)

            # 创建处理链
            self.chain = (
                ai_prompt
                | self.llm
                | parser
            )

            logger.info("模块合并AI分析处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化AI分析处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: Dict[str, Any]) -> AgentInput:
        """
        准备输入数据

        Args:
            input_data: 原始输入数据，包含module_analyses字典

        Returns:
            AgentInput: 准备后的输入数据
        """
        input_data: AgentInput = await super().prepare_input(input_data)
        
        # 确保必要参数存在
        if "module_analyses" not in input_data.parameters:
            raise ValueError("缺少module_analyses参数")
        
        if "project_name" not in input_data.parameters:
            input_data.parameters["project_name"] = "未知项目"
            
        if "analysis_scope" not in input_data.parameters:
            input_data.parameters["analysis_scope"] = f"包含{len(input_data.parameters['module_analyses'])}个模块"

        logger.info(f"准备AI分析输入，包含{len(input_data.parameters['module_analyses'])}个模块")
        return input_data

    @retry_async(max_retries=3)
    async def _process(
        self,
        input_data: AgentInput
    ) -> Tuple[Any, Dict[str, Any]]:
        """
        处理AI智能整合

        Args:
            input_data: 输入数据

        Returns:
            Tuple[Any, Dict[str, Any]]: (AI分析结果, 元数据)
        """
        try:
            # 调用处理链进行AI分析
            # result = await self.chain.first.ainvoke(input_data.parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(input_data.parameters)

            return result, {"analyzer": "module_merger_ai"}
        except Exception as e:
            logger.error(f"模块合并AI分析失败: {str(e)}")
            raise

    async def _shutdown(self) -> None:
        """关闭分析器资源"""
        logger.info("关闭模块合并AI分析器资源")
        self.chain = None
        self.llm = None
