#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能目录树分片器
将大型项目目录树分片成多个平衡的子树，以便于后续处理
"""
from __future__ import annotations
from datetime import datetime, timezone
from typing import List, Dict, Tuple, Set, Optional, Any
import copy
import heapq

from pydantic import BaseModel, Field, field_validator

from .project_info_models import TreeNode


class ChunkMetrics(BaseModel):
    """分片指标信息"""
    chunk_id: int = Field(..., description="分片ID")
    path_char_count: int = Field(default=0, description="路径字符总数")
    node_count: int = Field(default=0, description="节点总数")
    file_count: int = Field(default=0, description="文件总数")
    directory_count: int = Field(default=0, description="目录总数")
    max_depth: int = Field(default=0, description="最大深度")
    
    def __lt__(self, other: ChunkMetrics) -> bool:
        """支持优先队列比较"""
        return self.path_char_count < other.path_char_count


class DirectoryTreeChunker:
    """
    智能目录树分片器
    将目录树划分为多个子树，每个子树包含完整的从根目录到子目录的 TreeNode 结构
    """
    
    def __init__(self, max_chunk_size: int = 10000, balance_factor: float = 0.2):
        """
        初始化目录树分片器
        
        Args:
            max_chunk_size: 每个分片的最大路径字符总数目标
            balance_factor: 平衡因子(0-1)，控制分片大小的均衡程度，越接近1越均衡但分片数可能增加
        """
        self.max_chunk_size = max_chunk_size
        self.balance_factor = balance_factor
        self.total_path_chars = 0
        self.total_nodes = 0
        self.node_metrics: Dict[str, int] = {}  # 存储每个节点路径的字符数
    
    def _calculate_tree_metrics(self, node: TreeNode, parent_path: str = "") -> int:
        """
        递归计算树的指标
        
        Args:
            node: 当前节点
            parent_path: 父节点路径
            
        Returns:
            当前节点及其所有子节点的路径字符总数
        """
        current_path = f"{parent_path}/{node.name}" if parent_path else node.name
        path_chars = len(current_path)
        
        # 存储当前节点的路径字符数
        self.node_metrics[node.path] = path_chars
        self.total_nodes += 1
        
        # 递归计算子节点的路径字符数
        children_chars = 0
        if node.children:
            for child in node.children:
                children_chars += self._calculate_tree_metrics(child, current_path)
        
        total_chars = path_chars + children_chars
        return total_chars
    
    def _create_subtree_copy(self, original_node: TreeNode, include_paths: Set[str]) -> Optional[TreeNode]:
        """
        创建子树的深拷贝，仅包含指定路径的节点
        
        Args:
            original_node: 原始节点
            include_paths: 需要包含在子树中的路径集合
            
        Returns:
            复制的子树节点或None(如果节点不在包含路径中)
        """
        # 如果当前节点路径不在包含集合中，则不包含此节点
        if original_node.path not in include_paths:
            return None
            
        # 创建当前节点的拷贝
        new_node = copy.deepcopy(original_node)
        new_node.children = []  # 清空子节点列表，后续添加符合条件的子节点
        
        # 递归处理子节点
        if original_node.children:
            for child in original_node.children:
                new_child = self._create_subtree_copy(child, include_paths)
                if new_child:
                    new_node.children.append(new_child)
        
        return new_node
    
    def _get_node_subtree_size(self, node: TreeNode) -> int:
        """
        获取节点子树的路径字符总数
        
        Args:
            node: 当前节点
            
        Returns:
            子树的路径字符总数
        """
        total = self.node_metrics[node.path]
        
        if node.children:
            for child in node.children:
                total += self._get_node_subtree_size(child)
        
        return total
    
    def _distribute_nodes(self, root: TreeNode, chunk_count: int) -> List[Set[str]]:
        """
        分配节点到各个分片中
        使用目录聚类优先策略，确保同一目录下的文件尽量保持在同一个分片中
        
        Args:
            root: 根节点
            chunk_count: 分片数量
            
        Returns:
            分片列表，每个分片是一个包含节点路径的集合
        """
        # 初始化分片集合
        chunks: List[Set[str]] = [set() for _ in range(chunk_count)]
        chunks_metrics = [ChunkMetrics(chunk_id=i, path_char_count=0) for i in range(chunk_count)]
        
        # 创建优先队列(最小堆)，以分片当前大小作为优先级
        chunks_heap = [(0, i) for i in range(chunk_count)]
        heapq.heapify(chunks_heap)
        
        # 构建路径映射，记录每个节点到根节点的完整路径
        path_to_ancestors: Dict[str, List[str]] = {}
        # 构建目录到子节点的映射，用于目录聚类
        dir_to_children: Dict[str, List[TreeNode]] = {}
        # 记录所有目录节点
        all_dir_nodes: List[TreeNode] = []
        
        def build_path_maps(node: TreeNode, ancestors: List[str]):
            current_ancestors = ancestors + [node.path]
            path_to_ancestors[node.path] = current_ancestors
            
            # 如果是目录节点，添加到目录节点列表
            if node.type == "directory":
                all_dir_nodes.append(node)
                dir_to_children[node.path] = []
                
            # 记录父目录的子节点
            if ancestors and ancestors[-1] in dir_to_children:
                dir_to_children[ancestors[-1]].append(node)
                
            if node.children:
                for child in node.children:
                    build_path_maps(child, current_ancestors)
        
        # 从根节点开始构建所有映射
        build_path_maps(root, [])
        
        # 将目录和其子节点作为一个整体添加到分片中
        def add_dir_to_chunk(dir_path: str, chunk_id: int, visited_dirs: Set[str]) -> int:
            """将目录及其内容添加到分片，返回添加的字符总数"""
            if dir_path in visited_dirs:
                return 0
                
            visited_dirs.add(dir_path)
            
            # 添加目录本身及其祖先
            ancestors = path_to_ancestors[dir_path]
            char_count = 0
            
            for path in ancestors:
                if path not in chunks[chunk_id]:
                    chunks[chunk_id].add(path)
                    char_count += self.node_metrics[path]
                    
                    # 更新统计
                    chunks_metrics[chunk_id].node_count += 1
                    if "." in path.split("/")[-1] if "/" in path else path:
                        chunks_metrics[chunk_id].file_count += 1
                    else:
                        chunks_metrics[chunk_id].directory_count += 1
            
            # 添加目录的所有子节点（文件）
            for child in dir_to_children.get(dir_path, []):
                if child.type == "file" and child.path not in chunks[chunk_id]:
                    chunks[chunk_id].add(child.path)
                    char_count += self.node_metrics[child.path]
                    
                    # 更新统计
                    chunks_metrics[chunk_id].node_count += 1
                    chunks_metrics[chunk_id].file_count += 1
                    
            return char_count
        
        # 分配目录到分片
        def distribute_dirs_to_chunks():
            # 按照目录大小排序（从大到小），保证大目录能够独立分配
            sorted_dirs = sorted(
                all_dir_nodes,
                key=lambda d: sum(self.node_metrics.get(c.path, 0) for c in dir_to_children.get(d.path, [])),
                reverse=True
            )
            
            # 已访问的目录
            visited_dirs = set()
            
            # 确保根目录在每个分片中
            root_path = root.path
            for i in range(chunk_count):
                chunks[i].add(root_path)
                chunks_metrics[i].path_char_count += self.node_metrics[root_path]
                chunks_metrics[i].node_count += 1
                chunks_metrics[i].directory_count += 1
            
            # 处理除根目录外的其他目录
            for dir_node in sorted_dirs:
                if dir_node.path == root_path or dir_node.path in visited_dirs:
                    continue
                    
                # 获取当前最小的分片
                smallest_chunk_chars, smallest_chunk_id = heapq.heappop(chunks_heap)
                
                # 计算将目录及其内容添加到分片的成本
                new_char_count = add_dir_to_chunk(dir_node.path, smallest_chunk_id, visited_dirs)
                
                # 更新分片指标
                chunks_metrics[smallest_chunk_id].path_char_count += new_char_count
                
                # 将更新后的分片放回堆中
                heapq.heappush(chunks_heap, 
                              (chunks_metrics[smallest_chunk_id].path_char_count, 
                               smallest_chunk_id))
        
        # 开始分配节点
        distribute_dirs_to_chunks()
        
        # 处理可能遗漏的节点（例如，如果有任何节点不在已访问的目录中）
        visited_paths = set()
        for chunk in chunks:
            visited_paths.update(chunk)
            
        def handle_remaining_nodes(node: TreeNode):
            if node.path not in visited_paths:
                # 获取当前最小的分片
                smallest_chunk_chars, smallest_chunk_id = heapq.heappop(chunks_heap)
                
                # 添加节点及其祖先
                ancestors = path_to_ancestors[node.path]
                for path in ancestors:
                    if path not in chunks[smallest_chunk_id]:
                        chunks[smallest_chunk_id].add(path)
                        chunks_metrics[smallest_chunk_id].path_char_count += self.node_metrics[path]
                        chunks_metrics[smallest_chunk_id].node_count += 1
                        
                        if "." in path.split("/")[-1] if "/" in path else path:
                            chunks_metrics[smallest_chunk_id].file_count += 1
                        else:
                            chunks_metrics[smallest_chunk_id].directory_count += 1
                        
                        visited_paths.add(path)
                
                # 将更新后的分片放回堆中
                heapq.heappush(chunks_heap, 
                              (chunks_metrics[smallest_chunk_id].path_char_count, 
                               smallest_chunk_id))
            
            # 递归处理子节点
            if node.children:
                for child in node.children:
                    handle_remaining_nodes(child)
        
        # 处理可能遗漏的节点
        handle_remaining_nodes(root)
        
        return chunks

    def _optimize_chunk_count(self, root: TreeNode) -> int:
        """
        优化分片数量
        根据目录树大小和最大分片大小，计算合适的分片数量
        
        Args:
            root: 根节点
            
        Returns:
            优化后的分片数量
        """
        # 第一次估算：按照总字符数除以最大分片大小
        estimated_chunks = max(1, self.total_path_chars // self.max_chunk_size)
        
        # 增加平衡因子影响，确保更均衡的分片
        balanced_chunks = int(estimated_chunks * (1 + self.balance_factor))
        
        # 限制最小和最大分片数
        min_chunks = 1
        max_chunks = min(100, self.total_nodes // 10)  # 避免分片过多或过少
        
        return max(min_chunks, min(balanced_chunks, max_chunks))
    
    def chunk_directory_tree(self, root: TreeNode) -> List[TreeNode]:
        """
        将目录树分片为多个子树
        
        Args:
            root: 目录树根节点
            
        Returns:
            分片后的子树列表，每个子树是从根到叶子节点的完整路径
        """
        # 1. 计算整个树的指标
        self.total_path_chars = self._calculate_tree_metrics(root)
        
        # 2. 优化分片数量
        chunk_count = self._optimize_chunk_count(root)
        
        # 3. 分配节点到各个分片
        chunk_paths = self._distribute_nodes(root, chunk_count)
        
        # 4. 为每个分片创建子树
        chunked_trees = []
        for i, paths in enumerate(chunk_paths):
            subtree = self._create_subtree_copy(root, paths)
            if subtree:
                chunked_trees.append(subtree)
        
        return chunked_trees


def chunk_tree(
    tree: TreeNode, 
    max_chunk_size: int = 10000, 
    balance_factor: float = 0.2
) -> List[TreeNode]:
    """
    将目录树分片为多个子树的便捷函数
    
    Args:
        tree: 要分片的目录树
        max_chunk_size: 每个分片的最大路径字符总数目标
        balance_factor: 平衡因子(0-1)，控制分片大小的均衡程度
        
    Returns:
        分片后的子树列表
    """
    chunker = DirectoryTreeChunker(max_chunk_size, balance_factor)
    return chunker.chunk_directory_tree(tree)
