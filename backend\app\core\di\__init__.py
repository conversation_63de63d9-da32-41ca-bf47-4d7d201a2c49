"""
依赖注入容器管理器
"""
from typing import Optional
import structlog
from app.core.di.containers import Container
from sqlalchemy.orm import Session
import asyncio

from app.models.github.github_initializer import GitHubInitializer

logger = structlog.get_logger(__name__)

class ContainerManager:
    """容器管理器
    
    提供全局单例访问和资源生命周期管理
    """
    
    def __init__(self):
        """初始化容器管理器"""
        self._container: Optional[Container] = None
        logger.debug("容器管理器初始化")
    
    @property
    def container(self) -> Container:
        """获取容器实例
        
        如果容器未初始化，则延迟初始化
        
        Returns:
            Container: 容器实例
        """
        if self._container is None:
            from .containers import Container
            self._container = Container()
            logger.debug("容器实例创建")
        return self._container
    
    @container.setter
    def container(self, value: Optional[Container]) -> None:
        """设置容器实例
        
        Args:
            value: 容器实例或None
        """
        self._container = value
        if value is None:
            logger.debug("容器实例已清理")
    
    async def init_resources(self) -> None:
        """初始化资源
        
        初始化容器管理的所有资源，包括：
        1. 数据库连接
        2. 缓存连接
        3. 消息队列连接
        4. 其他外部服务连接
        """
        try:
            # 确保容器已创建
            container = self.container
            container.check_dependencies()
            container.init_resources()
            await asyncio.sleep(0.0)

            # 初始化数据库
            db = container.database()
            
            # 创建数据库表
            db.create_all()
            logger.info("数据库表创建成功")
            
            # 获取数据库提供者
            session_provider = container.async_session_provider()
            
            # 初始化默认数据
            async with session_provider() as session:
                try:
                    # 下载task启动
                    from app.core.di import container_manager
                    container_manager.container.github_downloader()
                    # 初始化默认权限
                    from app.models.rbac.initializer import RBACInitializer
                    initialize_rbac = RBACInitializer(session)
                    initialize_github = GitHubInitializer(session)
                    await initialize_rbac.initialize()
                    await initialize_github.initialize()

                    # myclass init here start task run forever
                    # 提交事务
                    await session.commit()
                    
                except Exception as e:
                    # 回滚事务
                    await session.rollback()
                    raise
            
            logger.info("容器资源初始化成功")
            
        except Exception as e:
            logger.error(
                "容器资源初始化失败",
                error=str(e),
                error_type=type(e).__name__
            )
            raise
    
    async def shutdown_resources(self) -> None:
        """清理资源
        
        清理容器管理的所有资源，包括：
        1. 关闭数据库连接
        2. 关闭缓存连接
        3. 关闭消息队列连接
        4. 关闭其他外部服务连接
        """
        if self.container is None:
            return
            
        try:
            # 清理数据库资源
            if self.container.database():
                db = self.container.database()
                await db.cleanup()
                logger.info("数据库资源清理成功")
            
            # 清理Redis资源
            if self.container.redis_client():
                self.container.redis_client().close()
                await asyncio.sleep(0)  # 让出控制权确保资源被释放
                logger.info("Redis资源清理成功")
            
            if self.container.redis_pool():
                self.container.redis_pool().disconnect()
                await asyncio.sleep(0)
                logger.info("Redis连接池清理成功")
            
            logger.info("容器资源清理完成")
            
        except Exception as e:
            logger.error(
                "容器资源清理失败",
                error=str(e),
                error_type=type(e).__name__
            )
            raise
        finally:
            self.container = None

# 创建全局容器管理器实例
container_manager = ContainerManager()

# 导出公共接口
__all__ = [
    'container_manager',
]
