# pyright: reportImportCycles=false

"""与包部件相关的开放包装约定（OPC）对象。"""

from __future__ import annotations

from typing import TYPE_CHECKING, Callable, Type, cast

from app.utils.slx_parser.opc.oxml import serialize_part_xml
from app.utils.slx_parser.opc.packuri import PackURI
from app.utils.slx_parser.opc.rel import Relationships
from app.utils.slx_parser.opc.shared import cls_method_fn
from app.utils.slx_parser.oxml.parser import parse_xml
from app.utils.slx_parser.opc.shared import lazyproperty

if TYPE_CHECKING:
    from app.utils.slx_parser.oxml.xmlchemy import BaseOxmlElement
    from app.utils.slx_parser.opc.package import OpcPackage


class Part:
    """包部件的基类。

    提供通用的属性和方法，但旨在由客户端代码进行子类化以实现特定的部件行为。
    """

    def __init__(
        self,
        partname: PackURI,
        content_type: str,
        blob: bytes | None = None,
        package: OpcPackage | None = None,
    ):
        super(Part, self).__init__()
        self._partname = partname
        self._content_type = content_type
        self._blob = blob
        self._package = package

    def after_unmarshal(self):
        """后反序列化处理的入口点，例如用于解析部件XML。

        可以被子类重写而无需调用super。
        """
        # 不要在这里放置任何代码，只是在未被子类重写时捕获调用
        pass

    def before_marshal(self):
        """序列化前处理的入口点，例如在必要时完成部件命名。

        可以被子类重写而无需调用super。
        """
        # 不要在这里放置任何代码，只是在未被子类重写时捕获调用
        pass

    @property
    def blob(self) -> bytes:
        """此包部件的内容作为字节序列。

        可以是文本或二进制。旨在被子类重写。默认行为是返回加载的blob。
        """
        return self._blob or b""

    @property
    def content_type(self):
        """此部件的内容类型。"""
        return self._content_type

    def drop_rel(self, rId: str):
        """如果引用计数小于2，则删除由`rId`标识的关系。

        引用计数为0的关系是隐式关系。
        """
        if self._rel_ref_count(rId) < 2:
            del self.rels[rId]

    @classmethod
    def load(cls, partname: PackURI, content_type: str, blob: bytes, package: OpcPackage):
        return cls(partname, content_type, blob, package)

    def load_rel(self, reltype: str, target: Part | str, rId: str, is_external: bool = False):
        """返回新添加的`reltype`类型的|_Relationship|实例。

        新关系将目标部件与此部件通过键`rId`关联。

        如果`is_external`为|True|，则目标模式设置为``RTM.EXTERNAL``。
        用于从序列化包加载时，其中rId是已知的。在操作部件时添加新关系还有其他方法。
        """
        return self.rels.add_relationship(reltype, target, rId, is_external)

    @property
    def package(self):
        """此部件所属的|OpcPackage|实例。"""
        return self._package

    @property
    def partname(self):
        """持有此部件部件名的|PackURI|实例，例如'/ppt/slides/slide1.xml'。"""
        return self._partname

    @partname.setter
    def partname(self, partname: str):
        if not isinstance(partname, PackURI):
            tmpl = "partname必须是PackURI的实例，得到的是'%s'"
            raise TypeError(tmpl % type(partname).__name__)
        self._partname = partname

    def part_related_by(self, reltype: str) -> Part:
        """返回此部件具有`reltype`关系的部件。

        如果找不到这样的关系则引发|KeyError|，如果找到多个这样的关系则引发|ValueError|。
        提供解析隐式相关部件的能力，如Slide -> SlideLayout。
        """
        return self.rels.part_with_reltype(reltype)

    def relate_to(self, target: Part | str, reltype: str, is_external: bool = False) -> str:
        """返回到`target`的`reltype`关系的rId键。

        如果存在现有关系则返回该关系的`rId`，否则创建新关系。
        """
        if is_external:
            return self.rels.get_or_add_ext_rel(reltype, cast(str, target))
        else:
            rel = self.rels.get_or_add(reltype, cast(Part, target))
            return rel.rId

    @property
    def related_parts(self):
        """通过rId映射相关部件的字典，因此子对象可以解析部件XML中存在的显式关系，
        例如从sldIdLst到特定的|Slide|实例。"""
        return self.rels.related_parts

    @lazyproperty
    def rels(self):
        """持有此部件关系的|Relationships|实例。"""
        # -- 通过保留遗留的`._rels`属性防止在`python-docx-template`中出现故障 --
        self._rels = Relationships(self._partname.baseURI)
        return self._rels

    def target_ref(self, rId: str) -> str:
        """返回由`rId`标识的关系中包含的URL。"""
        rel = self.rels[rId]
        return rel.target_ref

    def _rel_ref_count(self, rId: str) -> int:
        """返回此部件中对由`rId`标识的关系的引用计数。

        只有XML部件可以包含引用，因此对于`Part`这是0。
        """
        return 0


class PartFactory:
    """提供一种方式让客户端代码指定|Part|的子类，该子类将由|Unmarshaller|基于其内容类型
    和/或自定义可调用对象构造。

    将``PartFactory.part_class_selector``设置为可调用对象将导致该对象对包中的每个部件
    都使用参数``content_type, reltype``调用一次。如果可调用对象返回一个对象，则该对象
    用作该部件的类。如果返回|None|，则部件类选择回退到在``PartFactory.part_type_for``
    中定义的内容类型映射。如果这两者都没有返回类，则使用``PartFactory.default_part_type``
    中包含的类来构造部件，默认为``opc.package.Part``。
    """

    part_class_selector: Callable[[str, str], Type[Part] | None] | None = None
    part_type_for: dict[str, Type[Part]] = {}
    default_part_type = Part

    def __new__(
        cls,
        partname: PackURI,
        content_type: str,
        reltype: str,
        blob: bytes,
        package: Package,
    ):
        PartClass: Type[Part] | None = None
        if cls.part_class_selector is not None:
            part_class_selector = cls_method_fn(cls, "part_class_selector")
            PartClass = part_class_selector(content_type, reltype)
        if PartClass is None:
            PartClass = cls._part_cls_for(content_type)
        return PartClass.load(partname, content_type, blob, package)

    @classmethod
    def _part_cls_for(cls, content_type: str):
        """返回为`content_type`注册的自定义部件类，如果没有为`content_type`注册自定义类，
        则返回默认部件类。"""
        if content_type in cls.part_type_for:
            return cls.part_type_for[content_type]
        return cls.default_part_type


class XmlPart(Part):
    """包含XML负载的包部件的基类，这是大多数部件的情况。

    为|Part|基类提供额外的方法，用于处理XML负载的解析和重新序列化，以及管理与其他部件的关系。
    """

    def __init__(
        self,
        partname: PackURI,
        content_type: str,
        element: BaseOxmlElement,
        package: OpcPackage,
    ):
        super(XmlPart, self).__init__(partname, content_type, package=package)
        self._element = element

    @property
    def blob(self):
        return serialize_part_xml(self._element)

    @property
    def element(self):
        """此XML部件的根XML元素。"""
        return self._element

    @classmethod
    def load(cls, partname: PackURI, content_type: str, blob: bytes, package: OpcPackage):
        element = parse_xml(blob)
        return cls(partname, content_type, element, package)

    @property
    def part(self):
        """父协议的一部分，文档的"子对象"不会知道包含它们的部件，因此必须询问它们的父对象。

        对于子对象来说，委托链在这里结束。
        """
        return self

    def _rel_ref_count(self, rId: str) -> int:
        """返回此部件的XML中对由`rId`标识的关系的引用计数。"""
        rIds = cast("list[str]", self._element.xpath("//@r:id"))
        return len([_rId for _rId in rIds if _rId == rId])
