"""
角色模型转换器
"""
from re import S
from typing import Optional
from app.models.rbac.role import RoleModel
from app.schemas.rbac.role import Role
from ..base import BaseConverter
from .permission import PermissionConverter
from .permission_group import PermissionGroupConverter

class RoleConverter(BaseConverter[RoleModel, Role]):
    """角色模型转换器"""
    
    def __init__(self):
        """初始化转换器"""
        super().__init__()
        self.permission_converter = PermissionConverter()
        self.permission_group_converter = PermissionGroupConverter()
    
    def to_schema(self, model: RoleModel) -> Role:
        """将角色模型转换为schema
        
        Args:
            model: 角色模型实例
            
        Returns:
            角色schema实例
        """
        # 临时存储permissions和users避免循环引用
        self._store_temp_attr(model, 'permissions', [])
        self._store_temp_attr(model, 'users', [])
        self._store_temp_attr(model, 'permission_groups', [])
        self._store_temp_attr(model, 'parent', None)
        self._store_temp_attr(model, 'children', [])
        
        # 转换基本属性
        role = Role.model_validate(model)
        
        # 恢复但不转换，避免循环引用
        self._restore_temp_attr(model, 'permissions')
        self._restore_temp_attr(model, 'permission_groups')        
        self._restore_temp_attr(model, 'users')
        self._restore_temp_attr(model, 'children')
        self._restore_temp_attr(model, 'parent')
        
        return role
    
    def to_model(self, schema: Role) -> RoleModel:
        """将角色schema转换为模型
        
        Args:
            schema: 角色schema实例
            
        Returns:
            角色模型实例
        """
        # 转换基本属性
        role = RoleModel()
        for field in schema.model_fields:
            if field not in ['permissions', 'users'] and hasattr(schema, field):
                value = getattr(schema, field)
                if value is not None:
                    setattr(role, field, value)
        
        # 转换permissions
        if schema.permissions:
            role.permissions = [
                self.permission_converter.to_model(permission)
                for permission in schema.permissions
            ]
        
        return role
