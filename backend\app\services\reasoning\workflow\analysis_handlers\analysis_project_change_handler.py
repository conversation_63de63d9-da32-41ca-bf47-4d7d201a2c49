#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目变化分析处理器
负责分析项目的变化情况，包括新增、修改和删除的文件
"""
import structlog
from typing import List, Dict, Set

from ..models import AnalysisConfig, AnalysisState
from .base_handler import BaseAnalysisHandler

logger = structlog.get_logger(__name__)

class AnalysisProjectChangeHandler(BaseAnalysisHandler):
    """
    项目变化分析处理器
    负责分析项目的变化情况，包括新增、修改和删除的文件
    """

    def initialize(self) -> None:
        """
        初始化项目变化分析处理器
        该处理器不需要特殊的初始化操作
        """
        pass

    async def process(self, state: AnalysisState) -> AnalysisState:
        """
        处理项目变化分析

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        # 1. 从缓存获取状态
        cached_state = self.get_cached_state()

        # 2. 执行项目变化分析
        try:
            # 如果没有缓存状态，则所有文件都视为新增
            if not cached_state:
                logger.info("没有找到缓存的项目状态，将所有文件视为新增")
                modules = list(state.files.keys())
                state.pending_modules = modules
                state.changed_modules = []
                state.deleted_modules = []
                state.is_incremental_analysis = True
                return state

            # 如果缓存的状态中没有文件信息，则所有文件都视为新增
            if not cached_state.files:
                logger.info("缓存的项目状态中没有文件信息，将所有文件视为新增")
                modules = list(state.files.keys())
                state.pending_modules = modules
                state.changed_modules = []
                state.deleted_modules = []
                state.is_incremental_analysis = True
                return state

            # 分析项目变化
            current_files = state.files
            cached_files = cached_state.files

            # 找出新增、修改和删除的文件
            new_modules = []
            changed_modules = []
            deleted_modules = []

            # 找出新增和修改的文件
            for path, file_info in current_files.items():
                if path not in cached_files:
                    new_modules.append(path)
                else:
                    cached_file_info = cached_files[path]
                    # 优先使用文件哈希进行变化检测
                    if self._is_file_changed(file_info, cached_file_info):
                        changed_modules.append(path)

            # 找出删除的文件
            for path in cached_files.keys():
                if path not in current_files:
                    deleted_modules.append(path)

            # 更新状态
            state.new_modules = new_modules
            state.changed_modules = changed_modules
            state.deleted_modules = deleted_modules

            # 设置为增量分析
            state.is_incremental_analysis = True

            logger.info(f"项目变化分析完成: 新增 {len(new_modules)} 个文件, 修改 {len(changed_modules)} 个文件, 删除 {len(deleted_modules)} 个文件")
            # 如果是增量分析，则保留上次分析的结果
            if state.is_incremental_analysis:
                # 保留上次分析的模块分析结果，但移除已删除和已变化文件的分析结果
                self._update_module_analyses(state, cached_state)

                # 更新待分析模块列表和已完成模块列表
                self._update_module_lists(state)

                logger.info(f"增量分析模式: 保留 {len(state.module_analyses)} 个模块分析结果, 待分析 {len(state.pending_modules)} 个模块, 已完成 {len(state.completed_modules)} 个模块")
            else:
                # 如果不是增量分析，则重置所有分析结果
                self._reset_analysis_results(state)

                logger.info(f"全量分析模式: 待分析 {len(state.pending_modules)} 个模块")
        except Exception as e:
            logger.error(f"项目变化分析失败: {str(e)}")
            state.errors["analyze_project_changes"] = str(e)

            # 出错时默认为全量分析
            state.new_modules = list(state.files.keys())
            state.changed_modules = []
            state.deleted_modules = []
            state.is_incremental_analysis = False
            state.pending_modules = list(state.files.keys())

        # 3. 设置缓存
        self.set_cached_state(state)

        # 4. 返回分析结果
        return state

    def _is_file_changed(self, current_file, cached_file) -> bool:
        """
        检测文件是否发生变化

        优先使用文件哈希进行检测，如果哈希不可用则回退到修改时间检测

        Args:
            current_file: 当前文件信息 (FileInfo)
            cached_file: 缓存的文件信息 (FileInfo)

        Returns:
            bool: 如果文件发生变化返回True，否则返回False
        """
        # 优先使用文件哈希进行变化检测
        if current_file.file_hash is not None and cached_file.file_hash is not None:
            # 如果两个文件都有哈希值，则比较哈希值
            is_hash_changed = current_file.file_hash != cached_file.file_hash
            if is_hash_changed:
                logger.debug(f"文件哈希变化检测: {current_file.path} - 哈希值不同")
            return is_hash_changed

        # 如果只有一个文件有哈希值，说明文件发生了变化
        if (current_file.file_hash is None) != (cached_file.file_hash is None):
            logger.debug(f"文件哈希变化检测: {current_file.path} - 哈希值可用性不同")
            return True

        # 回退到修改时间检测
        if current_file.last_modified is not None and cached_file.last_modified is not None:
            is_time_changed = current_file.last_modified > cached_file.last_modified
            if is_time_changed:
                logger.debug(f"文件时间变化检测: {current_file.path} - 修改时间不同")
            return is_time_changed

        # 如果无法比较，则认为文件发生了变化（保守策略）
        logger.debug(f"文件变化检测: {current_file.path} - 无法比较，默认为已变化")
        return True

    def _update_module_analyses(self, state: AnalysisState, cached_state: AnalysisState) -> None:
        """
        更新模块分析结果

        Args:
            state: 当前分析状态
            cached_state: 缓存的分析状态
        """
        # 获取已删除和已变化的模块
        affected_modules = set(state.deleted_modules + state.changed_modules)

        # 保留上次分析的模块分析结果，但移除已删除和已变化文件的分析结果
        state.module_analyses = {k: v for k, v in cached_state.module_analyses.items()
                                if k not in affected_modules}

        # 保留其他分析结果，但在后续分析中会重新生成
        state.structure_analysis = cached_state.structure_analysis
        state.architecture_analysis = cached_state.architecture_analysis
        state.dependency_analysis = cached_state.dependency_analysis

    def _update_module_lists(self, state: AnalysisState) -> None:
        """
        更新待分析模块列表和已完成模块列表

        Args:
            state: 当前分析状态
        """
        # 更新待分析模块列表，只包含新增和修改的文件
        state.pending_modules = list(set(state.new_modules + state.changed_modules))

        # 更新已完成模块列表，包含所有已分析且未变化的模块
        state.completed_modules = list(state.module_analyses.keys())

        # 确保待分析模块列表中不包含已完成的模块
        state.pending_modules = [m for m in state.pending_modules if m not in state.completed_modules]

        # 确保待分析模块列表中包含所有当前文件中存在但未分析的模块
        all_current_modules = set(state.files.keys())
        analyzed_modules = set(state.completed_modules + state.pending_modules)
        unanalyzed_modules = all_current_modules - analyzed_modules

        if unanalyzed_modules:
            state.pending_modules.extend(unanalyzed_modules)
            logger.info(f"发现 {len(unanalyzed_modules)} 个未分析的模块，添加到待分析列表")

    def _reset_analysis_results(self, state: AnalysisState) -> None:
        """
        重置所有分析结果

        Args:
            state: 当前分析状态
        """
        state.module_analyses = {}
        state.merged_modules = {}
        modules=list(state.files.keys())
        state.pending_modules = modules
        state.completed_modules = []