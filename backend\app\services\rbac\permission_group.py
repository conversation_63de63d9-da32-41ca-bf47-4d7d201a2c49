"""
权限组服务模块

提供权限组管理相关功能
"""
from datetime import datetime, timezone
from typing import Optional, List
import structlog
from sqlalchemy import select, and_, or_, func, delete
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import DatabaseError

from app.core.database import DatabaseError
from app.core.di.providers import SessionProvider, AsyncSessionProvider
from app.models.rbac.permission_group import PermissionGroupModel
from app.models.rbac.role import RoleModel
from app.models.rbac.permission import PermissionModel

from app.schemas.rbac.permission_group import PermissionGroup, PermissionGroupListResponse, PermissionGroupCreateRequest, PermissionGroupUpdateRequest, PermissionGroupPermissionRequest
from app.model_converters.rbac.permission_group import PermissionGroupConverter

logger = structlog.get_logger(__name__)

class PermissionGroupService:
    """权限组服务类"""
    
    def __init__(
        self,
        session: SessionProvider,
        async_session: AsyncSessionProvider
    ):
        """初始化权限组服务
        
        Args:
            session: 数据库会话提供者
            async_session: 异步数据库会话提供者
        """
        self.session = session
        self.async_session = async_session
        self.permission_group_converter = PermissionGroupConverter()
        logger.debug("权限组服务初始化完成")

    async def get_by_id(self, group_id: str) -> Optional[PermissionGroup]:
        """根据ID获取权限组

        Args:
            group_id: 权限组ID

        Returns:
            权限组信息，不存在返回None
        """
        async with self.async_session() as session:
            stmt = (
                select(PermissionGroupModel)
                .options(
                    selectinload(PermissionGroupModel.permissions),
                    selectinload(PermissionGroupModel.roles)
                )
                .where(PermissionGroupModel.id == group_id)
            )
            result = await session.execute(stmt)
            group = result.scalar_one_or_none()
            return self.permission_group_converter.to_schema(group) if group else None

    async def get_by_code(self, code: str) -> Optional[PermissionGroup]:
        """根据权限组代码获取权限组

        Args:
            code: 权限组代码

        Returns:
            权限组信息，不存在返回None
        """
        async with self.async_session() as session:
            stmt = (
                select(PermissionGroupModel)
                .options(
                    selectinload(PermissionGroupModel.permissions),
                    selectinload(PermissionGroupModel.roles)
                )
                .where(PermissionGroupModel.code == code)
            )
            result = await session.execute(stmt)
            group = result.scalar_one_or_none()
            return self.permission_group_converter.to_schema(group) if group else None

    async def get_list(
        self,
        *,
        page: int = 1,
        page_size: int = 100,
        is_active: Optional[bool] = None,
        role_id: Optional[str] = None,
        search: Optional[str] = None
    ) -> PermissionGroupListResponse:
        """获取权限组列表

        Args:
            page: 页码
            page_size: 每页记录数
            is_active: 是否启用
            role_id: 角色ID
            search: 搜索关键词(代码、名称、描述)

        Returns:
            权限组列表分页响应
        """
        async with self.async_session() as session:
            stmt = (
                select(PermissionGroupModel)
                .options(
                    selectinload(PermissionGroupModel.permissions),
                    selectinload(PermissionGroupModel.roles)
                )
            )
            
            # 构建查询条件
            conditions = []
            
            if is_active is not None:
                conditions.append(PermissionGroupModel.is_active == is_active)
                
            if role_id is not None:
                stmt = stmt.join(PermissionGroupModel.roles)
                conditions.append(RoleModel.id == role_id)
                
            if search:
                search_condition = or_(
                    PermissionGroupModel.code.ilike(f"%{search}%"),
                    PermissionGroupModel.name.ilike(f"%{search}%"),
                    PermissionGroupModel.description.ilike(f"%{search}%")
                )
                conditions.append(search_condition)
                
            if conditions:
                stmt = stmt.where(and_(*conditions))
                
            # 获取总记录数
            count_stmt = select(func.count()).select_from(stmt.subquery())
            total = await session.scalar(count_stmt)
            
            # 计算分页
            skip = (page - 1) * page_size
            
            # 获取分页数据
            stmt = (
                stmt.order_by(PermissionGroupModel.sort_order.asc(), PermissionGroupModel.id.desc())
                .offset(skip)
                .limit(page_size)
            )
            result = await session.execute(stmt)
            groups = result.scalars().all()
            
            return PermissionGroupListResponse(
                total=total,
                permission_groups=[self.permission_group_converter.to_schema(g) for g in groups],
                page=page,
                page_size=page_size
            )

    async def create(
        self,
        request: PermissionGroupCreateRequest
    ) -> PermissionGroup:
        """创建权限组

        Args:
            request: 权限组创建请求

        Returns:
            创建的权限组信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 权限组代码已存在
        """
        async with self.async_session() as session:
            try:
                # 检查权限组代码是否存在
                stmt = (
                    select(PermissionGroupModel)
                    .where(PermissionGroupModel.code == request.code)
                )
                result = await session.execute(stmt)
                if result.scalar_one_or_none():
                    raise ValueError("权限组代码已存在")
                
                # 创建权限组
                group = PermissionGroupModel(
                    code=request.code,
                    name=request.name,
                    description=request.description,
                    is_active=request.is_active,
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc)
                )
                
                # 分配权限
                if request.permission_ids:
                    result = await session.execute(
                        select(PermissionModel).where(PermissionModel.id.in_(request.permission_ids))
                    )
                    permissions = result.scalars().all()
                    if len(permissions) != len(request.permission_ids):
                        raise ValueError("部分权限不存在")
                    group.permissions = permissions
                    
                # 分配角色
                if request.role_ids:
                    result = await session.execute(
                        select(RoleModel).where(RoleModel.id.in_(request.role_ids))
                    )
                    roles = result.scalars().all()
                    if len(roles) != len(request.role_ids):
                        raise ValueError("部分角色不存在")
                    group.roles = roles
                    
                session.add(group)
                await session.commit()
                await session.refresh(group)
                
                logger.info(
                    "权限组创建成功",
                    code=request.code,
                    name=request.name,
                    group_id=group.id
                )
                
                return self.permission_group_converter.to_schema(group)
                
            except Exception as e:
                logger.error(
                    "权限组创建失败",
                    code=request.code,
                    name=request.name,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("权限组创建失败") from e

    async def update(
        self,
        request: PermissionGroupUpdateRequest
    ) -> PermissionGroup:
        """更新权限组信息

        Args:
            request: 更新权限组的请求数据

        Returns:
            更新后的权限组信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 权限组代码已存在
        """
        async with self.async_session() as session:
            try:
                # 获取权限组
                stmt = (
                    select(PermissionGroupModel)
                    .options(
                        selectinload(PermissionGroupModel.permissions),
                        selectinload(PermissionGroupModel.roles)
                    )
                    .where(PermissionGroupModel.id == request.permission_group_id)
                )
                result = await session.execute(stmt)
                group = result.scalar_one_or_none()
                if not group:
                    raise ValueError("权限组不存在")
                
                # 检查权限组代码是否重复
                if request.code and request.code != group.code:
                    stmt = (
                        select(PermissionGroupModel)
                        .where(PermissionGroupModel.code == request.code)
                    )
                    result = await session.execute(stmt)
                    if result.scalar_one_or_none():
                        raise ValueError("权限组代码已存在")
                    group.code = request.code
                    
                # 更新其他字段
                if request.name is not None:
                    group.name = request.name
                if request.description is not None:
                    group.description = request.description
                if request.is_active is not None:
                    group.is_active = request.is_active
                    
                # 更新权限
                if request.permission_ids is not None:
                    result = await session.execute(
                        select(PermissionModel).where(PermissionModel.id.in_(request.permission_ids))
                    )
                    permissions = result.scalars().all()
                    if len(permissions) != len(request.permission_ids):
                        raise ValueError("部分权限不存在")
                    group.permissions = permissions
                    
                # 更新角色
                if request.role_ids is not None:
                    result = await session.execute(
                        select(RoleModel).where(RoleModel.id.in_(request.role_ids))
                    )
                    roles = result.scalars().all()
                    if len(roles) != len(request.role_ids):
                        raise ValueError("部分角色不存在")
                    group.roles = roles
                    
                group.updated_at = datetime.now(timezone.utc)
                await session.commit()
                await session.refresh(group)
                
                logger.info(
                    "权限组更新成功",
                    group_id=request.permission_group_id,
                    code=request.code,
                    name=request.name
                )
                
                return self.permission_group_converter.to_schema(group)
                
            except Exception as e:
                logger.error(
                    "权限组更新失败",
                    group_id=request.permission_group_id,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("权限组更新失败") from e

    async def add_permissions(
        self,
        data: PermissionGroupPermissionRequest
    ) -> Optional[PermissionGroup]:
        """添加权限到权限组
        
        Args:
            data: 添加权限请求数据
            
        Returns:
            更新后的权限组信息
            
        Raises:
            ValueError: 参数错误
            DatabaseError: 数据库错误
        """
        async with self.async_session() as session:
            try:
                # 查询权限组
                stmt = (
                    select(PermissionGroupModel)
                    .options(
                        selectinload(PermissionGroupModel.permissions),
                        selectinload(PermissionGroupModel.roles)
                    )
                    .where(PermissionGroupModel.id == data.permission_group_id)
                )
                result = await session.execute(stmt)
                group = result.scalar_one_or_none()
                
                if not group:
                    return None
                
                # 查询要添加的权限
                stmt = (
                    select(PermissionModel)
                    .where(PermissionModel.id.in_(data.permission_ids))
                )
                result = await session.execute(stmt)
                permissions = result.scalars().all()
                
                # 添加权限
                for permission in permissions:
                    if permission not in group.permissions:
                        group.permissions.append(permission)
                
                await session.commit()
                
                logger.info(
                    "权限组添加权限成功",
                    group_id=group.id,
                    permission_ids=data.permission_ids
                )
                
                return self.permission_group_converter.to_schema(group)
                
            except Exception as e:
                logger.error(
                    "权限组添加权限失败",
                    group_id=data.permission_group_id,
                    permission_ids=data.permission_ids,
                    error=str(e)
                )
                await session.rollback()
                raise
    
    async def remove_permissions(
        self,
        data: PermissionGroupPermissionRequest
    ) -> Optional[PermissionGroup]:
        """从权限组移除权限
        
        Args:
            data: 移除权限请求数据
            
        Returns:
            更新后的权限组信息
            
        Raises:
            ValueError: 参数错误
            DatabaseError: 数据库错误
        """
        async with self.async_session() as session:
            try:
                # 查询权限组
                stmt = (
                    select(PermissionGroupModel)
                    .options(
                        selectinload(PermissionGroupModel.permissions),
                        selectinload(PermissionGroupModel.roles)
                    )
                    .where(PermissionGroupModel.id == data.permission_group_id)
                )
                result = await session.execute(stmt)
                group = result.scalar_one_or_none()
                
                if not group:
                    return None
                
                # 查询要移除的权限
                stmt = (
                    select(PermissionModel)
                    .where(PermissionModel.id.in_(data.permission_ids))
                )
                result = await session.execute(stmt)
                permissions = result.scalars().all()
                
                # 移除权限
                for permission in permissions:
                    if permission in group.permissions:
                        group.permissions.remove(permission)
                
                await session.commit()
                
                logger.info(
                    "权限组移除权限成功",
                    group_id=group.id,
                    permission_ids=data.permission_ids
                )
                
                return self.permission_group_converter.to_schema(group)
                
            except Exception as e:
                logger.error(
                    "权限组移除权限失败",
                    group_id=data.permission_group_id,
                    permission_ids=data.permission_ids,
                    error=str(e)
                )
                await session.rollback()
                raise

    async def delete(self, group_id: str) -> bool:
        """删除权限组

        Args:
            group_id: 权限组ID

        Returns:
            是否删除成功

        Raises:
            DatabaseError: 数据库错误
        """
        async with self.async_session() as session:
            try:
                result = await session.execute(
                    delete(PermissionGroupModel).where(PermissionGroupModel.id == group_id)
                )
                await session.commit()
                
                deleted = result.rowcount > 0
                if deleted:
                    logger.info("权限组删除成功", group_id=group_id)
                else:
                    logger.warning("权限组不存在", group_id=group_id)
                    
                return deleted
                
            except Exception as e:
                logger.error(
                    "权限组删除失败",
                    group_id=group_id,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("权限组删除失败") from e
