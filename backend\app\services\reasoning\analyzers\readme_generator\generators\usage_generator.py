#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用方法章节生成器
"""
import logging
import os
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone

from langchain.chains import <PERSON><PERSON>hain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser

from . import Generator
from ..section_models import UsageSection, CodeExample
from .....ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser
from ......utils.retry import retry_async

logger = logging.getLogger(__name__)


class UsageGenerator(Generator):
    """
    使用方法章节生成器
    负责生成README文档的使用方法章节
    """

    def __init__(self, llm: ChatOpenAI):
        """初始化使用方法章节生成器"""
        super().__init__(llm)

    async def create_chain(self) -> None:
        """初始化处理链"""
        try:
            # 使用方法章节生成提示模板
            usage_template = """
            作为经验丰富的技术文档撰写专家和软件使用指导顾问，请基于项目分析数据生成全面、详细且易于理解的使用方法章节文档。

            ## 项目基本信息
            项目名称: {{ project_name }}
            项目路径: {{ project_path }}

            {%- if structure_analysis %}

            ## 使用相关结构信息
            {%- if structure_analysis.primary_language %}
            主要编程语言: {{ structure_analysis.primary_language }}
            {%- endif %}

            {%- if structure_analysis.entries %}

            ### 项目入口点:
            {%- for entry in structure_analysis.entries[:4] %}
            - **{{ entry.name }}**: {{ entry.description }}
            {%- endfor %}
            {%- endif %}

            {%- if structure_analysis.configs %}

            ### 配置文件:
            {%- for config in structure_analysis.configs[:4] %}
            - **{{ config.name }}** ({{ config.config_format }}): {{ config.description }}
            {%- if config.config_scope %}  作用域: {{ config.config_scope }}{%- endif %}
            {%- if config.is_required %}  必需配置{%- endif %}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if dependency_analysis %}

            ## 技术栈和运行环境
            {%- if dependency_analysis.tech_stack %}
            {%- if dependency_analysis.tech_stack.primary_language %}
            主要语言: {{ dependency_analysis.tech_stack.primary_language }}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.frameworks %}
            核心框架: {%- for framework in dependency_analysis.tech_stack.frameworks[:3] %}{{ framework }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.build_tools %}
            构建工具: {%- for tool in dependency_analysis.tech_stack.build_tools[:3] %}{{ tool }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.databases %}
            数据存储: {%- for db in dependency_analysis.tech_stack.databases[:3] %}{{ db }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- endif %}
            {%- endif %}

            {%- if module_merger_analyses %}

            ## 核心功能模块
            {%- for group_key, merger_result in module_merger_analyses.items() %}
            {%- if merger_result.ai_analysis and merger_result.ai_analysis.functional_modules %}

            ### {{ group_key }} 功能模块:
            {%- for module in merger_result.ai_analysis.functional_modules[:3] %}
            - **{{ module.name }}**: {{ module.purpose }}
            {%- if module.features %}  特性: {%- for feature in module.features[:2] %}{{ feature.name }}{%- if not loop.last %}, {%- endif %}{%- endfor %}{%- endif %}
            {%- endfor %}
            {%- endif %}
            {%- endfor %}
            {%- endif %}

            ## 使用说明生成要求与原则

            ### 核心生成原则
            1. **实用导向**：基于实际项目数据生成可操作的使用指南，避免臆想不存在的功能
            2. **操作清晰**：提供详细的分步操作指南，确保用户能够按步骤完成操作
            3. **示例真实**：所有代码示例必须可执行且符合项目实际用法
            4. **逐步引导**：从基础安装到高级功能，采用循序渐进的指导方式
            5. **问题预防**：主动识别和解决用户可能遇到的常见问题
            6. **最佳实践**：提供效率提升和错误避免的实用建议

            ### 内容生成任务
            请生成一个全面、详细且易于理解的使用方法章节，包含以下5个核心要素：

            1. **安装与设置**（150-200字）：详细的安装步骤（包括所有必要的依赖项）、不同环境下的安装方法（如开发环境、生产环境）、配置说明（包括必要的环境变量或配置文件）
            2. **基本使用方法**（200-250字）：启动和运行项目的详细步骤、命令行参数或配置选项的说明、基本操作流程的概述
            3. **核心功能使用指南**：详细介绍3-5个核心功能的使用方法，每个功能包含功能描述（50-80字）、使用步骤（分步骤详细说明）、参数选项（如适用）、代码示例（确保示例可执行且符合项目实际用法）、预期输出或结果说明
            4. **常见问题与解决方案**：列出3-5个常见问题或错误、提供每个问题的详细解决步骤
            5. **使用技巧与最佳实践**：提供3-5个提高效率或避免常见陷阱的技巧

            ### 输出语言要求
            所有生成的使用方法章节文档内容必须使用中文(简体中文)输出。包括安装与设置、基本使用方法、核心功能使用指南、问题解决方案等内容都必须是中文。只有技术名词、项目名称和专有名词可保持原样。

            ### 输出质量要求
            1. **实际数据驱动**：内容必须基于提供的实际项目数据，不要臆想不存在的功能或特性
            2. **代码示例质量**：所有代码示例必须是可执行的，并且符合项目的实际用法
            3. **语言清晰简洁**：使用清晰、简洁的语言，避免技术术语过载，确保不同技术水平的用户都能理解
            4. **指令完整性**：确保指令的完整性，不要遗漏关键步骤，每个操作都要有明确的说明
            5. **用户体验导向**：从用户角度思考，提供易于跟随的操作流程和实用的使用建议
            6. **实用性最大化**：生成的使用说明应该能够帮助用户快速上手并有效使用项目的各项功能

            {{ format_instructions }}
            """

            # 创建Parser
            usage_parser = AnalysisOutputParser(pydantic_object=UsageSection)
            parser = OutputFixingParser.from_llm(parser=usage_parser, llm=self.llm)

            # 获取格式指令
            usage_format_instructions = usage_parser.get_format_instructions()

            # 创建提示模板
            usage_prompt = PromptTemplate.from_template(
                usage_template,
                template_format="jinja2"
            ).partial(format_instructions=usage_format_instructions)

            # 创建处理链
            self.chain = (
                usage_prompt
                | self.llm
                | parser
            )

            logger.info("使用方法章节生成器处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: AgentInput) -> None:
        """
        准备输入数据并设置self.input_parameters

        Args:
            input_data: 原始输入数据
        """
        # 初始化self.input_parameters
        self.input_parameters = input_data.parameters.copy()

        logger.info("使用方法章节生成器输入参数准备完成")

    @retry_async(max_retries=3)
    async def process(self) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询

        Returns:
            Tuple[Any, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 调用LLM处理链
            # result = await self.chain.first.ainvoke(self.input_parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(self.input_parameters)

            # 设置章节顺序
            if result:
                result.order = 4  # 使用方法章节通常是第四个

            return result, {"generator": "usage_generator"}
        except Exception as e:
            logger.error(f"使用方法章节生成失败: {str(e)}")
            raise
