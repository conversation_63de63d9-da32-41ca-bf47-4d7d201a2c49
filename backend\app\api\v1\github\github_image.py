"""
GitHub项目图片上传API处理器
"""
import json
import os
import urllib
import uuid

import structlog

from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide

from app.api.base import BaseHandler
from app.core.config import settings
from app.services.github.github_project import GitHubProjectService

logger = structlog.get_logger(__name__)


class GitHubImageUploadHandler(BaseHandler):
    """GitHub项目图片上传处理器"""
    
    def initialize(self):
        """初始化处理器"""
        try:
            if os.name == 'nt':  # Windows
                self.upload_dir = settings.github.GITHUB_IMAGE_UPLOAD_DIR_WINDOWS
            else:  # Linux/Mac
                self.upload_dir = settings.github.GITHUB_IMAGE_UPLOAD_DIR_LINUX
            # 打印日志确认路径
            logger.info(f"上传目录设置为: {self.upload_dir}")
            # 确保上传目录存在
            os.makedirs(self.upload_dir, exist_ok=True)
        except Exception as e:
            logger.error(f"初始化上传目录失败: {str(e)}")
            self.upload_dir = "static/images"  # 设置一个默认值

        # 父类初始化放在最后
        super().initialize()
    
    # @require_auth(required=True)
    async def post(self) -> None:
        """处理图片上传请求"""
        try:
            # 检查是否有文件上传
            if not self.request.files or 'image' not in self.request.files:
                self.write_error(500, error_message="请上传图片文件")

            # 获取上传的文件
            file_items = self.request.files['image']

            # 处理所有上传的图片
            uploaded_files = []
            for file_item in file_items:
                # 获取文件信息
                filename = file_item['filename']
                content_type = file_item['content_type']

                # 检查文件类型
                if not content_type.startswith('image/'):
                    continue  # 跳过非图片文件

                # 生成唯一文件名
                file_ext = os.path.splitext(filename)[1]
                new_filename = f"{uuid.uuid4().hex}{file_ext}"

                # 文件保存路径
                file_path = os.path.join(self.upload_dir, new_filename)

                # 保存文件
                with open(file_path, 'wb') as f:
                    f.write(file_item['body'])

                # 生成文件URL
                file_url = f"{settings.github.GITHUB_IMAGE_BASE_URL}/{new_filename}"

                # 添加到结果列表
                uploaded_files.append({
                    "original_name": filename,
                    "url": file_url,
                    "size": len(file_item['body'])
                })

            # 返回结果
            self.success_response({
                "files": uploaded_files
            })
        except Exception as e:
            logger.error("上传图片失败", error=str(e))
            self.write_error(500, error_message=f"上传图片失败: {str(e)}")


class GitHubImageListHandler(BaseHandler):
    """GitHub图片列表"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    async def get(self) -> None:
        """获取图片列表
        支持两种模式：
        1. 不带隐藏参数：返回当前图片列表
        2. 带隐藏参数：用新列表替换当前列表
        """
        try:
            project_id = self.get_argument("project_id")
            if not project_id:
                raise ValueError("project_id不能为空")
            ans = await self.github_project_service.get_image_list(project_id)
            self.success_response(ans)
        except ValueError as ve:
            logger.error("参数错误", error=str(ve))
            self.write_error(400, error_message=str(ve))
        except Exception as e:
            logger.error("获取图片列表失败", error=str(e))
            self.write_error(500, error_message=f"获取图片列表失败: {str(e)}")