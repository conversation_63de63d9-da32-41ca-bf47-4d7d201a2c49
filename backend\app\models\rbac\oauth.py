"""
OAuth认证相关模型
"""
from datetime import datetime, timezone
from typing import Optional
from sqlalchemy import String, ForeignKey, DateTime, Boolean, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.model_base import ModelBase

class OAuthAccountModel(ModelBase):
    """OAuth账号模型"""
    
    __tablename__ = "oauth_accounts"
    
    # 关联用户ID
    user_id: Mapped[str] = mapped_column(
        String(36),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        comment="关联用户ID"
    )
    
    # OAuth提供商
    provider: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        comment="OAuth提供商"
    )
    
    # OAuth提供商用户ID
    provider_user_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="OAuth提供商用户ID"
    )
    
    # OAuth访问令牌
    access_token: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="OAuth访问令牌"
    )
    
    # OAuth刷新令牌
    refresh_token: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="OAuth刷新令牌"
    )
    
    # 令牌过期时间
    expires_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="令牌过期时间"
    )
    
    # 用户信息
    user_info: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="用户信息JSON"
    )
    
    # 是否已验证
    is_verified: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否已验证"
    )
    
    # 关联用户
    user = relationship("UserModel", back_populates="oauth_accounts")
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<OAuthAccount(id={self.id}, user_id={self.user_id}, provider={self.provider})>"
