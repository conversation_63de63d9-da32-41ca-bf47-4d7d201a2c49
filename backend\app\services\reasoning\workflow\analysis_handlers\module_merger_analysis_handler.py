#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块合并分析处理器
调用AI分析器和编码分析器，生成完整的模块合并分析结果
支持并发处理多个模块分组以提升性能
支持分组级缓存机制以优化性能
"""
import asyncio
import time
import json
import hashlib
import structlog
from typing import Dict, Any, Optional, List, Tuple

from ..models import AnalysisConfig, AnalysisState
from .base_handler import BaseAnalysisHandler
from ....ai_agent_core import agent_manager
from ...analyzers.module_merger_ai_analyzer import ModuleMergerAIAnalyzer
from ...analyzers.module_merger_code_analyzer import ModuleMergerCodeAnalyzer
from ...analyzers.module_merger_models import ModuleMergerAnalysisResult
from ..cache_manager import CacheManager
from .....utils.batch_processing_utils import calculate_batch_size, record_performance

logger = structlog.get_logger(__name__)


class ModuleMergerAnalysisHandler(BaseAnalysisHandler):
    """
    模块合并分析处理器
    整合AI智能分析和编码关系分析
    """

    def __init__(self, config: AnalysisConfig, cache_dir: str = '.cache'):
        """
        初始化模块合并分析处理器

        Args:
            config: 分析配置
            cache_dir: 缓存目录
        """
        self.ai_analyzer: Optional[ModuleMergerAIAnalyzer] = None
        self.code_analyzer: Optional[ModuleMergerCodeAnalyzer] = None
        super().__init__(config, cache_dir)

    def initialize(self) -> None:
        """
        初始化分析器资源
        """
        try:
            # 初始化AI分析器 - 使用agent_manager加载
            self.ai_analyzer = agent_manager.load("ModuleMergerAIAnalyzerAgent")
            logger.info("AI分析器初始化完成")
            
            # 初始化编码分析器 - 直接实例化，因为它不是AI代理
            self.code_analyzer = ModuleMergerCodeAnalyzer()
            logger.info("编码分析器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化分析器失败: {str(e)}")
            raise

    async def process(self, state: AnalysisState) -> AnalysisState:
        """
        处理模块合并分析

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        # 1. 从缓存获取状态
        cached_state = self.get_cached_state()

        # 2. 执行模块合并分析
        try:
            # 检查是否需要执行模块合并分析
            if not state.module_groups:
                logger.warning("没有模块分组数据，跳过合并分析")
                return state

            if not state.module_analyses:
                logger.warning("没有模块分析结果，跳过合并分析")
                return state

            # 检查是否可以使用缓存 - 使用module_merger_analyses字段
            if cached_state and cached_state.module_merger_analyses and state.is_incremental_analysis:
                # 检查是否有结构性变化
                has_structural_changes = len(state.new_modules) > 0 or len(state.deleted_modules) > 0

                # 如果没有结构性变化，可以使用缓存结果
                if not has_structural_changes:
                    logger.info("没有结构性变化，使用缓存的模块合并分析结果")
                    state.module_merger_analyses = cached_state.module_merger_analyses
                    state.analysis_progress = 0.95  # 合并分析完成，进度95%
                    return state
                else:
                    # 有结构性变化时，清除已删除分组的缓存
                    self._clear_deleted_groups_cache(state)

            # 执行模块合并分析
            state = await self._analyze_module_merger(state)

            logger.info("模块合并分析完成")
        except Exception as e:
            logger.error(f"模块合并分析失败: {str(e)}")
            state.errors["module_merger_analysis"] = str(e)

        # 3. 设置缓存
        self.set_cached_state(state)

        # 4. 返回分析结果
        return state

    async def _analyze_module_merger(self, state: AnalysisState) -> AnalysisState:
        """
        执行模块合并分析（支持并发处理）

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        logger.info("开始模块合并分析")

        try:
            # 检查分析器是否已初始化
            if self.ai_analyzer is None or self.code_analyzer is None:
                raise ValueError("分析器未初始化")

            # 初始化合并分析结果存储
            if not state.module_merger_analyses:
                state.module_merger_analyses = {}

            # 准备分组数据
            valid_groups = self._prepare_group_data(state)
            if not valid_groups:
                logger.warning("没有有效的模块分组数据")
                return state

            # 在增量分析模式下，过滤出需要重新分析的分组
            if state.is_incremental_analysis:
                groups_to_analyze = self._filter_groups_for_incremental_analysis(state, valid_groups)
                logger.info(f"增量分析模式: 需要重新分析 {len(groups_to_analyze)} 个分组，总共 {len(valid_groups)} 个分组")
            else:
                groups_to_analyze = valid_groups
                logger.info(f"完整分析模式: 准备处理 {len(groups_to_analyze)} 个模块分组")

            # 如果没有需要分析的分组，直接返回
            if not groups_to_analyze:
                logger.info("没有需要重新分析的分组")
                return state

            # 根据分组数量决定处理策略
            total_groups = len(groups_to_analyze)

            if total_groups <= 2:
                # 分组较少时使用串行处理
                logger.info("分组数量较少，使用串行处理")
                await self._process_groups_sequentially(state, groups_to_analyze)
            else:
                # 分组较多时使用并发处理
                logger.info("分组数量较多，使用并发处理")
                await self._process_groups_concurrently(state, groups_to_analyze)

            # 更新分析进度
            state.analysis_progress = 0.95  # 合并分析完成，进度95%
            logger.info(f"模块合并分析完成，处理了 {len(state.module_merger_analyses)} 个分组")

        except Exception as e:
            logger.error(f"模块合并分析失败: {str(e)}")
            state.errors["module_merger_analysis"] = str(e)
            raise

        return state

    def _prepare_group_data(self, state: AnalysisState) -> List[Tuple[str, Dict[str, Any], Dict[str, Any]]]:
        """
        准备分组数据，过滤出有效的分组

        Args:
            state: 当前分析状态

        Returns:
            List[Tuple[str, Dict[str, Any], Dict[str, Any]]]: (group_key, group_info, group_module_analyses)
        """
        valid_groups = []

        for group_key, group_info in state.module_groups.items():
            # 获取该分组的模块分析结果
            group_module_analyses = {}
            for module_path in group_info['module_paths']:
                if module_path in state.module_analyses:
                    group_module_analyses[module_path] = state.module_analyses[module_path]

            if group_module_analyses:
                valid_groups.append((group_key, group_info, group_module_analyses))
            else:
                logger.warning(f"分组 {group_key} 没有有效的模块分析结果，跳过")

        return valid_groups

    def _filter_groups_for_incremental_analysis(self, state: AnalysisState,
                                               valid_groups: List[Tuple[str, Dict[str, Any], Dict[str, Any]]]) -> List[Tuple[str, Dict[str, Any], Dict[str, Any]]]:
        """
        在增量分析模式下过滤出需要重新分析的分组

        Args:
            state: 当前分析状态
            valid_groups: 有效的分组数据

        Returns:
            需要重新分析的分组列表
        """
        groups_to_analyze = []

        for group_key, group_info, group_module_analyses in valid_groups:
            # 检查分组是否包含变更的模块
            group_modules = set(group_info.get('module_paths', []))
            changed_modules = set(state.changed_modules or [])
            new_modules = set(state.new_modules or [])
            deleted_modules = set(state.deleted_modules or [])

            # 如果分组包含新增、修改或删除的模块，则需要重新分析
            has_changes = bool(
                group_modules.intersection(changed_modules) or
                group_modules.intersection(new_modules) or
                group_modules.intersection(deleted_modules)
            )

            if has_changes:
                logger.info(f"分组 {group_key} 包含变更模块，需要重新分析")
                groups_to_analyze.append((group_key, group_info, group_module_analyses))
            else:
                # 检查是否已有缓存的分析结果
                cache_key = self._generate_group_cache_key(group_key, group_info, group_module_analyses)
                cached_result = self.cache_manager.get(cache_key)

                if cached_result:
                    try:
                        # 尝试从缓存加载结果
                        merger_result = ModuleMergerAnalysisResult.model_validate_json(cached_result)
                        state.module_merger_analyses[group_key] = merger_result
                        logger.info(f"分组 {group_key} 使用缓存结果")
                    except Exception as e:
                        logger.warning(f"分组 {group_key} 缓存结果无效，需要重新分析: {str(e)}")
                        groups_to_analyze.append((group_key, group_info, group_module_analyses))
                else:
                    logger.info(f"分组 {group_key} 没有缓存结果，需要重新分析")
                    groups_to_analyze.append((group_key, group_info, group_module_analyses))

        return groups_to_analyze

    async def _process_groups_sequentially(self, state: AnalysisState,
                                         valid_groups: List[Tuple[str, Dict[str, Any], Dict[str, Any]]]) -> None:
        """
        串行处理分组（用于分组数量较少的情况）

        Args:
            state: 当前分析状态
            valid_groups: 有效的分组数据
        """
        for group_key, group_info, group_module_analyses in valid_groups:
            logger.info(f"串行处理模块分组: {group_info['display_name']}")

            try:
                merger_result = await self._analyze_single_group(
                    group_key, group_info, group_module_analyses
                )
                state.module_merger_analyses[group_key] = merger_result
                logger.info(f"完成分组 {group_key} 的合并分析")

            except Exception as e:
                logger.error(f"分组 {group_key} 分析失败: {str(e)}")
                state.errors[f"group_{group_key}"] = str(e)

    async def _process_groups_concurrently(self, state: AnalysisState,
                                         valid_groups: List[Tuple[str, Dict[str, Any], Dict[str, Any]]]) -> None:
        """
        并发处理分组（用于分组数量较多的情况）

        Args:
            state: 当前分析状态
            valid_groups: 有效的分组数据
        """
        total_groups = len(valid_groups)

        # 计算最优批处理大小
        batch_size = calculate_batch_size(
            max_workers=min(self.config.parallel_workers, total_groups),
            task_key="module_merger_analysis"
        )

        logger.info(f"使用并发处理，批处理大小: {batch_size}")

        # 记录开始时间
        start_time = time.time()
        successful_count = 0

        # 分批处理分组
        for i in range(0, total_groups, batch_size):
            batch_groups = valid_groups[i:i + batch_size]
            batch_start_time = time.time()

            logger.info(f"处理批次 {i//batch_size + 1}，包含 {len(batch_groups)} 个分组")

            # 创建并发任务
            tasks = []
            for group_key, group_info, group_module_analyses in batch_groups:
                task = asyncio.create_task(
                    self._analyze_single_group(group_key, group_info, group_module_analyses),
                    name=f"group_{group_key}"
                )
                tasks.append((group_key, task))

            # 等待批次完成
            batch_successful = 0
            for group_key, task in tasks:
                try:
                    merger_result = await task
                    state.module_merger_analyses[group_key] = merger_result
                    batch_successful += 1
                    successful_count += 1
                    logger.info(f"完成分组 {group_key} 的合并分析")

                except Exception as e:
                    logger.error(f"分组 {group_key} 分析失败: {str(e)}")
                    state.errors[f"group_{group_key}"] = str(e)

            # 记录批次性能
            batch_duration = time.time() - batch_start_time
            if batch_successful > 0:
                record_performance(
                    task_key="module_merger_analysis",
                    batch_size=len(batch_groups),
                    items_processed=batch_successful,
                    total_time=batch_duration
                )

            logger.info(f"批次完成，成功: {batch_successful}/{len(batch_groups)}，用时: {batch_duration:.2f}秒")

        # 记录总体性能
        total_duration = time.time() - start_time
        logger.info(f"并发处理完成，总成功: {successful_count}/{total_groups}，总用时: {total_duration:.2f}秒")

    async def _analyze_single_group(self, group_key: str, group_info: Dict[str, Any],
                                  group_module_analyses: Dict[str, Any]) -> ModuleMergerAnalysisResult:
        """
        分析单个模块分组（支持分组级缓存）

        Args:
            group_key: 分组键
            group_info: 分组信息
            group_module_analyses: 分组的模块分析结果

        Returns:
            ModuleMergerAnalysisResult: 合并分析结果
        """
        # 生成分组缓存键
        cache_key = self._generate_group_cache_key(group_key, group_info, group_module_analyses)

        # 尝试从缓存获取分析结果
        cached_result = self.cache_manager.get(cache_key)
        if cached_result:
            try:
                # 尝试反序列化缓存的分析结果
                merger_result = ModuleMergerAnalysisResult.model_validate_json(cached_result)
                logger.info(f"从缓存中加载分组 {group_key} 的合并分析结果")
                return merger_result
            except Exception as e:
                logger.warning(f"反序列化缓存的分组合并分析结果失败: {str(e)}")
                # 缓存反序列化失败，继续正常分析

        logger.info(f"开始分析模块分组: {group_info['display_name']}")

        # 执行编码分析（同步）
        code_analysis_result = self.code_analyzer.analyze(
            module_analyses=group_module_analyses,
            project_name=self.config.project_name or "未知项目",
            analysis_scope=f"分组: {group_info['display_name']}"
        )

        # 执行AI分析（异步）
        ai_analysis_result = await self._run_ai_analysis(
            group_module_analyses,
            self.config.project_name or "未知项目",
            f"分组: {group_info['display_name']}"
        )

        # 组合分析结果
        merger_result = ModuleMergerAnalysisResult(
            project_name=self.config.project_name or "未知项目",
            ai_analysis=ai_analysis_result,
            code_analysis=code_analysis_result
        )

        # 缓存分析结果
        try:
            merger_result_json = merger_result.model_dump_json()
            self.cache_manager.set(cache_key, merger_result_json)
            logger.info(f"分组 {group_key} 合并分析结果已缓存")
        except Exception as e:
            logger.warning(f"缓存分组合并分析结果失败: {str(e)}")

        return merger_result

    async def _run_ai_analysis(self, module_analyses: Dict[str, Any],
                              project_name: str, analysis_scope: str):
        """
        运行AI分析

        Args:
            module_analyses: 模块分析结果
            project_name: 项目名称  
            analysis_scope: 分析范围

        Returns:
            AI分析结果
        """
        try:
            # 准备输入数据 - 按照标准格式
            input_data = {
                "parameters": {
                    "module_analyses": module_analyses,
                    "project_name": project_name,
                    "analysis_scope": analysis_scope
                }
            }

            # 调用AI分析器
            agent_input = await self.ai_analyzer.prepare_input(input_data)
            output = await self.ai_analyzer.process(agent_input)
            ai_result = output.response.result

            logger.info(f"AI分析完成，项目: {project_name}")
            return ai_result

        except Exception as e:
            logger.error(f"AI分析失败: {str(e)}")
            raise

    def _clear_deleted_groups_cache(self, state: AnalysisState) -> None:
        """
        清除已删除分组的缓存

        Args:
            state: 分析状态
        """
        if not state.deleted_modules:
            return

        # 找出受影响的分组
        affected_groups = set()
        for module_path in state.deleted_modules:
            for group_key, group_info in state.module_groups.items():
                if module_path in group_info.get('module_paths', []):
                    affected_groups.add(group_key)

        if affected_groups:
            logger.info(f"清除 {len(affected_groups)} 个受影响分组的缓存")
            for group_key in affected_groups:
                # 由于模块变化，我们需要清除该分组的所有缓存
                # 这里简化处理，实际可以实现更精确的缓存清理
                logger.info(f"清除分组 {group_key} 的缓存")

    def _generate_group_cache_key(self, group_key: str, group_info: Dict[str, Any],
                                group_module_analyses: Dict[str, Any]) -> str:
        """
        生成分组缓存键

        Args:
            group_key: 分组键
            group_info: 分组信息
            group_module_analyses: 分组的模块分析结果

        Returns:
            缓存键
        """
        # 创建包含相关信息的字典（不包含模块分析结果的哈希）
        cache_info = {
            "project_name": self.config.project_name or "未知项目",
            "group_key": group_key,
            "group_display_name": group_info.get('display_name', ''),
            "module_paths": sorted(group_info.get('module_paths', [])),
            "module_count": len(group_module_analyses)
        }

        # 将字典转换为JSON字符串并计算哈希值
        try:
            cache_str = json.dumps(cache_info, sort_keys=True)
            cache_hash = hashlib.md5(cache_str.encode()).hexdigest()
            return f"group_merger_analysis_{group_key}_{cache_hash}"
        except TypeError as e:
            # 如果JSON序列化失败，记录错误并使用备用方法
            logger.warning(f"分组缓存键生成失败: {str(e)}，使用备用方法")
            return f"group_merger_analysis_{group_key}_{len(group_module_analyses)}"


