#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/4/27 11:30
# @File    : statistics.py
# @Description: 
"""

from datetime import datetime, timezone
from typing import Optional, List, Dict
import structlog
from sqlalchemy import select, and_, or_, func, delete
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import DatabaseError

from app.core.database import DatabaseError
from app.core.di.providers import SessionProvider, AsyncSessionProvider
from app.models.rbac.permission_group import PermissionGroupModel
from app.models.rbac.role import RoleModel
from app.models.rbac.permission import PermissionModel
from app.models.statistics import StatisticsModel

from app.schemas.rbac.permission_group import PermissionGroup, PermissionGroupListResponse, \
    PermissionGroupCreateRequest, PermissionGroupUpdateRequest, PermissionGroupPermissionRequest
from app.model_converters.rbac.permission_group import PermissionGroupConverter

logger = structlog.get_logger(__name__)


class StatisticsService:
    """权限组服务类"""

    def __init__(
            self,
            session: SessionProvider,
            async_session: AsyncSessionProvider
    ):
        self.session = session
        self.async_session = async_session
        logger.debug("统计服务初始化完成")

    async def get_statistics(self) -> Dict[str, int]:
        """获取所有统计数据

        Returns:
            Dict[str, int]: 统计名称和对应数值的字典
        """
        async with self.async_session() as session:
            try:
                result = await session.execute(
                    select(StatisticsModel)
                )
                stats = result.scalars().all()

                # 转换为字典格式返回
                return {stat.name: int(stat.count) for stat in stats}
            except Exception as e:
                logger.error("获取统计数据失败", error=str(e))
                raise DatabaseError("获取统计数据失败") from e

    async def increment_count(self, name: str, increment: int = 1) -> int:
        """增加指定统计计数

        Args:
            name: 统计名称
            increment: 增加的数量，默认为1

        Returns:
            int: 更新后的计数值
        """
        async with self.async_session() as session:
            try:
                # 查找是否存在该统计项
                stmt = select(StatisticsModel).where(StatisticsModel.name == name)
                result = await session.execute(stmt)
                stat = result.scalar_one_or_none()

                if stat:
                    # 如果存在，更新计数
                    new_count = int(stat.count) + increment
                    stat.count = str(new_count)
                    await session.commit()
                    logger.info(f"统计 {name} 计数增加 {increment}，当前值: {new_count}")
                    return new_count
                else:
                    # 如果不存在，创建新的统计项
                    new_stat = StatisticsModel(
                        name=name,
                        count=str(increment)
                    )
                    session.add(new_stat)
                    await session.commit()
                    logger.info(f"创建新统计项 {name}，初始值: {increment}")
                    return increment

            except Exception as e:
                await session.rollback()
                logger.error(f"更新统计 {name} 失败", error=str(e))
                raise DatabaseError(f"更新统计失败: {str(e)}") from e

    async def create_or_update(self, name: str, count: int) -> int:
        """创建或更新统计项

        Args:
            name: 统计名称
            count: 计数值

        Returns:
            int: 设置后的计数值
        """
        async with self.async_session() as session:
            try:
                # 查找是否存在该统计项
                stmt = select(StatisticsModel).where(StatisticsModel.name == name)
                result = await session.execute(stmt)
                stat = result.scalar_one_or_none()

                if stat:
                    # 如果存在，更新计数
                    stat.count = str(count)
                    await session.commit()
                    logger.info(f"统计 {name} 更新为 {count}")
                else:
                    # 如果不存在，创建新的统计项
                    new_stat = StatisticsModel(
                        name=name,
                        count=str(count)
                    )
                    session.add(new_stat)
                    await session.commit()
                    logger.info(f"创建新统计项 {name}，值: {count}")

                return count

            except Exception as e:
                await session.rollback()
                logger.error(f"创建或更新统计 {name} 失败", error=str(e))
                raise DatabaseError(f"创建或更新统计失败: {str(e)}") from e
