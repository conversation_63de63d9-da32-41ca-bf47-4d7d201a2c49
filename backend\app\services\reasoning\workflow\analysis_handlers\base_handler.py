#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础分析处理器
定义所有分析处理器的通用接口和方法
"""
from abc import ABC, abstractmethod
from typing import Optional

import structlog

from ..models import AnalysisConfig, AnalysisState
from ..cache_manager import CacheManager

logger = structlog.get_logger(__name__)

class BaseAnalysisHandler(ABC):
    """
    基础分析处理器
    所有具体的分析处理器都应该继承自这个类
    """

    def __init__(self, config: AnalysisConfig, cache_dir: str = '.cache'):
        """
        初始化基础分析处理器

        Args:
            config: 分析配置
            cache_dir: 缓存目录
        """
        self.config = config
        self.cache_key = config.project_path
        self.cache_manager = CacheManager.get_instance(cache_dir)

        # 调用子类的初始化方法
        self.initialize()

    @abstractmethod
    def initialize(self) -> None:
        """
        抽象初始化方法
        子类必须实现此方法来进行特定的初始化操作

        这个方法在基础初始化完成后被调用，
        子类可以在这里进行：
        - 特定资源的初始化
        - 外部服务的连接
        - 配置验证
        - 其他必要的设置
        """
        pass

    @abstractmethod
    async def process(self, state: AnalysisState) -> AnalysisState:
        """
        处理分析状态
        
        Args:
            state: 当前分析状态
            
        Returns:
            更新后的分析状态
        """
        pass
    
    def get_cached_state(self) -> Optional[AnalysisState]:
        """
        从缓存获取分析状态
        
        Returns:
            缓存的分析状态，如果没有则返回None
        """
        cached_state_json = self.cache_manager.get_state(self.cache_key)
        if cached_state_json:
            try:
                return AnalysisState.model_validate_json(cached_state_json)
            except Exception as e:
                logger.error(f"反序列化缓存状态失败: {str(e)}")
        return None
    
    def set_cached_state(self, state: AnalysisState) -> bool:
        """
        设置缓存的分析状态
        
        Args:
            state: 要缓存的分析状态
            
        Returns:
            是否成功设置缓存
        """
        try:
            return self.cache_manager.set_state(self.cache_key, state)
        except Exception as e:
            logger.error(f"缓存分析状态失败: {str(e)}")
            return False
    

