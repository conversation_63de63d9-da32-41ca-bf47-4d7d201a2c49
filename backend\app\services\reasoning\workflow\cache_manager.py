#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存管理器
用于管理分析状态的缓存
支持diskcache主缓存和MongoDB持久化存储的双写机制
"""
import os
import json
import structlog
from datetime import datetime, timezone
from typing import Any, Optional, Dict, Union
from diskcache import Cache
from pymongo import MongoClient
from pymongo.database import Database
from pymongo.collection import Collection

from app.core.config import settings
from pydantic import BaseModel

logger = structlog.get_logger(__name__)


class CacheManager:
    """
    缓存管理器
    使用diskcache作为主缓存，MongoDB作为持久化存储
    支持双写机制确保数据一致性
    """
    _instance = None
    _cache = None

    # MongoDB集合名称
    MONGODB_COLLECTION = "analysis_results"

    @classmethod
    def get_instance(cls, cache_dir: str = '.cache') -> 'CacheManager':
        """
        获取缓存管理器实例（单例模式）

        Args:
            cache_dir: 缓存目录

        Returns:
            CacheManager: 缓存管理器实例
        """
        if cls._instance is None:
            cls._instance = CacheManager(cache_dir)
        return cls._instance

    def __init__(self, cache_dir: str = '.cache'):
        """
        初始化缓存管理器

        Args:
            cache_dir: 缓存目录
        """
        # 初始化主缓存
        if CacheManager._cache is None:
            os.makedirs(cache_dir, exist_ok=True)
            CacheManager._cache = Cache(cache_dir)

        # 初始化MongoDB连接
        self.mongodb_client = None
        self.mongodb_db = None
        self.mongodb_collection = None

        try:
            # 直接使用配置中的MongoDB连接URL
            self.mongodb_client = MongoClient(settings.mongodb.MONGODB_URL)
            self.mongodb_db = self.mongodb_client[settings.mongodb.MONGODB_DB]
            self.mongodb_collection = self.mongodb_db[self.MONGODB_COLLECTION]

            # 测试连接
            self.mongodb_client.admin.command('ping')
            mongodb_available = True
            logger.info("MongoDB连接成功",
                       url=settings.mongodb.MONGODB_URL,
                       database=settings.mongodb.MONGODB_DB)

        except Exception as e:
            logger.warning("MongoDB连接失败，将仅使用主缓存", error=str(e))
            mongodb_available = False
            self.mongodb_client = None
            self.mongodb_db = None
            self.mongodb_collection = None

        logger.info("缓存管理器初始化完成",
                   cache_dir=cache_dir,
                   mongodb_available=mongodb_available)

    def get(self, key: str, default=None) -> Any:
        """
        获取缓存值
        优先从主缓存获取，失败时从MongoDB获取作为fallback
        支持反向同步：当主缓存有数据但MongoDB中不存在时，自动同步到MongoDB

        Args:
            key: 缓存键
            default: 默认值

        Returns:
            Any: 缓存值或默认值
        """
        try:
            # 首先尝试从主缓存获取
            value = CacheManager._cache.get(key, None)
            if value is not None:
                # 反向同步：检查MongoDB中是否存在该数据，如果不存在则同步写入
                if self.mongodb_collection is not None:
                    try:
                        # 检查MongoDB中是否已存在该缓存键
                        existing_doc = self.mongodb_collection.find_one({"cache_key": key})
                        if not existing_doc:
                            # MongoDB中不存在，进行反向同步
                            document = {
                                "cache_key": key,
                                "value": value,
                                "created_at": datetime.now(timezone.utc)
                            }

                            result = self.mongodb_collection.insert_one(document)
                            if result.inserted_id:
                                logger.debug("反向同步：主缓存数据已同步到MongoDB", key=key)
                            else:
                                logger.warning("反向同步：MongoDB写入失败", key=key)

                    except Exception as e:
                        logger.warning("反向同步：MongoDB操作异常", key=key, error=str(e))

                return value

            # 主缓存失败时，尝试从MongoDB获取
            if self.mongodb_collection is not None:
                try:
                    document = self.mongodb_collection.find_one({"cache_key": key})
                    if document and "value" in document:
                        mongo_value = document["value"]
                        # 同时更新主缓存
                        try:
                            CacheManager._cache.set(key, mongo_value)
                            logger.debug("从MongoDB恢复缓存到主缓存", key=key)
                        except Exception as e:
                            logger.warning("更新主缓存失败", key=key, error=str(e))

                        return mongo_value
                except Exception as e:
                    logger.warning("从MongoDB获取缓存失败", key=key, error=str(e))

            return default

        except Exception as e:
            logger.error("获取缓存值失败", key=key, error=str(e))
            return default

    def set(self, key: str, value: Any, expire: int = None) -> bool:
        """
        设置缓存值
        同时写入主缓存和MongoDB实现双写机制

        Args:
            key: 缓存键
            value: 缓存值
            expire: 过期时间（秒）

        Returns:
            bool: 是否成功（以主缓存操作为准）
        """
        # 主要操作：写入主缓存
        main_success = False
        try:
            main_success = CacheManager._cache.set(key, value, expire=expire)
            if main_success:
                logger.debug("主缓存写入成功", key=key)
            else:
                logger.warning("主缓存写入失败", key=key)
        except Exception as e:
            logger.error("主缓存写入异常", key=key, error=str(e))

        # 辅助操作：写入MongoDB（不影响主流程）
        if self.mongodb_collection is not None:
            try:
                # 构建MongoDB文档
                document = {
                    "cache_key": key,
                    "value": value,
                    "created_at": datetime.now(timezone.utc)
                }

                # 使用upsert操作，如果存在则更新，不存在则插入
                result = self.mongodb_collection.update_one(
                    {"cache_key": key},
                    {"$set": document},
                    upsert=True
                )

                if result.modified_count > 0 or result.upserted_id:
                    logger.debug("MongoDB缓存写入成功", key=key)
                else:
                    logger.warning("MongoDB缓存写入失败", key=key)

            except Exception as e:
                logger.warning("MongoDB缓存写入异常", key=key, error=str(e))

        return main_success

    def delete(self, key: str) -> bool:
        """
        删除缓存值
        同时从主缓存和MongoDB删除

        Args:
            key: 缓存键

        Returns:
            bool: 是否成功（以主缓存操作为准）
        """
        # 主要操作：从主缓存删除
        main_success = False
        try:
            main_success = CacheManager._cache.delete(key)
            if main_success:
                logger.debug("主缓存删除成功", key=key)
            else:
                logger.debug("主缓存键不存在", key=key)
        except Exception as e:
            logger.error("主缓存删除异常", key=key, error=str(e))

        # 辅助操作：从MongoDB删除（不影响主流程）
        if self.mongodb_collection is not None:
            try:
                result = self.mongodb_collection.delete_one({"cache_key": key})

                if result.deleted_count > 0:
                    logger.debug("MongoDB缓存删除成功", key=key)
                else:
                    logger.debug("MongoDB缓存键不存在", key=key)

            except Exception as e:
                logger.warning("MongoDB缓存删除异常", key=key, error=str(e))

        return main_success

    def clear(self) -> bool:
        """
        清空缓存
        同时清空主缓存和MongoDB中的分析缓存

        Returns:
            bool: 是否成功（以主缓存操作为准）
        """
        # 主要操作：清空主缓存
        main_success = False
        try:
            main_success = CacheManager._cache.clear()
            if main_success:
                logger.info("主缓存清空成功")
            else:
                logger.warning("主缓存清空失败")
        except Exception as e:
            logger.error("主缓存清空异常", error=str(e))

        # 辅助操作：清空MongoDB中的分析缓存（不影响主流程）
        if self.mongodb_collection is not None:
            try:
                result = self.mongodb_collection.delete_many({})  # 删除所有文档
                deleted_count = result.deleted_count

                logger.info("MongoDB分析缓存清空成功", deleted_count=deleted_count)

            except Exception as e:
                logger.warning("MongoDB缓存清空异常", error=str(e))

        return main_success

    def close(self) -> None:
        """
        关闭缓存连接
        """
        try:
            # 关闭主缓存
            if CacheManager._cache:
                CacheManager._cache.close()
                logger.info("主缓存连接已关闭")

            # 关闭MongoDB连接
            if self.mongodb_client:
                self.mongodb_client.close()
                logger.info("MongoDB连接已关闭")

            logger.info("缓存管理器已关闭")

        except Exception as e:
            logger.error("关闭缓存连接失败", error=str(e))

    def get_state(self, key: str) -> Any:
        """
        获取分析状态

        Args:
            key: 缓存键

        Returns:
            Any: 分析状态或None
        """
        state_json = self.get(key)
        if state_json:
            return state_json
        return None

    def set_state(self, key: str, state: Any, expire: int = None) -> bool:
        """
        设置分析状态
        自动序列化状态对象并使用双写机制

        Args:
            key: 缓存键
            state: 分析状态对象（Pydantic模型或可序列化对象）
            expire: 过期时间（秒）

        Returns:
            bool: 是否成功
        """
        try:
            # 序列化状态对象
            state_json = None

            # 检查是否为Pydantic模型
            if isinstance(state, BaseModel):
                # Pydantic对象，使用model_dump_json方法
                state_json = state.model_dump_json(indent=4)
            else:
                # 非Pydantic对象，使用标准JSON序列化
                try:
                    state_json = json.dumps(state, indent=4, ensure_ascii=False, default=str)
                except (TypeError, ValueError) as json_error:
                    logger.error("对象序列化失败", key=key, error=str(json_error))
                    return False

            # 使用双写机制存储
            return self.set(key, state_json, expire=expire)

        except Exception as e:
            logger.error("缓存状态失败", key=key, error=str(e))
            return False
