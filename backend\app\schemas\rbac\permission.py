"""
权限相关的Pydantic模型
包含权限的创建、更新和查询模型
"""
from datetime import datetime
from typing import Optional,List
from typing_extensions import Self
from pydantic import BaseModel, Field, field_validator, model_validator
from enum import Enum

class PermissionType(str, Enum):
    """权限类型"""
    MENU = "menu"  # 菜单权限
    OPERATION = "operation"  # 操作权限
    DATA = "data"  # 数据权限
    API = "api"  # 接口权限
    BUTTON = "button"  # 按钮权限

class Permission(BaseModel):
    """权限数据模型"""
    id: str = Field(..., description='权限ID')
    name: str = Field(..., min_length=2, max_length=100, description='权限名称')
    code: str = Field(..., min_length=2, max_length=100, pattern=r'^[a-z_]+(?::[a-z_]+){0,2}$', description='权限标识符')
    description: Optional[str] = Field(None, max_length=200, description='描述信息')
    type: PermissionType = Field(..., description='权限类型')
    group_id: str = Field(..., description='所属权限组ID')
    sort_order: int = Field(0, ge=0, description='排序号')
    is_active: bool = Field(True, description='是否启用')
    created_at: datetime = Field(..., description='创建时间')
    updated_at: datetime = Field(..., description='更新时间')
    version: str = Field('1.0.0', description='版本号')

    # @field_validator('code')
    # def validate_code(cls, v: str) -> str:
    #     if not v.islower() or ':' not in v:
    #         raise ValueError('权限标识符必须是小写字母、下划线组成，并且包含一个冒号分隔符')
    #     resource, action = v.split(':')
    #     if not resource or not action:
    #         raise ValueError('权限标识符格式必须为 resource:action')
    #     return v

    def __hash__(self) -> int:
        """实现哈希方法，使Permission可以用在Set中
        基于id和code生成哈希值，确保唯一性
        """
        return hash((self.id, self.code))
    
    def __eq__(self, other: object) -> bool:
        """实现相等比较方法
        两个权限对象的id和code相同时视为相等
        """
        if not isinstance(other, Permission):
            return False
        return self.id == other.id and self.code == other.code

    def __contains__(self, item: object) -> bool:
        """实现包含判断方法
        两个权限对象的id或code相同时视为包含
        """
        if not isinstance(item, Permission):
            return False
        return self.id == item.id or self.code == item.code

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "1",
                "name": "创建用户",
                "code": "system:user:add",
                "description": "创建新用户的权限",
                "group_id": "1",
                "sort_order": 0,
                "is_active": True,
                "created_at": "2022-01-01 12:00:00",
                "updated_at": "2022-01-01 12:00:00",
                "version": "1.0.0"
            }
        }

class PermissionCreateRequest(BaseModel):
    """创建权限的请求数据模型"""
    name: str = Field(..., min_length=2, max_length=100, description='权限名称')
    code: str = Field(..., min_length=2, max_length=100, pattern=r'^[a-z_]+:[a-z_]+$', description='权限标识符')
    type: PermissionType = Field(..., description='权限类型')
    description: Optional[str] = Field(None, max_length=200, description='描述信息')
    group_id: str = Field(..., description='所属权限组ID')
    sort_order: int = Field(0, ge=0, description='排序号')
    is_active: bool = Field(True, description='是否启用')

    @field_validator('code')
    def validate_code(cls, v: str) -> str:
        if not v.islower() or ':' not in v:
            raise ValueError('权限标识符必须是小写字母、下划线组成，并且包含一个冒号分隔符')
        resource, action = v.split(':')
        if not resource or not action:
            raise ValueError('权限标识符格式必须为 resource:action')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "name": "创建用户",
                "code": "system:user:add",
                "description": "创建新用户的权限",
                "group_id": "1",
                "type": "menu",
                "sort_order": 0,
                "is_active": True
            }
        }

class PermissionUpdateRequest(BaseModel):
    """更新权限的请求数据模型"""
    permission_id: str = Field(..., description='权限ID')
    name: Optional[str] = Field(None, min_length=2, max_length=100, description='权限名称')
    code: Optional[str] = Field(None, min_length=2, max_length=100, pattern=r'^[a-z_]+:[a-z_]+$', description='权限标识符')
    description: Optional[str] = Field(None, max_length=200, description='描述信息')
    group_id: Optional[str] = Field(None, description='所属权限组ID')
    type: Optional[PermissionType] = Field(None, description='权限类型')
    sort_order: Optional[int] = Field(None, ge=0, description='排序号')
    is_active: Optional[bool] = Field(None, description='是否启用')

    @field_validator('code')
    def validate_code(cls, v: str) -> str:
        if not v.islower() or ':' not in v:
            raise ValueError('权限标识符必须是小写字母、下划线组成，并且包含一个冒号分隔符')
        resource, action = v.split(':')
        if not resource or not action:
            raise ValueError('权限标识符格式必须为 resource:action')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "permission_id": "1",
                "name": "创建用户",
                "code": "system:user:add",
                "description": "创建新用户的权限",
                "group_id": "1",
                "type": "menu",
                "sort_order": 1,
                "is_active": True
            }
        }

class PermissionListResponse(BaseModel):
    """权限列表分页响应"""
    total: int = Field(..., description="总记录数")
    permissions: List[Permission] = Field(..., description="权限列表")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(0, description="总页数")

    @model_validator(mode="after")
    def compute_total_pages(self) -> Self:
        """计算总页数"""    
        if self.total and self.page_size and self.total > 0 and self.page_size > 0:
            self.total_pages = (self.total + self.page_size - 1) // self.page_size
        else:
            self.total_pages = 0
        return self

    class Config:
        """配置"""
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        
# 解决Permission类的循环引用
PermissionListResponse.model_rebuild()