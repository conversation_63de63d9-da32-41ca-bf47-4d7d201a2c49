#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
# @Author: tongbang.cui
# @File: filetools.py
# @Date: 2025/03/20
# @Version: 1.0.0
# @Description: 文件处理工具服务
"""
import os
import sys
import json
import logging
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import difflib
import fnmatch
import shutil
from datetime import datetime
from enum import Enum

from mcp.server import Server
from mcp.server.stdio import stdio_server
from mcp.types import TextContent, Tool

from pydantic import BaseModel, Field

# 创建模型定义
class ReadFileArgs(BaseModel):
    path: str

class ReadMultipleFilesArgs(BaseModel):
    paths: List[str]

class WriteFileArgs(BaseModel):
    path: str
    content: str

class EditOperation(BaseModel):
    oldText: str = Field(..., description="Text to search for - must match exactly")
    newText: str = Field(..., description="New text to replace with")

class EditFileArgs(BaseModel):
    path: str
    edits: List[EditOperation]
    dryRun: bool = Field(False, description="Preview changes using git-style diff format")

class CreateDirectoryArgs(BaseModel):
    path: str

class ListDirectoryArgs(BaseModel):
    path: str

class DirectoryTreeArgs(BaseModel):
    path: str

class MoveFileArgs(BaseModel):
    source: str
    destination: str

class SearchFilesArgs(BaseModel):
    path: str
    pattern: str
    excludePatterns: Optional[List[str]] = []

class GetFileInfoArgs(BaseModel):
    path: str

class FileInfo(BaseModel):
    size: int
    created: datetime
    modified: datetime
    accessed: datetime
    isDirectory: bool
    isFile: bool
    permissions: str

class FileTool(str, Enum):
    READ_FILE = "read_file"
    READ_MULTIPLE_FILES = "read_multiple_files"
    WRITE_FILE = "write_file"
    EDIT_FILE = "edit_file"
    CREATE_DIRECTORY = "create_directory"
    LIST_DIRECTORY = "list_directory"
    DIRECTORY_TREE = "directory_tree"
    MOVE_FILE = "move_file"
    SEARCH_FILES = "search_files"
    GET_FILE_INFO = "get_file_info"
    LIST_ALLOWED_DIRECTORIES = "list_allowed_directories"

# 保存允许访问的目录
allowed_directories = []

# 路径验证和安全工具
def normalize_path(p: str) -> str:
    """Normalize path to ensure consistent path representation."""
    return os.path.normpath(p)

def expand_home(filepath: str) -> str:
    """Expand user home directory path (~)."""
    if filepath.startswith('~/') or filepath == '~':
        return os.path.join(os.path.expanduser('~'), filepath[1:] if filepath != '~' else '')
    return filepath

async def validate_path(requested_path: str) -> str:
    """
    Validate path to ensure it's within allowed directories and return resolved path.
    """
    expanded_path = expand_home(requested_path)
    absolute = os.path.abspath(expanded_path)
    normalized_requested = normalize_path(absolute)

    # Check if path is within allowed directories
    is_allowed = any(normalized_requested.startswith(dir) for dir in allowed_directories)
    if not is_allowed:
        raise ValueError(f"Access denied - path exceeds allowed directory scope: {absolute} not in {', '.join(allowed_directories)}")

    # Handle symbolic links, check their real path
    try:
        real_path = os.path.realpath(absolute)
        normalized_real = normalize_path(real_path)
        is_real_path_allowed = any(normalized_real.startswith(dir) for dir in allowed_directories)
        if not is_real_path_allowed:
            raise ValueError("Access denied - symbolic link target exceeds allowed directory scope")
        return real_path
    except Exception:
        # For new files that don't exist yet, validate parent directory
        parent_dir = os.path.dirname(absolute)
        try:
            real_parent_path = os.path.realpath(parent_dir)
            normalized_parent = normalize_path(real_parent_path)
            is_parent_allowed = any(normalized_parent.startswith(dir) for dir in allowed_directories)
            if not is_parent_allowed:
                raise ValueError("Access denied - parent directory exceeds allowed directory scope")
            return absolute
        except Exception:
            raise ValueError(f"Parent directory does not exist: {parent_dir}")

# 文件统计信息
async def get_file_stats(file_path: str) -> FileInfo:
    """Get file statistics."""
    stats = os.stat(file_path)
    return FileInfo(
        size=stats.st_size,
        created=datetime.fromtimestamp(stats.st_ctime),
        modified=datetime.fromtimestamp(stats.st_mtime),
        accessed=datetime.fromtimestamp(stats.st_atime),
        isDirectory=os.path.isdir(file_path),
        isFile=os.path.isfile(file_path),
        permissions=oct(stats.st_mode)[-3:]
    )

# 文件搜索功能
async def search_files(
    root_path: str,
    pattern: str,
    exclude_patterns: List[str] = []
) -> List[str]:
    """Recursively search for files and directories matching the pattern."""
    results = []

    async def search(current_path: str):
        try:
            entries = os.listdir(current_path)
            
            for entry in entries:
                full_path = os.path.join(current_path, entry)
                
                try:
                    # Validate each path
                    await validate_path(full_path)
                    
                    # Check if path matches exclude patterns
                    relative_path = os.path.relpath(full_path, root_path)
                    should_exclude = any(
                        fnmatch.fnmatch(
                            relative_path, 
                            pattern if '*' in pattern else f"**/{pattern}/**"
                        ) 
                        for pattern in exclude_patterns
                    )
                    
                    if should_exclude:
                        continue
                    
                    if pattern.lower() in entry.lower():
                        results.append(full_path)
                    
                    if os.path.isdir(full_path):
                        await search(full_path)
                except Exception:
                    # Skip invalid paths during search
                    continue
        except Exception:
            # Continue if directory is inaccessible
            pass

    await search(root_path)
    return results

# 文件编辑和差异工具
def normalize_line_endings(text: str) -> str:
    """Normalize line endings to \n."""
    return text.replace('\r\n', '\n')

def create_unified_diff(original_content: str, new_content: str, filepath: str = 'file') -> str:
    """Create unified diff."""
    # Ensure diff comparison has consistent line endings
    normalized_original = normalize_line_endings(original_content)
    normalized_new = normalize_line_endings(new_content)
    
    return '\n'.join(difflib.unified_diff(
        normalized_original.splitlines(),
        normalized_new.splitlines(),
        fromfile=filepath,
        tofile=filepath,
        lineterm=''
    ))

async def apply_file_edits(
    file_path: str,
    edits: List[Dict[str, str]],
    dry_run: bool = False
) -> str:
    """Apply file edits and return diff."""
    # Read file content and normalize line endings
    with open(file_path, 'r', encoding='utf-8') as f:
        content = normalize_line_endings(f.read())
    
    # Apply edits in order
    modified_content = content
    for edit in edits:
        normalized_old = normalize_line_endings(edit["oldText"])
        normalized_new = normalize_line_endings(edit["newText"])
        
        # If exact match exists, use it
        if normalized_old in modified_content:
            modified_content = modified_content.replace(normalized_old, normalized_new)
            continue
        
        # Otherwise, try line-by-line matching with flexible whitespace handling
        old_lines = normalized_old.split('\n')
        content_lines = modified_content.split('\n')
        match_found = False
        
        for i in range(len(content_lines) - len(old_lines) + 1):
            potential_match = content_lines[i:i + len(old_lines)]
            
            # Compare lines with normalized whitespace
            is_match = all(
                old_line.strip() == content_line.strip()
                for old_line, content_line in zip(old_lines, potential_match)
            )
            
            if is_match:
                # Preserve original indentation of first line
                original_indent = content_lines[i].split(content_lines[i].lstrip())[0]
                new_lines = []
                
                for j, line in enumerate(normalized_new.split('\n')):
                    if j == 0:
                        new_lines.append(original_indent + line.lstrip())
                    else:
                        # For subsequent lines, try to maintain relative indentation
                        if j < len(old_lines):
                            old_indent = old_lines[j].split(old_lines[j].lstrip())[0] if old_lines[j].strip() else ''
                            new_indent = line.split(line.lstrip())[0] if line.strip() else ''
                            
                            if old_indent and new_indent:
                                relative_indent = len(new_indent) - len(old_indent)
                                indent = original_indent + ' ' * max(0, relative_indent)
                                new_lines.append(indent + line.lstrip())
                            else:
                                new_lines.append(line)
                        else:
                            new_lines.append(line)
                
                content_lines[i:i + len(old_lines)] = new_lines
                modified_content = '\n'.join(content_lines)
                match_found = True
                break
        
        if not match_found:
            raise ValueError(f"Exact match for edit not found:\n{edit['oldText']}")
    
    # Create unified diff
    diff = create_unified_diff(content, modified_content, file_path)
    
    # Format diff output
    formatted_diff = f"```diff\n{diff}\n```\n\n"
    
    if not dry_run:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
    
    return formatted_diff
    
async def read_file(args: ReadFileArgs) -> TextContent:
    valid_path = await validate_path(args.path)
    with open(valid_path, "r", encoding="utf-8") as f:
        content = f.read()
    return TextContent(text=content)

async def read_multiple_files(args: ReadMultipleFilesArgs) -> TextContent:
    results = []
    for file_path in args.paths:
        try:
            valid_path = await validate_path(file_path)
            with open(valid_path, "r", encoding="utf-8") as f:
                content = f.read()
            results.append(f"{file_path}:\n{content}\n")
        except Exception as e:
            results.append(f"{file_path}: Error - {str(e)}")
    
    return TextContent(text="\n---\n".join(results))

async def write_file(args: WriteFileArgs) -> TextContent:
    valid_path = await validate_path(args.path)
    
    # Ensure directory exists
    directory = os.path.dirname(valid_path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)
        
    with open(valid_path, "w", encoding="utf-8") as f:
        f.write(args.content)
    
    return TextContent(text=f"Successfully wrote to {args.path}")

async def edit_file(args: EditFileArgs) -> TextContent:
    valid_path = await validate_path(args.path)
    result = await apply_file_edits(valid_path, [edit.dict() for edit in args.edits], args.dryRun)
    return TextContent(text=result)

async def create_directory(args: CreateDirectoryArgs) -> TextContent:
    valid_path = await validate_path(args.path)
    os.makedirs(valid_path, exist_ok=True)
    return TextContent(text=f"Successfully created directory {args.path}")

async def list_directory(args: ListDirectoryArgs) -> TextContent:
    valid_path = await validate_path(args.path)
    entries = os.listdir(valid_path)
    formatted = []
    
    for entry in entries:
        full_path = os.path.join(valid_path, entry)
        entry_type = "[DIR]" if os.path.isdir(full_path) else "[FILE]"
        formatted.append(f"{entry_type} {entry}")
    
    return TextContent(text="\n".join(formatted))

async def directory_tree(args: DirectoryTreeArgs) -> TextContent:
    valid_path = await validate_path(args.path)
    
    async def build_tree(current_path: str) -> List[Dict[str, Any]]:
        entries = os.listdir(current_path)
        result = []
        
        for entry in entries:
            full_path = os.path.join(current_path, entry)
            entry_data = {
                "name": entry,
                "type": "directory" if os.path.isdir(full_path) else "file"
            }
            
            if os.path.isdir(full_path):
                entry_data["children"] = await build_tree(full_path)
            
            result.append(entry_data)
        
        return result
    
    tree_data = await build_tree(valid_path)
    return TextContent(text=json.dumps(tree_data, indent=2, ensure_ascii=False))

async def move_file(args: MoveFileArgs) -> TextContent:
    valid_source_path = await validate_path(args.source)
    valid_dest_path = await validate_path(args.destination)
    
    # Ensure target directory exists
    dest_dir = os.path.dirname(valid_dest_path)
    if dest_dir and not os.path.exists(dest_dir):
        os.makedirs(dest_dir)
        
    shutil.move(valid_source_path, valid_dest_path)
    return TextContent(text=f"Successfully moved {args.source} to {args.destination}")

async def search_files_tool(args: SearchFilesArgs) -> TextContent:
    valid_path = await validate_path(args.path)
    results = await search_files(valid_path, args.pattern, args.excludePatterns or [])
    
    if results:
        return TextContent(text="\n".join(results))
    else:
        return TextContent(text="No matches found")

async def get_file_info(args: GetFileInfoArgs) -> TextContent:
    valid_path = await validate_path(args.path)
    info = await get_file_stats(valid_path)
    
    # Convert Pydantic model to dictionary and format as text
    info_dict = info.model_dump()
    formatted_info = "\n".join([f"{key}: {value}" for key, value in info_dict.items()])
    
    return TextContent(text=formatted_info)

async def list_allowed_directories() -> TextContent:
    return TextContent(text=f"Allowed directories:\n{chr(10).join(allowed_directories)}")

# 服务实现
async def serve(dirs: List[str]) -> None:
    """Start file tool server."""
    global allowed_directories
    
    logger = logging.getLogger(__name__)
    
    # Normalize all paths
    allowed_directories = [normalize_path(os.path.abspath(expand_home(dir))) for dir in dirs]
    
    # Validate all directories exist and are accessible
    for dir in dirs:
        try:
            expanded = expand_home(dir)
            if not os.path.isdir(expanded):
                logger.error(f"Error: {expanded} is not a directory")
                return
        except Exception as e:
            logger.error(f"Error accessing directory {dir}: {e}")
            return
    
    server = Server("secure-filesystem-server")
    
    @server.list_tools()
    async def list_tools() -> List[Tool]:
        return [
            Tool(
                name=FileTool.READ_FILE,
                description="Read the full content of a file from the filesystem."
                            "Handles various text encodings and provides detailed error messages,"
                            "if the file cannot be read. Use this tool when you need to inspect"
                            "the content of a single file. Works only within allowed directories.",
                inputSchema=ReadFileArgs.model_json_schema()
            ),
            Tool(
                name=FileTool.READ_MULTIPLE_FILES,
                description="Read the content of multiple files simultaneously."
                            "This is more efficient than reading files one by one when you need"
                            "to analyze or compare multiple files. Each file's content is returned"
                            "with its path as a reference. Failure to read a single file does not"
                            "stop the entire operation. Works only within allowed directories.",
                inputSchema=ReadMultipleFilesArgs.model_json_schema()
            ),
            Tool(
                name=FileTool.WRITE_FILE,
                description="Create a new file or completely overwrite an existing file with new content."
                            "Use with caution, as it will overwrite existing files without warning."
                            "Handles text content with proper encoding. Works only within allowed directories.",
                inputSchema=WriteFileArgs.model_json_schema()
            ),
            Tool(
                name=FileTool.EDIT_FILE,
                description="Perform line-based edits on text files. Each edit replaces an exact sequence"
                            "of lines with new content. Returns a git-style diff showing the changes made."
                            "Works only within allowed directories.",
                inputSchema=EditFileArgs.model_json_schema()
            ),
            Tool(
                name=FileTool.CREATE_DIRECTORY,
                description="Create a new directory or ensure a directory exists."
                            "Can create multiple nested directories in a single operation."
                            "If the directory already exists, this operation will silently succeed."
                            "Useful for setting up project directory structures or ensuring required paths exist."
                            "Works only within allowed directories.",
                inputSchema=CreateDirectoryArgs.model_json_schema()
            ),
            Tool(
                name=FileTool.LIST_DIRECTORY,
                description="Get a detailed list of all files and directories in a specified path."
                            "Results clearly distinguish files and directories with [FILE] and [DIR] prefixes."
                            "This tool is essential for understanding directory structures and finding specific files"
                            "within directories. Works only within allowed directories.",
                inputSchema=ListDirectoryArgs.model_json_schema()
            ),
            Tool(
                name=FileTool.DIRECTORY_TREE,
                description="Get a recursive tree view of files and directories in JSON format."
                            "Each entry includes 'name', 'type' (file/directory), and 'children' for directories."
                            "Files do not have a 'children' array, while directories always have a 'children' array"
                            "(which may be empty). Output is formatted with 2-space indentation for readability."
                            "Works only within allowed directories.",
                inputSchema=DirectoryTreeArgs.model_json_schema()
            ),
            Tool(
                name=FileTool.MOVE_FILE,
                description="Move or rename files and directories. Can move files between directories"
                            "and rename them in a single operation. If the target exists, the operation will fail."
                            "Useful for moving files across different directories and for simple renames within"
                            "the same directory. Both source and target must be within allowed directories.",
                inputSchema=MoveFileArgs.model_json_schema()
            ),
            Tool(
                name=FileTool.SEARCH_FILES,
                description="Recursively search for files and directories matching a pattern."
                            "Searches all subdirectories from the starting path. Search is case-insensitive"
                            "and matches partial names. Returns the full paths of all matches."
                            "Useful for finding files when you don't know their exact location."
                            "Works only within allowed directories.",
                inputSchema=SearchFilesArgs.model_json_schema()
            ),
            Tool(
                name=FileTool.GET_FILE_INFO,
                description="Retrieve detailed metadata about a file or directory."
                            "Returns comprehensive information, including size, creation time, last modification time,"
                            "permissions, and type. This tool is ideal for understanding file characteristics"
                            "without needing to read the actual content. Works only within allowed directories.",
                inputSchema=GetFileInfoArgs.model_json_schema()
            ),
            Tool(
                name=FileTool.LIST_ALLOWED_DIRECTORIES,
                description="Return a list of directories allowed by this server."
                            "Use this tool to understand which directories are accessible before attempting"
                            "to access files.",
                inputSchema={}
            ),
        ]

    @server.call_tool()
    async def call_tool(name: str, arguments: dict) -> List[TextContent]:
        """Unified call to defined tool methods. Routes to the appropriate handling function based on tool name."""
        try:
            # Convert dictionary-type arguments to corresponding Pydantic models
            match name:
                case FileTool.READ_FILE:
                    args = ReadFileArgs(**arguments)
                    result = await read_file(args)
                    return [result]
                    
                case FileTool.READ_MULTIPLE_FILES:
                    args = ReadMultipleFilesArgs(**arguments)
                    result = await read_multiple_files(args)
                    return [result]
                    
                case FileTool.WRITE_FILE:
                    args = WriteFileArgs(**arguments)
                    result = await write_file(args)
                    return [result]
                    
                case FileTool.EDIT_FILE:
                    args = EditFileArgs(**arguments)
                    result = await edit_file(args)
                    return [result]
                    
                case FileTool.CREATE_DIRECTORY:
                    args = CreateDirectoryArgs(**arguments)
                    result = await create_directory(args)
                    return [result]
                    
                case FileTool.LIST_DIRECTORY:
                    args = ListDirectoryArgs(**arguments)
                    result = await list_directory(args)
                    return [result]
                    
                case FileTool.DIRECTORY_TREE:
                    args = DirectoryTreeArgs(**arguments)
                    result = await directory_tree(args)
                    return [result]
                    
                case FileTool.MOVE_FILE:
                    args = MoveFileArgs(**arguments)
                    result = await move_file(args)
                    return [result]
                    
                case FileTool.SEARCH_FILES:
                    args = SearchFilesArgs(**arguments)
                    result = await search_files_tool(args)
                    return [result]
                    
                case FileTool.GET_FILE_INFO:
                    args = GetFileInfoArgs(**arguments)
                    result = await get_file_info(args)
                    return [result]
                    
                case FileTool.LIST_ALLOWED_DIRECTORIES:
                    result = await list_allowed_directories()
                    return [result]
                    
                case _:
                    raise ValueError(f"Unknown tool: {name}")
        except Exception as e:
            return [TextContent(text=f"Error: {str(e)}", type="error")]
    
    # Start server
    options = server.create_initialization_options()
    async with stdio_server() as (read_stream, write_stream):
        await server.run(read_stream, write_stream, options, raise_exceptions=True)
    
    logger.info("Secure filesystem server running on stdio")
    logger.info(f"Allowed directories: {allowed_directories}")

def main():
    """Main function, handles command-line arguments and starts the server."""
    # Command-line argument parsing
    if len(sys.argv) <= 1:
        print("Usage: python filetools.py <allowed_directory> [other_directories...]", file=sys.stderr)
        sys.exit(1)
    
    dirs = sys.argv[1:]
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        asyncio.run(serve(dirs))
    except KeyboardInterrupt:
        print("Server terminated", file=sys.stderr)
    except Exception as e:
        print(f"Fatal error running server: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    sys.argv.append(os.path.dirname(__file__))
    main()
