#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多粒度分析模型定义
包含函数级、模块级和项目级分析使用的Pydantic模型
"""
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Literal
from uuid import uuid4

from pydantic import BaseModel, Field, field_validator

# 注意力分析结果的Pydantic模型
class AttentionPointInfo(BaseModel):
    """注意点信息"""
    type: str = Field(default="", description="注意点类型")
    score: float = Field(default=0.0, description="重要性评分")
    reason: str = Field(default="", description="注意原因")
    line: int = Field(default=0, description="相关行号")
    function: str = Field(default="", description="相关函数名")
    file: str = Field(default="", description="相关文件")
    module: str = Field(default="", description="相关模块")
    pattern: str = Field(default="", description="相关模式")
    description: str = Field(default="", description="详细描述")


class AnalysisFinding(BaseModel):
    """分析发现模型"""
    id: str = Field(default=str(uuid4()), description="发现标识符")
    title: str = Field(default="", description="发现标题")
    description: str = Field(default="", description="详细描述")
    severity: str = Field(default="", description="严重程度(info/warning/critical)")
    related_paths: List[str] = Field(default_factory=list, description="相关路径")
    suggestions: List[str] = Field(default_factory=list, description="相关建议")
