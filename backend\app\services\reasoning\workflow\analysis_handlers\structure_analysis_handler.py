#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结构分析处理器
负责分析项目的目录结构和文件组织
"""
import asyncio
import structlog
from pathlib import Path
from typing import Optional, List

from ..models import AnalysisConfig, AnalysisState
from .base_handler import BaseAnalysisHandler
from ....ai_agent_core import agent_manager
from ...analyzers.project_structure_analyzer import ProjectStructureAnalyzer
from ...analyzers.project_structure_models import ProjectStructureAnalysis

logger = structlog.get_logger(__name__)

class StructureAnalysisHandler(BaseAnalysisHandler):
    """
    结构分析处理器
    负责分析项目的目录结构和文件组织
    """

    def __init__(self, config: AnalysisConfig, cache_dir: str = '.cache'):
        """
        初始化结构分析处理器

        Args:
            config: 分析配置
            cache_dir: 缓存目录
        """
        self.structure_analyzer: Optional[ProjectStructureAnalyzer] = None
        self.max_concurrent_chunks = config.parallel_workers
        super().__init__(config, cache_dir)

    def initialize(self) -> None:
        """
        初始化结构分析器
        """
        try:
            self.structure_analyzer = agent_manager.load("ProjectStructureAnalyzerAgent")
            logger.info("结构分析器初始化完成")
        except Exception as e:
            logger.error(f"结构分析器初始化失败: {str(e)}")
            raise

    async def process(self, state: AnalysisState) -> AnalysisState:
        """
        处理结构分析

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        # 1. 从缓存获取状态
        cached_state = self.get_cached_state()

        # 2. 执行结构分析
        try:
            # 检查是否需要执行结构分析
            if not self.config.analyze_structure:
                logger.info("配置指定不分析结构，跳过结构分析")
                return state

            # 检查是否可以使用缓存
            if cached_state and cached_state.structure_analysis and state.is_incremental_analysis:
                # 检查是否有新增或删除的模块
                has_structural_changes = len(state.new_modules) > 0 or len(state.deleted_modules) > 0

                # 如果没有新增或删除的模块，可以使用缓存的结构分析结果
                if not has_structural_changes:
                    logger.info("没有新增或删除的模块，使用缓存的结构分析结果")
                    state.structure_analysis = cached_state.structure_analysis
                    state.analysis_progress = 0.2  # 结构分析完成，进度20%
                    return state

                # 如果有新增或删除的模块，需要重新分析项目结构
                logger.info(f"检测到 {len(state.new_modules)} 个新增模块和 {len(state.deleted_modules)} 个删除模块，需要重新分析项目结构")

            # 执行结构分析
            state = await self._analyze_structure(state)

            logger.info("项目结构分析完成")
        except Exception as e:
            logger.error(f"结构分析失败: {str(e)}")
            state.errors["structure_analysis"] = str(e)
            raise

        # 3. 设置缓存
        self.set_cached_state(state)

        # 4. 返回分析结果
        return state

    async def _analyze_structure(self, state: AnalysisState) -> AnalysisState:
        """
        分析项目结构
        使用分片并发分析方式提高大型项目的分析性能

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        logger.info("开始项目结构分析")

        try:
            # 检查结构分析器是否已初始化
            if self.structure_analyzer is None:
                raise ValueError("结构分析器未初始化")

            # 检查是否有分片结构数据
            if state.project_structure.structured_chunks:
                logger.info(f"使用分片并发分析，共 {len(state.project_structure.structured_chunks)} 个分片")
                structure_analysis = await self._analyze_structure_chunks(state)
            else:
                logger.info("分片数据不可用，回退到完整结构分析")
                structure_analysis = await self._analyze_complete_structure(state)

            # 保存分析结果
            state.structure_analysis = structure_analysis

            # 更新进度
            state.analysis_progress = 0.2
            logger.info("项目结构分析完成")
        except Exception as e:
            logger.error(f"项目结构分析失败: {str(e)}")
            state.errors["structure_analysis"] = str(e)
            raise

        return state

    async def _analyze_structure_chunks(self, state: AnalysisState) -> ProjectStructureAnalysis:
        """
        使用分片并发分析项目结构

        Args:
            state: 当前分析状态

        Returns:
            合并后的项目结构分析结果
        """
        chunks = state.project_structure.structured_chunks
        max_workers = min(len(chunks), self.max_concurrent_chunks)
        logger.info(f"开始并发分析 {len(chunks)} 个结构分片，使用 {max_workers} 个工作线程")

        # 分批并发分析分片
        try:
            all_results = []

            # 将分片分批处理，每批最多 max_workers 个
            for i in range(0, len(chunks), max_workers):
                batch_chunks = chunks[i:i + max_workers]
                batch_tasks = []

                # 创建当前批次的分析任务
                for j, chunk in enumerate(batch_chunks):
                    chunk_index = i + j
                    task = self._analyze_single_chunk(state, chunk, chunk_index)
                    batch_tasks.append(task)

                logger.debug(f"处理批次 {i//max_workers + 1}，包含 {len(batch_tasks)} 个分片")

                # 并发执行当前批次的任务
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                all_results.extend(batch_results)

            chunk_results = all_results

            # 过滤成功的结果
            successful_results = []
            failed_count = 0
            for i, result in enumerate(chunk_results):
                if isinstance(result, Exception):
                    logger.warning(f"分片 {i} 分析失败: {str(result)}")
                    failed_count += 1
                else:
                    successful_results.append(result)

            if not successful_results:
                logger.error("所有分片分析都失败，回退到完整结构分析")
                return await self._analyze_complete_structure(state)

            if failed_count > 0:
                logger.warning(f"{failed_count} 个分片分析失败，使用 {len(successful_results)} 个成功结果进行合并")

            # 合并分析结果
            merged_result = self._merge_chunk_results(successful_results, state)
            logger.info(f"成功合并 {len(successful_results)} 个分片的分析结果")

            return merged_result

        except Exception as e:
            logger.error(f"分片并发分析失败: {str(e)}")
            logger.info("回退到完整结构分析")
            return await self._analyze_complete_structure(state)

    async def _analyze_single_chunk(self, state: AnalysisState, chunk: str, chunk_index: int) -> ProjectStructureAnalysis:
        """
        分析单个结构分片

        Args:
            state: 当前分析状态
            chunk: 结构分片字符串
            chunk_index: 分片索引

        Returns:
            分片的结构分析结果
        """
        try:
            logger.debug(f"开始分析分片 {chunk_index}")

            # 准备输入数据
            input_data = {
                "parameters": {
                    "project_name": state.project_name,
                    "project_path": state.project_path,
                    "project_structure": chunk,
                }
            }

            # 调用结构分析器
            agent_input = await self.structure_analyzer.prepare_input(input_data)
            output = await self.structure_analyzer.process(agent_input)
            result = output.response.result

            logger.debug(f"分片 {chunk_index} 分析完成")
            return result

        except Exception as e:
            logger.error(f"分片 {chunk_index} 分析失败: {str(e)}")
            raise

    async def _analyze_complete_structure(self, state: AnalysisState) -> ProjectStructureAnalysis:
        """
        分析完整项目结构（降级策略）

        Args:
            state: 当前分析状态

        Returns:
            完整的项目结构分析结果
        """
        try:
            logger.info("使用完整结构分析")

            # 准备输入数据
            input_data = {
                "parameters": {
                    "project_name": state.project_name,
                    "project_path": state.project_path,
                    "project_structure": state.project_structure.structure_string,
                }
            }

            # 调用结构分析器
            agent_input = await self.structure_analyzer.prepare_input(input_data)
            output = await self.structure_analyzer.process(agent_input)
            result = output.response.result

            logger.info("完整结构分析完成")
            return result

        except Exception as e:
            logger.error(f"完整结构分析失败: {str(e)}")
            raise

    def _normalize_path(self, path: str) -> str:
        """
        规范化路径，处理相对路径、绝对路径、路径分隔符差异等问题

        Args:
            path: 原始路径字符串

        Returns:
            规范化后的路径字符串
        """
        if not path:
            return ""

        try:
            # 使用 pathlib.Path 进行路径规范化
            normalized = Path(path).as_posix()  # 统一使用正斜杠

            # 移除开头的 "./"
            if normalized.startswith("./"):
                normalized = normalized[2:]

            # 确保路径不以斜杠开头（除非是绝对路径）
            if normalized.startswith("/") and not Path(path).is_absolute():
                normalized = normalized[1:]

            return normalized
        except Exception:
            # 如果路径规范化失败，返回原始路径
            return path

    def _validate_component_path(self, component_path: str, project_root: str) -> bool:
        """
        验证组件路径的有效性

        Args:
            component_path: 组件路径
            project_root: 项目根目录

        Returns:
            bool: 路径是否有效
        """
        if not component_path or not component_path.strip():
            return False

        try:
            # 构建完整路径
            if Path(component_path).is_absolute():
                full_path = Path(component_path)
            else:
                full_path = Path(project_root) / component_path

            # 检查路径是否存在
            return full_path.exists()
        except Exception as e:
            logger.debug(f"路径验证异常: {component_path}, 错误: {str(e)}")
            return False

    def _merge_chunk_results(self, chunk_results: List[ProjectStructureAnalysis], state: AnalysisState) -> ProjectStructureAnalysis:
        """
        合并多个分片的分析结果

        Args:
            chunk_results: 分片分析结果列表
            state: 当前分析状态

        Returns:
            合并后的项目结构分析结果
        """
        logger.info(f"开始合并 {len(chunk_results)} 个分片结果")

        # 创建合并后的结果对象
        merged_result = ProjectStructureAnalysis(
            project_name=state.project_name,
            project_path=state.project_path,
            primary_language="",
        )

        # 用于去重的集合
        seen_core_components = set()
        seen_configs = set()
        seen_dependencies = set()
        seen_tests = set()
        seen_builds = set()
        seen_docs = set()
        seen_entries = set()

        # 语言统计
        language_counts = {}

        # 项目类型统计
        project_type_counts = {}

        # 合并各个分片的结果
        for chunk_result in chunk_results:
            # 统计主要语言
            if chunk_result.primary_language:
                language_counts[chunk_result.primary_language] = language_counts.get(chunk_result.primary_language, 0) + 1

            # 统计项目类型
            if chunk_result.project_type:
                project_type_counts[chunk_result.project_type] = project_type_counts.get(chunk_result.project_type, 0) + 1

            # 合并核心组件（去重）
            for core_comp in chunk_result.core:
                # 验证路径有效性
                if not self._validate_component_path(core_comp.path, state.project_path):
                    logger.warning(f"跳过无效路径的核心组件: {core_comp.name} ({core_comp.path})")
                    continue

                # 使用规范化路径进行去重，但保持名称字符串比较
                normalized_path = self._normalize_path(core_comp.path)
                comp_key = (core_comp.name, normalized_path)
                if comp_key not in seen_core_components:
                    seen_core_components.add(comp_key)
                    merged_result.core.append(core_comp)

            # 合并配置组件（去重）
            for config in chunk_result.configs:
                # 验证路径有效性
                if not self._validate_component_path(config.path, state.project_path):
                    logger.warning(f"跳过无效路径的配置组件: {config.name} ({config.path})")
                    continue

                # 使用规范化路径进行去重，但保持名称字符串比较
                normalized_path = self._normalize_path(config.path)
                config_key = (config.name, normalized_path)
                if config_key not in seen_configs:
                    seen_configs.add(config_key)
                    merged_result.configs.append(config)

            # 合并依赖管理组件（去重）
            for dependency in chunk_result.dependencies:
                # 验证路径有效性
                if not self._validate_component_path(dependency.path, state.project_path):
                    logger.warning(f"跳过无效路径的依赖管理组件: {dependency.name} ({dependency.path})")
                    continue

                # 使用规范化路径进行去重，但保持名称字符串比较
                normalized_path = self._normalize_path(dependency.path)
                dep_key = (dependency.name, normalized_path)
                if dep_key not in seen_dependencies:
                    seen_dependencies.add(dep_key)
                    merged_result.dependencies.append(dependency)

            # 合并测试组件（去重）
            for test in chunk_result.tests:
                # 验证路径有效性
                if not self._validate_component_path(test.path, state.project_path):
                    logger.warning(f"跳过无效路径的测试组件: {test.name} ({test.path})")
                    continue

                # 使用规范化路径进行去重，但保持名称字符串比较
                normalized_path = self._normalize_path(test.path)
                test_key = (test.name, normalized_path)
                if test_key not in seen_tests:
                    seen_tests.add(test_key)
                    merged_result.tests.append(test)

            # 合并构建部署组件（去重）
            for build in chunk_result.builds:
                # 验证路径有效性
                if not self._validate_component_path(build.path, state.project_path):
                    logger.warning(f"跳过无效路径的构建部署组件: {build.name} ({build.path})")
                    continue

                # 使用规范化路径进行去重，但保持名称字符串比较
                normalized_path = self._normalize_path(build.path)
                build_key = (build.name, normalized_path)
                if build_key not in seen_builds:
                    seen_builds.add(build_key)
                    merged_result.builds.append(build)

            # 合并文档组件（去重）
            for doc in chunk_result.docs:
                # 验证路径有效性
                if not self._validate_component_path(doc.path, state.project_path):
                    logger.warning(f"跳过无效路径的文档组件: {doc.name} ({doc.path})")
                    continue

                # 使用规范化路径进行去重，但保持名称字符串比较
                normalized_path = self._normalize_path(doc.path)
                doc_key = (doc.name, normalized_path)
                if doc_key not in seen_docs:
                    seen_docs.add(doc_key)
                    merged_result.docs.append(doc)

            # 合并入口点组件（去重）
            for entry in chunk_result.entries:
                # 验证路径有效性
                if not self._validate_component_path(entry.path, state.project_path):
                    logger.warning(f"跳过无效路径的入口点组件: {entry.name} ({entry.path})")
                    continue

                # 使用规范化路径进行去重，但保持名称字符串比较
                normalized_path = self._normalize_path(entry.path)
                entry_key = (entry.name, normalized_path)
                if entry_key not in seen_entries:
                    seen_entries.add(entry_key)
                    merged_result.entries.append(entry)

        # 确定主要语言（出现次数最多的）
        if language_counts:
            merged_result.primary_language = max(language_counts, key=language_counts.get)

        # 确定项目类型（出现次数最多的）
        if project_type_counts:
            merged_result.project_type = max(project_type_counts, key=project_type_counts.get)

        # 按重要性排序各组件
        merged_result.core.sort(key=lambda x: x.business_importance, reverse=True)
        merged_result.docs.sort(key=lambda x: x.doc_importance, reverse=True)

        # 按优先级排序
        priority_order = {"critical": 4, "high": 3, "medium": 2, "low": 1}
        merged_result.configs.sort(key=lambda x: priority_order.get(x.config_priority, 0), reverse=True)
        merged_result.tests.sort(key=lambda x: priority_order.get(x.test_priority, 0), reverse=True)
        merged_result.entries.sort(key=lambda x: (x.is_primary, priority_order.get(x.startup_priority, 0)), reverse=True)

        logger.info(f"合并完成: 核心组件 {len(merged_result.core)} 个, "
                   f"配置文件 {len(merged_result.configs)} 个, "
                   f"依赖管理 {len(merged_result.dependencies)} 个, "
                   f"测试组件 {len(merged_result.tests)} 个, "
                   f"构建部署 {len(merged_result.builds)} 个, "
                   f"文档组件 {len(merged_result.docs)} 个, "
                   f"入口点 {len(merged_result.entries)} 个")

        return merged_result
