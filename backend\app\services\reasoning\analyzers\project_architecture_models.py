#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多粒度分析模型定义
包含函数级、模块级和项目级分析使用的Pydantic模型
"""
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Literal
import uuid

from pydantic import BaseModel, Field, field_validator
from .common_models import AttentionPointInfo, AnalysisFinding


class ArchitectureLayer(BaseModel):
    """架构层次信息"""
    name: str = Field(default="", description="层名称")
    responsibilities: List[str] = Field(default_factory=list, description="职责")
    components: List[str] = Field(default_factory=list, description="包含的组件")


class ArchitectureComponent(BaseModel):
    """架构组件信息"""
    name: str = Field(default="", description="组件名称")
    path: str = Field(default="", description="组件路径")
    purpose: str = Field(default="", description="组件用途")
    layer: str = Field(default="", description="所属架构层")


class ArchitecturePatternInfo(BaseModel):
    """架构模式信息"""
    name: str = Field(default="", description="模式名称")
    description: str = Field(default="", description="描述")
    applied_at: List[str] = Field(default_factory=list, description="应用位置")


class ArchitectureQuality(BaseModel):
    """架构质量评估"""
    maintainability: float = Field(default=0.0, description="可维护性评分(0-10)")
    extensibility: float = Field(default=0.0, description="可扩展性评分(0-10)")
    modularity: float = Field(default=0.0, description="模块化程度(0-10)")
    overall: float = Field(default=0.0, description="总体评分(0-10)")


class ArchitectureAntiPattern(BaseModel):
    """架构反模式"""
    name: str = Field(default="", description="反模式名称")
    description: str = Field(default="", description="问题描述")
    severity: str = Field(default="", description="严重程度(high/medium/low)")


class ArchitectureRecommendation(BaseModel):
    """架构改进建议"""
    target_area: str = Field(default="", description="目标改进区域")
    recommendation: str = Field(default="", description="具体建议")
    priority: str = Field(default="", description="优先级(high/medium/low)")


class ArchitectureAnalysis(BaseModel):
    """项目架构分析结果"""
    # 基础信息
    project_name: str = Field(default="", description="项目名称")
    project_path: str = Field(default="", description="项目路径")
    main_languages: List[str] = Field(default_factory=list, description="主要编程语言")
    architecture_style: str = Field(default="", description="整体架构风格")
    
    # 架构组件分析
    architecture_layers: List[ArchitectureLayer] = Field(default_factory=list, description="架构层次")
    architecture_components: List[ArchitectureComponent] = Field(default_factory=list, description="架构组件")

    # 架构模式识别
    architecture_patterns: List[ArchitecturePatternInfo] = Field(default_factory=list, description="识别的架构模式")
    anti_patterns: List[ArchitectureAntiPattern] = Field(default_factory=list, description="识别的架构反模式")

    # 架构评估
    architecture_quality: ArchitectureQuality = Field(default_factory=ArchitectureQuality, description="架构质量评估")
    
    # 改进建议
    recommendations: List[ArchitectureRecommendation] = Field(default_factory=list, description="改进建议")
    findings: List[AnalysisFinding] = Field(default_factory=list, description="架构分析发现")

    def to_markdown(self) -> str:
        """
        将ArchitectureAnalysis的内容转换为markdown格式
        
        Returns:
            str: markdown格式的内容
        """
        markdown = []
        
        # 标题和基本信息
        markdown.append(f"# {self.project_name} 项目架构分析\n")
        
        markdown.append("## 基本信息\n")
        markdown.append(f"- **项目路径**: {self.project_path}")
        
        if self.main_languages:
            markdown.append("- **主要编程语言**:")
            for lang in self.main_languages:
                markdown.append(f"  - {lang}")
        
        if self.architecture_style:
            markdown.append(f"- **整体架构风格**: {self.architecture_style}\n")
        else:
            markdown.append("")
        
        # 架构层次
        if self.architecture_layers:
            markdown.append("## 架构层次\n")
            for layer in self.architecture_layers:
                markdown.append(f"### {layer.name}")
                
                if layer.responsibilities:
                    markdown.append("\n**职责:**")
                    for resp in layer.responsibilities:
                        markdown.append(f"- {resp}")
                
                if layer.components:
                    markdown.append("\n**包含组件:**")
                    for comp in layer.components:
                        markdown.append(f"- {comp}")
                markdown.append("")
        
        # 架构组件
        if self.architecture_components:
            markdown.append("## 架构组件\n")
            # 创建表格标题和分隔符
            markdown.append("| 组件名称 | 所属层 | 组件路径 | 组件用途 |")
            markdown.append("| ------- | ------ | ------- | ------- |")
            
            for comp in self.architecture_components:
                markdown.append(f"| {comp.name} | {comp.layer} | {comp.path} | {comp.purpose} |")
            markdown.append("")
        
        # 架构模式识别
        if self.architecture_patterns:
            markdown.append("## 识别的架构模式\n")
            for pattern in self.architecture_patterns:
                markdown.append(f"### {pattern.name}")
                markdown.append(f"{pattern.description}")
                
                if pattern.applied_at:
                    markdown.append("\n**应用位置:**")
                    for location in pattern.applied_at:
                        markdown.append(f"- {location}")
                markdown.append("")
        
        # 架构反模式
        if self.anti_patterns:
            markdown.append("## 识别的架构反模式\n")
            for anti in self.anti_patterns:
                severity_mark = {
                    "high": "🔴", 
                    "medium": "🟠", 
                    "low": "🟡"
                }.get(anti.severity.lower(), "")
                
                markdown.append(f"### {severity_mark} {anti.name}")
                markdown.append(f"{anti.description}")
                markdown.append(f"\n**严重程度**: {anti.severity}")
                markdown.append("")
        
        # 架构质量评估
        markdown.append("## 架构质量评估\n")
        
        # 创建评分表格
        markdown.append("| 指标 | 评分(0-10) |")
        markdown.append("| --- | --------- |")
        markdown.append(f"| 可维护性 | {self.architecture_quality.maintainability} |")
        markdown.append(f"| 可扩展性 | {self.architecture_quality.extensibility} |")
        markdown.append(f"| 模块化程度 | {self.architecture_quality.modularity} |")
        markdown.append(f"| **总体评分** | **{self.architecture_quality.overall}** |")
        markdown.append("")
        
        # 改进建议
        if self.recommendations:
            markdown.append("## 改进建议\n")
            for rec in self.recommendations:
                priority_mark = {
                    "high": "🔴", 
                    "medium": "🟠", 
                    "low": "🟡"
                }.get(rec.priority.lower(), "")
                
                markdown.append(f"### {priority_mark} {rec.target_area}")
                markdown.append(f"{rec.recommendation}")
                markdown.append(f"\n**优先级**: {rec.priority}")
                markdown.append("")
        
        # 分析发现
        if self.findings:
            markdown.append("## 架构分析发现\n")
            for finding in self.findings:
                markdown.append(f"### {finding.title}")
                markdown.append(f"{finding.description}")
                markdown.append(f"\n**严重程度**: {finding.severity}")
                
                if finding.suggestions:
                    markdown.append("\n**建议:**")
                    for rec in finding.suggestions:
                        markdown.append(f"- {rec}")
                markdown.append("")
        
        return "\n".join(markdown)
