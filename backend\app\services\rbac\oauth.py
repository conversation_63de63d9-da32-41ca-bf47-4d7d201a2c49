"""
OAuth认证服务模块

提供OAuth认证、用户信息获取等功能
"""
import random
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any, List, Tuple, Union
import re
import json
import uuid
from urllib.parse import urlencode

import requests
import structlog
from sqlalchemy import select, and_, or_, func, delete
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import DatabaseError
import httpx
from authlib.integrations.httpx_client import OAuth2Client

from app.core.database import DatabaseError
from app.core.di.providers import SessionProvider, AsyncSessionProvider, RedisProvider
from app.core.config import settings, OAuthProviderSettings
from app.models.rbac.user import UserModel
from app.models.rbac.oauth import OAuthAccountModel
from app.schemas.rbac.oauth import (
    OAuthProvider,
    OAuthProviderList,
    OAuthUserInfo,
    OAuthAccount,
    OAuthAccountCreate,
    OAuthAccountUpdate
)
from app.schemas.rbac.user import User, UserCreateRequest
from app.schemas.rbac.token import Token
from app.services.rbac.auth import AuthService
from app.services.rbac.user import UserService
from app.utils.uuid_generator import UUIDGenerator

logger = structlog.get_logger(__name__)


class OAuthService:
    """OAuth认证服务类"""

    def __init__(
            self,
            session: SessionProvider,
            async_session: AsyncSessionProvider,
            redis: RedisProvider,
            auth_service: AuthService,
            user_service: UserService
    ):
        """初始化OAuth认证服务
        
        Args:
            session: 数据库会话提供者
            async_session: 异步数据库会话提供者
            redis: Redis客户端
            auth_service: 认证服务
            user_service: 用户服务
        """
        self.session = session
        self.async_session = async_session
        self.redis = redis
        self.auth_service = auth_service
        self.user_service = user_service
        logger.debug("OAuth认证服务初始化完成")

    async def get_providers(self) -> OAuthProviderList:
        """获取可用的OAuth提供商列表
        
        Returns:
            提供商列表
        """
        providers = []

        if not settings.oauth.ENABLED:
            return OAuthProviderList(providers=providers)

        # 添加已启用的提供商
        for provider_name in settings.oauth.ENABLED_PROVIDERS:
            if provider_name == "wechat" and provider_name in settings.oauth.ENABLED_PROVIDERS:
                providers.append(OAuthProvider(
                    provider="wechat",
                    name="微信",
                    authorize_url=settings.oauth.WECHAT.AUTHORIZE_URL
                ))
            elif provider_name == "github" and provider_name in settings.oauth.ENABLED_PROVIDERS:
                # https://github.com/login/oauth/authorize?client_id=Ov23liICPxYpHYkYir5O&redirect_uri=http://localhost:8000/callback&state=76d3dd86ed504aee8a380bd8babf87a1&allow_signup=false
                payload = {
                    "response_type": "code",
                    'client_id': settings.oauth.GITHUB.GITHUB_CLIENT_ID,
                    'redirect_uri': settings.oauth.GITHUB.GITHUB_REDIRECT_URI,
                    # 权限：全部
                    'scope': 'repo'
                }

                providers.append(OAuthProvider(
                    provider="github",
                    name="GitHub",
                    payload=payload,
                    authorize_url=settings.oauth.GITHUB.AUTHORIZE_URL + '?' + urlencode(payload)
                ))
            elif provider_name == "gitee" and provider_name in settings.oauth.ENABLED_PROVIDERS:
                # https://github.com/login/oauth/authorize?client_id=Ov23liICPxYpHYkYir5O&redirect_uri=http://localhost:8000/callback&state=76d3dd86ed504aee8a380bd8babf87a1&allow_signup=false
                payload = {
                    "response_type": "code",
                    'client_id': settings.oauth.GITEE.GITEE_CLIENT_ID,
                    'redirect_uri': settings.oauth.GITEE.GITEE_REDIRECT_URI,
                    # gitee没有这个参数
                    #'scope': 'repo'
                }

                providers.append(OAuthProvider(
                    provider="gitee",
                    name="Gitee",
                    payload=payload,
                    authorize_url=settings.oauth.GITEE.AUTHORIZE_URL + '?' + urlencode(payload)
                ))


        return OAuthProviderList(providers=providers)

    async def _fetch_github_emails(self, access_token: str) -> List[Dict[str, Any]]:
        """获取GitHub用户邮箱列表

        Args:
            access_token: 访问令牌

        Returns:
            邮箱列表，每个邮箱包含 email, primary, verified 等信息

        Raises:
            ValueError: 获取邮箱失败
        """
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://api.github.com/user/emails",
                headers={
                    "Accept": "application/vnd.github+json",
                    "Authorization": f"Bearer {access_token}",
                    "X-GitHub-Api-Version": "2022-11-28"
                }
            )

            if response.status_code != 200:
                logger.error(f"获取GitHub用户邮箱失败: {response.text}")
                raise ValueError(f"获取GitHub用户邮箱失败: {response.text}")

            return response.json()

    async def _get_primary_email(self, emails: List[Dict[str, Any]]) -> Optional[str]:
        """从邮箱列表中获取主邮箱

        Args:
            emails: 邮箱列表

        Returns:
            主邮箱地址，如果没有则返回None
        """
        if not emails:
            return None

        # 首先尝试找到主邮箱
        primary_email = next((e["email"] for e in emails if e.get("primary") and e.get("verified")), None)
        if primary_email:
            return primary_email

        # 如果没有主邮箱，使用第一个已验证的邮箱
        verified_email = next((e["email"] for e in emails if e.get("verified")), None)
        return verified_email

    async def get_authorize_url(self, provider: str, redirect_uri: Optional[str] = None,
                                state: Optional[str] = None) -> str:
        """获取OAuth授权URL
        
        Args:
            provider: 提供商名称
            redirect_uri: 重定向URI
            state: 状态参数
        
        Returns:
            授权URL
        
        Raises:
            ValueError: 提供商不支持或未启用
        """
        if not settings.oauth.ENABLED:
            raise ValueError("OAuth认证未启用")

        if provider not in settings.oauth.ENABLED_PROVIDERS:
            raise ValueError(f"OAuth提供商未启用: {provider}")

        # 获取提供商配置
        # provider_config = self._get_provider_config(provider)

        # 使用提供的重定向URI或配置中的默认值

        # 创建OAuth客户端

        if provider == 'github':
            # 生成授权URL
            provider_config = settings.oauth.GITHUB
            client = OAuth2Client(
                client_id=provider_config.GITHUB_CLIENT_ID,
                client_secret=provider_config.GITHUB_CLIENT_SECRET,
                scope=provider_config.SCOPE
            )
            redirect_uri = redirect_uri or provider_config.GITHUB_REDIRECT_URI
            url = client.create_authorization_url(
                provider_config.AUTHORIZE_URL,
                redirect_uri=redirect_uri,
                state="github",
                provider=provider
            )[0]

        if provider == 'gitee':
            provider_config = settings.oauth.GITEE
            client = OAuth2Client(
                client_id=provider_config.GITEE_CLIENT_ID,
                client_secret=provider_config.GITEE_CLIENT_SECRET,
            )
            redirect_uri = redirect_uri or provider_config.GITEE_REDIRECT_URI
            url = client.create_authorization_url(
                provider_config.AUTHORIZE_URL,
                redirect_uri=redirect_uri,
                #provider=provider,
                state="gitee"
                
            )[0]
        return url


    async def handle_github_callback(self, code: str, state: Optional[str] = None) -> Tuple[User, Token]:
        user, token = await self.solve_callback(provider="github", code=code)
        return user, token

    async def handle_gitee_callback(self, code: str, state: Optional[str] = None) -> Tuple[User, Token]:
        user, token = await self.solve_callback(provider="gitee", code=code)
        return user, token

    async def solve_callback(self, provider: str, code: str) -> Dict[str, Any]:

        if not settings.oauth.ENABLED:
            raise ValueError("OAuth认证未启用")

        client = None
        if provider == 'github':
            # 生成授权URL
            provider_config = settings.oauth.GITHUB
            client = OAuth2Client(
                client_id=provider_config.GITHUB_CLIENT_ID,
                client_secret=provider_config.GITHUB_CLIENT_SECRET,
                scope=provider_config.SCOPE
            )

        if provider == 'gitee':
            provider_config = settings.oauth.GITEE
            client = OAuth2Client(
                client_id=provider_config.GITEE_CLIENT_ID,
                client_secret=provider_config.GITEE_CLIENT_SECRET,
            )

        # 获取访问令牌
        token_data = await self._fetch_token(client, provider, code)

        # 获取用户信息
        user_info, need_bind_mail = await self._fetch_user_info(provider, token_data)

        # 查找或创建用户
        user, created = await self._find_or_create_user(user_info, token_data, need_bind_mail)

        access_token = self.auth_service.create_access_token(
            data={"sub": user.id, "is_superuser": user.is_superuser}
        )
        refresh_token = self.auth_service.create_refresh_token(
            data={"sub": user.id}
        )
        # 创建访问令牌
        token = await self.user_service.create_token(user_id=user.id, access_token=access_token,
                                                     refresh_token=refresh_token,
                                                     expires_at=datetime.now() + timedelta(minutes=settings.security.JWT_ACCESS_TOKEN_EXPIRE_MINUTES))

        return user, token

    async def _fetch_token(self, client: OAuth2Client, provider: str, code: str) -> Dict[str, Any]:

        try:


            if provider == "github":
                redirect_uri = settings.oauth.GITHUB.GITHUB_REDIRECT_URI
            elif provider == "gitee":
                redirect_uri = settings.oauth.GITEE.GITEE_REDIRECT_URI
            else:
                raise ValueError(f"不支持的OAuth提供商: {provider}")


            token_data = client.fetch_token(
                settings.oauth.GITHUB.TOKEN_URL if provider == "github" else settings.oauth.GITEE.TOKEN_URL,
                code=code,
                grant_type="authorization_code",
                redirect_uri=redirect_uri
            )
            logger.info(f"获取{provider} OAuth令牌成功", token_data=token_data)
            return token_data

        except Exception as e:
            logger.error("获取OAuth令牌失败", provider=provider, error=str(e))
            raise ValueError(f"获取OAuth令牌失败: {str(e)}")

    async def _fetch_user_info(self, provider: str, token_data: Dict[str, Any]) -> OAuthUserInfo:
        """获取OAuth用户信息
        
        Args:
            provider: 提供商名称
            token_data: 令牌数据
        
        Returns:
            用户信息
        
        Raises:
            ValueError: 获取用户信息失败
        """
        provider_config = self._get_provider_config(provider)
        access_token = token_data.get("access_token")

        if not access_token:
            raise ValueError("访问令牌为空")

        try:
            # 根据不同提供商处理用户信息获取
            if provider == "wechat":
                return await self._fetch_wechat_user_info(provider_config, access_token, token_data.get("openid"))
            elif provider == "github":
                return await self._fetch_github_user_info(provider_config, access_token)
            elif provider == "gitee":
                return await self._fetch_gitee_user_info(provider_config, access_token)
            else:
                raise ValueError(f"不支持的OAuth提供商: {provider}")
        except Exception as e:
            logger.error("获取OAuth用户信息失败", provider=provider, error=str(e))
            raise ValueError(f"获取OAuth用户信息失败: {str(e)}")

    async def _fetch_wechat_user_info(self, provider_config: OAuthProviderSettings, access_token: str,
                                      openid: str) -> OAuthUserInfo:

        raise ValueError("暂时不支持微信登陆")

        if not openid:
            raise ValueError("微信OpenID为空")

        async with httpx.AsyncClient() as client:
            response = await client.get(
                provider_config.USERINFO_URL,
                params={
                    "access_token": access_token,
                    "openid": openid,
                    "lang": "zh_CN"
                }
            )

            if response.status_code != 200:
                raise ValueError(f"获取微信用户信息失败: {response.text}")
            need_bind_mail = False
            data = response.json()

            if "errcode" in data and data["errcode"] != 0:
                raise ValueError(f"获取微信用户信息失败: {data.get('errmsg', '未知错误')}")

            return OAuthUserInfo(
                provider="wechat",
                provider_user_id=openid,
                name=data.get("nickname"),
                nickname=data.get("nickname"),
                avatar=data.get("headimgurl"),
                raw_data=data
            )

    async def _fetch_dingtalk_user_info(self, provider_config: OAuthProviderSettings,
                                        access_token: str) -> OAuthUserInfo:
        """获取钉钉用户信息
        
        Args:
            provider_config: 提供商配置
            access_token: 访问令牌
        
        Returns:
            用户信息
        
        Raises:
            ValueError: 获取用户信息失败
        """
        async with httpx.AsyncClient() as client:
            response = await client.get(
                provider_config.USERINFO_URL,
                params={
                    "access_token": access_token
                }
            )

            if response.status_code != 200:
                raise ValueError(f"获取钉钉用户信息失败: {response.text}")

            data = response.json()

            if data.get("errcode") != 0:
                raise ValueError(f"获取钉钉用户信息失败: {data.get('errmsg', '未知错误')}")

            user_info = data.get("user_info", {})

            return OAuthUserInfo(
                provider="dingtalk",
                provider_user_id=user_info.get("unionid"),
                name=user_info.get("nick"),
                nickname=user_info.get("nick"),
                raw_data=data
            )

    async def _fetch_feishu_user_info(self, provider_config: OAuthProviderSettings, access_token: str) -> OAuthUserInfo:
        """获取飞书用户信息
        
        Args:
            provider_config: 提供商配置
            access_token: 访问令牌
        
        Returns:
            用户信息
        
        Raises:
            ValueError: 获取用户信息失败
        """
        async with httpx.AsyncClient() as client:
            response = await client.get(
                provider_config.USERINFO_URL,
                headers={
                    "Authorization": f"Bearer {access_token}"
                }
            )

            if response.status_code != 200:
                raise ValueError(f"获取飞书用户信息失败: {response.text}")

            data = response.json()

            if data.get("code") != 0:
                raise ValueError(f"获取飞书用户信息失败: {data.get('msg', '未知错误')}")

            user_info = data.get("data", {})

            return OAuthUserInfo(
                provider="feishu",
                provider_user_id=user_info.get("union_id"),
                name=user_info.get("name"),
                nickname=user_info.get("name"),
                email=user_info.get("email"),
                avatar=user_info.get("avatar_url"),
                raw_data=data
            )

    async def _fetch_github_user_info(self, provider_config: OAuthProviderSettings, access_token: str) -> OAuthUserInfo:
        """获取GitHub用户信息
        
        Args:
            provider_config: 提供商配置
            access_token: 访问令牌
        
        Returns:
            用户信息
        
        Raises:
            ValueError: 获取用户信息失败
        """
        async with httpx.AsyncClient() as client:
            response = await client.get(
                provider_config.USERINFO_URL,
                headers={
                    "Authorization": f"Bearer {access_token}"
                }
            )

            if response.status_code != 200:
                raise ValueError(f"获取GitHub用户信息失败: {response.text}")
            need_bind_mail = False
            data = response.json()
            try:
                emails = await self._fetch_github_emails(access_token)
                email = await self._get_primary_email(emails)
            except ValueError as e:
                logger.error(f"获取邮箱失败: {str(e)}")
                email = data.get("email")  # 使用基本信息中的邮箱作为备选
            if not email and emails:
                email = emails[0]  # 使用第一个已验证的邮箱
            if not email:
                # 如果仍然没有邮箱，提示用户
                raise ValueError("请先在GitHub设置中公开您的邮箱，或添加一个已验证的邮箱")

            name = str(data.get("name"))+uuid.uuid4().hex[:4]
            if email is None or email == "None" or email == "":
                # 根据提供商和用户名创建一个伪邮箱
                # normalized_username = re.sub(r'[^a-zA-Z0-9]', '', name)
                # email = f"{normalized_username}_gitee_{uuid.uuid4().hex[:8]}@oauth.example.com"
                need_bind_mail = True


            return OAuthUserInfo(
                provider="github",
                provider_user_id=str(data.get("id")),
                name=str(data.get("name")),
                nickname=str(data.get("login")),
                email=str(email),
                avatar=str(data.get("avatar_url")),
                raw_data=data
            ),need_bind_mail


    async def _fetch_gitee_user_info(self, provider_config: OAuthProviderSettings, access_token: str) -> OAuthUserInfo:
        """获取Gitee用户信息

        Args:
            provider_config: 提供商配置
            access_token: 访问令牌

        Returns:
            用户信息

        Raises:
            ValueError: 获取用户信息失败
        """
        async with httpx.AsyncClient() as client:
            response = await client.get(
                provider_config.USERINFO_URL,
                headers={
                    "Authorization": f"Bearer {access_token}"
                }
            )

            if response.status_code != 200:
                raise ValueError(f"获取GitHub用户信息失败: {response.text}")

            data = response.json()
            need_bind_mail = False
            email = str(data.get("email"))
            if not email or email == '未公开邮箱' or email == 'None':
                need_bind_mail = True
                # raise ValueError(f"该第三方登陆账号没有邮箱，请先绑定Gitee主邮箱或公开Gitee邮箱")

            name = str(data.get("name"))+uuid.uuid4().hex[:4]
            # if email is None or email == "None" or email == "":
            #     # 根据提供商和用户名创建一个伪邮箱
            #     normalized_username = re.sub(r'[^a-zA-Z0-9]', '', name)
            #     email = f"{normalized_username}_gitee_{uuid.uuid4().hex[:8]}@oauth.example.com"

            return OAuthUserInfo(
                provider="gitee",
                provider_user_id=str(data.get("id")),
                name=str(data.get("name")),
                nickname=str(data.get("login")),
                email=str(email),
                avatar=str(data.get("avatar_url")),
                raw_data=data
            ), need_bind_mail

    async def _find_or_create_user(self, user_info: OAuthUserInfo, token_data: Dict[str, Any], need_bind_mail: bool = False) -> Tuple[User, bool]:
        """查找或创建用户
        
        Args:Ov23liICPxYpHYkYir5O
            user_info: 用户信息
            token_data: 令牌数据
        
        Returns:
            用户信息和是否新创建的标志
        
        Raises:
            DatabaseError: 数据库操作错误
        """
        async with self.async_session() as session:
            try:
                # 查找OAuth账号
                stmt = select(OAuthAccountModel).where(
                    and_(
                        OAuthAccountModel.provider == user_info.provider,
                        OAuthAccountModel.provider_user_id == user_info.provider_user_id
                    )
                ).options(selectinload(OAuthAccountModel.user))

                result = await session.execute(stmt)
                oauth_account = result.scalar_one_or_none()

                if oauth_account:
                    # 更新OAuth账号信息
                    oauth_account.access_token = token_data.get("access_token")
                    oauth_account.refresh_token = token_data.get("refresh_token")
                    if token_data.get("expires_in"):
                        oauth_account.expires_at = datetime.now(timezone.utc) + timedelta(
                            seconds=token_data.get("expires_in", 0))
                    else:
                        oauth_account.expires_at = None

                    # 弱智ai，前面写一堆认证怎么这就大笔一挥不管了？
                    oauth_account.user_info = json.dumps(user_info.raw_data)
                    oauth_account.updated_at = datetime.now(timezone.utc)

                    await session.commit()

                    # 获取用户信息
                    user = await self.user_service.get_by_id(oauth_account.user_id)
                    return user, False

                # 如果没用这个注册过
                user = None
                if user_info.email:
                    user = await self.user_service.get_by_email(user_info.email)
                # 但是邮箱还是注册过了 就会报错
                if user:
                    raise ValueError("该第三方账户所属邮箱" + str(user_info.email) + "已被注册")
                if not user:
                    # 创建新用户
                    username = f"{user_info.provider}_{uuid.uuid4().hex[:10]}"
                    nickname = f"{user_info.provider}_{uuid.uuid4().hex[:10]}"
                    email = user_info.email
                    if email is None or email == "None" or email == "":
                        truncated_username = username[:8]
                        email = f"{truncated_username}-@unauth.com"
                    # 生成随机密码

                    # gitee-api如果拿不到email返回的是字符串的‘None’而非None
                    password = ''.join(str(random.randint(0, 9)) for _ in range(5)) + "aA."
                    user_data = {
                        "username": username,
                        "nickname": nickname,
                        "email": email,
                        "password": password,
                        "is_active": True,
                        "needs_email_binding": need_bind_mail
                    }

                    # 验证并创建模型
                    create_request = UserCreateRequest.model_validate(user_data)

                    user = await self.user_service.create(create_request)

                # 创建OAuth账号
                expires_at =None
                if token_data.get("expires_in"):
                    expires_at = datetime.now(timezone.utc) + timedelta(
                        seconds=token_data.get("expires_in", 0))
                oauth_account = OAuthAccountModel(
                    id=str(uuid.uuid4()),
                    user_id=user.id,
                    provider=user_info.provider,
                    provider_user_id=user_info.provider_user_id,
                    access_token=token_data.get("access_token"),
                    refresh_token=token_data.get("refresh_token"),
                    expires_at=expires_at,
                    user_info=json.dumps(user_info.raw_data),
                    is_verified=True,
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc)
                )

                session.add(oauth_account)
                await session.commit()

                return user, True

            except Exception as e:
                await session.rollback()
                logger.error("查找或创建用户失败", error=str(e))
                raise DatabaseError(f"查找或创建用户失败: {str(e)}")

    async def get_oauth_accounts(self, user_id: str) -> List[OAuthAccount]:
        """获取用户的OAuth账号列表
        
        Args:
            user_id: 用户ID
        
        Returns:
            OAuth账号列表
        
        Raises:
            DatabaseError: 数据库操作错误
        """
        async with self.async_session() as session:
            try:
                stmt = select(OAuthAccountModel).where(
                    OAuthAccountModel.user_id == user_id
                ).order_by(OAuthAccountModel.created_at.desc())

                result = await session.execute(stmt)
                accounts = result.scalars().all()

                return [
                    OAuthAccount(
                        id=account.id,
                        user_id=account.user_id,
                        provider=account.provider,
                        provider_user_id=account.provider_user_id,
                        is_verified=account.is_verified,
                        created_at=account.created_at,
                        updated_at=account.updated_at
                    )
                    for account in accounts
                ]
            except Exception as e:
                logger.error("获取OAuth账号列表失败", error=str(e))
                raise DatabaseError(f"获取OAuth账号列表失败: {str(e)}")

    async def delete_oauth_account(self, account_id: str, user_id: str) -> bool:
        """删除OAuth账号
        
        Args:
            account_id: 账号ID
            user_id: 用户ID
        
        Returns:
            是否成功
        
        Raises:
            ValueError: 账号不存在或不属于该用户
            DatabaseError: 数据库操作错误
        """
        async with self.async_session() as session:
            try:
                # 查找账号
                stmt = select(OAuthAccountModel).where(
                    and_(
                        OAuthAccountModel.id == account_id,
                        OAuthAccountModel.user_id == user_id
                    )
                )

                result = await session.execute(stmt)
                account = result.scalar_one_or_none()

                if not account:
                    raise ValueError("OAuth账号不存在或不属于该用户")

                # 删除账号
                await session.delete(account)
                await session.commit()

                return True
            except ValueError as e:
                raise e
            except Exception as e:
                await session.rollback()
                logger.error("删除OAuth账号失败", error=str(e))
                raise DatabaseError(f"删除OAuth账号失败: {str(e)}")

    def _get_provider_config(self, provider: str) -> OAuthProviderSettings:
        """获取提供商配置
        
        Args:
            provider: 提供商名称
        
        Returns:
            提供商配置
        
        Raises:
            ValueError: 提供商不支持
        """
        if provider == "wechat":
            return settings.oauth.WECHAT
        elif provider == "github":
            return settings.oauth.GITHUB
        elif provider == "gitee":
            return settings.oauth.GITEE
        else:
            raise ValueError(f"不支持的OAuth提供商: {provider}")
