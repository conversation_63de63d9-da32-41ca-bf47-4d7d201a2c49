#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目级代码分析器
"""
import logging
import os
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone

from langchain.chains import <PERSON><PERSON>hain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser
from .common_models import AttentionPointInfo, AnalysisFinding
from ....utils.retry import retry_async

from .project_architecture_models import (
    ArchitectureAnalysis,
    ArchitectureQuality,
    ArchitecturePatternInfo,
    ArchitectureLayer,
    ArchitectureComponent,
    ArchitectureRecommendation,
    ArchitectureAntiPattern,
)
from ...ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser

logger = logging.getLogger(__name__)


@AgentManager.register("ProjectArchitectureAnalyzerAgent")
class ProjectArchitectureAnalyzer(BaseAgent):
    """
    项目级架构分析器
    分析项目级别的代码，识别项目结构、模块关系、入口点、核心模块等
    """

    def __init__(self):
        """初始化项目级分析器"""
        super().__init__()
        self.name = "ProjectArchitectureAnalyzer"
        self.chain = None

    async def _initialize(self) -> None:
        """初始化分析器特定资源"""
        logger.info("初始化项目级架构分析器特定资源")
        # 在这里可以添加特定于项目分析器的初始化逻辑

    async def _create_chain(self) -> None:
        """初始化处理链"""
        try:
            # 架构模式分析提示模板
            architecture_template = """
            # 项目架构分析

            作为软件架构专家，请对项目进行全面的架构分析，包括架构风格识别、层次结构分析、组件关系评估、架构模式识别、质量评估以及改进建议。
            
            ## 项目信息
            项目名称: {{ project_name }}
            项目路径: {{ project_path }}

            {%- if project_structure %}
            ## 项目结构概览
            {{ project_structure }}
            {% endif -%}

            {%- if project_structure_analysis %}
            ## 项目组件分析
            
            {%- if project_structure_analysis.cores %}
            ### 核心业务组件
            {%- for core in project_structure_analysis.cores %}
            - **{{ core.name }}** ({{ core.component_kind }}) - {{ core.description }}
              - 路径: {{ core.path }}
              - 业务重要性: {{ "%.1f"|format(core.business_importance) }}
            {%- endfor %}
            {% endif -%}

            {%- if project_structure_analysis.entries %}
            ### 入口点组件
            {%- for entry in project_structure_analysis.entries %}
            - **{{ entry.name }}** ({{ entry.entry_type }}) - {{ entry.description }}
              - 路径: {{ entry.path }}
              - 运行环境: {{ entry.runtime_environment }}
              - 主要入口: {{ "是" if entry.is_primary else "否" }}
            {%- endfor %}
            {% endif -%}

            {%- if project_structure_analysis.configs %}
            ### 配置管理组件
            {%- for config in project_structure_analysis.configs %}
            - **{{ config.name }}** ({{ config.config_format }}) - {{ config.description }}
              - 路径: {{ config.path }}
              - 作用域: {{ config.config_scope }}
              - 必需: {{ "是" if config.is_required else "否" }}
              - 优先级: {{ config.config_priority }}
            {%- endfor %}
            {% endif -%}
            {% endif -%}

            {%- if project_dependency_analysis %}
            ## 项目依赖分析
            {{ project_dependency_analysis }}
            {% endif -%}

            ## 分析要求

            请按照以下专业架构分析框架进行全面评估：

            1. **架构风格与基础信息**
               - 识别项目的整体架构风格（单体、微服务、分层、事件驱动、领域驱动等）
               - 确定项目使用的主要编程语言
               - 评估架构与项目需求的匹配度

            2. **架构层次分析**
               - 识别主要架构层次（表示层、业务逻辑层、数据访问层等）
               - 分析每层的职责边界和内聚性
               - 评估层次间的依赖关系和耦合度

            3. **架构组件分析**
               - 识别关键架构组件及其所属层次
               - 分析组件的路径位置和主要用途
               - 评估组件间的交互模式和依赖关系

            4. **架构模式识别**
               - 识别项目中应用的架构模式（MVC、MVVM、Repository、Factory等）
               - 分析模式的应用位置和实现质量
               - 评估模式选择的合理性

            5. **架构反模式识别**
               - 识别潜在的架构反模式（大泥球、意大利面条代码、循环依赖等）
               - 分析反模式的严重程度和影响范围
               - 评估反模式的修复难度

            6. **架构质量评估**
               - 评估可维护性（代码组织、文档、一致性）
               - 评估可扩展性（新功能添加的难易程度）
               - 评估模块化程度（组件独立性、重用性）
               - 给出总体架构质量评分

            7. **改进建议**
               - 提供具体、可行的架构改进建议
               - 明确每个建议的目标改进区域
               - 为建议分配合理的优先级（high/medium/low）

            ## 输出要点

            请注意以下分析要点：
            1. 架构风格应基于项目的实际结构和组织方式，而不仅仅是使用的技术栈
            2. 架构层次应明确职责边界，每层应有清晰的组件列表
            3. 架构组件应包含名称、路径、用途和所属层次
            4. 架构模式识别应具体到应用位置，而不仅是泛泛而谈
            5. 架构反模式应评估严重程度（high/medium/low）
            6. 架构质量评分应在0-10范围内，基于客观标准
            7. 改进建议应具体、可行且有明确的优先级（high/medium/low）
            8. 架构发现应包含标题、描述、严重程度和具体建议            
            
            ## 输出语言要求
            所有分析结果使用中文(简体中文)输出，技术名称和代码保持原文。
            
            {{ format_instructions }}
            """

            # 创建Parser
            architecture_parser = AnalysisOutputParser(pydantic_object=ArchitectureAnalysis)
            parser = OutputFixingParser.from_llm(parser=architecture_parser, llm=self.llm)

            # 获取格式指令
            architecture_format_instructions = architecture_parser.get_format_instructions()

            # 创建提示模板
            architecture_prompt = PromptTemplate.from_template(
                architecture_template,
                template_format="jinja2"
            ).partial(format_instructions=architecture_format_instructions)

            # 创建处理链
            self.chain = (
                architecture_prompt
                | self.llm
                | parser
            )

            logger.info("项目级架构分析处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: Dict[str, Any]) -> AgentInput:
        """
        准备输入数据

        Args:
            input_data: 原始输入数据

        Returns:
            AgentInput: 准备后的输入数据
        """
        input_data: AgentInput = await super().prepare_input(input_data)

        # 获取项目结构分析结果
        project_structure = input_data.parameters.get("project_structure")
        if project_structure:
            # 将项目结构分析结果转换为markdown格式以便LLM理解
            structure_string = project_structure.structure_string
            # 如果项目结构字符串超过30000字符，则截断
            if len(structure_string) > 30000:
                structure_string = structure_string[:30000] + "\n\n... (结构内容过长，已截断)"
            input_data.parameters["project_structure"] = structure_string
        project_structure_analysis = input_data.parameters.get("project_structure_analysis")
        if project_structure_analysis:
            # 将项目结构分析结果转换为markdown格式以便LLM理解
            input_data.parameters["project_structure_analysis"] = project_structure_analysis
        # 获取项目依赖分析结果
        project_dependency_analysis = input_data.parameters.get("project_dependency_analysis")
        if project_dependency_analysis:
            # 将依赖分析结果转换为简化的markdown格式
            input_data.parameters["project_dependency_analysis"] = project_dependency_analysis.to_simple_markdown()

        return input_data

    @retry_async(max_retries=3)
    async def _process(
        self,
        input_data: AgentInput
    ) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询

        Args:
            input_data: 输入数据

        Returns:
            Tuple[Any, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 调用处理链
            # result = await self.chain.first.ainvoke(input_data.parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(input_data.parameters)

            return result, {"analyzer": "project architecture analyzer"}

        except Exception as e:
            logger.error(f"项目级分析失败: {str(e)}")
            raise

    async def _shutdown(self) -> None:
        """关闭分析器资源"""
        logger.info("关闭项目级代码分析器资源")
        # 释放可能的资源
        self.chain = None
        self.llm = None
