#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多粒度注意力机制模块
在函数级、模块级和项目级等不同粒度上分析代码
"""

from .module_analyzer import ModuleAnalyzer
from .project_structure_analyzer import ProjectStructureAnalyzer
from .project_architecture_analyzer import ProjectArchitectureAnalyzer
from .project_dependency_analyzer import ProjectDependencyAnalyzer
from .module_merger_ai_analyzer import ModuleMergerAIAnalyzer
from .module_merger_code_analyzer import ModuleMergerCodeAnalyzer
from .attention import AttentionUnit, AttentionContext
from .common_models import AttentionPointInfo, AnalysisFinding
from .module_models import (
    # 模块级分析相关模型
    ModuleAnalysis,
    DependencyGraph,
    DependencyNode, 
    DependencyEdge,
)

from .module_merger_models import (
    # 模块合并分析相关模型
    ModuleMergerAnalysisResult,
    AIAnalysisResult,
    CodeAnalysisResult,
    ArchitecturalComponent,
    FunctionalModule,
    AIAnalysisInsights,
    ModuleCluster,
    DependencyMatrix,
    CodeRelationshipAnalysis,
)

from .project_structure_models import (
    # 项目级分析相关模型
    ProjectStructureAnalysis,
)

# 导入项目扫描相关模型
from ..project_scanning.project_info_models import (
    ProjectStructure,
    DirectoryInfo,
    FileInfo,
    TreeNode
)

from .project_architecture_models import (
    # 项目架构分析相关模型
    ArchitectureAnalysis,
    ArchitecturePatternInfo,
    ArchitectureQuality,
    ArchitectureRecommendation,
    ArchitecturePatternInfo
)

from .project_dependency_models import (
    # 项目依赖分析相关模型
    DependencyAnalysis,
    ExternalDependency,
)

__all__ = [
    # 分析器类
    "ModuleAnalyzer",
    "ProjectStructureAnalyzer",
    "ProjectArchitectureAnalyzer",
    "ProjectDependencyAnalyzer",
    "ModuleMergerAIAnalyzer",
    "ModuleMergerCodeAnalyzer",

    # 注意力机制
    "AttentionUnit",
    "AttentionContext",

    # 模型
    "AttentionPointInfo",
    "AnalysisFinding",

    # 模块级分析模型
    "ModuleAnalysis",
    "DependencyGraph",
    "DependencyNode", 
    "DependencyEdge",

    #项目级分析模型
    "ProjectStructure",
    "DirectoryInfo",
    "FileInfo",
    "TreeNode",
    "ProjectStructureAnalysis",

    # 项目架构分析模型
    "ArchitectureAnalysis",
    "ArchitecturePatternInfo",
    "ArchitectureQuality",
    "ArchitectureRecommendation",

    # 项目依赖分析模型
    "DependencyAnalysis",
    "ExternalDependency",

    # 模块合并分析模型
    "ModuleMergerAnalysisResult",
    "AIAnalysisResult",
    "CodeAnalysisResult",
    "ArchitecturalComponent",
    "FunctionalModule",
    "AIAnalysisInsights",
    "ModuleCluster",
    "DependencyMatrix",
    "CodeRelationshipAnalysis",
]
