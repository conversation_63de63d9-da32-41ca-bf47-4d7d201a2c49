"""
认证服务模块

提供用户认证、权限验证等功能
"""
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any, List, Tuple, Set
import structlog
from sqlalchemy import select, and_, or_, func, delete
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import DatabaseError
import jwt

from app.core.database import DatabaseError
from app.core.di.providers import SessionProvider, AsyncSessionProvider, RedisProvider
from app.core.config import settings
from app.models.rbac.user import UserModel
from app.models.rbac.role import RoleModel
from app.models.rbac.permission import PermissionModel
from app.models.rbac.permission_group import PermissionGroupModel
from app.models.rbac.token import TokenModel
from app.utils.security import verify_password
from app.schemas.rbac.user import User, UserLoginRequest, UserLogoutRequest
from app.schemas.rbac.token import (
    Token,
    RefreshTokenRequest
)
from app.services.rbac.user import UserService
from app.schemas.rbac.permission import Permission
from app.model_converters.rbac.permission import PermissionConverter

logger = structlog.get_logger(__name__)

class AuthService:
    """认证服务类"""
    
    def __init__(
        self,
        session: SessionProvider,
        async_session: AsyncSessionProvider,
        user_service: UserService,
        redis: RedisProvider
    ):
        """初始化认证服务
        
        Args:
            session: 数据库会话提供者
            async_session: 异步数据库会话提供者
            user_service: 用户服务
            redis: Redis客户端
        """
        self.session = session
        self.async_session = async_session
        self.user_service = user_service
        self.redis = redis
        self.permission_converter = PermissionConverter()
        logger.debug("认证服务初始化完成")

    def create_access_token(
        self,
        data: dict
    ) -> str:
        """创建访问令牌

        Args:
            data: 令牌数据

        Returns:
            访问令牌
        """
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=settings.security.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
        )
        to_encode.update({"exp": expire, "type": "access"})
        encoded_jwt = jwt.encode(
            to_encode,
            settings.security.JWT_SECRET,
            algorithm=settings.security.JWT_ALGORITHM
        )
        return encoded_jwt

    def create_refresh_token(
        self,
        data: dict
    ) -> str:
        """创建刷新令牌

        Args:
            data: 令牌数据

        Returns:
            刷新令牌
        """
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + timedelta(
            days=settings.security.JWT_REFRESH_TOKEN_EXPIRE_DAYS
        )
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(
            to_encode,
            settings.security.JWT_SECRET,
            algorithm=settings.security.JWT_ALGORITHM
        )
        return encoded_jwt

    async def verify_access_token(
        self,
        token: str
    ) -> User:
        """验证访问令牌

        Args:
            token: JWT令牌

        Returns:
            用户信息

        Raises:
            ValueError: 令牌无效或用户不存在
        """
        try:
            payload = jwt.decode(
                token,
                settings.security.JWT_SECRET,
                algorithms=[settings.security.JWT_ALGORITHM]
            )
            if payload.get("type") != "access":
                raise ValueError("无效的访问令牌")
                
            user_id = payload.get("sub")
            if not user_id:
                raise ValueError("无效的访问令牌")
                
            user = await self.user_service.get_by_id(user_id)
            if not user:
                raise ValueError("用户不存在")
                
            if not user.is_active:
                raise ValueError("用户已被禁用")
                
            return user
            
        except jwt.ExpiredSignatureError:
            raise ValueError("令牌已过期")
        except jwt.InvalidTokenError:
            raise ValueError("无效的令牌")

    async def verify_refresh_token(
        self,
        token: str
    ) -> User:
        """验证刷新令牌

        Args:
            token: JWT令牌

        Returns:
            用户信息

        Raises:
            ValueError: 令牌无效或用户不存在
        """
        try:
            payload = jwt.decode(
                token,
                settings.security.JWT_SECRET,
                algorithms=[settings.security.JWT_ALGORITHM]
            )
            if payload.get("type") != "refresh":
                raise ValueError("无效的刷新令牌")
                
            user_id = payload.get("sub")
            if not user_id:
                raise ValueError("无效的刷新令牌")
                
            user = await self.user_service.get_by_id(user_id)
            if not user:
                raise ValueError("用户不存在")
                
            if not user.is_active:
                raise ValueError("用户已被禁用")
                
            return user
            
        except jwt.ExpiredSignatureError:
            raise ValueError("令牌已过期")
        except jwt.InvalidTokenError:
            raise ValueError("无效的令牌")

    async def login(
        self,
        request: UserLoginRequest
    ) -> Tuple[Optional[User], Optional[Token]]:
        """用户登录

        Args:
            request: 登录请求

        Returns:
            令牌信息

        Raises:
            ValueError: 登录失败
        """

        user = await self.authenticate(
            username=request.username,
            password=request.password
        )
        
        # 生成令牌
        access_token = self.create_access_token(
            data={"sub": user.id, "is_superuser": user.is_superuser}
        )
        refresh_token = self.create_refresh_token(
            data={"sub": user.id}
        )
        token = await self.user_service.create_token(
            user_id=user.id,
            access_token=access_token,
            refresh_token=refresh_token,
            expires_at=datetime.now(timezone.utc) + timedelta(minutes=settings.security.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        )
        return user, token

    async def refresh_token(
        self,
        request: RefreshTokenRequest
    ) -> Token:
        """刷新访问令牌

        Args:
            request: 刷新令牌请求

        Returns:
            新的令牌信息

        Raises:
            ValueError: 刷新失败
        """
        try:
            user = await self.verify_refresh_token(request.refresh_token)
            
            # 删除过期的令牌
            await self.user_service.delete_expired_tokens(user.id)

            token = await self.user_service.get_token_by_refresh_token(request.refresh_token)
            if token:
                try:
                    await self.verify_access_token(token.access_token)
                    return token
                except Exception:
                    await self.user_service.delete_user_tokens(user.id)

            # 生成新令牌
            access_token = self.create_access_token(
                data={"sub": user.id, "is_superuser": user.is_superuser}
            )
            refresh_token = self.create_refresh_token(
                data={"sub": user.id}
            )
            
            token = await self.user_service.create_token(
                user_id=user.id,
                access_token=access_token,
                refresh_token=refresh_token,
                expires_at=datetime.now(timezone.utc) + timedelta(minutes=settings.security.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
            )

            return token
            
        except Exception as e:
            logger.error("刷新令牌失败", error=str(e))
            raise ValueError("无效的刷新令牌")



    async def authenticate(
        self,
        username: str,
        password: str
    ) -> User:
        """用户认证

        Args:
            username: 用户名/邮箱/手机号/ID
            password: 密码

        Returns:
            用户信息

        Raises:
            ValueError: 认证失败
        """
        # 尝试通过不同字段查找用户
        user = await self.user_service.get_by_username(username)
        if not user:
            user = await self.user_service.get_by_phone(username)
            if not user:
                user = await self.user_service.get_by_email(username)
                if not user:
                    user = await self.user_service.get_by_id(username)
        
        if not user:
            logger.warning("用户不存在", username=username)
            raise ValueError("用户名或密码错误")

        # 前端审查这五个字
        if user.needs_email_binding:
            logger.warning("该用户第三方认证无效 请绑定邮箱::", username=username)
            raise ValueError("请绑定邮箱")

        if not user.is_active:
            logger.warning("用户已被禁用", username=username)
            raise ValueError("用户已被禁用")
            
        # 检查用户是否被锁定
        if await self.user_service.check_is_locked(user):
            logger.warning(
                "用户已被锁定",
                username=username,
                locked_until=user.locked_until
            )
            raise ValueError(f"账户已被锁定，请在{user.locked_until}后重试")
            
        # 验证密码
        if not verify_password(password, user.hashed_password):
            logger.warning("密码错误", username=username)
            await self.user_service.update_failed_login_attempts(user.id)
            raise ValueError("用户名或密码错误")
            
        # 重置失败登录次数
        await self.user_service.reset_failed_login_attempts(user.id)
        
        logger.info("用户认证成功", username=username)
        return user

    async def check_permission(
        self,
        *,
        user_id: str,
        permission_code: str
    ) -> bool:
        """检查用户是否拥有指定权限

        Args:
            user_id: 用户ID
            permission_code: 权限代码

        Returns:
            是否拥有权限
        """
        user = await self.user_service.get_by_id(user_id)
        if not user:
            return False
            
        # 超级管理员拥有所有权限
        if user.is_superuser:
            return True
            
        # 遍历用户的所有角色
        for role in user.roles:
            if not role.is_active:
                continue
                
            # 检查角色权限
            has_permission = await self.check_role_permission(
                role_id=role.id,
                permission_code=permission_code
            )
            if has_permission:
                return True
                
        return False

    async def check_role_permission(
        self,
        *,
        role_id: str,
        permission_code: str,
        checked_roles: Optional[List[str]] = None
    ) -> bool:
        """检查角色是否拥有指定权限

        Args:
            role_id: 角色ID
            permission_code: 权限代码
            checked_roles: 已检查过的角色ID列表，用于防止循环继承

        Returns:
            是否拥有权限
        """
        # 防止循环继承
        if checked_roles is None:
            checked_roles = []
            
        if role_id in checked_roles:
            return False
            
        checked_roles.append(role_id)
        
        async with self.async_session() as session:
            stmt = (
                select(RoleModel)
                .options(
                    selectinload(RoleModel.permissions),
                    selectinload(RoleModel.permission_groups)
                        .selectinload(PermissionGroupModel.permissions)
                )
                .where(RoleModel.id == role_id)
            )
            result = await session.execute(stmt)
            role = result.scalar_one_or_none()
            
            if not role or not role.is_active:
                return False
                
            # 检查直接权限
            for permission in role.permissions:
                if permission.code == permission_code and permission.is_active:
                    return True
                    
            # 检查权限组中的权限
            for group in role.permission_groups:
                if not group.is_active:
                    continue
                for permission in group.permissions:
                    if permission.code == permission_code and permission.is_active:
                        return True
                    
            # 如果角色支持继承，检查父角色
            if role.is_inherit and role.parent_id:
                return await self.check_role_permission(
                    role_id=role.parent_id,
                    permission_code=permission_code,
                    checked_roles=checked_roles
                )
                
        return False

    async def logout(self, request: UserLogoutRequest) -> None:
        """用户登出

        Args:
            request: 登出请求

        Raises:
            DatabaseError: 数据库错误
            ValueError: 令牌无效
        """
        # 验证并解析令牌
        user = await self.verify_access_token(request.token)
        
        # 删除访问令牌和对应的刷新令牌
        await self.user_service.delete_user_tokens(user.id)

    async def get_user_permissions(self, user_id: str) -> List[Permission]:
        """获取用户所有权限，包括从角色和权限组继承的权限
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户所有权限列表
        """
        async with self.async_session() as session:
            # 查询用户及其关联数据
            # .selectinload(PermissionModel.group),
            stmt = (
                select(UserModel)

                .options(
                    selectinload(UserModel.roles)
                    .selectinload(RoleModel.permissions)
                    .selectinload(PermissionModel.roles),
                    selectinload(UserModel.roles)
                        .selectinload(RoleModel.permissions)
                        .selectinload(PermissionModel.group),
                    selectinload(UserModel.roles)
                    .selectinload(RoleModel.permission_groups)
                    .selectinload(PermissionGroupModel.permissions)


                )
                .where(UserModel.id == user_id)
            )
            result = await session.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                return []
            
            # 收集所有权限
            permissions: Set[Permission] = set()
            
            # 处理用户的角色
            for role in user.roles:
                if not role.is_active:
                    continue
                    
                # 添加角色直接关联的权限
                for permission in role.permissions:
                    if permission.is_active:
                        permissions.add(self.permission_converter.to_schema(permission))

                # 不考虑权限组和父角色

                # 添加角色通过权限组继承的权限
                for group in role.permission_groups:
                    if not group.is_active:
                        continue
                    for permission in group.permissions:
                        if permission.is_active:
                            permissions.add(self.permission_converter.to_schema(permission))

                # 如果角色允许继承，递归处理父角色
                if role.is_inherit and role.parent:
                    parent_permissions = await self._get_role_permissions(role.parent.id, session)
                    permissions.update(parent_permissions)
            
            return list(permissions)

    async def _get_role_permissions(self, role_id: str, session=None) -> Set[Permission]:
        """递归获取角色的所有权限"""
        # 如果没有传 session，则新建一个
        if session is None:
            async with self.async_session() as session:
                return await self._get_role_permissions(role_id, session)

        # 查询角色及其关联数据
        stmt = (
            select(RoleModel)
                .options(
                selectinload(RoleModel.permissions),
                selectinload(RoleModel.permission_groups)
                    .selectinload(PermissionGroupModel.permissions),
                selectinload(RoleModel.parent)
            )
                .where(RoleModel.id == role_id)
        )
        result = await session.execute(stmt)
        role = result.scalar_one_or_none()

        if not role or not role.is_active:
            return set()

        # 收集所有权限
        permissions: Set[Permission] = set()

        # 添加角色直接关联的权限
        for permission in role.permissions:
            if permission.is_active:
                permissions.add(self.permission_converter.to_schema(permission))

        # 添加角色通过权限组继承的权限
        for group in role.permission_groups:
            if not group.is_active:
                continue
            for permission in group.permissions:
                if permission.is_active:
                    permissions.add(self.permission_converter.to_schema(permission))

        # 如果角色允许继承，递归处理父角色
        if role.is_inherit and role.parent:
            parent_permissions = await self._get_role_permissions(role.parent.id, session)
            permissions.update(parent_permissions)

        return permissions




