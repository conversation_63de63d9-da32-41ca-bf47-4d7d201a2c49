"""关系相关对象。"""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Dict, cast

from app.utils.slx_parser.opc.oxml import CT_Relationships

if TYPE_CHECKING:
    from app.utils.slx_parser.opc.part import Part


class Relationships(Dict[str, "_Relationship"]):
    """用于|_Relationship|实例的集合对象，具有列表语义。"""

    def __init__(self, baseURI: str):
        super(Relationships, self).__init__()
        self._baseURI = baseURI
        self._target_parts_by_rId: dict[str, Any] = {}

    def add_relationship(
        self, reltype: str, target: Part | str, rId: str, is_external: bool = False
    ) -> "_Relationship":
        """返回一个新添加的|_Relationship|实例。"""
        rel = _Relationship(rId, reltype, target, self._baseURI, is_external)
        self[rId] = rel
        if not is_external:
            self._target_parts_by_rId[rId] = target
        return rel

    def get_or_add(self, reltype: str, target_part: Part) -> _Relationship:
        """返回指向`target_part`的`reltype`类型的关系，如果集合中不存在则新建。"""
        rel = self._get_matching(reltype, target_part)
        if rel is None:
            rId = self._next_rId
            rel = self.add_relationship(reltype, target_part, rId)
        return rel

    def get_or_add_ext_rel(self, reltype: str, target_ref: str) -> str:
        """返回指向`target_ref`的`reltype`类型外部关系的rId，如果集合中不存在则新建。"""
        rel = self._get_matching(reltype, target_ref, is_external=True)
        if rel is None:
            rId = self._next_rId
            rel = self.add_relationship(reltype, target_ref, rId, is_external=True)
        return rel.rId

    def part_with_reltype(self, reltype: str) -> Part:
        """返回具有匹配`reltype`的关系的目标部件，如果未找到则引发|KeyError|，
        如果找到多个匹配的关系则引发|ValueError|。"""
        rel = self._get_rel_of_type(reltype)
        return rel.target_part

    @property
    def related_parts(self):
        """字典，将集合中所有内部关系的rId映射到目标部件。"""
        return self._target_parts_by_rId

    @property
    def xml(self) -> str:
        """将此关系集合序列化为适合在OPC包中存储为.rels文件的XML。"""
        rels_elm = CT_Relationships.new()
        for rel in self.values():
            rels_elm.add_rel(rel.rId, rel.reltype, rel.target_ref, rel.is_external)
        return rels_elm.xml

    def _get_matching(
        self, reltype: str, target: Part | str, is_external: bool = False
    ) -> _Relationship | None:
        """从集合中返回匹配`reltype`、`target`和`is_external`的关系，
        如果未找到则返回None。"""

        def matches(rel: _Relationship, reltype: str, target: Part | str, is_external: bool):
            if rel.reltype != reltype:
                return False
            if rel.is_external != is_external:
                return False
            rel_target = rel.target_ref if rel.is_external else rel.target_part
            if rel_target != target:
                return False
            return True

        for rel in self.values():
            if matches(rel, reltype, target, is_external):
                return rel
        return None

    def _get_rel_of_type(self, reltype: str):
        """从集合中返回类型为`reltype`的单个关系。

        如果未找到匹配的关系则引发|KeyError|。
        如果找到多个匹配的关系则引发|ValueError|。
        """
        matching = [rel for rel in self.values() if rel.reltype == reltype]
        if len(matching) == 0:
            tmpl = "集合中没有类型为'%s'的关系"
            raise KeyError(tmpl % reltype)
        if len(matching) > 1:
            tmpl = "集合中存在多个类型为'%s'的关系"
            raise ValueError(tmpl % reltype)
        return matching[0]

    @property
    def _next_rId(self) -> str:  # pyright: ignore[reportReturnType]
        """集合中下一个可用的rId，从'rId1'开始，并利用编号中的任何空隙，
        例如对于rIds ['rId1', 'rId3']，返回'rId2'。"""
        for n in range(1, len(self) + 2):
            rId_candidate = "rId%d" % n  # 如'rId19'
            if rId_candidate not in self:
                return rId_candidate


class _Relationship:
    """关系到部件的值对象。"""

    def __init__(
        self, rId: str, reltype: str, target: Part | str, baseURI: str, external: bool = False
    ):
        super(_Relationship, self).__init__()
        self._rId = rId
        self._reltype = reltype
        self._target = target
        self._baseURI = baseURI
        self._is_external = bool(external)

    @property
    def is_external(self) -> bool:
        return self._is_external

    @property
    def reltype(self) -> str:
        return self._reltype

    @property
    def rId(self) -> str:
        return self._rId

    @property
    def target_part(self) -> Part:
        if self._is_external:
            raise ValueError(
                "当目标模式为External时，_Relationship的target_part属性未定义"
            )
        return cast("Part", self._target)

    @property
    def target_ref(self) -> str:
        if self._is_external:
            return cast(str, self._target)
        else:
            target = cast("Part", self._target)
            return target.partname.relative_ref(self._baseURI)
