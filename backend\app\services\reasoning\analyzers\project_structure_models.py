#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目结构分析模型 - 表层特征轻量级分析版本
专注于基于文件名、路径、扩展名等表层特征的组件分析，无需解析文件内容
"""
from typing import List, Optional, Literal

from pydantic import BaseModel, Field

# ============================================================================
# 核心组件模型
# ============================================================================

class CoreComponent(BaseModel):
    """核心业务组件模型 - 项目的主要业务逻辑组件"""
    name: str = Field(default="", description="组件名称")
    path: str = Field(default="", description="组件路径")
    component_kind: Literal["file", "directory", "module", "package", "other"] = Field(default="file", description="组件类型")

    # 核心组件表层属性
    business_importance: float = Field(ge=0.0, le=1.0, default=0.5, description="重要性评分")
    description: str = Field(default="", description="组件功能描述")


class ConfigurationComponent(BaseModel):
    """配置组件模型 - 项目配置文件的专门化模型"""
    name: str = Field(default="", description="配置文件名称")
    path: str = Field(default="", description="配置文件路径")
    config_format: Literal[
        "json", "yaml", "toml", "ini", "env", "xml", "properties",
        "hcl", "hocon", "hjson", "jsonc", "cfg", "conf", "other"
    ] = Field(default="json", description="配置文件格式")
    description: str = Field(default="", description="配置组件功能描述")

    # 配置表层属性
    config_scope: Literal["global", "environment", "feature", "local", "other"] = Field(default="global", description="配置作用域")
    is_required: bool = Field(default=True, description="是否为必需配置")
    config_priority: Literal["low", "medium", "high", "critical"] = Field(default="medium", description="配置优先级")


class DependencyComponent(BaseModel):
    """依赖管理组件模型 - 项目依赖管理文件的专门化模型"""
    name: str = Field(default="", description="依赖文件名称")
    path: str = Field(default="", description="依赖文件路径")
    package_manager: Literal["pip", "npm", "yarn", "pnpm", "poetry", "cargo", "go_mod", "maven", "gradle", "composer", "other"] = Field(default="pip", description="依赖管理器")
    description: str = Field(default="", description="依赖管理组件功能描述")

    # 依赖管理表层属性
    dependency_category: Literal[
        "runtime_dependencies", "dev_dependencies", "build_dependencies",
        "test_dependencies", "optional_dependencies", "peer_dependencies", "other"
    ] = Field(default="runtime_dependencies", description="依赖类型")

    has_lockfile: bool = Field(default=False, description="是否有对应的锁定文件")
    supports_versioning: bool = Field(default=True, description="是否支持版本管理")
    is_monorepo: bool = Field(default=False, description="是否为单体仓库配置")


class TestComponent(BaseModel):
    """测试组件模型 - 项目测试相关文件的专门化模型"""
    name: str = Field(default="", description="测试组件名称")
    path: str = Field(default="", description="测试文件或目录路径")
    test_category: Literal["unit", "integration", "e2e", "performance", "security", "smoke", "api", "ui", "example", "other"] = Field(default="unit", description="测试类型")
    test_framework: Optional[str] = Field(default=None, description="使用的测试框架")
    description: str = Field(default="", description="测试组件功能描述")

    # 测试专门化属性
    test_scope: Literal[
        "component_level", "service_level", "system_level", "acceptance_level", "other"
    ] = Field(default="component_level", description="测试范围级别")

    coverage_target: Optional[float] = Field(default=None, description="代码覆盖率目标")
    is_automated: bool = Field(default=True, description="是否为自动化测试")
    execution_environment: Literal["local", "ci_cd", "staging", "production", "other"] = Field(default="local", description="执行环境")
    test_priority: Literal["low", "medium", "high", "critical"] = Field(default="medium", description="测试优先级")


class BuildComponent(BaseModel):
    """构建部署组件模型 - 项目构建和部署相关文件的专门化模型"""
    name: str = Field(default="", description="构建文件名称")
    path: str = Field(default="", description="构建文件路径")
    build_tool: Literal["dockerfile", "docker_compose", "makefile", "ci_cd", "kubernetes", "terraform", "script", "helm", "other"] = Field(default="dockerfile", description="构建类型")
    description: str = Field(default="", description="构建部署组件功能描述")

    # 构建部署表层属性
    deployment_stage: Literal[
        "development", "testing", "staging", "production", "multi_stage", "other"
    ] = Field(default="development", description="部署阶段")

    target_platforms: List[str] = Field(default_factory=list, description="目标部署平台")
    build_stages: List[str] = Field(default_factory=list, description="构建阶段列表")
    supports_multi_arch: bool = Field(default=False, description="是否支持多架构")
    automation_level: Literal["manual", "semi_automated", "fully_automated"] = Field(default="manual", description="自动化程度")


class DocumentationComponent(BaseModel):
    """文档组件模型 - 项目文档文件的专门化模型"""
    name: str = Field(default="", description="文档名称")
    path: str = Field(default="", description="文档路径")
    doc_category: Literal["readme", "api_docs", "user_guide", "developer_guide", "changelog", "license", "contributing", "other"] = Field(default="other", description="文档类型")
    description: str = Field(default="", description="文档组件功能描述")

    # 文档表层属性
    doc_format: Literal["markdown", "rst", "html", "pdf", "txt", "wiki", "other"] = Field(default="markdown", description="文档格式")
    target_audience: Literal["end_users", "developers", "administrators", "contributors", "general", "other"] = Field(default="general", description="目标受众")
    maintenance_status: Literal["active", "occasional", "minimal", "deprecated"] = Field(default="active", description="维护程度")
    doc_importance: float = Field(ge=0.0, le=1.0, default=0.5, description="文档重要性评分")
    completeness_level: Literal["comprehensive", "basic", "minimal", "placeholder"] = Field(default="basic", description="完整性级别")


class EntryPointComponent(BaseModel):
    """入口点组件模型 - 项目入口点文件的专门化模型"""
    name: str = Field(default="", description="入口点文件名称")
    path: str = Field(default="", description="入口点文件路径")
    description: str = Field(default="", description="入口点组件功能描述")

    # 入口点表层属性
    entry_category: Literal[
        "main_script", "web_server", "cli_command", "api_gateway",
        "worker_process", "batch_job", "service_daemon", "other"
    ] = Field(default="main_script", description="入口点类型")

    execution_context: Literal[
        "standalone", "web_framework", "container", "serverless", "microservice", "other"
    ] = Field(default="standalone", description="执行环境上下文")

    is_primary: bool = Field(default=True, description="是否为主要入口点")
    startup_priority: Literal["low", "medium", "high", "critical"] = Field(default="medium", description="启动优先级")


# ============================================================================
# 项目结构分析结果模型
# ============================================================================

class ProjectStructureAnalysis(BaseModel):
    """项目结构分析结果 - 轻量级表层属性分析版本"""
    
    # 基本项目信息
    project_name: str = Field(default="", description="项目名称")
    project_path: str = Field(default="", description="项目根路径")
    primary_language: str = Field(default="", description="主要编程语言")
    project_type: Literal[
        "web_application", "library", "cli_tool", "framework",
        "microservice", "desktop_app", "mobile_app", "data_pipeline", "other"
    ] = Field(default="web_application", description="项目类型")
    
    # 专门化组件集合
    core: List[CoreComponent] = Field(default_factory=list, description="核心业务组件")
    configs: List[ConfigurationComponent] = Field(default_factory=list, description="配置组件")
    dependencies: List[DependencyComponent] = Field(default_factory=list, description="依赖管理组件")
    tests: List[TestComponent] = Field(default_factory=list, description="测试组件")
    builds: List[BuildComponent] = Field(default_factory=list, description="构建部署组件")
    docs: List[DocumentationComponent] = Field(default_factory=list, description="文档组件")
    entries: List[EntryPointComponent] = Field(default_factory=list, description="入口点组件")

    def to_markdown(self) -> str:
        """转换为Markdown格式"""
        markdown = []
        
        # 项目基本信息
        markdown.append(f"# {self.project_name} 项目结构分析\n")
        markdown.append("## 基本信息\n")
        markdown.append(f"- **项目路径**: {self.project_path}")
        markdown.append(f"- **项目类型**: {self.project_type}")
        markdown.append(f"- **主要语言**: {self.primary_language}\n")

        # 入口点组件
        if self.entries:
            markdown.append("## 项目入口点\n")
            markdown.append("| 入口点名称 | 类型 | 执行环境 | 主要入口 | 启动优先级 | 描述 | 路径 |")
            markdown.append("| --------- | ---- | -------- | -------- | ---------- | ---- | ---- |")

            # 按优先级和是否主要入口点排序
            priority_order = {"critical": 4, "high": 3, "medium": 2, "low": 1}
            sorted_entries = sorted(
                self.entries,
                key=lambda x: (x.is_primary, priority_order.get(x.startup_priority, 0)),
                reverse=True
            )

            for entry in sorted_entries:
                primary = "✓" if entry.is_primary else "✗"
                markdown.append(f"| {entry.name} | {entry.entry_category} | {entry.execution_context} | {primary} | {entry.startup_priority} | {entry.description} | {entry.path} |")
            markdown.append("")

        # 核心组件
        if self.core:
            markdown.append("## 核心组件\n")
            markdown.append("| 组件名称 | 类型 | 重要性 | 描述 | 路径 |")
            markdown.append("| ------- | ---- | ------ | ---- | ---- |")

            sorted_components = sorted(self.core, key=lambda x: x.business_importance, reverse=True)
            for comp in sorted_components:
                importance_bar = "█" * int(comp.business_importance * 10)
                markdown.append(f"| {comp.name} | {comp.component_kind} | {importance_bar} | {comp.description} | {comp.path} |")
            markdown.append("")
        
        # 配置组件
        if self.configs:
            markdown.append("## 配置管理\n")
            markdown.append("| 配置名称 | 格式 | 作用域 | 必需 | 优先级 | 描述 | 路径 |")
            markdown.append("| ------- | ---- | ------ | ---- | ------ | ---- | ---- |")

            for config in self.configs:
                required = "✓" if config.is_required else "✗"
                markdown.append(f"| {config.name} | {config.config_format} | {config.config_scope} | {required} | {config.config_priority} | {config.description} | {config.path} |")
            markdown.append("")
        
        # 依赖管理
        if self.dependencies:
            markdown.append("## 依赖管理\n")
            markdown.append("| 文件名 | 管理器 | 依赖类型 | 锁定文件 | 版本管理 | 单体仓库 | 描述 | 路径 |")
            markdown.append("| ------ | ------ | -------- | -------- | -------- | -------- | ---- | ---- |")

            for dep in self.dependencies:
                lockfile_status = "✓" if dep.has_lockfile else "✗"
                versioning = "✓" if dep.supports_versioning else "✗"
                monorepo_status = "✓" if dep.is_monorepo else "✗"
                markdown.append(f"| {dep.name} | {dep.package_manager} | {dep.dependency_category} | {lockfile_status} | {versioning} | {monorepo_status} | {dep.description} | {dep.path} |")
            markdown.append("")
        
        # 测试组件
        if self.tests:
            markdown.append("## 测试体系\n")
            markdown.append("| 测试名称 | 类型 | 范围 | 框架 | 自动化 | 优先级 | 覆盖率目标 | 描述 | 路径 |")
            markdown.append("| ------- | ---- | ---- | ---- | ------ | ------ | ---------- | ---- | ---- |")

            for test in self.tests:
                coverage = f"{test.coverage_target:.1%}" if test.coverage_target else "N/A"
                framework = test.test_framework or "N/A"
                automated = "✓" if test.is_automated else "✗"
                markdown.append(f"| {test.name} | {test.test_category} | {test.test_scope} | {framework} | {automated} | {test.test_priority} | {coverage} | {test.description} | {test.path} |")
            markdown.append("")
        
        # 构建部署
        if self.builds:
            markdown.append("## 构建部署\n")
            markdown.append("| 构建文件 | 类型 | 部署阶段 | 多架构 | 自动化程度 | 目标平台 | 描述 | 路径 |")
            markdown.append("| ------- | ---- | -------- | ------ | ---------- | -------- | ---- | ---- |")

            for build in self.builds:
                platforms = ", ".join(build.target_platforms) if build.target_platforms else "N/A"
                multi_arch = "✓" if build.supports_multi_arch else "✗"
                markdown.append(f"| {build.name} | {build.build_tool} | {build.deployment_stage} | {multi_arch} | {build.automation_level} | {platforms} | {build.description} | {build.path} |")
            markdown.append("")
        
        # 文档组件
        if self.docs:
            markdown.append("## 项目文档\n")
            markdown.append("| 文档名称 | 类型 | 格式 | 目标受众 | 维护程度 | 完整性 | 重要性 | 描述 | 路径 |")
            markdown.append("| ------- | ---- | ---- | -------- | -------- | ------ | ------ | ---- | ---- |")

            sorted_docs = sorted(self.docs, key=lambda x: x.doc_importance, reverse=True)
            for doc in sorted_docs:
                importance_bar = "█" * int(doc.doc_importance * 10)
                markdown.append(f"| {doc.name} | {doc.doc_category} | {doc.doc_format} | {doc.target_audience} | {doc.maintenance_status} | {doc.completeness_level} | {importance_bar} | {doc.description} | {doc.path} |")
            markdown.append("")
        
        return "\n".join(markdown)
