#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目扫描器
扫描项目结构、文件和目录信息，生成项目结构信息
"""
import os
import json
import toml
import xml.etree.ElementTree as ET
import fnmatch
import threading
import queue
import pathspec
import structlog
import hashlib
from pathlib import Path
from collections import Counter, defaultdict
from typing import List, Dict, Optional, Type, Set, Callable
from datetime import datetime, timezone

from .project_info_models import (
    ProjectStructure, TreeNode, DirectoryInfo, FileInfo
)
from .directory_tree_chunker import chunk_tree
from .directory_tree_formatter import format_chunked_trees, format_tree

logger = structlog.get_logger(__name__)

# 语言识别器
class LanguageDetector:
    """根据文件扩展名检测编程语言"""
    LANGUAGE_MAP = {
        '.py': 'Python',
        '.js': 'JavaScript',
        '.ts': 'TypeScript',
        '.java': 'Java',
        '.cpp': 'C++',
        '.c': 'C',
        '.h': 'C/C++ Header',
        '.go': 'Go',
        '.rs': 'Rust',
        '.rb': 'Ruby',
        '.php': 'PHP',
        '.cs': 'C#',
        '.kt': 'Kotlin',
        '.swift': 'Swift',
        '.scala': 'Scala',
        '.sh': 'Shell',
        # 前端相关
        '.html': 'HTML',
        '.htm': 'HTML',
        '.css': 'CSS',
        '.scss': 'SCSS',
        '.sass': 'SASS',
        '.less': 'LESS',
        '.jsx': 'React JSX',
        '.tsx': 'React TSX',
        '.vue': 'Vue',
        '.svelte': 'Svelte',
        # 数据相关
        '.sql': 'SQL',
        '.r': 'R',
        '.json': 'JSON',
        '.xml': 'XML',
        '.yaml': 'YAML',
        '.yml': 'YAML',
        '.toml': 'TOML',
        '.csv': 'CSV',
        '.md': 'Markdown',
        # 其他语言
        '.pl': 'Perl',
        '.m': 'Objective-C',
        '.mm': 'Objective-C++',
        '.dart': 'Dart',
        '.hs': 'Haskell',
        '.clj': 'Clojure',
        '.ex': 'Elixir',
        '.exs': 'Elixir',
        '.groovy': 'Groovy',
        '.ps1': 'PowerShell',
        '.lua': 'Lua',
        # 无扩展名或特殊文件
        'Dockerfile': 'Dockerfile',
        'Makefile': 'Makefile',
        'CMakeLists.txt': 'CMake',
        '.cmake': 'CMake',
        '.f': 'Fortran',
        '.f90': 'Fortran',
        '.f95': 'Fortran',
        '.proto': 'Protocol Buffers',
        '.graphql': 'GraphQL',
        '.tf': 'Terraform',
        '.sol': 'Solidity',
        '.pde': 'Processing',
        '.ino': 'Arduino',
    }

    @classmethod
    def detect(cls, file_type: str) -> Optional[str]:
        return cls.LANGUAGE_MAP.get(file_type.lower())

# 工具类：扫描并解析项目结构
class ProjectScanner:
    def __init__(self, root_path: str, ignore_patterns: List[str] = None,
                 max_chunk_size: int = 5000, balance_factor: float = 0.2):
        self.root_path = Path(root_path).resolve()
        self.project_name = self.root_path.name
        self.ignore_patterns = ignore_patterns or []

        # 分片配置参数
        self.max_chunk_size = max_chunk_size
        self.balance_factor = balance_factor

        # 默认添加一些常见的忽略模式
        default_ignore_patterns = [
            # 版本控制目录
            '.git',
            '.svn',
            # 编辑器配置目录
            '.vscode',
            '.idea',
            # Python缓存
            '__pycache__',
            '*.pyc',
            '*.pyo',
            # 系统文件
            '.DS_Store',
            # 依赖目录
            'node_modules',
            'venv',
            '.env',
            # 构建产物
            'build',
            'dist',
            # 日志文件
            'logs',
            '*.log',
            # 图片文件
            '*.jpg',
            '*.jpeg',
            '*.png',
            '*.gif',
            '*.bmp',
            '*.svg',
            '*.ico',
            '*.webp',
            # 音视频文件
            '*.mp3',
            '*.mp4',
            '*.avi',
            '*.mov',
            '*.wav',
            '*.ogg',
            '*.webm',
            # 依赖锁定文件
            'package-lock.json',  # npm
            'yarn.lock',          # Yarn
            'pnpm-lock.yaml',     # pnpm
            'composer.lock',      # PHP Composer
            'Gemfile.lock',       # Ruby
            'poetry.lock',        # Python Poetry
            'Cargo.lock',         # Rust
            'go.sum',             # Go modules
            'pubspec.lock',       # Dart/Flutter
            'mix.lock',           # Elixir
            'Podfile.lock',       # CocoaPods
            'project.lock.json',  # .NET
            'packages.lock.json', # NuGet
            # 压缩文件
            '*.zip',
            '*.rar',
            '*.7z',
            '*.tar',
            '*.gz',
            # 文档和表格
            '*.pdf',
            '*.doc',
            '*.docx',
            '*.xls',
            '*.xlsx',
            '*.ppt',
            '*.pptx',
            # 字体文件
            '*.ttf',
            '*.otf',
            '*.woff',
            '*.woff2',
            '*.eot',
            # 其他二进制资源
            '*.bin',
            '*.dat',
            '*.dll',
            '*.exe',
            '*.so',
        ]

        # 查找项目中的所有忽略文件并加载忽略模式
        ignore_files_patterns = self._find_and_load_ignore_files()

        # 合并所有的忽略模式
        all_patterns = self.ignore_patterns + default_ignore_patterns + ignore_files_patterns

        # 确保没有重复的忽略模式
        self.ignore_patterns = list(set(all_patterns))

        # 初始化pathspec匹配器
        self.spec = pathspec.PathSpec.from_lines(
            pathspec.patterns.GitWildMatchPattern, self.ignore_patterns
        )

        # 创建项目结构
        self.project_structure = ProjectStructure(
            project_name=self.project_name,
            root_path=str(self.root_path)
        )

        # 独立管理树形结构
        self.tree_structure: Optional[TreeNode] = TreeNode(
            name=self.project_name,
            path="",
            type="directory"
        )

        # 多线程处理配置
        self.max_workers = min(32, os.cpu_count() * 2 if os.cpu_count() else 4)  # 工作线程数

        # 线程队列和结果
        self.file_queue = queue.Queue()
        self.results_dict = {}
        self.lock = threading.Lock()

        # 临时存储目录和文件信息
        self.files_info = []
        self.directories_info = []

    def is_code_file(self, file_path: Path) -> bool:
        """判断是否为代码文件"""
        return LanguageDetector.detect(file_path.suffix.lower()) is not None

    def count_lines_of_code(self, file_path: Path) -> int:
        """使用二进制模式快速计算行数

        通过统计换行符数量确定行数，这比文本模式的readline()快很多
        """
        try:
            with open(file_path, 'rb') as f:
                # 计算换行符数量
                bufgen = iter(lambda: f.read(8192), b'')
                return sum(buf.count(b'\n') for buf in bufgen) + 1
        except (UnicodeDecodeError, IOError):
            return 0

    def calculate_file_hash(self, file_path: Path) -> Optional[str]:
        """计算文件的SHA256哈希值

        Args:
            file_path: 文件路径

        Returns:
            str: 文件的SHA256哈希值，如果计算失败返回None
        """
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, 'rb') as f:
                # 分块读取文件以处理大文件
                for chunk in iter(lambda: f.read(8192), b''):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except (IOError, OSError) as e:
            logger.warning(f"计算文件 {file_path} 哈希值失败: {str(e)}")
            return None

    def process_files_worker(self):
        """文件处理工作线程

        从队列获取文件，进行处理后将结果存入结果字典
        """
        while True:
            try:
                file_info, file_path = self.file_queue.get(block=False)

                # 计算文件哈希值（所有文件都计算）
                file_hash = self.calculate_file_hash(file_path)

                # 计算代码行数
                lines_of_code = None
                if file_info.language:  # 只处理代码文件
                    lines_of_code = self.count_lines_of_code(file_path)

                # 保存结果
                with self.lock:
                    file_info.file_hash = file_hash
                    if lines_of_code is not None:
                        file_info.lines_of_code = lines_of_code
                        self.results_dict[str(file_path)] = lines_of_code

                self.file_queue.task_done()
            except queue.Empty:
                break
            except Exception as e:
                logger.error(f"处理文件 {file_path} 时出错", error=str(e))
                self.file_queue.task_done()

    def should_ignore(self, path: str) -> bool:
        """检查路径是否应该被忽略

        Args:
            path: 相对于项目根目录的路径

        Returns:
            bool: 如果应该被忽略返回True，否则返回False
        """
        # 空字符串代表根目录，不应该被忽略
        if path == '':
            return False

        # 使用pathspec进行匹配，确保路径是Unix风格的
        unix_path = path.replace('\\', '/')
        is_ignore = self.spec.match_file(unix_path)
        # if is_ignore:
        #     logger.info(f"路径 {path} 被忽略")
        return is_ignore

    def _build_tree_structure(self, root_path, parent_node, current_depth=0):
        """
        递归构建树形结构

        Args:
            root_path: 当前目录路径
            parent_node: 父节点
            current_depth: 当前深度
        """
        for item in os.listdir(root_path):
            item_path = Path(root_path) / item
            rel_path = item_path.relative_to(self.root_path)
            rel_path_str = str(rel_path)

            # 检查是否需要忽略
            if self.should_ignore(rel_path_str):
                continue

            if item_path.is_dir():
                # 计算目录重要性
                # 基于目录中文件数量和子目录数量计算
                dir_importance = self._calculate_directory_importance(item_path)

                # 创建目录节点
                dir_node = TreeNode(
                    name=item,
                    path=rel_path_str,
                    type="directory",
                    children=[],
                    depth=current_depth,
                    importance=dir_importance,
                    last_modified=datetime.fromtimestamp(item_path.stat().st_mtime, timezone.utc)
                )
                parent_node.children.append(dir_node)
                # 递归处理子目录
                self._build_tree_structure(item_path, dir_node, current_depth + 1)
            else:
                # 计算文件重要性
                file_importance = self._calculate_file_importance(item_path)

                # 创建文件节点
                file_size = item_path.stat().st_size
                language = LanguageDetector.detect(item_path.suffix.lower())
                file_node = TreeNode(
                    name=item,
                    path=rel_path_str,
                    type="file",
                    size=file_size,
                    depth=current_depth,
                    importance=file_importance,
                    last_modified=datetime.fromtimestamp(item_path.stat().st_mtime, timezone.utc),
                    metadata={
                        "extension": item_path.suffix.lower(),
                        "language": language,
                        "is_code_file": language is not None
                    }
                )
                parent_node.children.append(file_node)

    def _calculate_directory_importance(self, dir_path: Path) -> int:
        """
        计算目录的重要性评分

        Args:
            dir_path: 目录路径

        Returns:
            int: 重要性评分 (1-5)
        """
        # 计算目录中的文件数量和子目录数量
        file_count = 0
        subdir_count = 0
        code_file_count = 0

        try:
            # 计算文件数量和子目录数量
            for path in dir_path.glob('*'):
                rel_path = path.relative_to(self.root_path)
                if self.should_ignore(str(rel_path)):
                    continue

                if path.is_file():
                    file_count += 1
                    # 检查是否为代码文件
                    if self.is_code_file(path):
                        code_file_count += 1
                elif path.is_dir():
                    subdir_count += 1

            # 基于文件数量和子目录数量计算重要性
            # 代码文件数量权重更高
            importance_score = min(5, 1 + (code_file_count * 0.5 + (file_count - code_file_count) * 0.2 + subdir_count * 0.3) / 10)
            return round(importance_score)
        except Exception as e:
            logger.warning(f"计算目录 {dir_path} 的重要性时出错: {str(e)}")
            return 1  # 默认重要性

    def _calculate_file_importance(self, file_path: Path) -> int:
        """
        计算文件的重要性评分

        Args:
            file_path: 文件路径

        Returns:
            int: 重要性评分 (1-5)
        """
        try:
            # 检查是否为代码文件
            is_code = self.is_code_file(file_path)

            # 获取文件大小
            file_size = file_path.stat().st_size

            # 计算代码行数（如果是代码文件）
            lines_of_code = 0
            if is_code:
                try:
                    lines_of_code = self.count_lines_of_code(file_path)
                except Exception:
                    pass

            # 基于文件类型、大小和代码行数计算重要性
            importance_score = 1  # 默认重要性

            # 代码文件比非代码文件重要
            if is_code:
                # 基于代码行数计算重要性
                if lines_of_code > 1000:
                    importance_score = 5
                elif lines_of_code > 500:
                    importance_score = 4
                elif lines_of_code > 200:
                    importance_score = 3
                elif lines_of_code > 50:
                    importance_score = 2
            else:
                # 非代码文件基于文件大小计算重要性
                if file_size > 1024 * 1024:  # > 1MB
                    importance_score = 3
                elif file_size > 100 * 1024:  # > 100KB
                    importance_score = 2

            # 特殊文件的重要性调整
            filename = file_path.name.lower()
            if filename in ['readme.md', 'license', 'dockerfile', 'makefile', 'requirements.txt', 'package.json']:
                importance_score = max(importance_score, 4)  # 这些文件通常很重要
            elif filename.startswith('main.') or filename.startswith('index.'):
                importance_score = max(importance_score, 4)  # 主文件通常很重要

            return importance_score
        except Exception as e:
            logger.warning(f"计算文件 {file_path} 的重要性时出错: {str(e)}")
            return 1  # 默认重要性

    def scan_directory(self):
        """扫描项目目录结构"""
        file_type_counter = Counter()
        language_counter = Counter()
        total_lines = 0
        total_size = 0
        directory_info_list = []  # 存储目录信息

        # 第一步：收集所有文件
        for root, dirs, files in os.walk(self.root_path):
            root_path = Path(root)
            rel_root = root_path.relative_to(self.root_path)
            rel_root_str = str(rel_root) if rel_root.parts else ""

            # 过滤掉需要忽略的目录 (使用替换方式修改dirs，直接影响os.walk的遍历)
            dirs_to_keep = []
            for dir_name in dirs:
                dir_path = rel_root / dir_name
                dir_path_str = str(dir_path)

                if not self.should_ignore(dir_path_str):
                    dirs_to_keep.append(dir_name)

                    # 创建目录信息
                    full_dir_path = root_path / dir_name
                    # 计算子目录和文件数
                    subdir_count = sum(1 for _ in full_dir_path.glob('*/') if not self.should_ignore(str(_.relative_to(self.root_path))))
                    file_count = sum(1 for _ in full_dir_path.glob('*') if _.is_file() and not self.should_ignore(str(_.relative_to(self.root_path))))

                    # 计算目录总大小和语言分布
                    dir_size = 0
                    dir_lines = 0
                    dir_languages = defaultdict(int)

                    # 遍历目录中的所有文件
                    for file_path in full_dir_path.glob('**/*'):
                        if file_path.is_file() and not self.should_ignore(str(file_path.relative_to(self.root_path))):
                            # 累计文件大小
                            file_size = file_path.stat().st_size
                            dir_size += file_size

                            # 检查是否为代码文件
                            language = LanguageDetector.detect(file_path.suffix.lower())
                            if language:
                                # 累计语言分布
                                dir_languages[language] += 1
                                # 累计代码行数
                                try:
                                    lines = self.count_lines_of_code(file_path)
                                    dir_lines += lines
                                except Exception as e:
                                    logger.warning(f"计算文件 {file_path} 代码行数失败: {str(e)}")

                    dir_info = DirectoryInfo(
                        name=dir_name,
                        path=dir_path_str,
                        file_count=file_count,
                        subdirectory_count=subdir_count,
                        last_modified=datetime.fromtimestamp(full_dir_path.stat().st_mtime, timezone.utc),
                        description=None,
                        total_size=dir_size,
                        total_lines_of_code=dir_lines,
                        languages=dict(dir_languages)
                    )
                    directory_info_list.append(dir_info)

            # 重要：替换dirs内容，这样会直接修改os.walk的遍历列表
            dirs[:] = dirs_to_keep

            # 处理文件
            for file_name in files:
                file_path = root_path / file_name
                rel_path = file_path.relative_to(self.root_path)
                rel_path_str = str(rel_path)

                # 检查文件是否应该被忽略
                if self.should_ignore(rel_path_str):
                    continue

                file_size = file_path.stat().st_size
                total_size += file_size
                extension = file_path.suffix.lower()

                # 获取编程语言
                language = LanguageDetector.detect(extension)
                if language:
                    language_counter[language] += 1

                # 创建文件信息对象
                file_info = FileInfo(
                    name=file_name,
                    path=rel_path_str,
                    size=file_size,
                    extension=extension,
                    language=language,
                    lines_of_code=None,  # 稍后填充
                    last_modified=datetime.fromtimestamp(file_path.stat().st_mtime, timezone.utc),
                    file_hash=None,  # 稍后填充
                )

                # 统计文件类型
                file_type_counter[extension] += 1
                self.files_info.append(file_info)

                # 设置是否为代码文件标志
                if self.is_code_file(file_path):
                    file_info.is_code_file = True

                # 添加所有文件到待处理队列中（用于计算哈希值和代码行数）
                self.file_queue.put((file_info, file_path))

        # 第二步：多线程处理文件哈希计算和代码行数计算
        if not self.file_queue.empty():
            logger.info(f"开始多线程处理 {self.file_queue.qsize()} 个文件的哈希值和代码行数计算")

            # 创建并启动工作线程
            workers = []
            for _ in range(min(self.max_workers, self.file_queue.qsize())):
                thread = threading.Thread(target=self.process_files_worker)
                thread.daemon = True
                workers.append(thread)
                thread.start()

            # 等待所有任务完成
            self.file_queue.join()

            # 将线程处理结果整合回文件信息列表
            for file_info in self.files_info:
                if file_info.path in self.results_dict:
                    file_info.lines_of_code = self.results_dict[file_info.path]
                    # 确保 is_code_file 字段正确设置
                    file_info.is_code_file = True

            # 更新总行数
            total_lines = sum(line_count for line_count in self.results_dict.values() if line_count is not None)

            logger.info(f"文件处理完成，计算了 {len([f for f in self.files_info if f.file_hash])} 个文件的哈希值")
        else:
            total_lines = 0

        # 添加根目录信息
        root_dir_info = DirectoryInfo(
            name=self.project_name,
            path="",
            file_count=len(self.files_info),
            subdirectory_count=len(directory_info_list),
            last_modified=datetime.now(timezone.utc),
            description=f"{self.project_name} 项目根目录",
            total_size=total_size,
            total_lines_of_code=total_lines,
            languages=dict(language_counter)
        )
        directory_info_list.insert(0, root_dir_info)

        # 更新项目结构信息
        self.project_structure.directories = directory_info_list
        self.project_structure.files = self.files_info
        self.project_structure.file_types = dict(file_type_counter)
        self.project_structure.language_summary = dict(language_counter)
        self.project_structure.total_lines_of_code = total_lines
        self.project_structure.total_size = total_size
        self.project_structure.total_files = len(self.files_info)
        self.project_structure.total_directories = len(directory_info_list)
        self.project_structure.last_modified = datetime.now(timezone.utc)

        # 计算目录深度
        max_depth = 0
        total_depth = 0
        for directory in directory_info_list:
            depth = len(directory.path.split('/')) if directory.path else 0
            max_depth = max(max_depth, depth)
            total_depth += depth

        if directory_info_list:
            self.project_structure.average_directory_depth = total_depth / len(directory_info_list)
            self.project_structure.max_directory_depth = max_depth

        # 构建树形结构
        logger.info("开始构建项目树形结构")
        self._build_tree_structure(self.root_path, self.tree_structure)

        # 设置根节点的重要性和元数据
        self.tree_structure.importance = 5  # 根节点始终最重要
        self.tree_structure.metadata = {
            "total_files": len(self.files_info),
            "total_directories": len(directory_info_list),
            "total_lines_of_code": total_lines,
            "total_size": total_size,
            "language_summary": dict(language_counter)
        }
        logger.info("项目树形结构构建完成")

    def add_ignore_pattern(self, pattern: str):
        """添加忽略模式

        Args:
            glob格式的忽略模式，如 "*.log" 或 "temp/**"
        """
        if pattern not in self.ignore_patterns:
            self.ignore_patterns.append(pattern)
            self.spec = pathspec.PathSpec.from_lines(
                pathspec.patterns.GitWildMatchPattern, self.ignore_patterns
            )

    def add_ignore_patterns(self, patterns: List[str]):
        """批量添加忽略模式

        Args:
            patterns: glob格式的忽略模式列表
        """
        for pattern in patterns:
            self.add_ignore_pattern(pattern)

    def scan(self) -> ProjectStructure:
        """执行项目扫描"""
        logger.info(f"开始扫描项目: {self.project_name}")
        self.scan_directory()

        # 生成完整的树结构字符串
        self.project_structure.structure_string = self.generate_tree_string()

        # 生成分片树结构
        self._generate_chunked_structure()

        logger.info(f"项目扫描完成: 共发现 {self.project_structure.total_files} 个文件, {self.project_structure.total_directories} 个目录")
        logger.info(f"生成了 {len(self.project_structure.structured_chunks)} 个结构化分片")
        return self.project_structure

    def export_to_json(self, output_file: str):
        """将分析结果导出为 JSON 文件"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(self.project_structure.model_dump_json(indent=2))

    def get_tree_structure(self) -> Optional[TreeNode]:
        """获取项目的树形结构

        Returns:
            Optional[TreeNode]: 项目的树形结构，如果未构建则返回None
        """
        return self.tree_structure

    def generate_tree_string(self, root_node: TreeNode = None) -> str:
        """生成字符类型的目录树信息

        Args:
            root_node: 树的根节点，默认为扫描器的树形结构

        Returns:
            str: 格式化的目录树字符串
        """
        if root_node is None:
            root_node = self.tree_structure

        formatted_tree = format_tree(root_node)

        return formatted_tree

    def _generate_chunked_structure(self) -> None:
        """生成分片树结构

        使用目录树分片器和格式化器生成结构化分片，并存储到项目结构中
        """
        try:
            if self.tree_structure is None:
                logger.warning("树结构未构建，跳过分片生成")
                return

            logger.info("开始生成项目结构分片")

            # 使用分片器对树结构进行分片
            chunked_trees = chunk_tree(
                self.tree_structure,
                max_chunk_size=self.max_chunk_size,
                balance_factor=self.balance_factor
            )

            # 使用格式化器将分片转换为字符串
            formatted_chunks = format_chunked_trees(chunked_trees)

            # 存储到项目结构中
            self.project_structure.structured_chunks = formatted_chunks

            logger.info(f"成功生成 {len(formatted_chunks)} 个结构化分片")

        except Exception as e:
            logger.warning(f"生成结构化分片失败: {str(e)}")
            # 确保即使分片失败，structured_chunks 也是一个空列表
            self.project_structure.structured_chunks = []

    def _find_and_load_ignore_files(self) -> List[str]:
        """查找项目中的所有忽略文件并加载忽略模式

        查找的文件包括：.gitignore, .dockerignore, .npmignore, .eslintignore等

        Returns:
            List[str]: 从忽略文件中加载的所有忽略模式
        """
        ignore_patterns = []

        # 定义要查找的忽略文件类型
        ignore_file_types = ['.gitignore', '.dockerignore', '.npmignore', '.eslintignore']
        all_ignore_files = []

        # 第一步：查找项目中的所有忽略文件
        for ignore_file_type in ignore_file_types:
            # 使用glob查找文件
            found_files = list(Path(self.root_path).glob(f'**/{ignore_file_type}'))
            if found_files:
                all_ignore_files.extend(found_files)
                logger.info(f"找到 {len(found_files)} 个 {ignore_file_type} 文件")

        # 如果没有找到忽略文件，直接返回空列表
        if not all_ignore_files:
            logger.info("项目中未找到任何忽略文件")
            return []

        # 第二步：加载所有忽略文件中的模式
        logger.info(f"开始加载 {len(all_ignore_files)} 个忽略文件")
        for ignore_file in all_ignore_files:
            try:
                with open(ignore_file, 'r', encoding='utf-8') as f:
                    # 读取并过滤空行和注释行
                    patterns = [
                        line.strip() for line in f.readlines()
                        if line.strip() and not line.strip().startswith('#')
                    ]

                    # 记录加载的模式数量
                    if patterns:
                        rel_path = ignore_file.relative_to(self.root_path)
                        logger.info(f"从 {rel_path} 加载了 {len(patterns)} 个忽略模式")
                        ignore_patterns.extend(patterns)
            except Exception as e:
                logger.error(f"加载 {ignore_file} 失败", error=str(e))

        # 去除重复的忽略模式
        unique_patterns = list(set(ignore_patterns))
        logger.info(f"总共加载了 {len(unique_patterns)} 个唯一的忽略模式")

        return unique_patterns


# 用法示例
if __name__ == "__main__":
    # 项目路径
    project_path = Path(os.path.dirname(os.path.abspath(__file__))).parent.parent.parent.parent

    # 创建扫描器
    scanner = ProjectScanner(project_path)

    # 执行扫描
    result = scanner.scan()

    # 根据格式输出结果
    output_file = f"{result.project_name}_structure.json"
    scanner.export_to_json(output_file)
    print(f"项目结构已保存到: {output_file}")
    print(f"项目: {result.project_name}")
    print(f"根路径: {result.root_path}")
    print(f"文件总数: {result.total_files}")
    print(f"目录总数: {result.total_directories}")
    print(f"代码总行数: {result.total_lines_of_code}")
    print()
    print("项目结构:")
    print(result.structure_string)
