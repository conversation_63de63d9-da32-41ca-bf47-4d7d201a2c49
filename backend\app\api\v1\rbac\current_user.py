"""
当前用户API处理
"""
from typing import Optional

import jwt
import structlog
from app.core.config import settings
from dependency_injector.wiring import inject, Provide
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.services.rbac.auth import AuthService
from app.services.rbac.user import UserService
from app.schemas.rbac.user import UserUpdateRequest, UserChangePasswordRequest, User, CurrentUserUpdateRequest
from app.core.middleware import auth_middleware, require_auth
from app.utils.security import get_current_user_id

logger = structlog.get_logger(__name__)

class CurrentUserHandler(BaseHandler):
    """当前用户信息处理器"""
    
    @inject
    def initialize(
        self,
        user_service: UserService = Provide[Container.user_service],
        auth_service: AuthService = Provide[Container.auth_service]

    ):
        """初始化处理器
        
        Args:
            user_service: 用户服务
        """
        super().initialize()
        self.user_service: UserService = user_service
        self.auth_service: AuthService = auth_service
    
    @require_auth(required=True)
    async def get(self):
        """获取当前用户信息"""
        try:
            # 从认证中获取用户ID
            auth_header = self.request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                self.write_error(401, error_message="未授权")
                return
            token = auth_header[7:]  # 移除'Bearer '前缀
            # 获取用户信息


            payload = jwt.decode(
                token,
                settings.security.JWT_SECRET,
                algorithms=[settings.security.JWT_ALGORITHM]
            )
            if payload.get("type") != "access":
                raise ValueError("无效的访问令牌")

            user_id = payload.get("sub")
            if not user_id:
                raise ValueError("无效的访问令牌")


            user_info = await self.user_service.get_user_with_roles(user_id, is_current_user_need_password=True)
            if not user_info:
                self.write_error(500, error_message="用户不存在")
                return
            self.success_response(user_info)
        except Exception as e:
            logger.error("获取当前用户信息失败", error=str(e), exc_info=True)
            self.write_error(500, error_message="获取当前用户信息失败: {str(e)}")
    
    @require_auth(required=True)
    async def put(self):
        """更新当前用户信息"""
        try:
            try:
                auth_header = self.request.headers.get('Authorization')
                if not auth_header or not auth_header.startswith('Bearer '):
                    self.write_error(401, error_message="未授权")
                    return
                token = auth_header[7:]  # 移除'Bearer '前缀
                # 获取用户信息

                payload = jwt.decode(
                    token,
                    settings.security.JWT_SECRET,
                    algorithms=[settings.security.JWT_ALGORITHM]
                )
                if payload.get("type") != "access":
                    raise ValueError("无效的访问令牌")

                user_id = payload.get("sub")
                if not user_id:
                    raise ValueError("无效的访问令牌")

            except Exception as e:
                self.write_error(500, error_message="解析访问令牌失败"+str(e))
                return

            data = self.json_body
            update_data = CurrentUserUpdateRequest.model_validate(data)
            # 更新用户信息
            updated_user = await self.user_service.update_current_user(
                user_id,
                update_data
            )
            
            # if not updated_user:
            #     self.send_error(404, message="用户不存在")
            #     return
            
            # 返回更新后的用户信息
            # self.write(updated_user.model_dump())
            self.success_response()
        except ValueError as e:
            logger.error("更新当前用户信息失败", error=str(e), exc_info=True)
            self.write_error(500, error_message="更新当前用户信息失败" + str(e))
            return
        except Exception as e:
            logger.error("更新当前用户信息失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"更新当前用户信息失败: {str(e)}")
        


class CurrentUserChangePasswordHandler(BaseHandler):
    """当前用户密码修改处理器（暂时禁用）6.4日不再需要原密码改为验证码登陆"""
    
    @inject
    def initialize(
        self,
        user_service: UserService = Provide[Container.user_service]
    ):
        """初始化处理器
        
        Args:
            user_service: 用户服务
        """
        super().initialize()
        self.user_service: UserService = user_service
    
    @require_auth(required=True)
    async def post(self):
        try:

            # 解析请求体
            try:
                auth_header = self.request.headers.get('Authorization')
                user_id = get_current_user_id(auth_header)
                data = self.json_body
                data["user_id"] = user_id
                password_data = UserChangePasswordRequest.model_validate(data)
            except Exception as e:
                self.send_error(400, message=f"无效的请求数据: {str(e)}")
                return
            
            # 修改密码
            await self.user_service.change_password(
                password_data
            )

            # 返回成功消息
            self.success_response(data=data,message="密码修改成功")
        except ValueError as e:
            logger.error("修改当前用户密码失败", error=str(e), exc_info=True)
            self.send_error(400, message=str(e))
        except Exception as e:
            logger.error("修改当前用户密码失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"修改当前用户密码失败: {str(e)}")
