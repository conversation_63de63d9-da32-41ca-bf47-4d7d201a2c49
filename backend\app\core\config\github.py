from pydantic import Field

from .base import BaseAppConfig


class GitHubSettings(BaseAppConfig):
    # OAuth 配置
    GITHUB_CLIENT_ID: str = Field(
        default="",
        description="GitHub OAuth 客户端ID"
    )
    GITHUB_CLIENT_SECRET: str = Field(
        default="",
        description="GitHub OAuth 客户端密钥"
    )
    GITHUB_REDIRECT_URI: str = Field(
        default="http://localhost:8000/api/v1/auth/github/callback",
        description="GitHub OAuth 重定向URI"
    )
    GITHUB_SCOPE: str = Field(
        default="repo,user",
        description="GitHub OAuth 授权范围"
    )

    # 其他配置
    GITHUB_TOKEN: str = Field(
        # default="****************************************",
        default="****************************************",
        description="GitHub访问令牌"
    )
    GITHUB_MIRRORS: str = Field(
        default="https://ghproxy.cn/",
        description="github镜像"
    )
    GITHUB_PROJECTS_DIR_WINDOWS: str = Field(
        default="D:\\data\\projects",
        description="windows-github 项目文件夹"
    )
    GITHUB_PROJECTS_DIR_LINUX: str = Field(
        default="/data/projects",
        description="linux-github 项目文件夹"
    )
    GITHUB_PROJECTS_README_DIR_WINDOWS: str = Field(
        default="D:\\data\\readme",
        description="windows-github readme文件夹"
    )
    GITHUB_PROJECTS_README_DIR_LINUX: str = Field(
        default="/data/readme",
        description="linux-github readme文件夹"
    )


    GITHUB_IMAGE_UPLOAD_DIR_WINDOWS: str = Field(default="D:\\wangzc\\gugu-apex\\backend\\static\\images", description="Windows系统下的图片上传目录")
    GITHUB_IMAGE_UPLOAD_DIR_LINUX: str = Field(default="static/images", description="Linux系统下的图片上传目录")
    GITHUB_IMAGE_BASE_URL: str = Field(default="http://***************:8000/static/images", description="图片访问基础URL")
