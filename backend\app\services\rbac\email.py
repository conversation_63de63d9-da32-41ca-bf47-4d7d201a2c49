import smtplib
import random
import string
from email.mime.text import MIMEText
from email.header import Header
from datetime import datetime, timedelta
from typing import Optional, Dict
import structlog
from app.core.config import settings

logger = structlog.get_logger(__name__)

class EmailService:
    """邮件服务"""

    def __init__(self):
        self.verification_codes: Dict[str, Dict[str, str]] = {}
        self.change_password_verification_codes: Dict[str, Dict[str, str]] = {}
        self.change_oauth_mail_codes: Dict[str, Dict[str, str]] = {}
        self.fast_auth_mail_codes: Dict[str, Dict[str, str]] = {}
        self.bind_change_mail_codes: Dict[str, Dict[str, str]] = {}  # 添加新的验证码存储
        # self.bind_change_mail_codes_original: Dict[str, Dict[str, str]] = {}  # 验证原绑定


        # 十分钟超时
        self.email_timeout = 10

    def generate_verification_code(self, length: int = 6) -> str:
        """生成验证码"""
        return ''.join(random.choices(string.digits, k=length))

    def send_bind_change_verification_code(self, email: str) -> bool:
        """发送更换绑定邮箱验证码

        Args:
            email: 邮箱地址

        Returns:
            bool: 发送成功返回True，否则返回False
        """
        code = self.generate_verification_code()
        subject = "您的更换绑定邮箱验证码" + str(datetime.now())
        content = f"您的更换绑定邮箱验证码：{code}，有效期为5分钟。"
        logger.info("发送更换绑定邮箱验证码 :: " + str(email) + str(code))
        if self.send_email(email, subject, content):
            self.bind_change_mail_codes[email] = {
                'code': code,
                'expire_time': datetime.now() + timedelta(minutes=self.email_timeout)
            }
            return True
        return False

    def send_password_reset_email(self, email: str, reset_url: str) -> bool:
        """发送密码重置邮件

        Args:
            email: 接收者邮箱地址
            reset_url: 密码重置链接

        Returns:
            bool: 发送成功返回True，否则返回False
        """
        try:
            subject = "密码重置请求"
            # 纯文本版本作为备用
            text_content = f"""
            密码重置

            您好，

            我们收到了重置您账户密码的请求。如果这不是您的操作，请忽略此邮件。

            请访问以下链接重置您的密码：
            {reset_url}

            此链接将在5分钟后失效。

            谢谢！

            此邮件由系统自动发送，请勿回复。
            """

            # 发送邮件
            self.send_email(
                to_email=email,
                subject=subject,
                content=text_content
            )

            logger.info("密码重置邮件发送成功", email=email)
            return True

        except Exception as e:
            logger.error("发送密码重置邮件失败", email=email, error=str(e))
            return False

    def send_email(self, to_email: str, subject: str, content: str) -> bool:
        """发送邮件"""
        try:
            msg = MIMEText(content, 'plain', 'utf-8')
            msg['From'] = settings.email.EMAIL_USER
            msg['To'] = to_email
            msg['Subject'] = Header(subject, 'utf-8')
            
            with smtplib.SMTP_SSL(
                settings.email.EMAIL_HOST,
                settings.email.EMAIL_PORT,
                timeout=settings.email.EMAIL_TIMEOUT
            ) as server:
                server.login(settings.email.EMAIL_USER, settings.email.EMAIL_PASSWORD)
                server.sendmail(settings.email.EMAIL_USER, [to_email], msg.as_string())
            return True
        except Exception as e:
            logger.error("发送邮件失败", error=str(e))
            return False
    
    def send_self_update_password_verification_code(self, email: str) -> bool:
        """发送验证码"""
        code = self.generate_verification_code()
        subject = "您的找回密码验证码" + str(datetime.now())
        content = f"您的找回密码验证码是：{code}，有效期为5分钟。"
        logger.info("发送邮件 :: " + str(email) + str(code))
        if self.send_email(email, subject, content):
            self.change_password_verification_codes[email] = {
                'code': code,
                'expire_time': datetime.now() + timedelta(minutes=self.email_timeout)
            }
            return True
        return False

    def send_self_oauth_email_verification_code(self, email: str) -> bool:
        """发送验证码"""
        code = self.generate_verification_code()
        subject = "您的绑定邮箱验证码" + str(datetime.now())
        content = f"您的绑定邮箱验证码：{code}，有效期为5分钟。"
        logger.info("发送邮件 :: " + str(email) + str(code))
        if self.send_email(email, subject, content):
            self.change_oauth_mail_codes[email] = {
                'code': code,
                'expire_time': datetime.now() + timedelta(minutes=self.email_timeout)
            }
            return True
        return False

    def send_verification_code(self, email: str) -> bool:
        """发送验证码"""
        code = self.generate_verification_code()
        subject = "您的注册验证码" + str(datetime.now())
        content = f"您的注册验证码是：{code}，有效期为5分钟。"
        logger.info("发送邮件 :: " + str(email) + str(code))
        if self.send_email(email, subject, content):
            self.verification_codes[email] = {
                'code': code,
                'expire_time': datetime.now() + timedelta(minutes=self.email_timeout)
            }
            return True
        return False


    def send_fast_login_verification_code(self, email: str) -> bool:
        """发送验证码"""
        code = self.generate_verification_code()
        subject = "您的验证码 " + str(datetime.now())
        content = f"您的验证码是：{code}，有效期为5分钟。"
        logger.info("发送快捷登陆邮件 :: " + str(email) + str(code))
        if self.send_email(email, subject, content):
            self.fast_auth_mail_codes[email] = {
                'code': code,
                'expire_time': datetime.now() + timedelta(minutes=self.email_timeout)
            }
            return True
        return False

    # change_password_verification_codes
    def change_password_verification_codes(self, email: str, code: str) -> bool:
        """验证验证码"""
        if email not in self.change_password_verification_codes:
            return False

        stored_code = self.change_password_verification_codes[email]
        if datetime.now() > stored_code['expire_time']:
            del self.change_password_verification_codes[email]
            return False

        if stored_code['code'] == code:
            del self.change_password_verification_codes[email]
            return True

        return False

    def _verify_code_generic(self, email: str, code: str, code_storage: dict) -> bool:
        """
        通用验证码验证函数

        Args:
            email: 邮箱地址
            code: 待验证的验证码
            code_storage: 验证码存储字典

        Returns:
            bool: 验证是否通过
        """
        if email not in code_storage:
            return False

        stored_code = code_storage[email]

        # 检查验证码是否过期
        if datetime.now() > stored_code['expire_time']:
            del code_storage[email]  # 只在过期时删除
            return False

        # 验证码匹配时删除并返回True
        if stored_code['code'] == code:
            del code_storage[email]
            return True

        # 验证码不匹配时只返回False，不删除
        return False

    def verify_code(self, email: str, code: str) -> bool:
        """注册业务 验证码"""
        return self._verify_code_generic(email, code, self.verification_codes)

    def verify_oauth_mail_codes(self, email: str, code: str) -> bool:
        """第三方登陆业务 验证码"""
        return self._verify_code_generic(email, code, self.change_oauth_mail_codes)

    def verify_change_password_codes(self, email: str, code: str) -> bool:
        """修改密码业务 验证码"""
        return self._verify_code_generic(email, code, self.change_password_verification_codes)

    def verify_fast_login_codes(self, email: str, code: str) -> bool:
        """快速登陆业务 验证码"""
        return self._verify_code_generic(email, code, self.fast_auth_mail_codes)



    def verify_bind_change_codes(self, email: str, code: str) -> bool:
        """验证更换绑定邮箱验证码

        Args:
            email: 邮箱地址
            code: 验证码

        Returns:
            bool: 验证成功返回True，否则返回False
        """
        logger.info(
            "开始验证更换绑定邮箱验证码",
            email=email,
            code=code,
            stored_codes=self.bind_change_mail_codes
        )
        result = self._verify_code_generic(email, code, self.bind_change_mail_codes)
        logger.info(
            "更换绑定邮箱验证码验证结果",
            email=email,
            result=result
        )
        return result
