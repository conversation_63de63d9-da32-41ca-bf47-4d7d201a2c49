"""核心属性部分，对应包中的 ``/docProps/core.xml`` 部分。"""

from __future__ import annotations

import datetime as dt
from typing import TYPE_CHECKING

from app.utils.slx_parser.opc.constants import CONTENT_TYPE as CT
from app.utils.slx_parser.opc.coreprops import CoreProperties
from app.utils.slx_parser.opc.packuri import PackURI
from app.utils.slx_parser.opc.part import XmlPart
from app.utils.slx_parser.oxml.coreprops import CT_CoreProperties

if TYPE_CHECKING:
    from app.utils.slx_parser.opc.package import OpcPackage


class CorePropertiesPart(XmlPart):
    """对应名为 ``/metadata/coreProperties.xml`` 的部分。

    这里的"core"是"Dublin Core"的简称，包含了在所有类型的文档中都比较常见的文档元数据，
    不仅仅是DOCX文档。
    """

    @classmethod
    def default(cls, package: OpcPackage):
        """返回一个新的 |CorePropertiesPart| 对象，使用默认值初始化其基本属性。"""
        core_properties_part = cls._new(package)
        core_properties = core_properties_part.core_properties
        core_properties.title = "slx"
        core_properties.last_modified_by = "shiyu"
        core_properties.revision = 1
        core_properties.modified = dt.datetime.now(dt.timezone.utc)
        return core_properties_part

    @property
    def core_properties(self):
        """一个 |CoreProperties| 对象，提供对此核心属性部分中包含的核心属性的读/写访问。"""
        return CoreProperties(self.element)

    @classmethod
    def _new(cls, package: OpcPackage) -> CorePropertiesPart:
        partname = PackURI("/docProps/core.xml")
        content_type = CT.OPC_CORE_PROPERTIES
        coreProperties = CT_CoreProperties.new()
        return CorePropertiesPart(partname, content_type, coreProperties, package)
