"""
Git 下载工具类
"""
import asyncio
import os
import platform
import subprocess
import shutil
import uuid
from urllib.parse import urlparse

import aiohttp
import structlog
from typing import Dict, Any, Optional, Tuple, List
import requests
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.models.github.github_project import GitHubProjectModel
from app.utils.status_enum import ProjectStatusEnum
from app.utils.mdfile_solver import ImgExtractor, ImgExtExtension
import markdown
import json

logger = structlog.get_logger(__name__)

class GitHubDownloader:
    """Git项目下载工具类"""

    _instance = None
    _download_queue = asyncio.Queue()
    _download_tasks = []
    _initialized = False
    _max_concurrent_downloads = 2  # 最大并发下载数
    _download_stats = {
        "total": 0,
        "success": 0,
        "failed": 0,
        "in_progress": 0
    }

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, session=None, async_session=None):
        if not self._initialized:
            # 检测操作系统
            os_type = platform.system()
            # 从配置中获取基础目录（根据操作系统）
            os_type = platform.system()
            if os_type == "Windows":
                base_dir = settings.github.GITHUB_PROJECTS_DIR_WINDOWS
            else:  # Linux 或 Mac
                base_dir = settings.github.GITHUB_PROJECTS_DIR_LINUX
            self.base_dir = base_dir
            logger.info(f"使用项目存储目录: {self.base_dir} (操作系统: {os_type})")

            # 创建目录（如果不存在）
            os.makedirs(self.base_dir, exist_ok=True)

            # 配置镜像列表
            self.mirrors = getattr(settings, "git_mirrors",
                                   ["https://ghproxy.cn/"])

            # 保存会话提供者
            self._session = session
            self._async_session = async_session
            for _ in range(self._max_concurrent_downloads):
                task = asyncio.create_task(self._process_download_queue())
                GitHubDownloader._download_tasks.append(task)
            logger.info(f"已启动 {self._max_concurrent_downloads} 个GitHub下载队列处理任务")

            # 确保下载任务已启动
            # if GitHubDownloader._download_task is None:
            #     GitHubDownloader._download_task = asyncio.create_task(self._process_download_queue())
            #     logger.info("GitHub下载队列处理任务已启动")
            self._initialized = True
        logger.info(f"Git下载器初始化完成，基础目录: {self.base_dir}，可用镜像: {self.mirrors}")

    @classmethod
    async def get_static_id_list(cls) -> List[int]:
        """获取静态ID列表（当前下载队列中的项目ID）

        Returns:
            List[int]: 当前下载队列中的项目ID列表
        """
        try:
            # 直接复制整个队列
            queue_copy = []
            temp_queue = asyncio.Queue()

            # 把原队列的所有内容复制到临时队列
            while not cls._download_queue.empty():
                try:
                    item = cls._download_queue.get_nowait()
                    queue_copy.append(item)
                    temp_queue.put_nowait(item)
                except asyncio.QueueEmpty:
                    break

            # 把临时队列的内容重新放回原队列
            while not temp_queue.empty():
                try:
                    item = temp_queue.get_nowait()
                    cls._download_queue.put_nowait(item)
                except asyncio.QueueEmpty:
                    break

            # 提取项目ID
            id_list = [item[0] for item in queue_copy]

            logger.info(f"获取到静态ID列表，共 {len(id_list)} 个项目")
            return id_list

        except Exception as e:
            logger.error(f"获取静态ID列表失败: {str(e)}")
            return []

    @classmethod
    async def get_download_queue_status(cls) -> Dict[str, int]:
        """获取下载队列状态

        Returns:
            Dict[str, int]: 包含队列状态的字典
        """
        return {
            "queue_size": cls._download_queue.qsize(),
            "active_tasks": len(cls._download_tasks),
            "max_concurrent": cls._max_concurrent_downloads,
            "total_downloads": cls._download_stats["total"],
            "successful_downloads": cls._download_stats["success"],
            "failed_downloads": cls._download_stats["failed"],
            "in_progress": cls._download_stats["in_progress"]
        }

    @classmethod
    async def add_to_download_queue(cls, project_id: int, repo_url: str) -> None:
        """添加项目到下载队列

        Args:
            project_id: 项目ID
            repo_url: 仓库URL
        """
        await cls._download_queue.put((project_id, repo_url))
        logger.info(f"项目 {project_id} 已添加到下载队列: {repo_url}")

    async def upload_local_image(self, local_image_path: str) -> Optional[str]:
        """上传本地图片并返回可访问的URL

        Args:
            local_image_path: 本地图片的完整路径

        Returns:
            Optional[str]: 上传后的可访问图片URL
        """
        try:
            # 确定上传目录
            if os.name == 'nt':  # Windows
                upload_dir = settings.github.GITHUB_IMAGE_UPLOAD_DIR_WINDOWS
            else:  # Linux/Mac
                upload_dir = settings.github.GITHUB_IMAGE_UPLOAD_DIR_LINUX

            # 确保上传目录存在
            os.makedirs(upload_dir, exist_ok=True)

            # 读取本地图片
            with open(local_image_path, 'rb') as f:
                image_data = f.read()

            # 获取文件扩展名
            ext = os.path.splitext(local_image_path)[1].lower()
            if ext not in ['.png', '.jpg', '.jpeg', '.gif']:
                logger.warning(f"不支持的图片格式: {ext}")
                return None

            # 生成唯一文件名
            new_filename = f"github_{uuid.uuid4().hex}{ext}"
            file_path = os.path.join(upload_dir, new_filename)

            # 保存图片
            with open(file_path, 'wb') as f:
                f.write(image_data)

            # 返回图片URL
            file_url = f"{settings.github.GITHUB_IMAGE_BASE_URL}/{new_filename}"
            logger.info(f"本地图片已上传: {file_url}")
            return file_url

        except Exception as e:
            logger.error(f"上传本地图片失败: {str(e)}")
            return None

    async def _get_pic_possible(self, local_path: str) -> Optional[str]:
        """
        Args:
            local_path: 本地仓库路径

        Returns:
            Optional[str]: 处理后的本地图片URL列表的JSON字符串
        """
        try:
            found_images = []

            #本地图片
            for root, _, files in os.walk(local_path):
                for file in files:
                    if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                        file_path = os.path.join(root, file)
                        try:
                            local_url = await self.upload_local_image(file_path)
                            if local_url:
                                found_images.append(local_url)
                        except Exception as e:
                            logger.warning(f"处理本地图片失败: {file_path}, 错误: {str(e)}")

            # md图片
            for root, _, files in os.walk(local_path):
                for file in files:
                    if file.lower().endswith('.md'):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()

                            md = markdown.Markdown(extensions=[ImgExtExtension()])
                            md.convert(content)

                            for img_url in md.images:
                                if img_url and (img_url.startswith('http://') or img_url.startswith('https://')):
                                    local_url = await self.download_and_store_github_image(img_url)
                                    if local_url:
                                        found_images.append(local_url)

                        except Exception as e:
                            logger.warning(f"处理Markdown文件失败: {file_path}, 错误: {str(e)}")

            # 去重
            found_images = list(set(found_images))[:10]
            # 2json
            return json.dumps(found_images)

        except Exception as e:
            logger.error(f"扫描图片时发生错误: {str(e)}")
            return None

    async def _process_download_queue(self) -> None:
        """处理下载队列的异步任务"""
        while True:
            try:
                # 从队列获取项目信息
                project_id, repo_url = await self._download_queue.get()
                self._download_stats["in_progress"] += 1
                self._download_stats["total"] += 1
                # 使用异步上下文管理器创建新的session 这里真的能行吗？
                async with self._async_session() as session:
                    try:
                        # 更新项目状态为下载中
                        await self._update_project_status(session, project_id, ProjectStatusEnum.DOWNLOADING.value)

                        # 下载仓库
                        success, local_path, project_info = await self._download_repository_async(repo_url)

                        if success:
                            # 更新项目信息
                            image_list = await self._get_pic_possible(local_path)
                            await self._update_project_info(session, project_id, local_path, project_info, image_list)
                            self._download_stats["success"] += 1
                            logger.info(f"项目 {project_id} 下载成功: {repo_url}")
                        else:
                            # 更新项目状态为下载失败
                            await self._update_project_status(session, project_id, ProjectStatusEnum.FAILED.value)
                            self._download_stats["failed"] += 1
                            logger.error(f"项目 {project_id} 下载失败: {repo_url}")
                        logger.info(f"项目 {project_id} 处理完成，程序休息2秒")
                        await asyncio.sleep(2)

                    except Exception as e:
                        logger.error(f"处理项目 {project_id} 时发生错误: {str(e)}")
                        await self._update_project_status(session, project_id, ProjectStatusEnum.FAILED.value)
                        self._download_stats["failed"] += 1
                    finally:
                        self._download_stats["in_progress"] -= 1
                        # 标记任务完成
                        self._download_queue.task_done()

            except Exception as e:
                logger.error(f"下载队列处理发生错误: {str(e)}")
                await asyncio.sleep(5)  # 发生错误时等待一段时间再继续

    async def _update_project_status(self, session: AsyncSession, project_id: int, status: str) -> None:
        """更新项目状态

        Args:
            session: 异步数据库会话
            project_id: 项目ID
            status: 新状态
        """
        try:
            project = await session.get(GitHubProjectModel, project_id)
            if project:
                project.project_phase = status
                await session.commit()
        except Exception as e:
            logger.error(f"更新项目状态失败: {str(e)}")
            await session.rollback()

    async def _update_project_info(self, session: AsyncSession, project_id: int,
                                   local_path: str, project_info: Dict[str, Any], image_list: str = None) -> None:
        """更新项目信息

        Args:
            session: 异步数据库会话
            project_id: 项目ID
            local_path: 本地路径
            project_info: 项目信息
        """
        try:
            project = await session.get(GitHubProjectModel, project_id)
            if project:
                if project_info.get("image_url"):
                    local_image_url = await self.download_and_store_github_image(project_info["image_url"])
                else:
                    local_image_url = None

                    # 处理 image_list
                image_urls = []
                if image_list:
                    image_urls = json.loads(image_list)

                # 如果有 local_image_url，将其添加到列表开头
                if local_image_url:
                    if local_image_url not in image_urls:
                        image_urls.insert(0, local_image_url)

                project.local_path = local_path
                project.image_url = local_image_url
                project.icon_url = local_image_url
                project.description_recommend = project_info.get("description_recommend", "")
                project.description_project = project_info.get("description_recommend", "")
                project.tags = project_info.get("tags", [])
                # 下载成功
                project.project_phase = ProjectStatusEnum.download_success.value
                #星数
                project.stars = project_info.get("stars", "0")
                project.image_list = json.dumps(image_urls)
                await session.commit()
        except Exception as e:
            logger.error(f"更新项目信息失败: {str(e)}")
            await session.rollback()


    async def download_and_store_github_image(self, image_url: Optional[str]) -> Optional[str]:
        if not image_url:
            return None
        try:
            # 确定上传目录
            if os.name == 'nt':  # Windows
                upload_dir = settings.github.GITHUB_IMAGE_UPLOAD_DIR_WINDOWS
            else:  # Linux/Mac
                upload_dir = settings.github.GITHUB_IMAGE_UPLOAD_DIR_LINUX

            # 确保上传目录存在
            os.makedirs(upload_dir, exist_ok=True)

            # 下载图片
            async with aiohttp.ClientSession() as session:
                async with session.get(image_url) as response:
                    if response.status != 200:
                        logger.error(f"下载GitHub图片失败: {image_url}, 状态码: {response.status}")
                        return None

                    image_data = await response.read()

                    # 获取文件扩展名
                    content_type = response.headers.get('Content-Type', '')
                    ext = '.jpg'  # 默认扩展名
                    if 'png' in content_type:
                        ext = '.png'
                    elif 'gif' in content_type:
                        ext = '.gif'
                    elif 'jpeg' in content_type or 'jpg' in content_type:
                        ext = '.jpg'

                    # 生成唯一文件名
                    new_filename = f"github_{uuid.uuid4().hex}{ext}"
                    file_path = os.path.join(upload_dir, new_filename)

                    # 保存图片
                    with open(file_path, 'wb') as f:
                        f.write(image_data)

                    # 返回图片URL
                    file_url = f"{settings.github.GITHUB_IMAGE_BASE_URL}/{new_filename}"
                    logger.info(f"GitHub图片已下载并存储: {file_url}")
                    return file_url

        except Exception as e:
            logger.error(f"处理GitHub图片失败: {str(e)}")
            return None

    def _use_mirror_url(self, repo_url: str) -> str:
        """使用镜像URL替换原始GitHub URL
        
        Args:
            repo_url: 原始仓库URL
            
        Returns:
            str: 使用镜像的URL
        """
        # 检查是否为GitHub URL
        if "github.com" in repo_url and self.mirrors:
            # 使用第一个可用的镜像
            return f"{self.mirrors[0]}{repo_url}"
        return repo_url


    def download_repository(self, repo_url: str) -> Tuple[bool, str, Dict[str, Any]]:
        """下载Git仓库
        
        Args:
            repo_url: 仓库URL
            
        Returns:
            Tuple[bool, str, Dict[str, Any]]: (成功标志, 本地路径, 项目信息)
        """
        # 解析URL，获取项目名称
        parsed_url = urlparse(repo_url)
        path_parts = parsed_url.path.strip('/').split('/')
        
        if len(path_parts) < 2:
            logger.error(f"无效的GitHub URL: {repo_url}")
            return False, "", {}
        
        owner, repo_name = path_parts[-2], path_parts[-1]
        if repo_name.endswith('.git'):
            repo_name = repo_name[:-4]
        
        # 创建本地目录路径（考虑操作系统差异）
        local_path = os.path.join(self.base_dir, owner, repo_name)

        logger.info(f"本地目录::{local_path}")
        normalized_path = self._normalize_path(local_path)
        
        # 检查目录是否已存在，如果存在则清空
        if os.path.exists(normalized_path):
            logger.info(f"目标目录已存在，将被清空: {normalized_path}")
            shutil.rmtree(normalized_path)
        
        # 创建目录
        os.makedirs(normalized_path, exist_ok=True)
        
        # 使用镜像URL
        mirror_repo_url = self._use_mirror_url(repo_url)
        
        # 执行git clone
        try:
            logger.info(f"开始克隆仓库: {repo_url}")
            if mirror_repo_url != repo_url:
                logger.info(f"使用镜像URL: {mirror_repo_url}")
                
            subprocess.run(
                ["git", "clone", mirror_repo_url, normalized_path],
                check=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            logger.info(f"仓库克隆成功: {repo_url}")
            
            # 获取项目信息
            project_info = self._get_repository_info(repo_url, owner, repo_name)
            project_info["local_path"] = normalized_path
            project_info["name"] = repo_name
            
            return True, normalized_path, project_info
            
        except subprocess.CalledProcessError as e:
            logger.error(f"仓库克隆失败: {str(e)}", error=e.stderr.decode() if e.stderr else "")
            return False, "", {}
        except Exception as e:
            logger.error(f"下载过程中发生错误: {str(e)}")
            return False, "", {}

    async def _download_repository_async(self, repo_url: str) -> Tuple[bool, str, Dict[str, Any]]:
        """异步下载Git仓库

        Args:
            repo_url: 仓库URL

        Returns:
            Tuple[bool, str, Dict[str, Any]]: (成功标志, 本地路径, 项目信息)
        """
        # 在线程池中执行同步的git clone操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.download_repository, repo_url)

    def _normalize_path(self, path: str) -> str:
        """根据操作系统规范化路径
        
        Args:
            path: 原始路径
            
        Returns:
            str: 规范化后的路径
        """
        # 检测操作系统
        os_type = platform.system()
        
        if os_type == "Windows":
            # Windows路径处理
            return path.replace('/', '\\')
        else:
            # Linux/Mac路径处理
            return path.replace('\\', '/')
    
    def _get_repository_info(self, repo_url: str, owner: str, repo_name: str) -> Dict[str, Any]:
        """获取仓库信息
        
        Args:
            repo_url: 仓库URL
            owner: 仓库所有者
            repo_name: 仓库名称
            
        Returns:
            Dict[str, Any]: 仓库信息
        """
        # 尝试从GitHub API获取仓库信息
        api_url = f"https://api.github.com/repos/{owner}/{repo_name}"
        
        try:
            headers = {}
            if settings.github.GITHUB_TOKEN:
                headers["Authorization"] = f"token {settings.github.GITHUB_TOKEN}"
                
            response = requests.get(api_url, headers=headers)
            response.raise_for_status()
            
            repo_data = response.json()
            
            # 提取需要的信息
            tags = []
            if "topics" in repo_data:
                tags = repo_data["topics"]
            elif "language" in repo_data and repo_data["language"]:
                tags = [repo_data["language"]]
            
            return {
                "repository_url": repo_url,
                "description_recommend": repo_data.get("description", ""),
                "tags": tags,
                "stars": str(repo_data.get("stargazers_count", 0)),  #
                "image_url": repo_data.get("owner", {}).get("avatar_url", None),
                "status": "true"
            }
            
        except Exception as e:
            logger.warning(f"无法从GitHub API获取仓库信息: {str(e)}")
            # 返回基本信息
            return {
                "repository_url": repo_url,
                "tags": [],
                "status": "true"
            }