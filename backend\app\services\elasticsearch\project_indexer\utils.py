"""
搜索工具函数模块

提供搜索相关的验证和工具函数。
注意：文档格式化功能现在由 Logstash 处理，此模块只负责搜索相关的工具函数。
"""

class SearchValidationUtils:
    """搜索验证工具类"""

    @staticmethod
    def validate_search_params(query: str = None,
                             page: int = 1,
                             page_size: int = 10) -> tuple:
        """验证搜索参数"""
        # 验证页码
        if page < 1:
            page = 1

        # 验证页面大小
        if page_size < 1:
            page_size = 10
        elif page_size > 100:
            page_size = 100

        # 验证查询字符串
        if query:
            query = query.strip()
            if len(query) == 0:
                query = None

        return query, page, page_size

    @staticmethod
    def validate_sort_params(sort_by: str = None, sort_order: str = "desc") -> tuple:
        """验证排序参数"""
        # 验证排序字段
        valid_sort_fields = [
            "name", "created_at", "updated_at", "cards_count",
            "likes_count", "views_count", "_score"
        ]

        if sort_by and sort_by not in valid_sort_fields:
            sort_by = "_score"

        # 验证排序方向
        if sort_order not in ["asc", "desc"]:
            sort_order = "desc"

        return sort_by, sort_order

    @staticmethod
    def validate_filters(filters: dict = None) -> dict:
        """验证过滤参数"""
        if not filters:
            return {}

        validated_filters = {}

        # 验证状态过滤
        if "status" in filters and filters["status"]:
            validated_filters["status"] = filters["status"]

        # 验证标签过滤
        if "tags" in filters and isinstance(filters["tags"], list):
            validated_filters["tags"] = [tag for tag in filters["tags"] if tag and isinstance(tag, str)]

        # 验证日期范围过滤
        if "date_range" in filters and isinstance(filters["date_range"], dict):
            validated_filters["date_range"] = filters["date_range"]

        return validated_filters

