#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多粒度分析模型定义
包含函数级、模块级和项目级分析使用的Pydantic模型
"""
from typing import List
from pydantic import BaseModel, Field

# 依赖相关子模型
class ExternalDependency(BaseModel):
    """外部依赖库模型"""
    name: str = Field(default="", description="依赖名称")
    version: str = Field(default="", description="版本")
    purpose: str = Field(default="", description="用途描述")
    is_direct: bool = Field(default=False, description="是否直接依赖")
    is_dev_dependency: bool = Field(default=False, description="是否开发依赖")
    license: str = Field(default="", description="许可证")
    is_vulnerable: bool = Field(default=False, description="是否存在已知安全漏洞")
    dependency_category: str = Field(default="other", description="依赖类型分类")
    source_url: str = Field(default="", description="依赖库的源码地址或官方网站URL")

class TechStack(BaseModel):
    """技术栈信息"""
    primary_language: str = Field(default="", description="主要编程语言")
    frameworks: List[str] = Field(default_factory=list, description="主要框架")
    databases: List[str] = Field(default_factory=list, description="数据库")
    frontend: List[str] = Field(default_factory=list, description="前端技术")
    backend: List[str] = Field(default_factory=list, description="后端技术")
    build_tools: List[str] = Field(default_factory=list, description="构建工具")
    deployment: List[str] = Field(default_factory=list, description="部署方式")

# 依赖分析响应模型
class DependencyAnalysis(BaseModel):
    """项目依赖分析结果"""
    # 基本信息
    project_name: str = Field(default="", description="项目名称")
    project_path: str = Field(default="", description="项目路径")

    # 技术栈概览
    tech_stack: TechStack = Field(default_factory=TechStack, description="项目技术栈概览")

    # 依赖分析
    external_dependencies: List[ExternalDependency] = Field(default_factory=list, description="外部依赖库")

    def to_markdown(self) -> str:
        """
        将DependencyAnalysis的内容转换为markdown格式

        Returns:
            str: markdown格式的内容
        """
        markdown = []

        # 标题和基本信息
        markdown.append(f"# {self.project_name} 项目依赖分析\n")

        markdown.append("## 基本信息\n")
        markdown.append(f"- **项目路径**: {self.project_path}\n")

        # 技术栈概览
        markdown.append("## 技术栈概览\n")

        if self.tech_stack.primary_language:
            markdown.append(f"- **主要编程语言**: {self.tech_stack.primary_language}")

        if self.tech_stack.frameworks:
            markdown.append("- **主要框架**:")
            for framework in self.tech_stack.frameworks:
                markdown.append(f"  - {framework}")

        if self.tech_stack.databases:
            markdown.append("- **数据库**:")
            for db in self.tech_stack.databases:
                markdown.append(f"  - {db}")

        if self.tech_stack.frontend:
            markdown.append("- **前端技术**:")
            for tech in self.tech_stack.frontend:
                markdown.append(f"  - {tech}")

        if self.tech_stack.backend:
            markdown.append("- **后端技术**:")
            for tech in self.tech_stack.backend:
                markdown.append(f"  - {tech}")

        if self.tech_stack.build_tools:
            markdown.append("- **构建工具**:")
            for tool in self.tech_stack.build_tools:
                markdown.append(f"  - {tool}")

        if self.tech_stack.deployment:
            markdown.append("- **部署方式**:")
            for deploy in self.tech_stack.deployment:
                markdown.append(f"  - {deploy}")

        markdown.append("")

        # 外部依赖分析
        if self.external_dependencies:
            markdown.append("## 外部依赖库分析\n")

            # 创建表格
            markdown.append("| 依赖名称 | 版本 | 用途 | 依赖类型 | 分类 | 许可证 | 安全状态 | 源地址 |")
            markdown.append("| ------- | --- | ---- | ------- | ---- | ----- | ------- | ------- |")

            for dep in self.external_dependencies:
                # 依赖类型
                dep_type = []
                if dep.is_direct:
                    dep_type.append("直接")
                else:
                    dep_type.append("间接")

                if dep.is_dev_dependency:
                    dep_type.append("开发")
                else:
                    dep_type.append("运行时")

                dep_type_str = "/".join(dep_type)

                # 直接使用依赖分类字段
                category = dep.dependency_category

                # 安全状态
                security = " 存在漏洞" if dep.is_vulnerable else " 安全"

                # 源码地址处理
                source_url = dep.source_url if dep.source_url else "未知"
                if dep.source_url and dep.source_url.startswith(('http://', 'https://')):
                    source_url = f"[链接]({dep.source_url})"

                markdown.append(f"| {dep.name} | {dep.version} | {dep.purpose} | {dep_type_str} | {category} | {dep.license} | {security} | {source_url} |")

            markdown.append("")

        return "\n".join(markdown)

    def to_simple_markdown(self) -> str:
        """
        将DependencyAnalysis的内容转换为简化的markdown格式
        只显示依赖的核心信息：名称、版本、用途

        Returns:
            str: 简化的markdown格式内容
        """
        markdown = []

        # 标题和基本信息
        markdown.append(f"# {self.project_name} 项目依赖分析\n")

        markdown.append("## 基本信息\n")
        markdown.append(f"- **项目路径**: {self.project_path}\n")

        # 技术栈概览
        markdown.append("## 技术栈概览\n")

        if self.tech_stack.primary_language:
            markdown.append(f"- **主要编程语言**: {self.tech_stack.primary_language}")

        if self.tech_stack.frameworks:
            markdown.append("- **主要框架**:")
            for framework in self.tech_stack.frameworks:
                markdown.append(f"  - {framework}")

        if self.tech_stack.databases:
            markdown.append("- **数据库**:")
            for db in self.tech_stack.databases:
                markdown.append(f"  - {db}")

        if self.tech_stack.frontend:
            markdown.append("- **前端技术**:")
            for tech in self.tech_stack.frontend:
                markdown.append(f"  - {tech}")

        if self.tech_stack.backend:
            markdown.append("- **后端技术**:")
            for tech in self.tech_stack.backend:
                markdown.append(f"  - {tech}")

        if self.tech_stack.build_tools:
            markdown.append("- **构建工具**:")
            for tool in self.tech_stack.build_tools:
                markdown.append(f"  - {tool}")

        if self.tech_stack.deployment:
            markdown.append("- **部署方式**:")
            for deploy in self.tech_stack.deployment:
                markdown.append(f"  - {deploy}")

        markdown.append("")

        # 外部依赖分析（简化版）
        if self.external_dependencies:
            markdown.append("## 外部依赖库分析\n")

            # 创建简化表格
            markdown.append("| 依赖名称 | 版本 | 用途 |")
            markdown.append("| ------- | --- | ---- |")

            for dep in self.external_dependencies:
                # 只显示核心信息：名称、版本、用途
                markdown.append(f"| {dep.name} | {dep.version} | {dep.purpose} |")

            markdown.append("")

        return "\n".join(markdown)
