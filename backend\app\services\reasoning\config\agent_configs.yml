agents:
  default:
    model:
      # name: "google/gemma-3-27b-it:free"
      # url: "https://openrouter.ai/api/v1"
      # api_key: "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"
      # name: "Qwen/QwQ-32B"
      # url: "https://api.siliconflow.cn/v1"
      # api_key: "sk-bsoebhnyjhjcedozdpkahzjdsnxsbuxpbjyisupvlkxhubww"
      # name: "qwq-32b"
      # url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      # api_key: "sk-6e0a3397d8e240d99cc959d7fe5639b1"
      # name: "DeepSeek-R1"
      # url: "https://lke.cloud.tencent.com/webim_exp/#/chat/auyODv"
      # api_key: "BYWdkleu"
      # name: "deepseek-resoner"
      # url: "http://172.24.21.213:3000/v1"
      # api_key: "sk-oOdy2ImyYoUfmBRiFbE1Ec4fDa0042E1B3Ab571a7bDeDb67"
      # ============================================================================================
      #name: "Qwen2.5-Coder-32B-Instruct"
      #name: "deepseek-resoner"
      name: "Qwen3-32B-2"
      url: "http://172.24.21.213:3000/v1"
      api_key: "sk-oOdy2ImyYoUfmBRiFbE1Ec4fDa0042E1B3Ab571a7bDeDb67"
      # ============================================================================================
      # name: "qwen2.5-coder:14b"
      # url: "http://172.24.21.253:11434/v1"
      # api_key: "sk-oOdy2ImyYoUfmBRiFbE1Ec4fDa0042E1B3Ab571a7bDeDb67"
      # name: "gpt-4"
      # url: "https://api.openai-hk.com/v1"
      # api_key: "hk-jq69ko10000529344e5d7cd59615118546b390933627dc6d"
      # temperature: 0.3
      # 先改成1.0

      # ===================================
#      temperature: 1.0
#      name: "deepseek-reasoner"
#      url: "https://api.deepseek.com/v1"
##      api_key: "***********************************"
#      # 付费api::
#      api_key: "sk-a6974e9dc17b476cb9626bb3e156f311"
      # ===================================

      # ===================================

      # ===================================
      streaming: true
      request_timeout: 200
      additional_params: {}
    capabilities: []
    description: "默认模型配置"

updated_at: "2025-03-17T11:38:19+08:00"
