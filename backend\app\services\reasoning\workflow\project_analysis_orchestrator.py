#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目分析协调器 V2
使用专门的处理器处理每个分析步骤
"""
import structlog

from .models import AnalysisConfig, AnalysisState
from .cache_manager import CacheManager



from ...ai_agent_core import agent_manager
from .analysis_handlers import (
    ScanProjectHandler,
    StructureAnalysisHandler,
    DependencyAnalysisHandler,
    ArchitectureAnalysisHandler,
    ModuleAnalysisHandler,
    ModuleMergerHandler,
    ModuleMergerAnalysisHandler,
    ReadmeGeneratorHandler,
    AnalysisValidatorHandler,
    AnalysisProjectChangeHandler
)

logger = structlog.get_logger(__name__)

class ProjectAnalysisOrchestrator:
    """
    项目分析协调器
    使用专门的处理器处理每个分析步骤
    """

    def __init__(self, config: AnalysisConfig, cache_dir: str = '.cache'):
        """
        初始化项目分析协调器

        Args:
            config: 项目分析配置
            cache_dir: 缓存目录
        """
        self.config = config
        self.cache_dir = cache_dir

        # 初始化各个处理器
        self.scan_project_handler = ScanProjectHandler(config, cache_dir)
        self.project_change_analyzer_handler = AnalysisProjectChangeHandler(config, cache_dir)
        self.structure_analysis_handler = StructureAnalysisHandler(config, cache_dir)
        self.dependency_analysis_handler = DependencyAnalysisHandler(config, cache_dir)
        self.architecture_analysis_handler = ArchitectureAnalysisHandler(config, cache_dir)
        self.module_analysis_handler = ModuleAnalysisHandler(config, cache_dir)
        self.module_merger_handler = ModuleMergerHandler(config, cache_dir)
        self.module_merger_analysis_handler = ModuleMergerAnalysisHandler(config, cache_dir)
        self.readme_generator_handler = ReadmeGeneratorHandler(config, cache_dir)
        self.analysis_validator_handler = AnalysisValidatorHandler(config, cache_dir)

    async def initialize(self):
        """初始化所有分析处理器"""
        try:
            logger.info("开始初始化分析处理器...")

            self.scan_project_handler.initialize()
            logger.info("项目扫描处理器初始化完成")

            self.project_change_analyzer_handler.initialize()
            logger.info("项目变化分析处理器初始化完成")

            self.structure_analysis_handler.initialize()
            logger.info("结构分析处理器初始化完成")

            self.dependency_analysis_handler.initialize()
            logger.info("依赖分析处理器初始化完成")

            self.architecture_analysis_handler.initialize()
            logger.info("架构分析处理器初始化完成")

            self.module_analysis_handler.initialize()
            logger.info("模块分析处理器初始化完成")

            self.module_merger_handler.initialize()
            logger.info("模块合并处理器初始化完成")

            self.module_merger_analysis_handler.initialize()
            logger.info("模块合并分析处理器初始化完成")

            self.readme_generator_handler.initialize()
            logger.info("README生成处理器初始化完成")

            self.analysis_validator_handler.initialize()
            logger.info("分析验证处理器初始化完成")

            await agent_manager.initialize()
            logger.info("AI代理管理器初始化完成")

            logger.info("所有分析处理器初始化完成")

        except Exception as e:
            logger.error(f"分析处理器初始化失败: {str(e)}")
            raise RuntimeError(f"项目分析协调器初始化失败: {str(e)}") from e

    async def scan_project(self, state: AnalysisState) -> AnalysisState:
        """
        扫描项目内容

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        state.stage = 'scan_project'
        state = await self.scan_project_handler.process(state)
        return state.__dict__

    async def analyze_project_changes(self, state: AnalysisState) -> AnalysisState:
        """
        分析项目变化情况

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        state.stage = 'analyze_project_changes'
        state = await self.project_change_analyzer_handler.process(state)
        return state.__dict__

    async def analyze_structure(self, state: AnalysisState) -> AnalysisState:
        """
        分析项目结构

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        state.stage = 'analyze_structure'
        state = await self.structure_analysis_handler.process(state)
        return state.__dict__

    async def analyze_dependencies(self, state: AnalysisState) -> AnalysisState:
        """
        分析项目依赖

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        state.stage = 'analyze_dependencies'
        state = await self.dependency_analysis_handler.process(state)
        return state.__dict__

    async def analyze_architecture(self, state: AnalysisState) -> AnalysisState:
        """
        分析项目架构

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        state.stage = 'analyze_architecture'
        state = await self.architecture_analysis_handler.process(state)
        return state.__dict__

    async def analyze_modules(self, state: AnalysisState) -> AnalysisState:
        """
        分析所有模块

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        state.stage = 'analyze_modules'
        state = await self.module_analysis_handler.process(state)
        return state.__dict__

    async def merge_modules(self, state: AnalysisState) -> AnalysisState:
        """
        合并模块并进行目录分组

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        state.stage = 'merge_modules'
        state = await self.module_merger_handler.process(state)
        return state.__dict__

    async def analyze_module_mergers(self, state: AnalysisState) -> AnalysisState:
        """
        对模块分组进行AI智能分析和编程关系分析

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        state.stage = 'analyze_module_mergers'
        state = await self.module_merger_analysis_handler.process(state)
        return state.__dict__

    async def generate_readme(self, state: AnalysisState) -> AnalysisState:
        """
        生成README文档

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        state.stage = 'generate_readme'
        state = await self.readme_generator_handler.process(state)
        return state.__dict__

    async def validate_analysis(self, state: AnalysisState) -> AnalysisState:
        """
        验证分析结果的质量和完整性

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        state.stage = 'validate_analysis'
        state = await self.analysis_validator_handler.process(state)
        return state.__dict__

    def should_analyze_architecture(self, state: AnalysisState) -> bool:
        """
        检查是否应该进行架构分析

        Args:
            state: 当前分析状态

        Returns:
            是否应该进行架构分析
        """
        # 必须先完成依赖分析
        # 如果配置中指定了不分析架构，则返回False
        if not self.config.analyze_architecture:
            return False
        return state.dependency_analysis is not None

    def should_generate_readme(self, state: AnalysisState) -> bool:
        """
        检查是否应该生成README文档

        Args:
            state: 当前分析状态

        Returns:
            是否应该生成README文档
        """
        # 必须先完成模块分析和合并分析
        # 检查是否有模块分析结果或合并分析结果
        has_modules = len(state.module_analyses) > 0
        has_merger_analyses = len(state.module_merger_analyses) > 0
        return has_modules or has_merger_analyses
