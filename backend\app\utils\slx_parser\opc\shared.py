"""opc模块共享的对象。"""

from __future__ import annotations

import functools
from typing import (
    TYPE_CHECKING,
    Any,
    Dict,
    Callable,
    Generic,
    Iterator,
    List,
    Tuple,
    TypeVar,
    cast,
)
_T = TypeVar("_T")


class CaseInsensitiveDict(Dict[str, Any]):
    """行为类似于dict的映射类型，但在匹配键时不区分大小写。

    例如 cid['A'] == cid['a']。注意这不是通用目的的实现，仅足够满足opc包的需求。
    它假设键为str类型，并且创建时为空；构造函数中传入的键不会被考虑。
    """

    def __contains__(self, key):
        return super(CaseInsensitiveDict, self).__contains__(key.lower())

    def __getitem__(self, key):
        return super(CaseInsensitiveDict, self).__getitem__(key.lower())

    def __setitem__(self, key, value):
        return super(CaseInsensitiveDict, self).__setitem__(key.lower(), value)


def cls_method_fn(cls: type, method_name: str):
    """返回`cls`中名为`method_name`的方法。"""
    return getattr(cls, method_name)



T = TypeVar("T")


class lazyproperty(Generic[T]):
    """类似@property的装饰器，但仅在首次访问时求值。

    与@property类似，这只能用于装饰仅有`self`参数的方法，并且像实例的属性一样访问，
    即不使用尾随括号。与@property不同的是，装饰的方法仅在首次访问时求值；
    结果值被缓存，第二次及以后的访问都返回相同的值，而不重新求值方法。

    与@property类似，这个类产生一个*数据描述符*对象，它存储在*类*的__dict__中，
    使用装饰方法的名称（通常是'fget'）。缓存的值存储在*实例*的__dict__中，使用相同的名称。

    因为它是一个数据描述符（而不是*非数据描述符*），它的`__get__()`方法在每次访问装饰属性时执行；
    同名的__dict__项被描述符"遮蔽"。

    虽然这可能比property在性能上有所改进，但它更大的好处可能是它的其他特性。
    一个常见的用途是构造协作对象，将这些"实际工作"从构造函数中移除，同时仍然只执行一次。
    它还解耦了客户端代码与任何顺序考虑；如果它从多个位置访问，可以确保在需要时随时准备就绪。

    大致基于：https://stackoverflow.com/a/6849299/1902513。

    lazyproperty是只读的。没有与@property的可选"setter"（或deleter）行为相对应的部分。
    这对于维护其不可变性和幂等性保证至关重要。尝试给lazyproperty赋值会无条件引发AttributeError。

    下面方法中的参数名称对应于此使用示例::

        class Obj(object)

            @lazyproperty
            def fget(self):
                return 'some result'

        obj = Obj()

    不适合用于包装函数（而不是方法），因为它不可调用。"""

    def __init__(self, fget: Callable[..., T]) -> None:
        """*fget*是装饰的方法（一个"getter"函数）。

        lazyproperty是只读的，所以只有一个*fget*函数（普通的@property还可以有fset和fdel函数）。
        选择这个名称是为了与Python的`property`类保持一致，后者对相应的参数使用这个名称。
        """
        # --- 保持对包装的getter方法的引用
        self._fget = fget
        # --- 并存储该装饰方法的名称
        self._name = fget.__name__
        # --- 采用fget的__name__、__doc__和其他属性
        functools.update_wrapper(self, fget)  # pyright: ignore

    def __get__(self, obj: Any, type: Any = None) -> T:
        """在每次访问类或实例上的'fget'属性时调用。

        *self*是这个lazyproperty描述符实例，它"包装"它装饰的属性方法（通常是`fget`）。

        当从对象实例访问属性时，*obj*是"宿主"对象实例，
        例如`obj = Obj(); obj.fget`。当从类访问时，*obj*为None，例如`Obj.fget`。

        在类和实例属性访问时，*type*都是托管装饰的getter方法（`fget`）的类。
        """
        # --- 当在类上访问时，例如Obj.fget，只返回这个描述符实例（上面已修补以看起来像fget）。
        if obj is None:
            return self  # type: ignore

        # --- 当在实例上访问时，首先检查实例__dict__中是否有与包装函数名称匹配的键的项
        value = obj.__dict__.get(self._name)
        if value is None:
            # --- 在首次访问时，__dict__项将不存在。求值fget()并将该值存储在
            # --- 宿主对象__dict__中同名的值中（通常是'fget'）
            value = self._fget(obj)
            obj.__dict__[self._name] = value
        return cast(T, value)

    def __set__(self, obj: Any, value: Any) -> None:
        """无条件引发异常，以保持只读行为。

        此装饰器旨在实现不可变（且幂等）的对象属性。因此，必须明确阻止对此属性的赋值。

        如果没有这个__set__方法，这个描述符将成为一个*非数据描述符*。这很好，因为一旦设置，
        缓存的值将被直接访问（在实例属性查找时，__dict__属性优先于非数据描述符）。
        问题是，没有什么可以阻止对缓存值的赋值，这将覆盖`fget()`的结果，
        并破坏这个装饰器的不可变性和幂等性保证。

        在2.8GHz的开发机器上测量时，带有此__set__()方法的性能大约为每次访问0.4微秒；
        因此相当快速，可能不是优化工作的主要目标。
        """
        raise AttributeError("不能设置属性")
