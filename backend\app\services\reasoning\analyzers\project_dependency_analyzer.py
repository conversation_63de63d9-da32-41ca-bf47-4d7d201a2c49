#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目级代码分析器
"""
import logging
import os
import asyncio
import aiohttp
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone
from pathlib import Path
from urllib.parse import urlparse

from langchain.chains import <PERSON><PERSON>hain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser
from .common_models import AttentionPointInfo
from ....utils.retry import retry_async
from ....utils.file_utils import read_file_content

from .project_dependency_models import (
    DependencyAnalysis
)
from .project_structure_models import (
    ProjectStructureAnalysis
)
from ...ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser

logger = logging.getLogger(__name__)


@AgentManager.register("ProjectDependencyAnalyzerAgent")
class ProjectDependencyAnalyzer(BaseAgent):
    """
    项目级依赖分析器
    分析项目级别的代码，识别项目结构、模块关系、入口点、核心模块等
    """

    def __init__(self):
        """初始化项目级依赖分析器"""
        super().__init__()
        self.name = "ProjectDependencyAnalyzer"
        self.chain = None

    async def _initialize(self) -> None:
        """初始化分析器特定资源"""
        logger.info("初始化项目级依赖分析器特定资源")
        # 在这里可以添加特定于项目分析器的初始化逻辑

    async def _validate_source_url(self, url: str) -> Optional[str]:
        """
        验证给定的URL是否可访问

        Args:
            url: 需要验证的URL

        Returns:
            如果URL有效则返回原URL，如果无效则返回None
        """
        if not url or not url.strip():
            return None

        url = url.strip()

        # 简单的URL格式预检查
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                logger.debug(f"URL格式无效: {url}")
                return None

            # 只验证HTTP/HTTPS协议
            if parsed.scheme not in ['http', 'https']:
                logger.debug(f"不支持的协议: {parsed.scheme} in {url}")
                return None

        except Exception as e:
            logger.debug(f"URL解析失败: {url}, 错误: {str(e)}")
            return None

        # 网络验证
        try:
            timeout = aiohttp.ClientTimeout(total=5)  # 5秒超时
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.head(url, allow_redirects=True) as response:
                    # 2xx状态码认为是有效的
                    if 200 <= response.status < 300:
                        logger.debug(f"URL验证成功: {url} (状态码: {response.status})")
                        return url
                    else:
                        logger.debug(f"URL验证失败: {url} (状态码: {response.status})")
                        return None

        except asyncio.TimeoutError:
            logger.debug(f"URL验证超时: {url}")
            return None
        except aiohttp.ClientError as e:
            logger.debug(f"URL验证网络错误: {url}, 错误: {str(e)}")
            return None
        except Exception as e:
            logger.debug(f"URL验证异常: {url}, 错误: {str(e)}")
            return None

    async def _validate_dependencies_urls(self, dependencies: List[Any]) -> List[Any]:
        """
        批量验证依赖的source_url

        Args:
            dependencies: 依赖列表

        Returns:
            验证后的依赖列表
        """
        if not dependencies:
            return dependencies

        logger.info(f"开始验证 {len(dependencies)} 个依赖的源地址")

        # 创建验证任务
        validation_tasks = []
        for dep in dependencies:
            if dep.source_url:
                task = self._validate_source_url(dep.source_url)
                validation_tasks.append((dep, task))
            else:
                validation_tasks.append((dep, None))

        # 并发执行验证
        validated_count = 0
        invalid_count = 0

        for dep, task in validation_tasks:
            if task is not None:
                try:
                    validated_url = await task
                    if validated_url:
                        validated_count += 1
                        logger.debug(f"依赖 {dep.name} 的URL验证成功: {dep.source_url}")
                    else:
                        invalid_count += 1
                        logger.info(f"依赖 {dep.name} 的URL验证失败，已清空: {dep.source_url}")
                        dep.source_url = ""  # 清空无效URL
                except Exception as e:
                    invalid_count += 1
                    logger.warning(f"依赖 {dep.name} 的URL验证异常: {dep.source_url}, 错误: {str(e)}")
                    dep.source_url = ""  # 清空异常URL

        logger.info(f"URL验证完成: 有效 {validated_count} 个, 无效 {invalid_count} 个")
        return dependencies

    async def _create_chain(self) -> None:
        """初始化处理链"""
        try:

            # 依赖分析提示模板
            dependency_analysis_template = """
            # 项目依赖结构分析

            作为依赖分析专家，请对项目进行全面的依赖结构分析，包括技术栈评估、外部依赖分析。

            ## 项目信息
            项目名称: {{ project_name }}
            项目路径: {{ project_path }}

            {%- if dependency_files %}

            ## 依赖管理文件
            以下是项目的依赖管理文件，定义了项目的外部依赖库和版本约束：
            {%- for file_path, content in dependency_files.items() %}
            ### {{ file_path }}
            {%- if structure_analysis and structure_analysis.dependencies %}
            {%- for dep in structure_analysis.dependencies %}
            {%- if dep.path == file_path and dep.description %}
            **文件描述**: {{ dep.description }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}
            ```
            {{ content }}
            ```
            {%- endfor %}
            {%- endif %}

            {%- if config_files %}

            ## 配置文件 (该部分内容只用作上下文参考，不需要进行分析)
            以下是项目的配置文件，包含了项目的设置和环境参数：
            {%- for file_path, content in config_files.items() %}
            ### {{ file_path }}
            {%- if structure_analysis and structure_analysis.configs %}
            {%- for config in structure_analysis.configs %}
            {%- if config.path == file_path and config.description %}
            **文件描述**: {{ config.description }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}
            ```
            {{ content }}
            ```
            {%- endfor %}
            {%- endif %}

            {%- if build_files %}

            ## 构建和部署文件 (该部分内容只用作上下文参考，不需要进行分析)
            以下是项目的构建和部署相关文件，定义了项目的构建流程和部署配置：
            {%- for file_path, content in build_files.items() %}
            ### {{ file_path }}
            {%- if structure_analysis and structure_analysis.builds %}
            {%- for build in structure_analysis.builds %}
            {%- if build.path == file_path and build.description %}
            **文件描述**: {{ build.description }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}
            ```
            {{ content }}
            ```
            {%- endfor %}
            {%- endif %}

            {%- if not config_files and not dependency_files and not build_files %}

            未找到相关配置或依赖文件。请基于项目路径和名称进行分析。
            {%- endif %}

            ## 分析任务

            请进行全面的项目依赖分析，包括：
            1. **技术栈识别** - 分析主要语言、框架、数据库、前后端技术、构建工具和部署方式
            2. **外部依赖梳理** - 提取所有依赖库的详细信息，包括版本、用途、类型和源码地址

            ## 依赖提取指导

            ### Python项目依赖提取
            - **requirements.txt**: 提取所有非注释行（不以#开头）的包名，忽略版本约束符号
            - **pyproject.toml**: 
              * [tool.poetry.dependencies] 部分的所有键名
              * [build-system.requires] 部分的所有包名
              * dependencies 数组中的所有包名
            - **setup.py**: install_requires列表中的所有包名
            - **Pipfile**: [packages]和[dev-packages]部分的所有键名
            - **environment.yml**: dependencies列表中的所有包名

            ### JavaScript/Node.js项目依赖提取
            - **package.json**: 
              * dependencies对象的所有键名
              * devDependencies对象的所有键名
              * peerDependencies对象的所有键名
              * optionalDependencies对象的所有键名
            - **yarn.lock/package-lock.json**: 所有包声明

            ### Java项目依赖提取
            - **pom.xml**: 所有<dependency>标签中的<artifactId>
            - **build.gradle**: dependencies块中的所有实现声明

            ### 其他语言项目依赖提取
            - **Go**: go.mod中所有require声明的模块
            - **Rust**: Cargo.toml中[dependencies]和[dev-dependencies]的所有键名
            - **Ruby**: Gemfile中所有gem声明
            - **PHP**: composer.json中require和require-dev的所有键名
            - **.NET**: *.csproj中所有PackageReference的Include属性值

            ## 输出数据模型

            ### TechStack (技术栈)
            - **primary_language**: 主要编程语言 (string)
            - **frameworks**: 主要框架列表 (List[string])
            - **databases**: 数据库技术列表 (List[string]) 
            - **frontend**: 前端技术列表 (List[string])
            - **backend**: 后端技术列表 (List[string])
            - **build_tools**: 构建工具列表 (List[string])
            - **deployment**: 部署方式列表 (List[string])

            ### ExternalDependency (外部依赖)
            - **name**: 依赖名称 (string, 必须在文件中明确存在)
            - **version**: 版本号 (string, 仅当文件中明确指定时填写，否则为空"")
            - **purpose**: 用途描述 (string, 基于依赖名称推断常见用途)
            - **is_direct**: 是否直接依赖 (boolean, 基于文件位置判断)
            - **is_dev_dependency**: 是否开发依赖 (boolean, 基于文件中的开发依赖标识)
            - **license**: 许可证 (string, 仅填写文件中明确声明的，未知时为"")
            - **is_vulnerable**: 是否存在漏洞 (boolean, 默认false，除非文件中有明确安全警告)
            - **dependency_category**: 依赖分类 (string, 建议使用：frontend/backend/tool/test/data/ai/other)
            - **source_url**: 源码地址 (string, 基于依赖名称推断GitHub地址或包管理器页面)

            ## 源码地址推断规则

            请尽量为每个依赖填充source_url，按以下优先级：
            1. **GitHub地址优先**：https://github.com/[组织]/[项目名]
            2. **包管理器页面**：当无法确定GitHub地址时
               - Python: https://pypi.org/project/[包名]
               - JavaScript: https://www.npmjs.com/package/[包名]
               - Java: Maven Central链接
               - .NET: https://www.nuget.org/packages/[包名]

            **常见库示例**：
            - requests → https://github.com/psf/requests  
            - numpy → https://github.com/numpy/numpy
            - react → https://github.com/facebook/react
            - express → https://github.com/expressjs/express
            - django → https://github.com/django/django
            - flask → https://github.com/pallets/flask
            - spring-boot → https://github.com/spring-projects/spring-boot
            - lodash → https://github.com/lodash/lodash

            ## 分析要求与原则

            ### 核心原则
            1. **基于文件内容**: 只从提供的依赖管理文件、配置文件和构建文件中提取信息
            2. **禁止臆想**: 不得添加未在文件中明确定义的依赖库、版本号、许可证信息
            3. **完整提取**: 必须扫描所有提供的依赖管理文件，提取其中的每一个依赖项，数量必须与文件中的依赖项数量匹配
            4. **准确分类**: 根据文件中的依赖声明位置准确判断is_direct和is_dev_dependency

            ### 完整性检查要点
            - 依赖文件中有多少个依赖，输出结果就应该有多少个ExternalDependency
            - 如果某个requirements.txt有30行依赖，必须输出30个依赖项
            - 如果package.json的dependencies和devDependencies总共有45个包，必须输出45个依赖项
            - 绝不允许因为依赖太多而只提取部分依赖

            ### 输出要求
            - **语言**: 所有分析结果使用中文(简体中文)输出，技术名称和代码保持原文
            - **完整性**: 严格按照数据模型结构输出，不可遗漏字段
            - **准确性**: 依赖名称必须在文件中存在，版本号仅在明确指定时填写
            - **一致性**: 空值使用""或[]，布尔值使用true/false

            {{ format_instructions }}
            """

            # 创建Parser
            dependency_parser = AnalysisOutputParser(pydantic_object=DependencyAnalysis)
            parser = OutputFixingParser.from_llm(parser=dependency_parser, llm=self.llm)

            # 获取格式指令
            dependency_format_instructions = dependency_parser.get_format_instructions()

            # 创建提示模板
            dependency_prompt = PromptTemplate.from_template(
                dependency_analysis_template,
                template_format="jinja2"
            ).partial(format_instructions=dependency_format_instructions)

            # 创建处理链
            self.chain = (
                dependency_prompt
                | self.llm
                | parser
            )

            logger.info("项目级依赖分析处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: Dict[str, Any]) -> AgentInput:
        """
        准备输入数据

        Args:
            input_data: 原始输入数据

        Returns:
            AgentInput: 准备后的输入数据
        """
        input_data: AgentInput = await super().prepare_input(input_data)
        project_path = Path(input_data.parameters["project_path"])
        structure_analysis: ProjectStructureAnalysis = input_data.parameters["structure_analysis"]

        # 准备不同类型的文件内容
        config_files = {}
        dependency_files = {}
        build_files = {}

        # 处理配置文件
        if structure_analysis and structure_analysis.configs:
            for config_component in structure_analysis.configs:
                full_path = project_path / config_component.path
                content = read_file_content(str(full_path.resolve()), 100)  # 限制为100字符
                if content:
                    config_files[config_component.path] = content

        # 处理依赖管理文件
        if structure_analysis and structure_analysis.dependencies:
            for dependency_component in structure_analysis.dependencies:
                full_path = project_path / dependency_component.path
                content = read_file_content(str(full_path.resolve()), 30000)
                if content:
                    dependency_files[dependency_component.path] = content

        # 处理构建和部署文件
        if structure_analysis and structure_analysis.builds:
            for build_component in structure_analysis.builds:
                full_path = project_path / build_component.path
                content = read_file_content(str(full_path.resolve()), 100)  # 限制为100字符
                if content:
                    build_files[build_component.path] = content

        # 将分类后的文件内容添加到输入参数中
        input_data.parameters["config_files"] = config_files
        input_data.parameters["dependency_files"] = dependency_files
        input_data.parameters["build_files"] = build_files

        return input_data

    @retry_async(max_retries=3)
    async def _process(
        self,
        input_data: AgentInput
    ) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询

        Args:
            input_data: 输入数据

        Returns:
            Tuple[Any, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 调用LLM处理链
            # result = await self.chain.first.ainvoke(input_data.parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(input_data.parameters)

            # # 验证依赖的source_url
            # if result.external_dependencies:
            #     logger.info("开始验证外部依赖的源地址")
            #     result.external_dependencies = await self._validate_dependencies_urls(result.external_dependencies)
            #     logger.info("外部依赖源地址验证完成")

            return result, {"analyzer": "project dependency analyzer"}
        except Exception as e:
            logger.error(f"项目级依赖分析失败: {str(e)}")
            raise

    async def _shutdown(self) -> None:
        """关闭分析器资源"""
        logger.info("关闭项目级代码分析器资源")
        # 释放可能的资源
        self.chain = None
        self.llm = None
