#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表导出工具
"""
import os
import json
from datetime import datetime, timezone
import structlog
from .models import DiagramState, DiagramFormat

logger = structlog.get_logger(__name__)


class DiagramExporter:
    """图表导出工具"""

    @staticmethod
    async def export_diagrams(state: DiagramState) -> DiagramState:
        """
        导出所有生成的图表

        Args:
            state: 当前图表生成状态

        Returns:
            更新后的图表生成状态
        """
        try:
            logger.info(f"开始导出架构图")
            state.current_step = "export_diagrams"
            if not state.config.output_dir:
                logger.warning("没有设置输出目录，无法导出图表")
                return state

            if not state.diagrams:
                logger.warning("没有可导出的图表")
                return state

            # 实现导出逻辑
            os.makedirs(state.config.output_dir, exist_ok=True)

            for diagram_type, formats in state.diagrams.items():
                for format_type, content in formats.items():
                    timestamp = state.start_time.strftime('%Y%m%d_%H%M%S')
                    filename = f"{state.project_name}_{diagram_type}_{timestamp}"

                    # 导出 Mermaid 格式
                    if format_type == DiagramFormat.MERMAID:
                        mmd_file_path = os.path.join(state.config.output_dir, f"{filename}.mmd")
                        with open(mmd_file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        logger.info(f"已导出Mermaid格式图表: {mmd_file_path}")

                        # 生成交互式 HTML 图表
                        if state.config.interactive:
                            try:
                                html_content = f"""
                                <!DOCTYPE html>
                                <html>
                                <head>
                                    <meta charset="UTF-8">
                                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                                    <title>{filename}</title>
                                    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
                                    <script>
                                        mermaid.initialize({{
                                            startOnLoad: true,
                                            theme: '{state.config.theme}',
                                            securityLevel: 'loose',
                                            flowchart: {{ htmlLabels: true }}
                                        }});
                                    </script>
                                    <style>
                                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                                        .mermaid {{ text-align: center; }}
                                    </style>
                                </head>
                                <body>
                                    <h1>{state.project_name} - {diagram_type} 图</h1>
                                    <div class="mermaid">
                                    {content}
                                    </div>
                                </body>
                                </html>
                                """
                                html_file_path = os.path.join(state.config.output_dir, f"{filename}.html")
                                with open(html_file_path, 'w', encoding='utf-8') as f:
                                    f.write(html_content)
                                logger.info(f"已生成交互式 HTML 图表: {html_file_path}")
                            except Exception as html_error:
                                logger.error(f"HTML 导出失败: {str(html_error)}")

                    # 其他格式的导出在这里实现
                    elif format_type == DiagramFormat.JSON:
                        json_file_path = os.path.join(state.config.output_dir, f"{filename}.json")
                        with open(json_file_path, 'w', encoding='utf-8') as f:
                            # 确保内容是字符串，如果是字典则转换为JSON字符串
                            if isinstance(content, dict):
                                f.write(json.dumps(content, indent=2, ensure_ascii=False))
                            else:
                                f.write(content)
                        logger.info(f"已导出JSON格式图表: {json_file_path}")

            # 生成索引文件，列出所有生成的图表
            index_content = f"# {state.project_name} 架构图生成报告\n\n"
            index_content += f"**生成时间**: {state.start_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            index_content += "## 生成的图表\n\n"

            for diagram_type, formats in state.diagrams.items():
                index_content += f"### {diagram_type} 图\n\n"
                for format_type in formats.keys():
                    timestamp = state.start_time.strftime('%Y%m%d_%H%M%S')
                    filename = f"{state.project_name}_{diagram_type}_{timestamp}"

                    if format_type == DiagramFormat.MERMAID:
                        index_content += f"- [Mermaid 格式]({filename}.mmd)\n"
                        if state.config.interactive:
                            index_content += f"- [HTML 交互式]({filename}.html)\n"
                    elif format_type == DiagramFormat.JSON:
                        index_content += f"- [JSON 格式]({filename}.json)\n"

                index_content += "\n"

            # 如果有错误，添加错误信息
            if state.errors:
                index_content += "## 错误信息\n\n"
                for error_key, error_msg in state.errors.items():
                    index_content += f"- **{error_key}**: {error_msg}\n"

            # 写入索引文件
            index_file_path = os.path.join(state.config.output_dir, "index.md")
            with open(index_file_path, 'w', encoding='utf-8') as f:
                f.write(index_content)
            logger.info(f"已生成索引文件: {index_file_path}")

            # 设置结束时间
            state.end_time = datetime.now(timezone.utc)

            logger.info(f"架构图导出完成")
            return state
        except Exception as e:
            logger.error(f"导出架构图失败: {str(e)}")
            state.errors["export_diagrams"] = str(e)
            state.end_time = datetime.now(timezone.utc)
            return state
