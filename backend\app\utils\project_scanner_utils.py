#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/6/17 16:27
# @File    : project_scanner_utils.py
# @Description: 
"""
# !/usr/bin/env python3
# -*- coding: utf-8 -*-
import aiohttp

from app.core.config import settings

"""
项目扫描工具类
支持GitHub和Gitee项目搜索
"""
import asyncio
from typing import Dict, List, Any, Optional
import structlog

logger = structlog.get_logger(__name__)


class ProjectScannerUtils:
    """项目扫描工具类"""

    # GitHub API基础URL
    GITHUB_API_BASE = "https://api.github.com"

    @staticmethod
    async def search_github_projects(**params) -> Dict[str, Any]:
        """GitHub项目搜索"""
        try:
            # 构建GitHub搜索查询
            query_parts = []

            if params.get("language"):
                query_parts.append(f"language:{params['language']}")

            # 星数范围筛选
            if params.get("stars_min") and params.get("stars_max"):
                query_parts.append(f"stars:{params['stars_min']}..{params['stars_max']}")
            elif params.get("stars_min"):
                query_parts.append(f"stars:>={params['stars_min']}")
            elif params.get("stars_max"):
                query_parts.append(f"stars:<={params['stars_max']}")

            # 项目规模范围筛选
            if params.get("size_min") and params.get("size_max"):
                query_parts.append(f"size:{params['size_min']}..{params['size_max']}")
            elif params.get("size_min"):
                query_parts.append(f"size:>={params['size_min']}")
            elif params.get("size_max"):
                query_parts.append(f"size:<={params['size_max']}")

            if params.get("keyword"):
                query_parts.append(params["keyword"])


            if not query_parts:
                query_parts.append("is:public")

            search_query = " ".join(query_parts)

            # 调用GitHub API
            result = await ProjectScannerUtils._call_github_api(
                q=search_query,
                sort=params.get("sort", "stars"),
                order=params.get("order", "desc"),
                page=params.get("page", 1),
                per_page=params.get("page_size", 30),
            )

            return {
                "source": "github",
                "total_count": result.get("total_count", 0),
                "items": result.get("items", []),
                "search_params": params
            }

        except Exception as e:
            logger.error(f"GitHub项目搜索失败: {str(e)}")
            raise

    @staticmethod
    async def search_gitee_projects(**params) -> Dict[str, Any]:
        """Gitee项目搜索"""
        try:
            # 构建Gitee搜索查询
            query_parts = []

            if params.get("language"):
                query_parts.append(f"lang:{params['language']}")

            # 星数范围筛选
            if params.get("stars_min") and params.get("stars_max"):
                query_parts.append(f"stars:{params['stars_min']}..{params['stars_max']}")
            elif params.get("stars_min"):
                query_parts.append(f"stars:{params['stars_min']}")
            elif params.get("stars_max"):
                query_parts.append(f"stars:{params['stars_max']}")

            # 项目规模范围筛选
            if params.get("size_min") and params.get("size_max"):
                query_parts.append(f"size:{params['size_min']}..{params['size_max']}")
            elif params.get("size_min"):
                query_parts.append(f"size:{params['size_min']}")
            elif params.get("size_max"):
                query_parts.append(f"size:{params['size_max']}")

            if params.get("keyword"):
                query_parts.append(params["keyword"])

            search_query = " ".join(query_parts) if query_parts else ""

            # 调用Gitee API
            result = await ProjectScannerUtils._call_gitee_api(
                q=search_query,
                sort=params.get("sort", "stars_count"),
                order=params.get("order", "desc"),
                page=params.get("page", 1),
                per_page=30
            )

            return {
                "source": "gitee",
                "total_count": result.get("total_count", 0),
                "items": result.get("items", []),
                "search_params": params
            }

        except Exception as e:
            logger.error(f"Gitee项目搜索失败: {str(e)}")
            raise

    @staticmethod
    async def _call_github_api(**params) -> Dict[str, Any]:
        """调用GitHub API"""
        try:
            # 构建API请求URL
            url = f"{ProjectScannerUtils.GITHUB_API_BASE}/search/repositories"

            # 准备请求参数
            request_params = {
                "q": params.get("q", ""),
                "sort": params.get("sort", "stars"),
                "order": params.get("order", "desc"),
                "page": params.get("page", 1),
                "per_page": min(params.get("per_page", 30), 100)  # GitHub限制每页最多100条
            }

            # 准备请求头
            headers = {
                "Accept": "application/vnd.github.v3+json",
                "User-Agent": "ProjectScanner/1.0"
            }

            # 如果有GitHub Token，添加到请求头中
            github_token = settings.github.GITHUB_TOKEN
            if github_token:
                headers["Authorization"] = f"token {github_token}"
                logger.info("使用GitHub Token进行API调用")
            else:
                logger.warning("未设置GITHUB_TOKEN，使用匿名访问（有速率限制）")

            # 发起异步HTTP请求
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=request_params, headers=headers) as response:
                    # 检查响应状态
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"GitHub API调用成功，返回 {len(data.get('items', []))} 个项目")
                        return data
                    elif response.status == 403:
                        # 速率限制
                        rate_limit_info = response.headers.get("X-RateLimit-Remaining", "unknown")
                        logger.warning(f"GitHub API速率限制，剩余请求次数: {rate_limit_info}")
                        raise Exception(f"GitHub API速率限制，请稍后重试或使用Token")
                    elif response.status == 401:
                        logger.error("GitHub API认证失败，请检查Token")
                        raise Exception("GitHub API认证失败")
                    else:
                        error_text = await response.text()
                        logger.error(f"GitHub API调用失败，状态码: {response.status}, 错误: {error_text}")
                        raise Exception(f"GitHub API调用失败: {response.status}")

        except aiohttp.ClientError as e:
            logger.error(f"网络请求错误: {str(e)}")
            raise Exception(f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"GitHub API调用异常: {str(e)}")
            raise

    @staticmethod
    async def _call_gitee_api(**params) -> Dict[str, Any]:
        """调用Gitee API"""
        # 这里实现Gitee API调用
        # 暂时返回模拟数据
        return {
            "total_count": 500,
            "items": [
                {
                    "id": 1,
                    "name": "example-repo",
                    "full_name": "user/example-repo",
                    "description": "Gitee示例项目",
                    "language": "Java",
                    "stars_count": 50,
                    "forks_count": 10,
                    "size": 3000,
                    "html_url": "https://gitee.com/user/example-repo",
                    "clone_url": "https://gitee.com/user/example-repo.git",
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z"
                }
            ]
        }