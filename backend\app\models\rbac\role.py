"""
角色相关模型
包含角色(RoleModel)及其继承关系
"""
from datetime import datetime, timezone
from sqlalchemy import ForeignKey, UniqueConstraint, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.model_base import ModelBase
from app.models.rbac.associations import role_permission, role_permission_group, user_role
from app.models.rbac.permission import PermissionModel
from app.models.rbac.permission_group import PermissionGroupModel
from app.models.rbac.user import UserModel

class RoleModel(ModelBase):
    """
    角色模型
    支持角色继承，一个角色可以继承多个权限组和具体权限
    可以设置父角色，继承父角色的所有权限
    """
    __tablename__ = 'roles'
    __table_args__ = (
        UniqueConstraint('parent_id', 'code', name='uq_role_parent_code'),
    )
    
    name: Mapped[str] = mapped_column(String(100), unique=True, index=True, comment='角色名称')
    code: Mapped[str] = mapped_column(String(100), unique=True, index=True, comment='角色标识符(小写英文+下划线)')
    description: Mapped[str | None] = mapped_column(String(200), default=None, comment='描述信息')
    parent_id: Mapped[str | None] = mapped_column(ForeignKey('roles.id', ondelete='SET NULL'), default=None, comment='父角色ID')
    is_active: Mapped[bool] = mapped_column(default=True, comment='是否启用')
    is_system: Mapped[bool] = mapped_column(default=False, comment='是否系统角色(系统角色不可删除)')
    is_inherit: Mapped[bool] = mapped_column(default=True, comment='是否继承父角色权限')
    sort_order: Mapped[int] = mapped_column(default=0, comment='排序号')

    # 关系定义
    parent: Mapped['RoleModel | None'] = relationship(
        'RoleModel',
        remote_side='RoleModel.id',
        foreign_keys=[parent_id],
        back_populates='children'
    )
    children: Mapped[list['RoleModel']] = relationship(
        'RoleModel',
        foreign_keys='[RoleModel.parent_id]',
        cascade='save-update, merge',
        order_by='RoleModel.sort_order',
        back_populates='parent'
    )
    permissions: Mapped[list['PermissionModel']] = relationship(
        'PermissionModel',
        secondary=role_permission,
        back_populates='roles'
    )
    permission_groups: Mapped[list['PermissionGroupModel']] = relationship(
        'PermissionGroupModel',
        secondary=role_permission_group,
        back_populates='roles'
    )
    users: Mapped[list['UserModel']] = relationship(
        'UserModel',
        secondary=user_role,
        back_populates='roles',
        lazy='selectin'
    )
