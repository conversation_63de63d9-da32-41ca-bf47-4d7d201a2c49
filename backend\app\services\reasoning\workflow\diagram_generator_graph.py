#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于LangGraph实现的项目图表生成工作流
自动分析项目代码并生成架构图和依赖图，支持多种图形输出格式
"""
import os
from typing import Dict, List, Any

import structlog
from langgraph.graph import StateGraph, END
from langgraph.graph.state import CompiledStateGraph
from .cache_manager import CacheManager

from .diagram_generators import (
    DiagramFormat, DiagramType, DiagramConfig, DiagramState,
    ArchitectureDiagramGenerator, DependencyDiagramGenerator,
    DiagramExporter
)

from .models import AnalysisState

logger = structlog.get_logger(__name__)


class DiagramGenerator:
    """项目图表生成器"""

    def __init__(self, config: DiagramConfig, cache_dir: str = '.cache'):
        """
        初始化图表生成器

        Args:
            config: 图表生成配置
            cache_dir: 缓存目录
        """
        self.config = config
        self.graph: CompiledStateGraph = self._build_graph()

        # 初始化缓存管理器
        self.cache_manager = CacheManager.get_instance(cache_dir)

        # 初始化图表生成器
        self.architecture_generator = ArchitectureDiagramGenerator(config)
        self.dependency_generator = DependencyDiagramGenerator(config)

    async def initialize(self):
        """初始化资源"""
        # 确保输出目录存在
        if self.config.output_dir:
            os.makedirs(self.config.output_dir, exist_ok=True)
            logger.info(f"图表生成器初始化完成，输出目录: {self.config.output_dir}")

    def _build_graph(self) -> CompiledStateGraph:
        """
        构建图表生成工作流图

        Returns:
            构建好的StateGraph
        """
        # 创建工作流状态图
        graph = StateGraph(DiagramState)

        # 添加节点
        graph.add_node("load_analysis_data", self._load_analysis_data)
        graph.add_node("generate_architecture_diagram", self._generate_architecture_diagram)
        graph.add_node("generate_dependency_diagram", self._generate_dependency_diagram)
        graph.add_node("export_diagrams", self._export_diagrams)

        # 定义工作流连接
        # 起点：加载分析数据
        graph.set_entry_point("load_analysis_data")

        # 从加载分析数据到架构图生成
        graph.add_conditional_edges(
            "load_analysis_data",
            self._should_generate_architecture_diagram,
            {
                True: "generate_architecture_diagram",
                False: self._get_next_diagram_node("architecture")
            }
        )

        # 从架构图到依赖图生成
        graph.add_conditional_edges(
            "generate_architecture_diagram",
            self._should_generate_dependency_diagram,
            {
                True: "generate_dependency_diagram",
                False: "export_diagrams"
            }
        )

        # 从依赖图到导出图表
        graph.add_edge("generate_dependency_diagram", "export_diagrams")

        # 导出是最后一步
        graph.add_edge("export_diagrams", END)

        return graph.compile()

    def _get_next_diagram_node(self, current_type: str) -> str:
        """获取下一个图表节点名称"""
        # 图表类型到节点名称的映射
        node_mapping = {
            "architecture": "generate_dependency_diagram",
            "dependency": "export_diagrams"
        }

        # 直接返回下一个节点的名称
        return node_mapping.get(current_type, "export_diagrams")

    async def _load_analysis_data(self, state: DiagramState) -> DiagramState:
        """从缓存中加载项目分析数据并初始化图表存储结构"""
        try:
            logger.info(f"开始从缓存加载项目分析数据: {state.project_name}")
            state.current_step = "load_analysis_data"

            # 加载项目分析状态
            # project_analysis_state_json = self.cache_manager.get_state('project_analysis_state')
            project_analysis_state_json = self.cache_manager.get_state(state.project_path)
            if not project_analysis_state_json:
                logger.error("未找到项目分析状态，无法生成图表，请先运行项目分析")
                state.errors["load_analysis_data"] = "未找到项目分析状态，无法生成图表，请先运行项目分析"
                return state

            try:
                # 解析项目分析状态
                analysis_state = AnalysisState.model_validate_json(project_analysis_state_json)

                # 加载结构分析数据
                if analysis_state.structure_analysis:
                    state.structure_analysis = analysis_state.structure_analysis
                    logger.info(f"从项目分析状态中加载项目结构分析数据")

                # 加载依赖分析数据
                if analysis_state.dependency_analysis:
                    state.dependency_analysis = analysis_state.dependency_analysis
                    logger.info(f"从项目分析状态中加载项目依赖分析数据")

                # 加载架构分析数据
                if analysis_state.architecture_analysis:
                    state.architecture_analysis = analysis_state.architecture_analysis
                    logger.info(f"从项目分析状态中加载项目架构分析数据")

                # 加载模块分析数据
                if analysis_state.module_analyses:
                    state.module_analyses = analysis_state.module_analyses
                    logger.info(f"从项目分析状态中加载模块分析数据，共 {len(analysis_state.module_analyses)} 个模块")

                # 加载模块合并分析数据
                if analysis_state.module_merger_analyses:
                    state.module_merger_analyses = analysis_state.module_merger_analyses
                    logger.info(f"从项目分析状态中加载模块合并分析数据，共 {len(analysis_state.module_merger_analyses)} 个分组")



                logger.info(f"从项目分析状态加载数据成功")
            except Exception as e:
                logger.error(f"解析项目分析状态失败: {str(e)}")
                state.errors["load_analysis_data"] = f"解析项目分析状态失败: {str(e)}"
                return state

            # 初始化图表存储结构
            # 初始化所有图表类型
            state.diagrams[DiagramType.ARCHITECTURE] = {}
            state.diagrams[DiagramType.DEPENDENCY] = {}

            logger.info(f"图表数据准备完成")
            return state
        except Exception as e:
            logger.error(f"加载分析数据失败: {str(e)}")
            state.errors["load_analysis_data"] = str(e)
            return state



    def _should_generate_architecture_diagram(self, state: DiagramState) -> bool:
        """判断是否应该生成架构图"""
        return DiagramType.ARCHITECTURE in state.config.diagram_types

    def _should_generate_dependency_diagram(self, state: DiagramState) -> bool:
        """判断是否应该生成依赖图"""
        return DiagramType.DEPENDENCY in state.config.diagram_types

    async def _generate_architecture_diagram(self, state: DiagramState) -> DiagramState:
        """生成架构图"""
        return await self.architecture_generator.generate(state)

    async def _generate_dependency_diagram(self, state: DiagramState) -> DiagramState:
        """生成依赖图"""
        return await self.dependency_generator.generate(state)

    async def _export_diagrams(self, state: DiagramState) -> DiagramState:
        """导出所有生成的图表"""
        return await DiagramExporter.export_diagrams(state)

    async def run(self) -> DiagramState:
        """
        运行图表生成流程

        Returns:
            生成完成后的状态
        """
        # 确保资源已初始化
        await self.initialize()

        # 准备初始状态
        initial_state = DiagramState(
            project_path=self.config.project_path,
            project_name=self.config.project_name or os.path.basename(os.path.abspath(self.config.project_path)),
            config=self.config
        )

        logger.info(f"开始生成项目图表: {initial_state.project_name}")
        logger.info(f"项目路径: {initial_state.project_path}")
        logger.info(f"图表类型: {', '.join(self.config.diagram_types)}")

        # 运行工作流
        result: DiagramState = await self.graph.ainvoke(initial_state)
        if isinstance(result, dict):
            result = DiagramState.model_validate(result)

        # 计算生成耗时
        duration = (result.end_time - result.start_time).total_seconds()
        logger.info(f"图表生成完成，总耗时: {duration:.2f}秒")

        # 检查是否有错误
        if result.errors:
            logger.warning(f"生成过程中出现 {len(result.errors)} 个错误")
            for error_key, error_msg in result.errors.items():
                logger.warning(f"错误 {error_key}: {error_msg}")

        return result


# 使用示例
async def generate_project_diagrams(
    project_path: str,
    project_name: str = None,
    output_dir: str = '',
    diagram_types: List[str] = None,
    diagram_formats: List[str] = None,
    detail_level: int = 2,
    colorize: bool = True,
    interactive: bool = False,
    cache_dir: str = '.cache'
) -> Dict[str, Any]:
    """
    为指定项目生成图表

    Args:
        project_path: 项目路径
        project_name: 项目名称，如果为None则使用目录名称
        output_dir: 输出目录
        diagram_types: 要生成的图表类型列表，默认为[DiagramType.ARCHITECTURE, DiagramType.DEPENDENCY]
        diagram_formats: 要生成的图表格式列表
        detail_level: 详细程度 (1-3)
        colorize: 是否使用颜色
        interactive: 是否生成交互式图表
        cache_dir: 缓存目录

    Returns:
        生成结果的字典表示
    """
    if not project_name:
        project_name = os.path.basename(os.path.abspath(project_path))

    # 设置默认值
    if diagram_types is None:
        diagram_types = [DiagramType.ARCHITECTURE, DiagramType.DEPENDENCY]

    if diagram_formats is None:
        diagram_formats = [DiagramFormat.MERMAID, DiagramFormat.JSON]

    # 创建图表生成配置
    config = DiagramConfig(
        project_path=project_path,
        project_name=project_name,
        output_dir=output_dir,
        diagram_format=diagram_formats,
        diagram_types=diagram_types,
        detail_level=detail_level,
        colorize=colorize,
        interactive=interactive,
        show_relationships=True,
        show_metrics=True,
        include_code_examples=False
    )

    # 创建并运行图表生成器
    generator = DiagramGenerator(config, cache_dir)
    result = await generator.run()

    # 提取结果并返回
    diagram_result = {
        "project_name": result.project_name,
        "generation_duration": (result.end_time - result.start_time).total_seconds(),
        "output_directory": output_dir,
        "diagrams": result.diagrams,
        "errors": result.errors,
        "index_file": os.path.join(output_dir, "index.md")
    }

    return diagram_result
