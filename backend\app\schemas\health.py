"""
健康检查相关的响应模型
"""
from datetime import datetime
from pydantic import BaseModel, Field

class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str = Field(..., description="服务状态")
    timestamp: datetime = Field(..., description="检查时间")
    version: str = Field(..., description="服务版本")
    service: str = Field(..., description="服务名称")
    environment: str = Field(..., description="运行环境")

    class Config:
        """配置"""
        from_attributes = True