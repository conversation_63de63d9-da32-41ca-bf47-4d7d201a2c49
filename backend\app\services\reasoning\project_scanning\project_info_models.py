#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多粒度分析模型定义
包含函数级、模块级和项目级分析使用的Pydantic模型
"""
from __future__ import annotations
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


# 文件和目录相关子模型
class FileInfo(BaseModel):
    """文件信息模型"""
    name: str = Field(..., description="文件名称")
    path: str = Field(..., description="文件路径")
    size: int = Field(..., description="文件大小(字节)")
    lines_of_code: Optional[int] = Field(None, description="代码行数（仅代码文件）")
    is_code_file: bool = Field(default=False, description="是否为代码文件")
    language: Optional[str] = Field(None, description="文件所属编程语言")
    last_modified: datetime = Field(..., description="最后修改时间")
    extension: str = Field(..., description="文件扩展名")
    file_hash: Optional[str] = Field(None, description="文件内容哈希值(SHA256)")


class DirectoryInfo(BaseModel):
    """目录信息模型"""
    name: str = Field(..., description="目录名称")
    path: str = Field(..., description="目录路径")
    file_count: int = Field(..., description="包含的文件数量")
    subdirectory_count: int = Field(..., description="包含的子目录数量")
    last_modified: datetime = Field(..., description="最后修改时间")
    description: Optional[str] = Field(None, description="目录描述")
    total_size: int = Field(default=0, description="目录总大小(字节)")
    total_lines_of_code: Optional[int] = Field(None, description="目录中所有代码文件的总行数")
    languages: Dict[str, int] = Field(default_factory=dict, description="目录中的语言分布")


class TreeNode(BaseModel):
    """树形结构节点"""
    name: str = Field(..., description="节点名称")
    path: str = Field(..., description="节点路径")
    type: str = Field(..., description="节点类型(file/directory)")
    children: Optional[List[TreeNode]] = Field(default_factory=list, description="子节点")
    size: Optional[int] = Field(None, description="文件大小(如果是文件)")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外元数据")
    importance: Optional[int] = Field(None, description="重要性评分(1-5)")
    depth: int = Field(default=0, description="从根节点的深度")
    last_modified: Optional[datetime] = Field(None, description="最后修改时间")


# 主项目结构模型
class ProjectStructure(BaseModel):
    """项目结构基本信息"""
    # 基本信息
    project_name: str = Field(..., description="项目名称")
    root_path: str = Field(..., description="项目根路径")
    project_type: str = Field(default="", description="项目类型")

    # 结构信息
    directories: List[DirectoryInfo] = Field(default_factory=list, description="项目目录结构")
    files: List[FileInfo] = Field(default_factory=list, description="项目文件列表")
    structure_string: str = Field(default="", description="项目结构的字符串表示")
    structured_chunks: List[str] = Field(default_factory=list, description="分片后的结构化目录树字符串列表")

    # 统计信息
    total_files: int = Field(default=0, description="项目总文件数")
    total_directories: int = Field(default=0, description="项目总目录数")
    total_lines_of_code: int = Field(default=0, description="项目总代码行数")
    total_size: int = Field(default=0, description="项目总大小(字节)")
    language_summary: Dict[str, int] = Field(default_factory=dict, description="编程语言统计")
    file_types: Dict[str, int] = Field(default_factory=dict, description="各类型文件数量统计")

    # 高级统计信息
    created_at: Optional[datetime] = Field(None, description="项目创建时间")
    last_modified: Optional[datetime] = Field(None, description="最后修改时间")
    average_directory_depth: Optional[float] = Field(None, description="平均目录深度")
    max_directory_depth: Optional[int] = Field(None, description="最大目录深度")
