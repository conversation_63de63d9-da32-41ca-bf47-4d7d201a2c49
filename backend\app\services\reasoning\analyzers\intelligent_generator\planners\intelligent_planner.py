#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI驱动的智能规划器

基于项目分析数据智能规划README文档的章节结构
继承自BaseAgent类，遵循项目现有的AI代理设计模式
"""
import logging
from typing import Any, Dict, List, Optional, Tuple
from pathlib import Path

from langchain.chains import <PERSON>MChain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser

from .....ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser
from ......utils.retry import retry_async
from .intelligent_planner_models import ReadmePlan

logger = logging.getLogger(__name__)


@AgentManager.register("IntelligentPlannerAgent")
class IntelligentPlanner(BaseAgent):
    """
    AI驱动的智能规划器
    基于项目分析数据智能规划README文档的章节结构
    """

    def __init__(self):
        """初始化智能规划器"""
        super().__init__()
        self.name = "IntelligentPlanner"
        self.chain = None

    async def _initialize(self) -> None:
        """初始化规划器特定资源"""
        logger.info("初始化智能规划器特定资源")

    async def _create_chain(self) -> None:
        """初始化处理链"""
        try:
            # README章节规划提示模板
            planning_template = """
            # README文档智能规划器

            作为专业的技术文档规划专家，请基于项目分析数据智能规划README文档的章节结构。

            ## 项目分析数据
            项目名称: {{ project_name }}
            项目类型: {{ project_type }}
            主要语言: {{ primary_language }}

            {%- if project_metadata %}
            ## 项目元数据
            {{ project_metadata }}
            {%- endif %}

            {%- if tech_stack %}
            ## 技术栈信息
            {{ tech_stack }}
            {%- endif %}

            {%- if structure %}
            ## 项目结构信息
            {{ structure }}
            {%- endif %}

            {%- if api_design %}
            ## API设计信息
            {{ api_design }}
            {%- endif %}

            {%- if modules %}
            ## 模块信息
            {{ modules }}
            {%- endif %}

            {%- if dependencies %}
            ## 依赖信息
            {{ dependencies }}
            {%- endif %}

            {%- if architecture %}
            ## 架构信息
            {{ architecture }}
            {%- endif %}

            ## 规划原则

            ### 核心原则
            1. **基于实际数据**：严格基于提供的项目分析数据进行规划
            2. **项目类型适配**：根据项目类型调整章节规划策略
            3. **目标受众考虑**：考虑开发者和最终用户的不同需求
            4. **逻辑层次清晰**：确保章节顺序合理，层次分明
            5. **必要性优先**：优先安排必要章节，合理配置可选章节

            ### 项目类型策略
            - **web_application**: 重点关注部署、API文档、用户指南、配置说明
            - **library**: 重点关注安装、API参考、示例代码、集成指南
            - **cli_tool**: 重点关注安装、使用说明、命令参考、配置选项
            - **framework**: 重点关注架构、扩展性、最佳实践、开发指南
            - **microservice**: 重点关注部署、API、监控、服务发现
            - **data_pipeline**: 重点关注配置、数据流、监控、故障排除

            ### 章节类型定义
            - **introduction**: 项目介绍和概述
            - **features**: 功能特性列表
            - **installation**: 安装和环境配置
            - **usage**: 基本使用方法
            - **api_reference**: API文档和接口说明
            - **architecture**: 架构设计和技术选型
            - **dependencies**: 依赖管理和版本要求
            - **contributing**: 贡献指南和开发规范
            - **license**: 许可证信息
            - **changelog**: 版本更新记录
            - **examples**: 示例代码和用例
            - **troubleshooting**: 故障排除和常见问题
            - **deployment**: 部署指南和运维说明
            - **configuration**: 配置参数和选项
            - **testing**: 测试指南和质量保证

            ### 优先级评分规则 (1-10)
            - **10分**: 必要核心章节（introduction, installation, usage）
            - **8-9分**: 重要功能章节（features, api_reference）
            - **6-7分**: 技术细节章节（architecture, dependencies）
            - **4-5分**: 开发相关章节（contributing, testing）
            - **2-3分**: 辅助信息章节（changelog, troubleshooting）

            ### 长度估算规则
            - **short**: 简短章节，1-3段内容
            - **medium**: 中等章节，4-8段内容
            - **long**: 详细章节，9段以上内容

            ## 规划要求

            ### 必须包含的基础章节
            1. **项目介绍** (introduction) - 必要
            2. **安装指南** (installation) - 必要
            3. **使用说明** (usage) - 必要

            ### 根据项目特征添加的章节
            - 如果有API: 添加 api_reference
            - 如果有复杂架构: 添加 architecture
            - 如果有多个功能: 添加 features
            - 如果有部署需求: 添加 deployment
            - 如果有配置文件: 添加 configuration
            - 如果有测试: 添加 testing
            - 如果有贡献指南: 添加 contributing

            ### 输出要求
            - **语言**: 所有分析结果使用中文(简体中文)输出，技术名称和代码保持原文
            - **完整性**: 严格按照数据模型结构输出，不可遗漏字段
            - **准确性**: 基于实际项目数据，不要推测不存在的功能
            - **逻辑性**: 章节顺序合理，依赖关系清晰
            - **实用性**: 内容要点具体可操作
            - **适配性**: 符合项目类型和复杂度

            {{ format_instructions }}
            """

            # 创建Parser
            planning_parser = AnalysisOutputParser(pydantic_object=ReadmePlan)
            parser = OutputFixingParser.from_llm(parser=planning_parser, llm=self.llm)

            # 获取格式指令
            planning_format_instructions = planning_parser.get_format_instructions()

            # 创建提示模板
            planning_prompt = PromptTemplate.from_template(
                planning_template,
                template_format="jinja2"
            ).partial(format_instructions=planning_format_instructions)

            # 创建处理链
            self.chain = (
                planning_prompt
                | self.llm
                | parser
            )

            logger.info("智能规划器处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: Dict[str, Any]) -> AgentInput:
        """
        准备输入数据
        只负责基础的输入参数验证和准备，不进行数据提取操作

        Args:
            input_data: 原始输入数据

        Returns:
            AgentInput: 准备后的输入数据
        """
        input_data: AgentInput = await super().prepare_input(input_data)

        # 基础参数验证
        required_params = [
            "project_name", "project_type", "primary_language",
            "project_metadata", "tech_stack", "structure",
            "api_design", "modules", "dependencies", "architecture"
        ]

        for param in required_params:
            if param not in input_data.parameters:
                raise ValueError(f"缺少必需的参数: {param}")

        logger.info(f"准备智能规划器输入数据，项目: {input_data.parameters.get('project_name', 'Unknown')}")

        return input_data

    @retry_async(max_retries=3)
    async def _process(
        self,
        input_data: AgentInput
    ) -> Tuple[Any, Dict[str, Any]]:
        """
        处理规划请求

        Args:
            input_data: 输入数据

        Returns:
            Tuple[ReadmePlan, Dict[str, Any]]: (规划结果, 元数据)
        """
        try:
            # 调用LLM处理链
            result = await self.chain.ainvoke(input_data.parameters)

            metadata = {
                "analyzer": "intelligent planner",
                "version": "1.0",
                "project_type": input_data.parameters.get("project_type", "unknown"),
                "sections_count": len(result.sections) if hasattr(result, 'sections') else 0
            }

            return result, metadata

        except Exception as e:
            logger.error(f"智能规划处理失败: {str(e)}")
            raise

    async def _shutdown(self) -> None:
        """关闭规划器资源"""
        logger.info("关闭智能规划器资源")
        self.chain = None
        self.llm = None
