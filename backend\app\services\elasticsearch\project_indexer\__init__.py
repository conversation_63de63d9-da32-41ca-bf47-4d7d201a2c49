"""
项目搜索器包

提供高性能的Elasticsearch项目搜索功能。

注意：索引管理现在由 Logstash 处理，此包只负责搜索功能。

主要特性：
- 智能查询构建和结果处理
- 多语言搜索支持（中英文）
- 高亮和聚合功能
- 完善的错误处理和监控
- 向后兼容现有API接口

使用示例：
    from app.services.elasticsearch.project_indexer import ProjectSearcher

    searcher = ProjectSearcher(es_client)

    # 搜索项目
    results = await searcher.search_projects(
        query="python",
        filters={"status": "active"},
        page=1,
        page_size=10
    )
"""

# 导入主要的公共接口
from .core import ProjectIndexer
from .search import SearchQueryBuilder, SearchResultProcessor, SearchEngine
from .utils import SearchValidationUtils

# 索引名称常量（由 Logstash 管理）
PROJECT_INDEX_NAME = "modular_projects"

# 导出公共接口
__all__ = [
    # 主要类
    "ProjectIndexer",

    # 功能模块
    "SearchQueryBuilder",
    "SearchResultProcessor",
    "SearchEngine",

    # 工具类
    "SearchValidationUtils",

    # 常量
    "PROJECT_INDEX_NAME",
]