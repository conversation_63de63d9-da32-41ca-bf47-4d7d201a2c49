#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目扫描处理器
负责扫描项目文件结构并初始化分析状态
"""
import structlog

from ..models import AnalysisConfig, AnalysisState
from ...project_scanning.project_scanner import ProjectScanner
from .base_handler import BaseAnalysisHandler

logger = structlog.get_logger(__name__)

class ScanProjectHandler(BaseAnalysisHandler):
    """
    项目扫描处理器
    负责扫描项目文件结构并初始化分析状态
    """

    def initialize(self) -> None:
        """
        初始化项目扫描处理器
        该处理器不需要特殊的初始化操作
        """
        pass

    async def process(self, state: AnalysisState) -> AnalysisState:
        """
        处理项目扫描

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        # 1. 执行项目扫描
        try:
            # 初始化项目扫描器
            project_path = state.project_path
            logger.info(f"初始化项目扫描器: {project_path}")
            scanner = ProjectScanner(root_path=project_path)

            # 执行项目扫描
            logger.info(f"开始扫描项目: {project_path}")
            project_structure = scanner.scan()

            # 获取所有代码文件
            files = {file_info.path: file_info for file_info in project_structure.files if file_info.is_code_file}

            # 更新状态
            state.project_structure = project_structure
            state.files = files
            state.analysis_progress = 0.1  # 扫描项目完成，进度10%

            logger.info(f"项目扫描完成，发现 {len(files)} 个代码文件")
        except Exception as e:
            logger.error(f"项目扫描失败: {str(e)}")
            state.errors["project_scan"] = str(e)

        # 2. 返回分析结果
        return state


