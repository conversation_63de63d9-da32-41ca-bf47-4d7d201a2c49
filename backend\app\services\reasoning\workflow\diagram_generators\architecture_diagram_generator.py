#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
架构图生成器
负责生成项目的高级架构图，展示系统的主要组件和它们之间的关系
"""
import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

from .base_generator import BaseDiagramGenerator
from .models import DiagramState, DiagramConfig, DiagramType, DiagramFormat

logger = logging.getLogger(__name__)


class ArchitectureDiagramGenerator(BaseDiagramGenerator):
    """架构图生成器"""

    def __init__(self, config: DiagramConfig):
        """
        初始化架构图生成器

        Args:
            config: 图表生成配置
        """
        super().__init__(config)
        self.diagram_type = DiagramType.ARCHITECTURE

    async def validate_required_data(self, state: DiagramState) -> Dict[str, Any]:
        """
        验证生成架构图所需的数据是否完整

        Args:
            state: 当前图表生成状态

        Returns:
            数据验证结果字典
        """
        if not state.architecture_analysis:
            return {"valid": False, "message": "缺少架构分析数据"}

        if not state.architecture_analysis.architecture_components and not state.architecture_analysis.architecture_layers:
            return {"valid": False, "message": "架构分析数据不包含架构组件或架构层次信息"}

        return {"valid": True, "message": "数据验证通过"}

    async def generate(self, state: DiagramState) -> DiagramState:
        """
        生成架构图

        Args:
            state: 当前图表生成状态

        Returns:
            更新后的图表生成状态
        """
        try:
            # 设置当前步骤
            state.current_step = "generating_architecture_diagram"
            logger.info(f"正在为项目 {state.project_name} 生成架构图")

            # 验证数据
            validation = await self.validate_required_data(state)
            if not validation["valid"]:
                state.errors["architecture_diagram"] = validation["message"]
                logger.warning(f"架构图生成数据验证失败: {validation['message']}")
                return state

            # 根据请求的格式生成图表
            for format_type in self.config.diagram_format:
                if format_type == DiagramFormat.MERMAID:
                    # 生成Mermaid格式的架构图
                    mermaid_diagram = await self._generate_mermaid_diagram(state)
                    state.diagrams[self.diagram_type][DiagramFormat.MERMAID] = mermaid_diagram

                elif format_type == DiagramFormat.JSON:
                    # 生成JSON格式的架构图
                    json_diagram = await self._generate_json_diagram(state)
                    state.diagrams[self.diagram_type][DiagramFormat.JSON] = json_diagram

            # 更新完成时间
            state.end_time = datetime.now(timezone.utc)
            logger.info(f"架构图生成完成，格式: {', '.join(self.config.diagram_format)}")

            return state

        except Exception as e:
            error_msg = f"生成架构图时发生错误: {str(e)}"
            state.errors["architecture_diagram"] = error_msg
            logger.error(error_msg, exc_info=True)
            return state

    async def _generate_mermaid_diagram(self, state: DiagramState) -> str:
        """
        生成Mermaid格式的架构图

        Args:
            state: 当前图表生成状态

        Returns:
            Mermaid格式的架构图字符串
        """
        mermaid_lines = ["flowchart TB"]

        # 样式定义
        styles = {
            "component": "style=filled,color=#D6EAF8,fontcolor=black",
            "pattern": "style=filled,color=#D5F5E3,fontcolor=black",
            "layer": "style=filled,color=#E8DAEF,fontcolor=black",
            "critical": "style=filled,color=#F1948A,fontcolor=black"
        }

        # 节点ID到展示名称的映射
        node_ids = {}
        nodes_added = set()

        # 处理架构层次
        if state.architecture_analysis.architecture_layers:
            # 添加层次分组
            for i, layer in enumerate(state.architecture_analysis.architecture_layers):
                layer_id = f"layer_{i}"
                layer_name = layer.name

                # 注册层级ID
                node_ids[layer_id] = layer_name

                # 添加层级分组
                mermaid_lines.append(f"    subgraph {layer_id}[\"<b>{layer_name}</b>\"]")

                # 如果层有组件，在层内添加组件
                if layer.components:
                    for j, component_name in enumerate(layer.components):
                        comp_id = f"{layer_id}_comp_{j}"
                        mermaid_lines.append(f"        {comp_id}[\"{component_name}\"]")
                        nodes_added.add(comp_id)
                        node_ids[comp_id] = component_name

                # 结束层级分组
                mermaid_lines.append("    end")

        # 添加架构组件（如果没有层级定义）
        if state.architecture_analysis.architecture_components and not state.architecture_analysis.architecture_layers:
            for i, component in enumerate(state.architecture_analysis.architecture_components):
                comp_id = f"comp_{i}"
                nodes_added.add(comp_id)
                node_ids[comp_id] = component.name

                # 根据组件定义添加组件
                mermaid_lines.append(f"    {comp_id}[\"{component.name}\"]")

        # 添加架构模式
        if state.architecture_analysis.architecture_patterns:
            # 添加架构模式分组
            mermaid_lines.append("    subgraph arch_patterns[\"<b>架构模式</b>\"]")

            for i, pattern in enumerate(state.architecture_analysis.architecture_patterns):
                pattern_id = f"pattern_{i}"
                mermaid_lines.append(f"        {pattern_id}(\"{pattern.name}\")")

            mermaid_lines.append("    end")

        # 添加组件之间的连接
        # 这里只是一个示例，实际项目中可能需要根据深入的架构分析定制连接逻辑
        # 这里简单地连接上层与下层
        if state.architecture_analysis.architecture_layers and len(state.architecture_analysis.architecture_layers) > 1:
            for i in range(len(state.architecture_analysis.architecture_layers) - 1):
                layer_id_from = f"layer_{i}"
                layer_id_to = f"layer_{i+1}"
                mermaid_lines.append(f"    {layer_id_from} --依赖--> {layer_id_to}")

        # 添加类样式
        mermaid_lines.append("    classDef component fill:#D6EAF8,stroke:#85C1E9,color:black")
        mermaid_lines.append("    classDef pattern fill:#D5F5E3,stroke:#7DCEA0,color:black")
        mermaid_lines.append("    classDef layer fill:#E8DAEF,stroke:#BB8FCE,color:black")
        mermaid_lines.append("    classDef critical fill:#F1948A,stroke:#E74C3C,color:black")

        # 为节点添加类
        for comp_id in nodes_added:
            mermaid_lines.append(f"    class {comp_id} component")

        # 为模式添加类
        if state.architecture_analysis.architecture_patterns:
            for i in range(len(state.architecture_analysis.architecture_patterns)):
                mermaid_lines.append(f"    class pattern_{i} pattern")

        return "\n".join(mermaid_lines)

    async def _generate_json_diagram(self, state: DiagramState) -> str:
        """
        生成JSON格式的架构图

        Args:
            state: 当前图表生成状态

        Returns:
            JSON格式的架构图字符串
        """
        diagram_data = {
            "project": {
                "name": state.project_name,
                "path": state.project_path,
                "architecture_style": state.architecture_analysis.architecture_style
            },
            "nodes": [],
            "layers": [],
            "patterns": [],
            "links": [],
            "quality": {},
            "metadata": {
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "type": "architecture_diagram"
            }
        }

        # 添加架构层次
        if state.architecture_analysis.architecture_layers:
            for i, layer in enumerate(state.architecture_analysis.architecture_layers):
                layer_data = {
                    "id": i,
                    "name": layer.name,
                    "responsibilities": layer.responsibilities,
                    "component_ids": []
                }
                diagram_data["layers"].append(layer_data)

        # 添加架构组件
        next_id = 0
        component_ids = {}

        if state.architecture_analysis.architecture_components:
            for component in state.architecture_analysis.architecture_components:
                component_id = next_id
                next_id += 1

                # 记录组件ID和对应层
                component_ids[component.name] = component_id

                # 如果组件有层级属性，将组件关联到层上
                if component.layer and state.architecture_analysis.architecture_layers:
                    for i, layer in enumerate(state.architecture_analysis.architecture_layers):
                        if layer.name == component.layer:
                            diagram_data["layers"][i]["component_ids"].append(component_id)
                            break

                # 添加组件数据
                component_data = {
                    "id": component_id,
                    "name": component.name,
                    "path": component.path,
                    "purpose": component.purpose,
                    "layer": component.layer
                }

                diagram_data["nodes"].append(component_data)

        # 添加架构模式
        if state.architecture_analysis.architecture_patterns:
            for i, pattern in enumerate(state.architecture_analysis.architecture_patterns):
                pattern_data = {
                    "id": i,
                    "name": pattern.name,
                    "description": pattern.description,
                    "applied_at": pattern.applied_at
                }
                diagram_data["patterns"].append(pattern_data)

        # 添加架构质量评估
        if hasattr(state.architecture_analysis, "architecture_quality"):
            quality = state.architecture_analysis.architecture_quality
            diagram_data["quality"] = {
                "maintainability": quality.maintainability,
                "extensibility": quality.extensibility,
                "modularity": quality.modularity,
                "overall": quality.overall
            }

        # 添加组件间的关系连接
        # 这里只是一个简单的示例，实际项目中可能需要从依赖分析中获取更多信息
        # 由于内部依赖相关逻辑已移除，这里使用架构分析中的组件关系
        if state.architecture_analysis.architecture_components:
            # 获取已添加的组件名称列表
            component_names = [node["name"] for node in diagram_data["nodes"]]

            # 根据架构层次创建连接
            if state.architecture_analysis.architecture_layers and len(state.architecture_analysis.architecture_layers) > 1:
                # 为每个层中的组件添加到下一层组件的连接
                for i in range(len(state.architecture_analysis.architecture_layers) - 1):
                    current_layer = state.architecture_analysis.architecture_layers[i]
                    next_layer = state.architecture_analysis.architecture_layers[i + 1]

                    # 获取当前层和下一层的组件
                    current_layer_components = current_layer.components
                    next_layer_components = next_layer.components

                    # 为当前层的每个组件添加到下一层每个组件的连接
                    for from_comp in current_layer_components:
                        if from_comp in component_names:
                            from_id = component_names.index(from_comp)

                            for to_comp in next_layer_components:
                                if to_comp in component_names:
                                    to_id = component_names.index(to_comp)

                                    link_data = {
                                        "source": from_id,
                                        "target": to_id,
                                        "type": "layer_dependency"
                                    }
                                    diagram_data["links"].append(link_data)

        return json.dumps(diagram_data, ensure_ascii=False, indent=2)
