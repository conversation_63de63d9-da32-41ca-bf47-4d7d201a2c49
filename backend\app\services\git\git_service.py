"""
Git服务模块

提供Git仓库操作功能，包括：
1. 查询已克隆的代码库信息
2. 获取仓库文件列表
3. 获取文件内容
4. 获取提交历史
5. 获取分支信息
"""
import os
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple

import structlog
from git import Repo, GitCommandError, InvalidGitRepositoryError

from app.exceptions.git import GitError
from app.schemas.git import (
    GitFileInfo, GitRepositoryInfo, GitBranchInfo, GitCommit, 
    GitCommitWithStats, GitFileContent, GitRepositoryListItem,
    GitAuthor, GitRepoStats, GitCommitStats
)

logger = structlog.get_logger(__name__)


class GitService:
    """Git服务
    
    提供对本地Git仓库的操作功能
    """
    
    def __init__(self, repo_path: Optional[str] = None):
        """初始化Git服务
        
        Args:
            repo_path: Git仓库路径，如果不提供则不会初始化仓库对象
        """
        self.repo_path = repo_path
        self.repo = None
        
        if repo_path:
            self.load_repository(repo_path)
    
    def load_repository(self, repo_path: str) -> None:
        """加载Git仓库
        
        Args:
            repo_path: Git仓库路径
            
        Raises:
            GitError: 如果仓库路径无效或不是Git仓库
        """
        try:
            if not os.path.exists(repo_path):
                raise GitError(message=f"仓库路径不存在: {repo_path}")
            
            self.repo_path = repo_path
            self.repo = Repo(repo_path)
            logger.info("加载Git仓库", repo_path=repo_path)
        except InvalidGitRepositoryError:
            logger.error("无效的Git仓库", repo_path=repo_path)
            raise GitError(message=f"无效的Git仓库: {repo_path}")
        except Exception as e:
            logger.error("加载Git仓库失败", error=str(e), repo_path=repo_path)
            raise GitError(message=f"加载Git仓库失败: {str(e)}")
    
    def get_repository_info(self) -> GitRepositoryInfo:
        """获取仓库基本信息
        
        Returns:
            GitRepositoryInfo: 仓库信息对象
            
        Raises:
            GitError: 如果仓库未加载或操作失败
        """
        if not self.repo:
            raise GitError(message="仓库未加载")
        
        try:
            # 获取远程仓库URL
            remote_url = ""
            if len(self.repo.remotes) > 0:
                remote_url = self.repo.remotes.origin.url
            
            # 获取当前分支
            try:
                current_branch = self.repo.active_branch.name
            except TypeError:
                # 处理HEAD分离状态
                current_branch = "HEAD detached"
            
            # 获取最新提交
            latest_commit = self.repo.head.commit
            latest_commit_info = GitCommit(
                id=latest_commit.hexsha,
                short_id=latest_commit.hexsha[:7],
                message=latest_commit.message.strip(),
                author=GitAuthor(
                    name=latest_commit.author.name,
                    email=latest_commit.author.email
                ),
                committed_date=datetime.fromtimestamp(latest_commit.committed_date, timezone.utc),
            )
            
            # 获取仓库统计信息
            stats = GitRepoStats(
                total_commits=sum(1 for _ in self.repo.iter_commits()),
                total_branches=len(list(self.repo.branches)),
                total_tags=len(list(self.repo.tags)),
            )
            
            # 构建仓库信息
            repo_info = GitRepositoryInfo(
                name=os.path.basename(self.repo_path),
                path=self.repo_path,
                remote_url=remote_url,
                current_branch=current_branch,
                latest_commit=latest_commit_info,
                stats=stats,
                is_dirty=self.repo.is_dirty(),
            )
            
            return repo_info
        except Exception as e:
            logger.error("获取仓库信息失败", error=str(e))
            raise GitError(message=f"获取仓库信息失败: {str(e)}")
    
    def get_branches(self) -> List[GitBranchInfo]:
        """获取仓库分支列表
        
        Returns:
            List[GitBranchInfo]: 分支信息列表
            
        Raises:
            GitError: 如果仓库未加载或操作失败
        """
        if not self.repo:
            raise GitError(message="仓库未加载")
        
        try:
            branches = []
            current_branch_name = ""
            
            try:
                current_branch_name = self.repo.active_branch.name
            except TypeError:
                # 处理HEAD分离状态
                current_branch_name = ""
            
            for branch in self.repo.branches:
                # 获取分支最新提交
                commit = branch.commit
                
                branch_info = GitBranchInfo(
                    name=branch.name,
                    is_current=branch.name == current_branch_name,
                    commit=GitCommit(
                        id=commit.hexsha,
                        short_id=commit.hexsha[:7],
                        message=commit.message.strip(),
                        author=GitAuthor(
                            name=commit.author.name,
                            email=commit.author.email
                        ),
                        committed_date=datetime.fromtimestamp(commit.committed_date, timezone.utc),
                    )
                )
                
                branches.append(branch_info)
            
            return branches
        except Exception as e:
            logger.error("获取分支列表失败", error=str(e))
            raise GitError(message=f"获取分支列表失败: {str(e)}")
    
    def get_commits(self, branch: Optional[str] = None, limit: int = 50) -> List[GitCommitWithStats]:
        """获取提交历史
        
        Args:
            branch: 分支名称，默认为当前分支
            limit: 返回的提交数量限制
            
        Returns:
            List[GitCommitWithStats]: 提交历史列表
            
        Raises:
            GitError: 如果仓库未加载或操作失败
        """
        if not self.repo:
            raise GitError(message="仓库未加载")
        
        try:
            # 确定要查询的分支
            commit_ref = branch if branch else "HEAD"
            
            commits = []
            for commit in self.repo.iter_commits(commit_ref, max_count=limit):
                commit_info = GitCommitWithStats(
                    id=commit.hexsha,
                    short_id=commit.hexsha[:7],
                    message=commit.message.strip(),
                    author=GitAuthor(
                        name=commit.author.name,
                        email=commit.author.email
                    ),
                    committed_date=datetime.fromtimestamp(commit.committed_date, timezone.utc),
                    stats=GitCommitStats(
                        additions=commit.stats.total["insertions"],
                        deletions=commit.stats.total["deletions"],
                        files_changed=len(commit.stats.files)
                    )
                )
                
                commits.append(commit_info)
            
            return commits
        except Exception as e:
            logger.error("获取提交历史失败", error=str(e), branch=branch)
            raise GitError(message=f"获取提交历史失败: {str(e)}")
    
    def get_file_content(self, file_path: str, ref: str = "HEAD") -> GitFileContent:
        """获取文件内容
        
        Args:
            file_path: 文件路径（相对于仓库根目录）
            ref: Git引用（分支、标签或提交ID），默认为HEAD
            
        Returns:
            GitFileContent: 文件内容对象
            
        Raises:
            GitError: 如果仓库未加载或操作失败
        """
        if not self.repo:
            raise GitError(message="仓库未加载")
        
        try:
            # 获取指定引用的树对象
            tree = self.repo.tree(ref)
            
            # 查找文件
            try:
                blob = tree[file_path]
                content = blob.data_stream.read().decode("utf-8", errors="replace")
                filename = os.path.basename(file_path)
                return GitFileContent(
                    name=filename,
                    content=content,
                    path=file_path
                )
            except KeyError:
                raise GitError(message=f"文件不存在: {file_path}")
        except Exception as e:
            logger.error("获取文件内容失败", error=str(e), file_path=file_path, ref=ref)
            raise GitError(message=f"获取文件内容失败: {str(e)}")
    
    def get_file_history(self, file_path: str, limit: int = 20) -> List[GitCommit]:
        """获取文件的提交历史
        
        Args:
            file_path: 文件路径（相对于仓库根目录）
            limit: 返回的提交数量限制
            
        Returns:
            List[GitCommit]: 文件的提交历史
            
        Raises:
            GitError: 如果仓库未加载或操作失败
        """
        if not self.repo:
            raise GitError(message="仓库未加载")
        
        try:
            commits = []
            for commit in self.repo.iter_commits(paths=file_path, max_count=limit):
                commit_info = GitCommit(
                    id=commit.hexsha,
                    short_id=commit.hexsha[:7],
                    message=commit.message.strip(),
                    author=GitAuthor(
                        name=commit.author.name,
                        email=commit.author.email
                    ),
                    committed_date=datetime.fromtimestamp(commit.committed_date, timezone.utc),
                )
                
                commits.append(commit_info)
            
            return commits
        except Exception as e:
            logger.error("获取文件历史失败", error=str(e), file_path=file_path)
            raise GitError(message=f"获取文件历史失败: {str(e)}")
    
    def _build_file_tree(self, flat_items: List[GitFileInfo], base_path: str = "") -> List[GitFileInfo]:
        """将扁平的文件列表构建为树结构
        
        Args:
            flat_items: 扁平的文件列表
            base_path: 基础路径，用于确定根节点
            
        Returns:
            List[GitFileInfo]: 树结构的根节点列表
        """
        # 创建根节点列表和路径到节点的映射
        root_items = []
        path_to_node = {}
        
        # 首先创建所有节点的副本，避免修改原始对象
        # 并建立路径到节点的映射
        for item in flat_items:
            # 创建一个新的GitFileInfo对象，确保children是空列表
            new_item = GitFileInfo(
                name=item.name,
                path=item.path,
                type=item.type,
                size=item.size,
                children=[]  # 确保是一个新的空列表
            )
            path_to_node[item.path] = new_item
        
        # 第二次遍历，构建树结构
        for item_path in path_to_node:
            item = path_to_node[item_path]
            parent_path = os.path.dirname(item.path)
            
            # 如果是根级别的项目或者父路径就是基础路径
            if not parent_path or parent_path == base_path:
                if item not in root_items:  # 避免重复添加
                    root_items.append(item)
            else:
                # 如果父路径存在于映射中，将当前项添加为其子项
                if parent_path in path_to_node:
                    parent = path_to_node[parent_path]
                    # 检查是否已经添加过，避免重复
                    if item not in parent.children:
                        parent.children.append(item)
                else:
                    # 如果父路径不存在，需要创建中间目录
                    # 从根路径开始，逐级创建目录
                    parts = parent_path.split("/")
                    current_path = ""
                    
                    for i, part in enumerate(parts):
                        if not part:  # 跳过空字符串
                            continue
                            
                        # 构建当前路径
                        if current_path:
                            current_path = f"{current_path}/{part}"
                        else:
                            current_path = part
                        
                        # 如果该路径还没有对应的节点，创建一个
                        if current_path not in path_to_node:
                            dir_item = GitFileInfo(
                                name=part,
                                path=current_path,
                                type="dir",
                                children=[]
                            )
                            path_to_node[current_path] = dir_item
                            
                            # 找到上一级目录
                            if i > 0:
                                parent_of_current = os.path.dirname(current_path)
                                if parent_of_current in path_to_node:
                                    # 检查是否已经添加过，避免重复
                                    if dir_item not in path_to_node[parent_of_current].children:
                                        path_to_node[parent_of_current].children.append(dir_item)
                            else:
                                # 这是顶级目录
                                if dir_item not in root_items:  # 避免重复添加
                                    root_items.append(dir_item)
                    
                    # 现在父路径应该存在了，将当前项添加为其子项
                    if parent_path in path_to_node:
                        # 检查是否已经添加过，避免重复
                        if item not in path_to_node[parent_path].children:
                            path_to_node[parent_path].children.append(item)
        
        # 对树中的每个节点的子项进行排序
        def sort_children(node):
            if node.children:
                node.children.sort(key=lambda x: (x.type != "dir", x.name.lower()))
                for child in node.children:
                    sort_children(child)
        
        for item in root_items:
            sort_children(item)
        
        # 对根节点排序
        root_items.sort(key=lambda x: (x.type != "dir", x.name.lower()))
        
        return root_items

    def list_repository_files(self, path: str = "", ref: str = "HEAD", recursive: bool = True, as_tree: bool = False) -> List[GitFileInfo]:
        """列出仓库文件
        
        Args:
            path: 目录路径（相对于仓库根目录），默认为根目录
            ref: Git引用（分支、标签或提交ID），默认为HEAD
            recursive: 是否递归获取子目录中的文件，默认为True
            as_tree: 是否以树结构返回结果，默认为False（扁平结构）
            
        Returns:
            List[GitFileInfo]: 文件和目录列表，当as_tree=True时，返回树结构；当as_tree=False时，返回扁平结构
            
        Raises:
            GitError: 如果仓库未加载或操作失败
        """
        if not self.repo:
            raise GitError(message="仓库未加载")
        
        try:
            # 获取指定引用的树对象
            tree = self.repo.tree(ref)
            
            # 如果指定了路径，获取该路径下的树对象
            if path:
                try:
                    tree = tree[path]
                    if not hasattr(tree, "trees"):
                        raise GitError(message=f"路径不是目录: {path}")
                except KeyError:
                    raise GitError(message=f"路径不存在: {path}")
            
            # 扁平结构结果列表
            flat_result = []
            
            # 递归获取所有文件
            if recursive:
                # 使用 GitPython 的 traverse() 方法递归遍历所有对象
                for item in tree.traverse():
                    # 跳过树对象本身
                    if item.path == path:
                        continue
                        
                    if item.type == "blob":
                        # 这是一个文件
                        file_item = GitFileInfo(
                            name=item.name,
                            path=item.path,
                            type="file",
                            size=item.size,
                        )
                        flat_result.append(file_item)
                    elif item.type == "tree":
                        # 这是一个目录
                        dir_item = GitFileInfo(
                            name=item.name,
                            path=item.path,
                            type="dir",
                        )
                        flat_result.append(dir_item)
            else:
                # 非递归模式，只获取当前目录下的文件和子目录
                # 添加目录
                for subtree in tree.trees:
                    item = GitFileInfo(
                        name=subtree.name,
                        path=subtree.path,
                        type="dir",
                    )
                    flat_result.append(item)
                
                # 添加文件
                for blob in tree.blobs:
                    item = GitFileInfo(
                        name=blob.name,
                        path=blob.path,
                        type="file",
                        size=blob.size,
                    )
                    flat_result.append(item)
            
            # 按名称排序，目录优先
            flat_result.sort(key=lambda x: (x.type != "dir", x.name.lower()))
            
            # 如果不需要树结构，直接返回扁平结构
            if not as_tree:
                return flat_result
            
            # 构建树结构
            return self._build_file_tree(flat_result, path)
            
        except Exception as e:
            logger.error("列出仓库文件失败", error=str(e), path=path, ref=ref)
            raise GitError(message=f"列出仓库文件失败: {str(e)}")
    
    @staticmethod
    def is_git_repository(path: str) -> bool:
        """检查路径是否为Git仓库
        
        Args:
            path: 要检查的路径
            
        Returns:
            bool: 如果是Git仓库则返回True，否则返回False
        """
        try:
            Repo(path)
            return True
        except (InvalidGitRepositoryError, Exception):
            return False
    
    @staticmethod
    def find_repositories(base_path: str) -> List[GitRepositoryListItem]:
        """在指定目录中查找Git仓库
        
        Args:
            base_path: 基础目录路径
            
        Returns:
            List[GitRepositoryListItem]: 找到的Git仓库列表
        """
        repos = []
        
        if not os.path.exists(base_path) or not os.path.isdir(base_path):
            logger.warning("基础目录不存在或不是目录", base_path=base_path)
            return repos
        
        try:
            # 遍历基础目录
            for root, dirs, _ in os.walk(base_path):
                # 检查当前目录是否为Git仓库
                if ".git" in dirs:
                    try:
                        repo = Repo(root)
                        
                        # 获取远程URL
                        remote_url = ""
                        if len(repo.remotes) > 0:
                            remote_url = repo.remotes.origin.url
                        
                        # 获取当前分支
                        try:
                            current_branch = repo.active_branch.name
                        except TypeError:
                            current_branch = "HEAD detached"
                        
                        # 获取最新提交
                        latest_commit = repo.head.commit
                        
                        repo_info = GitRepositoryListItem(
                            name=os.path.basename(root),
                            path=root,
                            remote_url=remote_url,
                            current_branch=current_branch,
                            latest_commit={
                                "id": latest_commit.hexsha[:7],
                                "message": latest_commit.message.strip().split("\n")[0],
                                "author": latest_commit.author.name,
                                "date": datetime.fromtimestamp(latest_commit.committed_date, timezone.utc).isoformat(),
                            }
                        )
                        
                        repos.append(repo_info)
                        
                        # 不再递归进入已确认的Git仓库
                        dirs.remove(".git")
                    except Exception as e:
                        logger.warning("处理仓库时出错", error=str(e), path=root)
        except Exception as e:
            logger.error("查找Git仓库失败", error=str(e), base_path=base_path)
        
        return repos

def test_git_service():
    # 演示GitService的使用方法
    import os
    import json
    from pydantic import BaseModel
    
    def print_json(obj):
        """打印对象为格式化的JSON"""
        if isinstance(obj, BaseModel):
            obj = obj.model_dump()
        if isinstance(obj, list):
            obj = [item.model_dump() if isinstance(item, BaseModel) else item for item in obj]
        print(json.dumps(obj, ensure_ascii=False, indent=2, default=str))
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 获取项目根目录 (假设是当前目录的祖父目录的祖父目录)
    project_root = os.path.abspath(os.path.join(current_dir, "..", "..", "..", ".."))
    
    print(f"当前目录: {current_dir}")
    print(f"项目根目录: {project_root}")
    print("\n" + "="*50 + "\n")
    
    # 1. 测试查找Git仓库
    print("1. 测试查找Git仓库")
    print(f"在 {project_root} 中查找Git仓库...")
    repos = GitService.find_repositories(project_root)
    print(f"找到 {len(repos)} 个Git仓库")
    print_json(repos)
    print("\n" + "="*50 + "\n")
    
    # 如果找到的仓库数量大于0，使用第一个仓库继续测试
    if len(repos) > 0:
        repo_path = repos[0].path
        print(f"使用仓库: {repo_path} 进行测试")
        
        try:
            # 初始化Git服务
            git_service = GitService(repo_path)
            
            # 2. 测试获取仓库信息
            print("\n2. 测试获取仓库信息")
            info = git_service.get_repository_info()
            print("仓库信息:")
            print_json(info)
            print("\n" + "="*50 + "\n")
            
            # 3. 测试获取分支列表
            print("\n3. 测试获取分支列表")
            branches = git_service.get_branches()
            print(f"分支列表 (共 {len(branches)} 个):")
            print_json(branches)
            print("\n" + "="*50 + "\n")
            
            # 4. 测试获取提交历史
            print("\n4. 测试获取提交历史")
            commits = git_service.get_commits(limit=5)
            print(f"提交历史 (共 {len(commits)} 个):")
            print_json(commits)
            print("\n" + "="*50 + "\n")
            
            # 5. 测试列出文件 (扁平结构)
            print("\n5. 测试列出文件 (扁平结构)")
            files = git_service.list_repository_files(path="", as_tree=False)
            print(f"文件列表 (共 {len(files)} 个):")
            # 只打印前10个文件避免输出过多
            print_json(files[:10] if len(files) > 10 else files)
            print("\n" + "="*50 + "\n")
            
            # 6. 测试列出文件 (树形结构)
            print("\n6. 测试列出文件 (树形结构)")
            files_tree = git_service.list_repository_files(path="", as_tree=True)
            print(f"文件树 (共 {len(files_tree)} 个根节点):")
            print_json(files_tree[:3] if len(files_tree) > 3 else files_tree)
            print("\n" + "="*50 + "\n")
            
            # 如果有文件，测试获取文件内容
            if len(files) > 0 and files[0].type == "file":
                # 7. 测试获取文件内容
                print("\n7. 测试获取文件内容")
                file_path = files[0].path
                print(f"读取文件: {file_path}")
                file_content = git_service.get_file_content(file_path)
                # 只打印前100个字符避免输出过多
                file_content.content = file_content.content[:100] + "..." if len(file_content.content) > 100 else file_content.content
                print_json(file_content)
                print("\n" + "="*50 + "\n")
                
                # 8. 测试获取文件历史
                print("\n8. 测试获取文件历史")
                file_history = git_service.get_file_history(file_path, limit=3)
                print(f"文件历史 (共 {len(file_history)} 个提交):")
                print_json(file_history)
                print("\n" + "="*50 + "\n")
        
        except GitError as e:
            print(f"Git错误: {e}")
        except Exception as e:
            print(f"发生错误: {e}")
    else:
        print("未找到Git仓库，无法继续测试")
