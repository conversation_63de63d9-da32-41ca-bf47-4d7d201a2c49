"""
GitHub项目服务
"""
import os
import random
from typing import Dict, Optional, List, Tuple, Any
from datetime import datetime
from urllib.parse import urlparse

import structlog
from sqlalchemy import select, and_, func, delete
from sqlalchemy.exc import IntegrityError
import json
import re
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage


from app.core.di.providers import SessionProvider, AsyncSessionProvider
from app.models.github.github_project import GitHubProjectModel as DBGitHubProject, GitHubProjectModel
from app.models.github.github_project_card import GitHubProjectCardModel
from app.schemas.github.github_card import GitHubCardCreate, GitHubCard, GitHubCardBase
from app.schemas.github.github_project import GitHubProject, GitHubProjectCreate
from app.schemas.github.github_share import GitHubSharedCreate
from app.utils.base64 import upload_base64_image
from app.utils.status_enum import ProjectStatusEnum
from app.services.elasticsearch.project_indexer import ProjectIndexer

logger = structlog.get_logger(__name__)

class GitHubProjectService:
    """GitHub项目服务"""

    def __init__(
        self,
        session: SessionProvider,
        async_session: AsyncSessionProvider,
        indexer: ProjectIndexer
    ):

        self.session = session
        self.async_session = async_session
        self.indexer = indexer  # 用于删除 Elasticsearch 索引中的项目文档
        logger.debug("GitHub项目服务初始化完成")

    async def set_shared_info(self, data: GitHubSharedCreate) -> Optional[Dict[str, Any]]:
        """设置项目分享信息

        Args:
            data: 分享信息数据

        Returns:
            Optional[Dict[str, Any]]: 更新后的项目信息，失败时返回None
        """
        try:
            async with self.async_session() as session:
                # 查找项目是否存在
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == data.project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()

                if not db_project:
                    logger.error("设置分享信息失败：项目不存在", project_id=data.project_id)
                    return None
                # 更新分享信息
                if data.shared_pic is not None:
                    pic_url =  upload_base64_image(data.shared_pic)
                    db_project.shared_pic = pic_url
                if data.shared_data is not None:
                    db_project.shared_data = data.shared_data

                # 提交更改
                await session.commit()
                await session.refresh(db_project)

                # same as get
                shared_data = None
                if db_project.shared_data:
                    try:
                        shared_data = json.loads(db_project.shared_data)
                        # 确保shared_data是list类型，如果不是可以转换或处理
                        if not isinstance(shared_data, list):
                            shared_data = [shared_data]  # 或者根据业务逻辑处理
                    except json.JSONDecodeError:
                        # 如果解析失败，可以返回空列表或原始数据，根据业务需求决定
                        shared_data = []  # 或者 shared_data = [db_project.shared_data]

                # 返回更新后的项目信息
                return {
                    "project_id": db_project.id,
                    "shared_pic": db_project.shared_pic,
                    "shared_data": shared_data
                }

        except Exception as e:
            logger.error("设置分享信息失败", error=str(e), project_id=data.project_id)
            return None

    async def get_shared_info(self, project_id: str) -> Optional[Dict[str, Any]]:
        """获取项目分享信息

        Args:
            project_id: 项目ID

        Returns:
            Optional[Dict[str, Any]]: 项目分享信息，不存在时返回None
        """
        try:
            async with self.async_session() as session:
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()

                if not db_project:
                    return None

                # shared_data 2 JSON
                shared_data = None
                if db_project.shared_data:
                    try:
                        shared_data = json.loads(db_project.shared_data)
                        # 确保shared_data是list类型，如果不是可以转换或处理
                        if not isinstance(shared_data, list):
                            shared_data = [shared_data]  # 或者根据业务逻辑处理
                    except json.JSONDecodeError:
                        # 如果解析失败，可以返回空列表或原始数据，根据业务需求决定
                        shared_data = []  # 或者 shared_data = [db_project.shared_data]

                return {
                    "project_id": db_project.id,
                    "shared_pic": db_project.shared_pic,
                    "shared_data": shared_data,  # 现在确保是list类型
                    "shared_count_link": db_project.shared_count_link,
                    "shared_count_qrcode": db_project.shared_count_qrcode,
                }


        except Exception as e:
            logger.error("获取分享信息失败", error=str(e), project_id=project_id)
            return None

    async def share_project(self, project_id: str, share_type:str) -> Optional[int]:
        """增加项目分享次数

        Args:
            project_id: 项目ID

        Returns:
            Optional[int]: 更新后的分享次数，失败时返回None
        """
        try:
            async with self.async_session() as session:
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()
                if not db_project:
                    logger.error("增加分享次数失败：项目不存在", project_id=project_id)
                    return None
                # 获取当前分享次数并加1
                if share_type == 'qrcode':
                    current_count = int(db_project.shared_count_qrcode) if db_project.shared_count_qrcode else 0
                    new_count = current_count + 1
                    db_project.shared_count_qrcode = str(new_count)
                if share_type == 'link':
                    current_count = int(db_project.current_count_link) if db_project.current_count_link else 0
                    new_count = current_count + 1
                    db_project.shared_count_link = str(new_count)
                # 提交更改
                await session.commit()
                return new_count

        except Exception as e:
            logger.error("增加分享次数失败", error=str(e), project_id=project_id)
            return None


    async def get_image_list(self, project_id: str) -> Optional[List]:
        try:
            async with self.async_session() as session:
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()
                if isinstance(db_project.image_list, str):
                    return json.loads(db_project.image_list)
                else:
                    return db_project.image_list  # 如果已经是列表就直接返回
        except Exception as e:
            logger.error(f"获取项目信息时发生错误: {str(e)}")
            return None

    async def update_project(self, project_id:str ,data: GitHubProject) -> Optional[GitHubProject]:
        """更新GitHub项目
        
        Args:
            data: 更新的项目数据
            
        Returns:
            Optional[GitHubProject]: 更新后的项目，失败时返回None
        """
        try:
            async with self.async_session() as session:
                # 查找项目是否存在
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()
                
                if not db_project:
                    logger.error("更新项目失败：项目不存在", project_id=data.id)
                    return None
                
                # 更新项目字段
                if data.get('name'):
                    db_project.name = data.get('name')
                if data.get('description_recommend') is not None:
                    db_project.description_recommend = data.get('description_recommend')
                if data.get('description_project') is not None:
                    db_project.description_project = data.get('description_project')
                if data.get('tags'):
                    db_project.tags = data.get('tags')
                if data.get('status'):
                    db_project.status = data.get('status')
                if data.get('local_path'):
                    db_project.local_path = data.get('local_path')
                if data.get('image_url') is not None:
                    db_project.image_url = data.get('image_url')
                if data.get('repository_url'):
                    db_project.repository_url = data.get('repository_url')
                if data.get('background_color'):
                    db_project.background_color = data.get('background_color')
                if data.get('buton_color'):
                    db_project.button_color = data.get('button_color')
                if data.get('icon_url') is not None:
                    db_project.icon_url = data.get('icon_url')

                # 更新时间
                # db_project.updated_at = datetime.utcnow()
                
                # 提交更改
                await session.commit()
                await session.refresh(db_project)
                # 返回更新后的项目
                return GitHubProject.model_validate(db_project.__dict__)
                
        except Exception as e:
            logger.error("更新项目失败", error=str(e), project_id=data.id)
            return None

    async def update_project_status(self, project_id: str, status: str) -> Optional[GitHubProject]:
        """只更新项目状态

        Args:
            project_id: 项目ID
            status: 新状态

        Returns:
            Optional[GitHubProject]: 更新后的项目，失败返回None
        """
        try:
            async with self.async_session() as session:
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()

                if not db_project:
                    logger.error("更新项目状态失败：项目不存在", project_id=project_id)
                    return None

                # 只更新状态字段
                db_project.status = status

                await session.commit()
                await session.refresh(db_project)

                return GitHubProject.model_validate(db_project.__dict__)

        except Exception as e:
            logger.error("更新项目状态失败", error=str(e), project_id=project_id)
            return None


    async def delete_project(self, project_id: str) -> bool:
        """删除GitHub项目
        
        Args:
            project_id: 项目ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            async with self.async_session() as session:
                # 查找项目是否存在
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()
                
                if not db_project:
                    logger.error("删除项目失败：项目不存在", project_id=project_id)
                    return False
                
                # 删除项目
                await session.delete(db_project)
                await session.commit()

                # 从 Elasticsearch 索引中删除项目文档（辅助清理功能）
                try:
                    es_delete_success = await self.indexer.delete_project_from_index(project_id)
                    if es_delete_success:
                        logger.info("成功从 Elasticsearch 索引中删除项目文档", project_id=project_id)
                    else:
                        logger.warning("从 Elasticsearch 索引中删除项目文档失败，但数据库删除成功", project_id=project_id)
                except Exception as es_error:
                    # Elasticsearch 删除失败不影响数据库删除操作的成功
                    logger.warning("Elasticsearch 索引删除操作异常，但数据库删除成功",
                                 project_id=project_id, es_error=str(es_error))

                logger.info("项目删除成功", project_id=project_id)
                return True
                
        except Exception as e:
            logger.error("删除项目失败", error=str(e), project_id=project_id)
            return False

    async def generate_cards(self,projects_ids: List[str])-> List[Dict[str, Any]] :
        results = []
        async with self.async_session() as session:
            for project_id in projects_ids:
                try:
                    # 检查项目是否已存在
                    stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                    result = await session.execute(stmt)
                    existing_project = result.scalar_one_or_none()

                    if not existing_project:
                        # 如果项目已存在，返回现有项目
                        logger.info(f"项目不存在: {project_id}")
                        results.append({
                            "success": False,
                            "project": str(project_id),
                            "error": ""
                        })
                        continue
                    from app.core.di import container_manager
                    readme_generate = container_manager.container.github_readme_generate()
                    await readme_generate.add_to_generate_queue(project_id)

                except Exception as e:
                    logger.error(f"生成项目时发生错误: {str(e)}")
                    results.append({
                        "success": False,
                        "project": None,
                        "error": f"服务器错误: {str(e)}"
                    })
        return results
    async def download_projects(self, projects_data: List[GitHubProjectCreate]) -> List[Dict[str, Any]]:
        """批量下载GitHub项目

        Args:
            projects_data: 项目创建参数列表

        Returns:
            List[Dict[str, Any]]: 每个项目的下载结果，包含成功标志、项目信息和错误信息
        """
        results = []
        async with self.async_session() as session:
            for data in projects_data:
                try:
                    # 检查项目是否已存在
                    repository_url = data.repository_url
                    stmt = select(DBGitHubProject).where(DBGitHubProject.repository_url == repository_url)
                    result = await session.execute(stmt)
                    existing_project = result.scalar_one_or_none()

                    if existing_project:
                        # 如果项目已存在，返回现有项目
                        logger.info(f"项目已存在: {data.repository_url}")
                        results.append({
                            "success": True,
                            "project": GitHubProject.model_validate(existing_project.__dict__),
                            "error": ""
                        })
                        continue

                    # 创建新项目记录
                    # background_colors = ["#ffe6e6", "#cef9fd", "#f9e9ff"]
                    background_colors = ["#e3ecf0", "#e3e5f5", "#f7f4eb", "#ebf6f0"]
                    button_colors = ["#07c4fe", "#f4acff", "#ffbaa0"]
                    project_model = GitHubProjectModel(
                        name=urlparse(data.repository_url).path.strip('/').split('/')[-1],
                        repository_url=data.repository_url,
                        background_color=random.choice(background_colors),
                        button_color=random.choice(button_colors),
                        project_phase=ProjectStatusEnum.DOWNLOADING.value,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                    )

                    session.add(project_model)
                    await session.commit()
                    await session.refresh(project_model)

                    # 添加到下载队列
                    from app.core.di import container_manager
                    downloader = container_manager.container.github_downloader()
                    await downloader.add_to_download_queue(project_model.id, data.repository_url)

                    results.append({
                        "success": True,
                        "project": GitHubProject.model_validate(project_model.__dict__),
                        "error": ""
                    })

                except IntegrityError as e:
                    logger.error(f"数据库完整性错误: {str(e)}")
                    await session.rollback()
                    results.append({
                        "success": False,
                        "project": None,
                        "error": f"数据库错误: {str(e)}"
                    })
                except Exception as e:
                    logger.error(f"下载项目时发生错误: {str(e)}")
                    results.append({
                        "success": False,
                        "project": None,
                        "error": f"服务器错误: {str(e)}"
                    })

        return results
    async def get_project(self, project_id: str) -> Optional[GitHubProject]:
        """获取项目信息
        
        Args:
            project_id: 项目ID
        Returns:
            Optional[GitHubProject]: 项目信息，不存在时返回None
        """
        try:
            async with self.async_session() as session:
                stmt = select(DBGitHubProject).where(DBGitHubProject.id == project_id)
                result = await session.execute(stmt)
                db_project = result.scalar_one_or_none()
                
                if not db_project:
                    return None
                
                return GitHubProject.model_validate(db_project.__dict__)
        except Exception as e:
            logger.error(f"获取项目信息时发生错误: {str(e)}")
            return None

    async def get_projects(
            self,
            *,
            page: int = 1,
            page_size: int = 10,
            status: Optional[str] = None,
            name: Optional[str] = None,
            recommend_description: Optional[str] = None
    ) -> List[GitHubProject]:
        """获取GitHub项目列表

        Args:
            page: 页码
            page_size: 每页记录数
            status: 项目状态(精确匹配)
            name: 项目名称(模糊匹配)
            recommend_description: 推荐描述搜索关键字

        Returns:
            Tuple[List[GitHubProject], int]: 项目列表和总记录数
        """
        try:
            async with self.async_session() as session:
                # 构建基础查询
                stmt = select(DBGitHubProject)

                # 构建查询条件
                conditions = []

                if status is not None:
                    conditions.append(DBGitHubProject.status == status)

                if name is not None:
                    conditions.append(DBGitHubProject.name.ilike(f"%{name}%"))

                if recommend_description:
                    conditions.append(DBGitHubProject.description_recommend.ilike(f"%{recommend_description}%"))

                # 应用查询条件
                if conditions:
                    stmt = stmt.where(and_(*conditions))

                # 获取总记录数
                count_stmt = select(func.count()).select_from(stmt.subquery())
                total = await session.scalar(count_stmt)

                # 计算分页
                skip = (page - 1) * page_size

                # 获取分页数据
                stmt = (
                    stmt.order_by(
                        DBGitHubProject.stars.desc(),
                        DBGitHubProject.created_at.desc(),
                        DBGitHubProject.updated_at.desc()
                    )
                        .offset(skip)
                        .limit(page_size)
                )

                result = await session.execute(stmt)
                db_projects = result.scalars().all()

                projects = [GitHubProject.model_validate(p.__dict__) for p in db_projects]

                return projects, total

        except Exception as e:
            logger.error("获取GitHub项目列表失败", error=str(e))
            return []
    
    async def count_projects(self) -> int:
        """获取项目总数
        
        Returns:
            int: 项目总数
        """
        try:
            async with self.async_session() as session:
                stmt = select(DBGitHubProject)
                result = await session.execute(stmt)
                count = len(result.scalars().all())
                return count
        except Exception as e:
            logger.error("获取项目总数失败", error=str(e))
            return 0

    # 卡片删除了让他写
    async def create_card(self, data: GitHubCardCreate) -> Optional[GitHubCard]:
        """创建卡片

        Args:
            data: 卡片创建数据

        Returns:
            GitHubCard: 创建的卡片数据
        """
        try:
            async with self.async_session() as session:
                # 检查项目是否存在
                project = await session.execute(
                    select(GitHubProjectModel).where(GitHubProjectModel.id == data.project_id)
                )
                project = project.scalar_one_or_none()
                if not project:
                    logger.error("项目不存在", project_id=data.project_id)
                    return None

                # 创建卡片
                card = GitHubProjectCardModel(
                    content=data.content,
                    title=data.title,
                    project_id=data.project_id,
                    sort_order=data.sort_order
                )
                session.add(card)
                await session.commit()
                await session.refresh(card)

                return GitHubCard.model_validate(card.__dict__)
        except Exception as e:
            logger.error("创建卡片失败", error=str(e))
            return None

    async def get_project_cards(self,  project_id: Optional[str] = None, page: int = 1, page_size: int = 10) -> List[GitHubCard]:
        """获取项目的所有卡片

        Args:
            project_id: 项目ID

        Returns:
            List[GitHubCard]: 卡片列表
        """
        try:
            async with self.async_session() as session:
                # 构建查询
                if project_id and project_id.strip():
                    # 原有的查询逻辑保持不变
                    result = await session.execute(
                        select(GitHubProjectCardModel)
                            .where(GitHubProjectCardModel.project_id == project_id)
                            .order_by(GitHubProjectCardModel.sort_order)
                            .offset((page - 1) * page_size)
                            .limit(page_size)
                    )
                else:
                    # 当project_id为空时，返回所有卡片
                    result = await session.execute(
                        select(GitHubProjectCardModel)
                            .order_by(GitHubProjectCardModel.sort_order)
                            .offset((page - 1) * page_size)
                            .limit(page_size)
                    )

                cards = result.scalars().all()
                return [GitHubCard.model_validate(card.__dict__) for card in cards]
        except Exception as e:
            logger.error("获取项目卡片失败", error=str(e), project_id=project_id)
            return []

    async def get_card(self, card_id: str) -> Optional[GitHubCard]:
        """获取卡片详情

        Args:
            card_id: 卡片ID

        Returns:
            Optional[GitHubCard]: 卡片详情
        """
        try:
            async with self.async_session() as session:
                result = await session.execute(
                    select(GitHubProjectCardModel).where(GitHubProjectCardModel.id == card_id)
                )
                card = result.scalar_one_or_none()

                if card:
                    return GitHubCard.model_validate(card.__dict__)
                return None
        except Exception as e:
            logger.error("获取卡片详情失败", error=str(e), card_id=card_id)
            return None

    async def update_card(self,data: GitHubCardBase) -> Optional[GitHubCard]:

        try:
            async with self.async_session() as session:
                result = await session.execute(
                    select(GitHubProjectCardModel).where(GitHubProjectCardModel.id == data.id)
                )
                card = result.scalar_one_or_none()

                if not card:
                    logger.error("卡片不存在", card_id=data.id)
                    return None

                # 更新卡片
                update_data = data.model_dump(exclude_unset=True)
                for key, value in update_data.items():
                    setattr(card, key, value)

                await session.commit()
                await session.refresh(card)

                return GitHubCard.model_validate(card.__dict__)
        except Exception as e:
            logger.error("更新卡片失败", error=str(e), card_id=data.id)
            return None

    async def delete_card(self, card_id: str) -> bool:

        try:
            async with self.async_session() as session:
                result = await session.execute(
                    select(GitHubProjectCardModel).where(GitHubProjectCardModel.id == card_id)
                )
                card = result.scalar_one_or_none()
                if not card:
                    logger.error("卡片不存在", card_id=card_id)
                    return False

                await session.delete(card)
                await session.commit()

                return True
        except Exception as e:
            logger.error("删除卡片失败", error=str(e), card_id=card_id)
            return False

    async def update_card_interaction(self, card_id: str, interaction_type: str) -> bool:
        """更新卡片互动数据（点赞、收藏、不喜欢）
        """
        try:
            async with self.async_session() as session:
                result = await session.execute(
                    select(GitHubProjectCardModel).where(GitHubProjectCardModel.id == card_id)
                )
                card = result.scalar_one_or_none()

                if not card:
                    logger.error("卡片不存在", card_id=card_id)
                    return False

                # 更新互动计数
                if interaction_type not in ["like", "collect", "dislike"]:
                    logger.error("无效的互动类型", interaction_type=interaction_type)
                    return False

                current_value = getattr(card, interaction_type)
                setattr(card, interaction_type, current_value + 1)
                await session.commit()
                return True
        except Exception as e:
            logger.error("更新卡片互动数据失败", error=str(e), card_id=card_id, interaction_type=interaction_type)
            return False

    async def create_project(self, data: GitHubProjectCreate) -> Optional[GitHubProject]:
        """创建GitHub项目

        Args:
            data: 项目创建数据模型

        Returns:
            Optional[GitHubProject]: 创建成功返回项目数据，失败返回None
        """
        try:
            async with self.async_session() as session:
                # 检查项目是否已存在
                stmt = select(DBGitHubProject).where(DBGitHubProject.repository_url == data.repository_url)
                result = await session.execute(stmt)
                existing_project = result.scalar_one_or_none()

                if existing_project:
                    logger.error("创建项目失败：项目已存在", repository_url=data.repository_url)
                    return None

                # 创建新项目实例
                db_project = DBGitHubProject(
                    name=data.name,
                    repository_url=data.repository_url,
                    description_recommend=data.description_recommend,
                    description_project=data.description_project,
                    status=data.status if hasattr(data, 'status') else "false",
                    tags=data.tags if hasattr(data, 'tags') else [],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    button_color=data.button_color,
                    background_color=data.background_color,
                    image_url=data.image_url,
                    icon_url=data.icon_url,
                    local_path=data.local_path
                )

                # 添加到数据库
                session.add(db_project)
                await session.commit()
                await session.refresh(db_project)

                # 返回创建的项目
                return GitHubProject.model_validate(db_project.__dict__)

        except IntegrityError as e:
            logger.error("创建项目失败：数据库完整性错误", error=str(e))
            return None
        except Exception as e:
            logger.error("创建项目失败", error=str(e))
            return None

    async def create_cards_by_analyzer_content(self, project_id: str, content: str) -> Tuple[bool, List[GitHubCard], str]:
        """使用大模型分析内容并批量创建项目卡片

        Args:
            project_id: 项目ID
            content: 要分析的Markdown内容

        Returns:
            Tuple[bool, List[GitHubCard], str]: (成功标志, 创建的卡片列表, 错误信息)
        """
        try:
            # 检查项目是否存在
            async with self.async_session() as session:
                project = await session.execute(
                    select(GitHubProjectModel).where(GitHubProjectModel.id == project_id)
                )
                project = project.scalar_one_or_none()
                if not project:
                    logger.error("批量创建卡片失败：项目不存在", project_id=project_id)
                    return False, [], "项目不存在"

            # 初始化LangChain的ChatOpenAI客户端
            llm = ChatOpenAI(
                temperature=0.0,
                model="deepseek-reasoner",
                base_url="https://api.deepseek.com/v1",
                max_tokens=8192,
                api_key="sk-b31c5a7c8d054f2db7d038a321d4920d"
            )

            # 构建提示词
            prompt = f"""
            请将以下Markdown文档分割成多个独立的Markdown文档，每个文档应该具有一个清晰的主题。
            
            要求:
            1. 返回纯JSON格式，不要包含代码块标记
            2. 使用如下格式的JSON数组：[{{"title": "文档标题", "content": "文档内容"}}]
            3. 每个文档应该有一个逻辑上独立的内容部分，适合作为单独的项目卡片
            4. 标题应简明扼要地总结文档内容
            
            原始Markdown文档:
            ```
            {content}
            ```
            """

            # 使用LangChain调用大模型
            messages = [HumanMessage(content=prompt)]
            response = llm.invoke(messages)
            result = response.content

            # 清理结果（移除可能的代码块标记）
            code_block_match = re.search(r'```(?:json)?\s*([\s\S]*?)```', result)
            if code_block_match:
                cleaned_json = code_block_match.group(1).strip()
            else:
                cleaned_json = result.strip()

            # 解析JSON
            try:
                documents = json.loads(cleaned_json)
                created_cards = []
                
                # 创建卡片
                async with self.async_session() as session:
                    # 获取当前项目卡片数量，用于设置排序顺序
                    result = await session.execute(
                        select(func.count()).select_from(GitHubProjectCardModel)
                        .where(GitHubProjectCardModel.project_id == project_id)
                    )
                    current_count = result.scalar_one() or 0
                    
                    for i, doc in enumerate(documents):
                        # 检查文档是否包含所需字段
                        if "title" not in doc or "content" not in doc:
                            logger.warning("文档缺少所需字段", doc=doc)
                            continue
                            
                        # 创建卡片
                        card = GitHubProjectCardModel(
                            title=doc["title"],
                            content=doc["content"],
                            project_id=project_id,
                            sort_order=current_count + i,  # 设置排序顺序
                            like=0,
                            collect=0,
                            dislike=0
                        )
                        session.add(card)
                    
                    # 提交所有变更
                    await session.commit()
                    
                    # 获取刚创建的卡片
                    result = await session.execute(
                        select(GitHubProjectCardModel)
                        .where(GitHubProjectCardModel.project_id == project_id)
                        .order_by(GitHubProjectCardModel.created_at.desc())
                        .limit(len(documents))
                    )
                    cards = result.scalars().all()
                    created_cards = [GitHubCard.model_validate(card.__dict__) for card in cards]
                
                logger.info("批量创建卡片成功", project_id=project_id, card_count=len(created_cards))
                return True, created_cards, ""
                
            except json.JSONDecodeError as e:
                logger.error("JSON解析错误", error=str(e), result=cleaned_json[:200])
                return False, [], f"解析分割结果失败: {str(e)}"
                
        except Exception as e:
            logger.error("批量创建卡片失败", error=str(e), project_id=project_id)
            return False, [], f"服务器错误: {str(e)}"

    async def solve_data(self, count: int) -> Dict:
        try:
            async with self.async_session() as session:
                # 步骤1：查询状态为 'gererating' 的项目
                generating_stmt = select(DBGitHubProject).where(DBGitHubProject.status == ProjectStatusEnum.Generate.value)
                result = await session.execute(generating_stmt)
                generating_projects = result.scalars().all()

                if not generating_projects:
                    return {
                        "status": "success",
                        "message": "没有找到状态为 'gererating' 的项目",
                        "data": {
                            "generating_count": 0,
                            "updated_count": 0,
                            "updated_projects": []
                        }
                    }

                # 记录要更新的项目
                projects_to_update = []

                # 步骤2：检查每个项目的卡片数量
                project_ids = [project.id for project in generating_projects]
                card_counts_stmt = (
                    select(
                        GitHubProjectCardModel.project_id,
                        func.count(GitHubProjectCardModel.id).label('card_count')
                    )
                        .where(GitHubProjectCardModel.project_id.in_(project_ids))
                        .group_by(GitHubProjectCardModel.project_id)
                )

                result = await session.execute(card_counts_stmt)
                card_counts = {row.project_id: row.card_count for row in result}

                # 步骤3：更新符合条件的项目状态
                updated_projects = []
                for project in generating_projects:
                    card_count = card_counts.get(project.id, 0)
                    if card_count > 5:
                        project.status = "true"
                        updated_projects.append({
                            "id": str(project.id),
                            "name": project.name,
                            "repository_url": project.repository_url,
                            "card_count": card_count,
                            "old_status": "generating",
                            "new_status": "true"
                        })

                await session.commit()

                return {
                    "status": "success",
                    "message": f"成功处理项目数据",
                    "data": {
                        "generating_count": len(generating_projects),
                        "updated_count": len(updated_projects),
                        "updated_projects": updated_projects
                    }
                }

        except Exception as e:
            logger.error(f"处理项目数据时发生错误: {str(e)}")
            return {
                "status": "error",
                "message": f"服务器错误: {str(e)}",
                "data": None
            }

    async def delete_wrong_cards_porject(self, count: int) -> Dict:
        """删除具有3-5张卡片的项目中的所有卡片

        Args:
            count: 要处理的项目数量

        Returns:
            Dict: 包含删除结果的字典
        """
        try:
            async with self.async_session() as session:
                # 首先查询具有3-5张卡片的项目
                card_count_subquery = (
                    select(
                        GitHubProjectCardModel.project_id,
                        func.count(GitHubProjectCardModel.id).label('card_count')
                    )
                        .group_by(GitHubProjectCardModel.project_id)
                        .having(and_(
                        func.count(GitHubProjectCardModel.id) >= 3,
                        func.count(GitHubProjectCardModel.id) <= 5
                    ))
                        .subquery()
                )

                # 查询符合条件的项目及其卡片
                stmt = (
                    select(DBGitHubProject, GitHubProjectCardModel)
                        .join(card_count_subquery, DBGitHubProject.id == card_count_subquery.c.project_id)
                        .join(GitHubProjectCardModel, DBGitHubProject.id == GitHubProjectCardModel.project_id)
                        .limit(count)
                )

                result = await session.execute(stmt)
                projects_and_cards = result.all()

                if not projects_and_cards:
                    return {
                        "status": "success",
                        "data": {
                            "processed_count": 0,
                            "processed_projects": []
                        },
                        "message": "没有找到符合条件的项目"
                    }

                # 整理要处理的项目和卡片
                processed_projects = {}
                for project, card in projects_and_cards:
                    if project.id not in processed_projects:
                        processed_projects[project.id] = {
                            "project": {
                                "id": str(project.id),
                                "name": project.name,
                                "repository_url": project.repository_url
                            },
                            "deleted_cards": []
                        }

                    # 记录要删除的卡片信息
                    processed_projects[project.id]["deleted_cards"].append({
                        "id": str(card.id),
                        "title": card.title
                    })

                    # 删除卡片
                    await session.delete(card)

                # 提交删除操作
                await session.commit()

                # 准备返回结果
                result_data = {
                    "processed_count": len(processed_projects),
                    "processed_projects": [
                        {
                            "project": project_data["project"],
                            "deleted_cards_count": len(project_data["deleted_cards"]),
                            "deleted_cards": project_data["deleted_cards"]
                        }
                        for project_data in processed_projects.values()
                    ]
                }

                return {
                    "status": "success",
                    "data": result_data,
                    "message": f"成功处理 {len(processed_projects)} 个项目的卡片"
                }

        except Exception as e:
            logger.error(f"删除项目卡片时发生错误: {str(e)}")
            return {
                "status": "error",
                "data": None,
                "message": f"服务器错误: {str(e)}"
            }

    async def queue_unanalyzed_projects(self, count: int) -> Dict:

        try:
            from app.core.di import container_manager
            readme_generate = container_manager.container.github_readme_generate()
            current_queue = await readme_generate.get_generate_queue()
            async with self.async_session() as session:
                # 查询未分析的项目，并排除已在队列中的项目
                stmt = select(DBGitHubProject).where(
                    and_(
                        # DBGitHubProject.architecture_mermaid.is_(None),
                        DBGitHubProject.project_phase == "download_success",
                        DBGitHubProject.id.notin_(current_queue)  # 排除已在队列中的项目
                    )
                ).limit(count)
                result = await session.execute(stmt)
                unanalyzed_projects = result.scalars().all()

                queued_projects = []
                failed_projects = []

                for project in unanalyzed_projects:
                    try:
                        # 检查本地路径是否存在
                        if not project.local_path:
                            logger.warning(f"项目 {project.id} 本地路径不存在，跳过分析")
                            failed_projects.append({
                                "id": str(project.id),
                                "error": "本地路径不存在"
                            })
                            continue

                        # 添加到生成队列
                        from app.core.di import container_manager
                        readme_generate = container_manager.container.github_readme_generate()
                        await readme_generate.add_to_generate_queue(str(project.id))

                        queued_projects.append(str(project.id))
                        logger.info(f"项目 {project.id} 已添加到分析队列")

                    except Exception as e:
                        logger.error(f"处理项目 {project.id} 时发生错误: {str(e)}")
                        failed_projects.append({
                            "id": str(project.id),
                            "error": str(e)
                        })

                # 获取当前队列状态
                queue_status = await readme_generate.get_generate_queue_status()

                return {
                    "status": "success",
                    "data": {
                        "queued_projects": queued_projects,
                        "failed_projects": failed_projects,
                        "queue_status": queue_status
                    },
                    "message": f"成功添加 {len(queued_projects)} 个项目到分析队列，{len(failed_projects)} 个项目处理失败"
                }

        except Exception as e:
            logger.error(f"查找未分析项目时发生错误: {str(e)}")
            return {
                "status": "error",
                "data": None,
                "message": f"服务器错误: {str(e)}"
            }

    async def batch_update_cards(self, project_id: str, cards_data: List[Dict]) -> List[GitHubCard]:
        """批量更新项目卡片（删除所有卡片并添加新卡片）

        Args:
            project_id: 项目ID
            cards_data: 卡片数据列表

        Returns:
            List[GitHubCard]: 创建的卡片列表
        """
        # 1. 删除项目下所有卡片
        ans = await self.delete_all_cards_by_project(project_id)
        if not ans:
            raise ValueError(500, "删除卡片失败 操作取消")
        # 2. 批量创建新卡片
        created_cards = []
        for card_data in cards_data:
            # 确保每个卡片数据中包含项目ID
            card_data["project_id"] = project_id
            # 创建卡片
            card = await self.create_card(GitHubCardCreate(**card_data))
            created_cards.append(card)

        return created_cards

    async def delete_all_cards_by_project(self, project_id: str) -> bool:
        """删除项目下的所有卡片

        Args:
            project_id: 项目ID

        Returns:
            bool: 是否删除成功
        """
        try:
            async with self.async_session() as session:
                # 检查项目是否存在
                project_result = await session.execute(
                    select(GitHubProjectModel).where(GitHubProjectModel.id == project_id)
                )
                project = project_result.scalar_one_or_none()
                if not project:
                    logger.error("删除所有卡片失败：项目不存在", project_id=project_id)
                    return False

                # 删除所有关联的卡片
                delete_stmt = delete(GitHubProjectCardModel).where(
                    GitHubProjectCardModel.project_id == project_id
                )
                await session.execute(delete_stmt)
                await session.commit()

                logger.info("已删除项目下的所有卡片", project_id=project_id)
                return True
        except Exception as e:
            logger.error("删除项目下所有卡片失败", error=str(e), project_id=project_id)
            return False

    async def get_file_content(self, project_id: str, file_path: str) -> dict:
        """获取项目文件内容

        Args:
            project_id: 项目ID
            file_path: 文件相对路径

        Returns:
            dict: 包含文件内容和相关信息的字典

        Raises:
            ValueError: 参数错误或文件读取错误
            NotFoundError: 项目或文件不存在
            PermissionError: 文件路径非法
        """
        # 获取项目详情
        project = await self.get_project(project_id)
        if not project:
            raise Exception(f"未找到项目: {project_id}")

        local_path = project.local_path
        if not local_path or not os.path.exists(local_path):
            raise Exception(f"项目本地路径不存在: {local_path}")

        # 获取文件的完整路径
        full_file_path = os.path.join(local_path, file_path)

        # 检查路径合法性（防止目录遍历攻击）
        if not os.path.normpath(full_file_path).startswith(os.path.normpath(local_path)):
            raise PermissionError("非法的文件路径")

        # 检查文件是否存在
        if not os.path.exists(full_file_path):
            raise Exception(f"文件不存在: {file_path}")

        # 检查是否是目录
        if os.path.isdir(full_file_path):
            raise ValueError(f"指定路径是一个目录，不是文件: {file_path}")

        # 获取文件大小
        file_size = os.path.getsize(full_file_path)

        # 读取文件内容和编码
        content, encoding = await self._read_file_content(full_file_path)

        # 返回符合预期格式的响应
        return {
            "file_path": file_path,
            "project_id": project_id,
            "file_size": file_size,
            "content": content,
            "encoding": encoding
        }

    async def _read_file_content(self, file_path: str) -> tuple[str, str]:
        """读取文件内容及其编码

        Args:
            file_path: 文件的完整路径

        Returns:
            tuple: (文件内容, 编码)

        Raises:
            ValueError: 文件读取失败
        """
        try:
            # 文件大小限制（例如10MB）
            max_size = 10 * 1024 * 1024
            if os.path.getsize(file_path) > max_size:
                return f"文件过大，超过了{max_size / (1024 * 1024)}MB的限制，无法显示", "unknown"

            # 二进制文件类型列表
            binary_extensions = [
                '.zip', '.rar', '.tar', '.gz', '.bz2', '.7z',  # 压缩文件
                '.exe', '.dll', '.so', '.dylib',  # 可执行文件
                '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico', '.svg',  # 图像
                '.mp3', '.wav', '.ogg', '.flac',  # 音频
                '.mp4', '.avi', '.mov', '.mkv',  # 视频
                '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',  # 文档
                '.o', '.obj', '.class', '.pyc',  # 编译文件
            ]

            # 检查是否是二进制文件
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext in binary_extensions:
                return f"[二进制文件 {file_ext}] - 无法显示内容", "binary"

            # 尝试以文本方式读取文件
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read(), "utf-8"
            except UnicodeDecodeError:
                # 如果UTF-8解码失败，尝试其他编码
                for encoding in ['gbk', 'latin-1', 'cp1252']:
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            return f.read(), encoding
                    except UnicodeDecodeError:
                        continue

                # 如果所有编码都失败，将其视为二进制文件
                return "[无法解码的文件] - 可能是二进制文件或使用了不支持的编码", "unknown"

        except Exception as e:
            logger.error(f"读取文件内容失败", file_path=file_path, error=str(e))
            raise ValueError(f"读取文件内容失败: {str(e)}")