#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能化依赖图生成器
基于dependency_category的动态样式分配，生成高质量的依赖关系可视化图表
"""
import logging
import json
from typing import Dict, Any, List, Optional, Tuple, Set
from datetime import datetime, timezone
from collections import defaultdict, Counter

from .base_generator import BaseDiagramGenerator
from .models import DiagramState, DiagramConfig, DiagramType, DiagramFormat
from .dependency_style_config import DependencyStyleConfig, NodeShape, CategoryStyle

logger = logging.getLogger(__name__)


class DependencyDiagramGenerator(BaseDiagramGenerator):
    """智能化依赖图生成器"""

    def __init__(self, config: DiagramConfig):
        """
        初始化智能化依赖图生成器

        Args:
            config: 图表生成配置
        """
        super().__init__(config)
        self.diagram_type = DiagramType.DEPENDENCY
        self.style_config = DependencyStyleConfig()

    async def validate_required_data(self, state: DiagramState) -> Dict[str, Any]:
        """
        验证生成依赖图所需的数据是否完整

        Args:
            state: 当前图表生成状态

        Returns:
            数据验证结果字典
        """
        try:
            # 检查依赖分析对象是否存在
            if not state.dependency_analysis:
                return {"valid": False, "message": "缺少依赖分析数据"}

            # 检查外部依赖列表是否存在且不为空
            if not state.dependency_analysis.external_dependencies:
                if self.config.include_external_dependencies:
                    logger.warning("依赖分析数据不包含外部依赖信息，将生成空依赖图")
                    return {"valid": True, "message": "依赖分析数据不包含外部依赖信息，将生成空依赖图", "warning": True}
                else:
                    return {"valid": False, "message": "依赖分析数据不包含外部依赖信息"}

            # 检查外部依赖数据的完整性
            for i, dep in enumerate(state.dependency_analysis.external_dependencies):
                if not dep.name:
                    logger.warning(f"第{i+1}个依赖缺少名称")

            return {"valid": True, "message": "数据验证通过"}

        except Exception as e:
            logger.error(f"验证依赖图数据时发生错误: {str(e)}")
            return {"valid": False, "message": f"验证依赖图数据时发生错误: {str(e)}"}

    async def generate(self, state: DiagramState) -> DiagramState:
        """
        生成依赖关系图

        Args:
            state: 当前图表生成状态

        Returns:
            更新后的图表生成状态
        """
        try:
            # 设置当前步骤
            state.current_step = "generating_smart_dependency_diagram"
            logger.info(f"正在为项目 {state.project_name} 生成智能化依赖关系图")

            # 验证数据
            validation = await self.validate_required_data(state)
            if not validation["valid"]:
                state.errors["dependency_diagram"] = validation["message"]
                logger.warning(f"依赖图生成数据验证失败: {validation['message']}")
                return state

            # 确保diagrams字典已初始化
            diagram_type_key = self.diagram_type
            if diagram_type_key not in state.diagrams:
                state.diagrams[diagram_type_key] = {}

            # 根据请求的格式生成图表
            for format_type in self.config.diagram_format:
                format_key = format_type

                if format_type == DiagramFormat.MERMAID:
                    # 生成Mermaid格式的依赖图
                    mermaid_diagram = await self._generate_smart_mermaid_diagram(state)
                    state.diagrams[diagram_type_key][format_key] = mermaid_diagram

                elif format_type == DiagramFormat.JSON:
                    # 生成JSON格式的依赖图
                    json_diagram = await self._generate_smart_json_diagram(state)
                    state.diagrams[diagram_type_key][format_key] = json_diagram

            # 更新完成时间
            state.end_time = datetime.now(timezone.utc)
            logger.info(f"智能化依赖图生成完成，格式: {', '.join(self.config.diagram_format)}")

            return state

        except Exception as e:
            error_msg = f"生成智能化依赖图时发生错误: {str(e)}"
            state.errors["dependency_diagram"] = error_msg
            logger.error(error_msg, exc_info=True)
            return state

    def _calculate_importance(self, dep) -> int:
        """计算依赖重要性"""
        importance = 1
        if dep.is_direct:
            importance += 3
        if dep.purpose and len(dep.purpose) > 10:
            importance += 2
        if dep.license:
            importance += 1
        if dep.is_vulnerable:
            importance += 4  # 安全问题优先级最高
        if not dep.is_dev_dependency:
            importance += 2  # 生产依赖更重要
        return importance

    def _escape_mermaid_text(self, text: str) -> str:
        """转义Mermaid中的特殊字符"""
        if not text:
            return ""
        escaped = text.replace('"', '\\"').replace('(', '\\(').replace(')', '\\)')
        escaped = escaped.replace('@', '').replace('/', '-').replace('#', '')
        return escaped

    def _escape_url_for_mermaid(self, url: str) -> str:
        """
        为Mermaid点击事件转义URL

        Args:
            url: 需要转义的URL

        Returns:
            转义后的URL
        """
        if not url:
            return ""

        # 转义双引号，但保持URL的基本结构
        url = url.replace('"', '%22')

        return url

    def _simplify_name(self, name: str, max_length: int = 18) -> str:
        """简化依赖名称"""
        if len(name) > max_length:
            return name[:max_length] + "..."
        return name

    def _get_mermaid_shape(self, shape: NodeShape, label: str) -> str:
        """获取Mermaid节点形状"""
        shape_templates = {
            NodeShape.RECTANGLE: f'["{label}"]',
            NodeShape.CIRCLE: f'(("{label}"))',
            NodeShape.HEXAGON: f'{{{{"{label}"}}}}',
            NodeShape.DIAMOND: f'{{"{label}"}}',
            NodeShape.ROUNDED_RECT: f'("{label}")',
            NodeShape.ELLIPSE: f'(["{label}"])',
            NodeShape.CYLINDER: f'[("{label}")]',
            NodeShape.STADIUM: f'(["{label}"])'
        }
        return shape_templates.get(shape, f'["{label}"]')

    def _organize_dependencies(self, all_deps: List) -> Dict[str, List[Tuple]]:
        """组织依赖项按分类"""
        deps_by_category = defaultdict(list)

        # 分类依赖并计算重要性
        for dep in all_deps:
            category = dep.dependency_category or "other"
            importance = self._calculate_importance(dep)
            deps_by_category[category].append((dep, importance))

        # 对每个分类的依赖按重要性排序（保留排序，移除数量限制）
        for category in deps_by_category:
            deps_by_category[category].sort(key=lambda x: x[1], reverse=True)

        # 对于大型项目，这可能会生成包含大量节点的图表
        total_deps = sum(len(deps) for deps in deps_by_category.values())
        if total_deps > 100:  # 仅作为性能提醒，不限制
            logger.warning(f"检测到大量依赖项 ({total_deps} 个)，生成的图表可能较大，建议注意渲染性能")

        return dict(deps_by_category)

    def _group_subgraphs_if_needed(self, deps_by_category: Dict[str, List[Tuple]]) -> Dict[str, List[Tuple]]:
        """
        根据子图数量管理策略对依赖进行分组

        Args:
            deps_by_category: 按分类组织的依赖字典

        Returns:
            分组后的依赖字典，可能包含分组子图
        """
        max_nodes_per_subgraph = 16  # 每个子图最多包含的节点数
        subgraph_threshold = 10      # 阈值

        logger.info(f"开始子图分组检查，当前分类: {list(deps_by_category.keys())}")

        # 计算当前子图总数和需要分组的分类
        total_subgraphs = len([cat for cat, deps in deps_by_category.items() if deps])
        large_categories = []

        for category, deps_with_importance in deps_by_category.items():
            if deps_with_importance:
                deps_count = len(deps_with_importance)
                logger.info(f"分类 '{category}' 包含 {deps_count} 个依赖")
                if deps_count > max_nodes_per_subgraph:
                    large_categories.append((category, deps_count))

        # 分组触发条件：总子图数超过阈值 OR 存在大分类
        should_group = total_subgraphs > subgraph_threshold or len(large_categories) > 0

        logger.info(f"分组检查结果: 总子图数={total_subgraphs}, 阈值={subgraph_threshold}, 大分类={len(large_categories)}, 是否分组={should_group}")

        if not should_group:
            logger.info("无需分组，返回原始数据")
            return deps_by_category

        logger.info(f"启动子图分组逻辑，大分类: {large_categories}")

        grouped_deps = {}

        for category, deps_with_importance in deps_by_category.items():
            if not deps_with_importance:
                continue

            deps_count = len(deps_with_importance)

            # 如果该分类的依赖数量不超过单个子图的最大节点数，不需要分组
            if deps_count <= max_nodes_per_subgraph:
                grouped_deps[category] = deps_with_importance
                logger.info(f"分类 '{category}' 无需分组，直接使用")
                continue

            # 计算需要分成多少个子图
            num_subgraphs = (deps_count + max_nodes_per_subgraph - 1) // max_nodes_per_subgraph
            nodes_per_subgraph = deps_count // num_subgraphs
            remainder = deps_count % num_subgraphs

            logger.info(f"分类 '{category}' 有 {deps_count} 个依赖，将分成 {num_subgraphs} 个子图，每个子图约 {nodes_per_subgraph} 个节点")

            # 分组依赖
            start_idx = 0
            for group_idx in range(num_subgraphs):
                # 计算当前组的大小（前面的组可能多一个节点）
                current_group_size = nodes_per_subgraph + (1 if group_idx < remainder else 0)
                end_idx = start_idx + current_group_size

                # 创建分组的键名
                group_key = f"{category}_group_{group_idx + 1}"
                grouped_deps[group_key] = deps_with_importance[start_idx:end_idx]

                logger.info(f"创建分组 '{group_key}'，包含 {current_group_size} 个依赖 (索引 {start_idx}-{end_idx-1})")

                start_idx = end_idx

        logger.info(f"分组完成，最终分组: {list(grouped_deps.keys())}")
        return grouped_deps

    def _parse_group_key(self, group_key: str, all_group_keys: List[str] = None) -> Tuple[str, Optional[str]]:
        """
        解析分组键，提取基础分类和分组信息

        Args:
            group_key: 分组键，如 "frontend_group_1" 或 "backend"
            all_group_keys: 所有分组键的列表，用于计算总分组数

        Returns:
            (基础分类, 分组信息) 的元组
        """
        logger.debug(f"解析分组键: {group_key}")

        if "_group_" in group_key:
            # 这是一个分组子图
            parts = group_key.split("_group_")
            if len(parts) != 2:
                logger.warning(f"分组键格式错误: {group_key}")
                return group_key, None

            base_category = parts[0]
            group_number = parts[1]

            # 如果提供了所有键，计算总分组数
            if all_group_keys:
                total_groups = self._get_total_groups_for_category(base_category, all_group_keys)
                group_info = f"({group_number}/{total_groups})"
                logger.debug(f"分组信息: {base_category} -> {group_info}")
            else:
                group_info = f"({group_number})"

            return base_category, group_info
        else:
            # 这是一个普通分类
            logger.debug(f"普通分类: {group_key}")
            return group_key, None

    def _get_total_groups_for_category(self, category: str, all_group_keys: List[str]) -> int:
        """
        获取指定分类的总分组数

        Args:
            category: 分类名称
            all_group_keys: 所有分组键的列表

        Returns:
            该分类的总分组数
        """
        count = 0
        pattern = f"{category}_group_"

        for key in all_group_keys:
            if key.startswith(pattern):
                count += 1

        # 如果没有分组，检查是否有原始分类
        if count == 0 and category in all_group_keys:
            count = 1

        logger.debug(f"分类 '{category}' 的总分组数: {count}")
        return count if count > 0 else 1

    async def _generate_smart_mermaid_diagram(self, state: DiagramState) -> str:
        """
        生成基于dependency_category的智能化Mermaid依赖图

        Args:
            state: 当前图表生成状态

        Returns:
            Mermaid格式的依赖图字符串
        """
        try:
            # 初始化Mermaid代码
            mermaid_lines = ["flowchart TB"]
            mermaid_lines.append("    %% 智能化依赖关系图")
            mermaid_lines.append("    %% 基于dependency_category的动态样式分配")

            # 处理外部依赖
            if not (self.config.include_external_dependencies and state.dependency_analysis.external_dependencies):
                mermaid_lines.append("    empty[\"暂无依赖数据\"]")
                return "\n".join(mermaid_lines)

            # 获取所有依赖并按分类组织
            all_deps = state.dependency_analysis.external_dependencies
            deps_by_category = self._organize_dependencies(all_deps)

            logger.info(f"原始分类数据: {[(cat, len(deps)) for cat, deps in deps_by_category.items()]}")

            # 应用子图分组策略
            grouped_deps = self._group_subgraphs_if_needed(deps_by_category)

            logger.info(f"分组后数据: {[(key, len(deps)) for key, deps in grouped_deps.items()]}")
            logger.info(f"是否发生分组: {len(grouped_deps) != len(deps_by_category) or any('_group_' in key for key in grouped_deps.keys())}")

            # 添加主项目节点
            project_name = self._escape_mermaid_text(state.project_name)
            mermaid_lines.append(f"    project{{{{\"📦 {project_name}\"}}}}")
            mermaid_lines.append("")

            # 创建节点ID映射
            node_ids = {}
            node_counter = 0
            clickable_nodes = []  # 存储可点击的节点信息

            # 为每个分类创建子图（使用分组后的数据）
            all_group_keys = list(grouped_deps.keys())
            logger.info(f"开始创建子图，所有分组键: {all_group_keys}")

            for group_key, deps_with_importance in grouped_deps.items():
                if not deps_with_importance:
                    continue

                logger.info(f"处理分组: {group_key}，包含 {len(deps_with_importance)} 个依赖")

                # 解析分组信息
                base_category, group_info = self._parse_group_key(group_key, all_group_keys)
                logger.info(f"解析结果: 基础分类={base_category}, 分组信息={group_info}")

                # 获取分类样式
                category_style = self.style_config.get_category_style(base_category)
                category_display = base_category.replace('_', ' ').title()
                if category_style.icon:
                    category_display = f"{category_style.icon} {category_display}"

                # 如果是分组子图，添加分组信息
                if group_info:
                    category_display += f" {group_info}"
                    logger.info(f"分组子图标题: {category_display}")
                else:
                    logger.info(f"普通子图标题: {category_display}")

                # 创建子图
                subgraph_id = f"{group_key}_subgraph"
                mermaid_lines.append(f"    subgraph {subgraph_id}[\"{category_display}\"]")
                mermaid_lines.append(f"        direction LR")  # 子图内部水平排列

                logger.info(f"创建子图: {subgraph_id} -> {category_display}")

                # 为该分类的每个依赖创建节点
                for dep, _ in deps_with_importance:
                    node_counter += 1
                    node_id = f"dep_{node_counter}"
                    node_ids[dep.name] = node_id

                    # 创建节点标签
                    dep_name = self._escape_mermaid_text(self._simplify_name(dep.name))
                    dep_version = self._escape_mermaid_text(dep.version) if dep.version else ""

                    if dep_version:
                        node_label = f"{dep_name} v{dep_version}"
                    else:
                        node_label = dep_name

                    # 添加许可证信息
                    if dep.license:
                        license_short = self._escape_mermaid_text(dep.license[:10])
                        node_label += f" ({license_short})"

                    # 获取节点形状
                    node_shape = self._get_mermaid_shape(category_style.shape, node_label)

                    # 添加节点
                    mermaid_lines.append(f"        {node_id}{node_shape}")

                    # 收集可点击节点信息
                    if dep.source_url and dep.source_url.strip():
                        # 验证URL格式并清理
                        source_url = dep.source_url.strip()
                        if source_url.startswith(('http://', 'https://')):
                            clickable_nodes.append({
                                'node_id': node_id,
                                'url': source_url,
                                'name': dep.name,
                                'tooltip': f"打开 {dep.name} 的源代码仓库"
                            })

                    # 如果有安全漏洞，添加警告标记
                    if dep.is_vulnerable:
                        vuln_id = f"vuln_{node_counter}"
                        mermaid_lines.append(f"        {vuln_id}[\"⚠️ 安全风险\"]")
                        mermaid_lines.append(f"        {node_id} -.-> {vuln_id}")

                mermaid_lines.append("    end")
                mermaid_lines.append("")

            # 添加连接关系
            mermaid_lines.append("    %% 依赖关系")

            # 项目到各分类子图的连接（使用分组后的数据）
            for group_key, deps_with_importance in grouped_deps.items():
                if deps_with_importance:
                    # 连接项目到子图外框（使用子图标识符）
                    subgraph_id = f"{group_key}_subgraph"
                    mermaid_lines.append(f"    project --> {subgraph_id}")

            mermaid_lines.append("")

            # 添加样式定义
            mermaid_lines.append("    %% 样式定义")
            mermaid_lines.append("    classDef project fill:#FADBD8,stroke:#E74C3C,color:black,stroke-width:5px,font-size:32px,padding:2px")
            mermaid_lines.append("    classDef vulnerable fill:#F1948A,stroke:#C0392B,color:white,stroke-width:3px")

            # 为每个分类定义样式（基于基础分类）
            processed_categories = set()
            for group_key in grouped_deps.keys():
                base_category, _ = self._parse_group_key(group_key)
                if base_category not in processed_categories:
                    category_style = self.style_config.get_category_style(base_category)
                    mermaid_lines.append(
                        f"    classDef {base_category} fill:{category_style.color},"
                        f"stroke:{category_style.border_color},"
                        f"color:{category_style.text_color}"
                    )
                    processed_categories.add(base_category)

            # 应用样式
            mermaid_lines.append("    %% 应用样式")
            mermaid_lines.append("    class project project")

            # 应用依赖节点样式（使用分组后的数据）
            for group_key, deps_with_importance in grouped_deps.items():
                base_category, _ = self._parse_group_key(group_key)
                for dep, _ in deps_with_importance:
                    if dep.name in node_ids:
                        node_id = node_ids[dep.name]
                        if dep.is_vulnerable:
                            mermaid_lines.append(f"    class {node_id} vulnerable")
                        else:
                            mermaid_lines.append(f"    class {node_id} {base_category}")

            # 添加点击事件
            if clickable_nodes:
                mermaid_lines.append("")
                mermaid_lines.append("    %% 点击事件定义")
                for node_info in clickable_nodes:
                    # 转义URL中的特殊字符
                    escaped_url = self._escape_url_for_mermaid(node_info['url'])
                    escaped_tooltip = self._escape_mermaid_text(node_info['tooltip'])
                    mermaid_lines.append(f"    click {node_info['node_id']} \"{escaped_url}\" \"{escaped_tooltip}\" _blank")

            return "\n".join(mermaid_lines)

        except Exception as e:
            logger.error(f"生成智能化Mermaid依赖图时发生错误: {str(e)}")
            return f"flowchart TB\n    error[\"生成依赖图时发生错误: {str(e)}\"]"

    async def _generate_smart_json_diagram(self, state: DiagramState) -> str:
        """
        生成基于dependency_category的智能化JSON依赖图

        Args:
            state: 当前图表生成状态

        Returns:
            JSON格式的依赖图字符串
        """
        try:
            diagram_data = {
                "project": {
                    "name": state.project_name,
                    "path": state.project_path
                },
                "nodes": [],
                "links": [],
                "groups": [],
                "styles": {},
                "metadata": {
                    "generated_at": datetime.now(timezone.utc).isoformat(),
                    "type": "smart_dependency_diagram",
                    "version": "3.0",
                    "generator": "SmartDependencyDiagramGenerator"
                }
            }

            # 处理外部依赖
            if not (self.config.include_external_dependencies and state.dependency_analysis.external_dependencies):
                diagram_data["metadata"]["warning"] = "暂无依赖数据"
                return json.dumps(diagram_data, ensure_ascii=False, indent=2)

            # 获取所有依赖并按分类组织
            all_deps = state.dependency_analysis.external_dependencies
            deps_by_category = self._organize_dependencies(all_deps)

            # 添加主项目节点
            project_node = {
                "id": "project",
                "name": state.project_name,
                "type": "project",
                "path": state.project_path,
                "importance": 10,
                "category": "project",
                "icon": "📦"
            }
            diagram_data["nodes"].append(project_node)

            # 创建节点ID映射
            node_ids = {"project": "project"}
            next_id = 1

            # 为每个分类添加样式配置
            for category in deps_by_category.keys():
                category_style = self.style_config.get_category_style(category)
                diagram_data["styles"][category] = {
                    "color": category_style.color,
                    "borderColor": category_style.border_color,
                    "textColor": category_style.text_color,
                    "shape": category_style.shape.value,
                    "icon": category_style.icon
                }

            # 添加依赖节点和分组
            for category, deps_with_importance in deps_by_category.items():
                if not deps_with_importance:
                    continue

                # 创建分组
                category_style = self.style_config.get_category_style(category)
                group = {
                    "id": category,
                    "name": category.replace('_', ' ').title(),
                    "type": category,
                    "icon": category_style.icon,
                    "nodes": [],
                    "style": category
                }

                # 添加该分类的所有依赖节点
                for dep, importance in deps_with_importance:
                    node_id = f"{category}_{next_id}"
                    next_id += 1
                    node_ids[dep.name] = node_id

                    # 创建节点
                    node = {
                        "id": node_id,
                        "name": dep.name,
                        "version": dep.version or "",
                        "type": category,
                        "license": dep.license or "",
                        "purpose": dep.purpose or "",
                        "isDev": dep.is_dev_dependency,
                        "isDirect": dep.is_direct,
                        "isVulnerable": dep.is_vulnerable,
                        "importance": importance,
                        "category": dep.dependency_category or "other",
                        "sourceUrl": dep.source_url or "",
                        "style": category
                    }
                    diagram_data["nodes"].append(node)
                    group["nodes"].append(node_id)

                # 添加分组
                diagram_data["groups"].append(group)

            # 添加连接关系
            for category, deps_with_importance in deps_by_category.items():
                if deps_with_importance:
                    # 连接项目到每个分类的第一个依赖
                    first_dep, _ = deps_with_importance[0]
                    if first_dep.name in node_ids:
                        first_node_id = node_ids[first_dep.name]
                        diagram_data["links"].append({
                            "source": "project",
                            "target": first_node_id,
                            "type": "primary",
                            "strength": 3,
                            "category": category
                        })

            # 添加图表配置信息
            diagram_data["config"] = {
                "layout": "force-directed",
                "theme": "smart",
                "nodeSize": "importance",
                "linkStrength": "strength",
                "showLabels": True,
                "showIcons": True,
                "grouping": "category",
                "unlimited": True,  # 标记为无限制模式
                "totalNodes": len(diagram_data["nodes"])  # 实际节点数量
            }

            return json.dumps(diagram_data, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"生成智能化JSON依赖图时发生错误: {str(e)}")
            error_data = {
                "error": f"生成依赖图时发生错误: {str(e)}",
                "metadata": {
                    "generated_at": datetime.now(timezone.utc).isoformat(),
                    "type": "smart_dependency_diagram_error"
                }
            }
            return json.dumps(error_data, ensure_ascii=False, indent=2)
