"""
OAuth认证相关的Schema
"""
from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, field_validator

class OAuthProvider(BaseModel):
    """OAuth提供商信息"""
    
    provider: str = Field(default=None, description="提供商名称")
    name: str = Field(default=None, description="提供商显示名称")
    authorize_url: str = Field(default=None, description="授权URL")
    payload: Optional[Dict[str, Any]] = Field(default=None, description="授权参数")

class OAuthProviderList(BaseModel):
    """OAuth提供商列表"""
    
    providers: List[OAuthProvider] = Field(default_factory=list, description="提供商列表")

class OAuthAuthorizeRequest(BaseModel):
    """OAuth授权请求"""
    
    provider: str = Field(..., description="提供商名称")
    redirect_uri: Optional[str] = Field(None, description="重定向URI")
    state: Optional[str] = Field(None, description="状态参数")
    
    @field_validator("provider")
    @classmethod
    def validate_provider(cls, v: str) -> str:
        """验证提供商"""
        valid_providers = ["wechat", "github"]
        if v not in valid_providers:
            raise ValueError(f"不支持的OAuth提供商: {v}")
        return v

class OAuthCallbackRequest(BaseModel):
    """OAuth回调请求"""
    
    provider: str = Field(..., description="提供商名称")
    code: str = Field(..., description="授权码")
    state: Optional[str] = Field(None, description="状态参数")
    
    @field_validator("provider")
    @classmethod
    def validate_provider(cls, v: str) -> str:
        """验证提供商"""
        valid_providers = ["wechat", "github"]
        if v not in valid_providers:
            raise ValueError(f"不支持的OAuth提供商: {v}")
        return v

class OAuthUserInfo(BaseModel):
    """OAuth用户信息"""
    
    provider: str = Field(..., description="提供商名称")
    provider_user_id: str = Field(..., description="提供商用户ID")
    name: Optional[str] = Field(None, description="用户名称")
    nickname: Optional[str] = Field(None, description="用户昵称")
    email: Optional[str] = Field(None, description="用户邮箱")
    avatar: Optional[str] = Field(None, description="用户头像")
    raw_data: Dict[str, Any] = Field(default_factory=dict, description="原始数据")

class OAuthAccount(BaseModel):
    """OAuth账号信息"""
    
    id: str = Field(..., description="OAuth账号ID")
    user_id: str = Field(..., description="用户ID")
    provider: str = Field(..., description="提供商名称")
    provider_user_id: str = Field(..., description="提供商用户ID")
    is_verified: bool = Field(..., description="是否已验证")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

class OAuthAccountCreate(BaseModel):
    """创建OAuth账号"""
    
    user_id: str = Field(..., description="用户ID")
    provider: str = Field(..., description="提供商名称")
    provider_user_id: str = Field(..., description="提供商用户ID")
    access_token: str = Field(..., description="访问令牌")
    refresh_token: Optional[str] = Field(None, description="刷新令牌")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    user_info: Optional[str] = Field(None, description="用户信息JSON")
    is_verified: bool = Field(default=False, description="是否已验证")

class OAuthAccountUpdate(BaseModel):
    """更新OAuth账号"""
    
    access_token: Optional[str] = Field(None, description="访问令牌")
    refresh_token: Optional[str] = Field(None, description="刷新令牌")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    user_info: Optional[str] = Field(None, description="用户信息JSON")
    is_verified: Optional[bool] = Field(None, description="是否已验证")
