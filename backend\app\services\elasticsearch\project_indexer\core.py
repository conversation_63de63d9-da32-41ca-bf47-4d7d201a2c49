"""
项目搜索器核心模块

提供统一的项目搜索功能接口。
注意：索引管理现在由 Logstash 处理，此模块只负责搜索功能。
"""
import logging
from typing import Dict, Any, Optional

from app.services.elasticsearch.client import ElasticsearchClient
from .search import SearchEngine, SearchResult

# 索引名称常量（由 Logstash 管理）
PROJECT_INDEX_NAME = "modular_projects"

logger = logging.getLogger(__name__)

class ProjectIndexer:
    """
    项目索引器类

    专门负责项目搜索功能，索引管理由 Logstash 处理。
    保持与现有 API 的向后兼容性。
    """

    def __init__(self, es_client: ElasticsearchClient = None):
        """
        初始化项目搜索器

        Args:
            es_client: Elasticsearch客户端，如果为None则创建新实例
        """
        self.es_client = es_client or ElasticsearchClient()
        self.search_engine = SearchEngine(self.es_client)

        # 搜索统计信息
        self.stats = {
            "search_count": 0,          # 搜索次数
            "last_search_at": None,     # 上次搜索时间
        }

    # ==================== 搜索相关方法 ====================

    async def search_projects(self,
                            query: str = None,
                            filters: Dict[str, Any] = None,
                            sort_by: str = None,
                            sort_order: str = "desc",
                            page: int = 1,
                            page_size: int = 10,
                            highlight: bool = True) -> Dict[str, Any]:
        """
        搜索项目

        Args:
            query: 搜索关键词
            filters: 过滤条件
            sort_by: 排序字段
            sort_order: 排序方式
            page: 页码
            page_size: 每页数量
            highlight: 是否高亮匹配文本

        Returns:
            Dict[str, Any]: 搜索结果
        """
        try:
            from datetime import datetime

            # 更新搜索统计
            self.stats["search_count"] += 1
            self.stats["last_search_at"] = datetime.now().isoformat()

            # 执行搜索
            search_result = await self.search_engine.search_projects(
                query=query,
                filters=filters,
                sort_by=sort_by,
                sort_order=sort_order,
                page=page,
                page_size=page_size,
                highlight=highlight
            )

            # 转换为兼容的字典格式
            return {
                "projects": search_result.projects,
                "total": search_result.total,
                "page": search_result.page,
                "page_size": search_result.page_size,
                "total_pages": search_result.total_pages,
                "query": search_result.query,
                "filters": search_result.filters,
                "sort_by": sort_by,
                "sort_order": sort_order,
                "search_time": search_result.search_time,
                "aggregations": search_result.aggregations
            }

        except Exception as e:
            logger.error(f"搜索项目异常: {str(e)}", exc_info=True)
            return {
                "projects": [],
                "total": 0,
                "page": page,
                "page_size": page_size,
                "total_pages": 0,
                "error": str(e)
            }

    # ==================== 统计和监控相关方法 ====================

    async def get_statistics(self) -> Dict[str, Any]:
        """
        获取搜索统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            return {
                "searcher_stats": self.stats,
                "note": "索引管理现在由 Logstash 处理"
            }

        except Exception as e:
            logger.error(f"获取统计信息异常: {str(e)}")
            return {
                "error": str(e),
                "searcher_stats": self.stats
            }

    # ==================== 兼容性方法 ====================

    async def get_project_by_id(self, project_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取项目（兼容性方法）

        Args:
            project_id: 项目ID

        Returns:
            Optional[Dict[str, Any]]: 项目文档
        """
        try:
            result = self.es_client.client.get(
                index=PROJECT_INDEX_NAME,
                id=project_id,
                ignore=[404]
            )

            if result.get("found"):
                return result["_source"]
            else:
                return None

        except Exception as e:
            logger.error(f"获取项目 {project_id} 异常: {str(e)}")
            return None

    async def delete_project_from_index(self, project_id: str) -> bool:
        """
        从 Elasticsearch 索引中删除指定项目的文档

        注意：此方法是辅助性的删除功能。索引管理主要由 Logstash 处理，
        但在某些情况下可能需要手动删除特定文档。

        Args:
            project_id: 要删除的项目ID

        Returns:
            bool: 删除操作是否成功
        """
        try:
            result = self.es_client.client.delete(
                index=PROJECT_INDEX_NAME,
                id=project_id,
                ignore=[404]  # 忽略文档不存在的错误
            )

            # 检查删除结果
            if result.get("result") in ["deleted", "not_found"]:
                if result.get("result") == "deleted":
                    logger.info(f"成功从索引中删除项目文档: {project_id}")
                else:
                    logger.info(f"项目文档不存在，无需删除: {project_id}")
                return True
            else:
                logger.error(f"删除项目文档失败，未知结果: {project_id}, 结果: {result.get('result')}")
                return False

        except Exception as e:
            logger.error(f"删除项目文档异常: {project_id}, 错误: {str(e)}")
            return False

