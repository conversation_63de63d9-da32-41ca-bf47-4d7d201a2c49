#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块合并处理器
负责按目录结构合并模块分析结果
"""
import os
from collections import defaultdict

import structlog

from ..models import AnalysisConfig, AnalysisState
from .base_handler import BaseAnalysisHandler

logger = structlog.get_logger(__name__)

class ModuleMergerHandler(BaseAnalysisHandler):
    """
    模块合并处理器
    负责按目录结构合并模块分析结果
    """

    def __init__(self, config: AnalysisConfig, cache_dir: str = '.cache'):
        """
        初始化模块合并处理器

        Args:
            config: 分析配置
            cache_dir: 缓存目录
        """
        super().__init__(config, cache_dir)

    def initialize(self) -> None:
        """
        初始化模块合并处理器
        该处理器不需要特殊的初始化操作
        """
        pass

    async def process(self, state: AnalysisState) -> AnalysisState:
        """
        处理模块合并

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        # 1. 从缓存获取状态
        cached_state = self.get_cached_state()

        # 2. 执行模块合并
        try:
            # 检查是否需要执行模块合并
            if not state.module_analyses:
                logger.warning("没有模块分析结果，跳过模块合并")
                return state

            # 检查是否可以使用缓存
            if cached_state and cached_state.module_groups and state.is_incremental_analysis:
                # 如果没有模块变化，使用缓存的分组结果
                if not (state.changed_modules or state.new_modules or state.deleted_modules):
                    logger.info("没有模块变化，使用缓存的分组结果")
                    state.module_groups = cached_state.module_groups
                    state.analysis_progress = 0.8  # 模块分组完成，进度80%
                    return state

            logger.info(f"开始模块合并分析，共 {len(state.module_analyses)} 个模块")

            # 按目录结构分组
            logger.info("创建目录结构分组")
            directory_groups = defaultdict(list)
            for module_path in state.module_analyses.keys():
                directory = os.path.dirname(module_path)
                if directory:  # 确保有目录
                    directory_groups[directory].append(module_path)
            
            # 初始化模块分组集合
            state.module_groups = {}
            
            # 按目录名称排序
            sorted_directories = sorted(directory_groups.keys())
            
            # 处理每个目录分组（按排序后的目录遍历）
            for directory in sorted_directories:
                module_paths = directory_groups[directory]
                # 设置组名和标识符
                display_name = f"{directory} 目录模块集合"
                
                # 准备要合并的模块
                modules_to_merge = {path: state.module_analyses[path] for path in module_paths if path in state.module_analyses}
                
                # 保存分组信息到state.module_groups
                state.module_groups[directory] = {
                    "display_name": display_name,
                    "module_paths": module_paths,
                    "module_count": len(modules_to_merge)
                }
                
                # 记录日志
                logger.info(f"已将{len(modules_to_merge)}个目录为'{directory}'的模块添加到分组")

            # 更新进度
            state.analysis_progress = 0.8  # 模块分组完成，进度80%

            # 计算分组总数
            group_count = len(state.module_groups)
            logger.info(f"模块分组分析完成，共创建了 {group_count} 个模块组")
        except Exception as e:
            logger.error(f"模块合并分析失败: {str(e)}")
            state.errors["merge_modules"] = str(e)

        # 3. 设置缓存
        self.set_cached_state(state)

        # 4. 返回分析结果
        return state