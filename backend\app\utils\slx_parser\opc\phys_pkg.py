"""提供一个访问`物理`OPC包（如zip文件）的通用接口。"""

import os
from zipfile import ZIP_DEFLATED, ZipFile, is_zipfile

from app.utils.slx_parser.opc.exceptions import PackageNotFoundError
from app.utils.slx_parser.opc.packuri import CONTENT_TYPES_URI


class PhysPkgReader:
    """物理包读取器对象的工厂类。"""

    def __new__(cls, pkg_file):
        # 如果`pkg_file`是字符串，将其视为路径
        if isinstance(pkg_file, str):
            if os.path.isdir(pkg_file):
                reader_cls = _DirPkgReader
            elif is_zipfile(pkg_file):
                reader_cls = _ZipPkgReader
            else:
                raise PackageNotFoundError("在'%s'未找到包" % pkg_file)
        else:  # 假定它是一个流，传递给Zip读取器处理
            reader_cls = _ZipPkgReader

        return super(PhysPkgReader, cls).__new__(reader_cls)


class PhysPkgWriter:
    """物理包写入器对象的工厂类。"""

    def __new__(cls, pkg_file):
        return super(PhysPkgWriter, cls).__new__(_ZipPkgWriter)


class _DirPkgReader(PhysPkgReader):
    """实现用于已解压到目录中的OPC包的|PhysPkgReader|接口。"""

    def __init__(self, path):
        """`path`是包含展开包的目录的路径。"""
        super(_DirPkgReader, self).__init__()
        self._path = os.path.abspath(path)

    def blob_for(self, pack_uri):
        """返回包目录中对应于`pack_uri`的文件内容。"""
        path = os.path.join(self._path, pack_uri.membername)
        with open(path, "rb") as f:
            blob = f.read()
        return blob

    def close(self):
        """提供与|ZipFileSystem|的接口一致性，但不执行任何操作，
        因为目录文件系统不需要关闭。"""
        pass

    @property
    def content_types_xml(self):
        """从包中返回`[Content_Types].xml`的二进制数据。"""
        return self.blob_for(CONTENT_TYPES_URI)

    def rels_xml_for(self, source_uri):
        """返回具有`source_uri`的源的关系项XML，如果该项没有关系项则返回None。"""
        try:
            rels_xml = self.blob_for(source_uri.rels_uri)
        except IOError:
            rels_xml = None
        return rels_xml


class _ZipPkgReader(PhysPkgReader):
    """实现用于zip文件OPC包的|PhysPkgReader|接口。"""

    def __init__(self, pkg_file):
        super(_ZipPkgReader, self).__init__()
        self._zipf = ZipFile(pkg_file, "r")

    def blob_for(self, pack_uri):
        """返回对应于`pack_uri`的二进制数据。

        如果zip存档中没有匹配的成员，则引发|ValueError|。
        """
        return self._zipf.read(pack_uri.membername)

    def close(self):
        """关闭zip存档，释放它正在使用的任何资源。"""
        self._zipf.close()

    @property
    def content_types_xml(self):
        """从zip包中返回`[Content_Types].xml`的二进制数据。"""
        return self.blob_for(CONTENT_TYPES_URI)

    def rels_xml_for(self, source_uri):
        """返回具有`source_uri`的源的关系项XML，如果没有关系项则返回None。"""
        try:
            rels_xml = self.blob_for(source_uri.rels_uri)
        except KeyError:
            rels_xml = None
        return rels_xml


class _ZipPkgWriter(PhysPkgWriter):
    """实现用于zip文件OPC包的|PhysPkgWriter|接口。"""

    def __init__(self, pkg_file):
        super(_ZipPkgWriter, self).__init__()
        self._zipf = ZipFile(pkg_file, "w", compression=ZIP_DEFLATED)

    def close(self):
        """关闭zip存档，刷新任何待处理的物理写入并释放它正在使用的任何资源。"""
        self._zipf.close()

    def write(self, pack_uri, blob):
        """将`blob`写入此zip包，使用对应于`pack_uri`的成员名。"""
        self._zipf.writestr(pack_uri.membername, blob)
