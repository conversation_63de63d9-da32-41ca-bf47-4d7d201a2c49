"""实现OPC包读写的对象。"""

from __future__ import annotations

from typing import IO, TYPE_CHECKING, Iterator, cast
import os

from app.utils.slx_parser.opc.constants import CONTENT_TYPE, RELATIONSHIP_TYPE as RT
from app.utils.slx_parser.opc.packuri import PACKAGE_URI, PackURI
from app.utils.slx_parser.opc.part import PartFactory
from app.utils.slx_parser.opc.parts.coreprops import CorePropertiesPart
from app.utils.slx_parser.opc.pkgreader import PackageReader
from app.utils.slx_parser.opc.pkgwriter import PackageWriter
from app.utils.slx_parser.opc.rel import Relationships
from app.utils.slx_parser.opc.shared import lazyproperty

if TYPE_CHECKING:
    from app.utils.slx_parser.opc.coreprops import CoreProperties
    from app.utils.slx_parser.opc.part import Part
    from app.utils.slx_parser.opc.rel import _Relationship  # pyright: ignore[reportPrivateUsage]


class OpcPackage:
    """|python-opc|的主要API类。

    通过调用:meth:`open`类方法并传入包文件路径或包含包的类文件对象来构造新实例。
    """

    def __init__(self):
        super(OpcPackage, self).__init__()

    def after_unmarshal(self):
        """反序列化后处理的入口点。

        可以被子类重写而无需调用super。
        """
        # 不要在这里放置任何代码，只是在未被子类重写时捕获调用
        pass

    @property
    def core_properties(self) -> CoreProperties:
        """|CoreProperties|对象，提供对此文档的Dublin Core属性的读写访问。"""
        return self._core_properties_part.core_properties

    def iter_rels(self) -> Iterator[_Relationship]:
        """通过对关系图进行深度优先遍历，生成包中每个关系的一个引用。"""

        def walk_rels(
            source: OpcPackage | Part, visited: list[Part] | None = None
        ) -> Iterator[_Relationship]:
            visited = [] if visited is None else visited
            for rel in source.rels.values():
                yield rel
                if rel.is_external:
                    continue
                part = rel.target_part
                if part in visited:
                    continue
                visited.append(part)
                new_source = part
                for rel in walk_rels(new_source, visited):
                    yield rel

        for rel in walk_rels(self):
            yield rel

    def iter_parts(self) -> Iterator[Part]:
        """通过对关系图进行深度优先遍历，生成包中每个部件的一个引用。"""

        def walk_parts(source, visited=[]):
            for rel in source.rels.values():
                if rel.is_external:
                    continue
                part = rel.target_part
                if part in visited:
                    continue
                visited.append(part)
                yield part
                new_source = part
                for part in walk_parts(new_source, visited):
                    yield part

        for part in walk_parts(self):
            yield part

    def load_rel(self, reltype: str, target: Part | str, rId: str, is_external: bool = False):
        """返回在此部件和`target`之间新添加的类型为`reltype`且键为`rId`的|_Relationship|实例。

        如果`is_external`为|True|，则目标模式设置为``RTM.EXTERNAL``。
        此方法用于从序列化包加载时，其中rId是已知的。在处理过程中添加新关系到包中有其他方法。
        """
        return self.rels.add_relationship(reltype, target, rId, is_external)

    @property
    def main_document_part(self):
        """返回此包的主文档部件的引用。

        示例包括WordprocessingML包的文档部件、PresentationML包的演示文稿部件
        或SpreadsheetML包的工作簿部件。
        """
        return self.part_related_by(RT.OFFICE_DOCUMENT)

    def next_partname(self, template: str) -> PackURI:
        """返回与`template`匹配的|PackURI|实例。

        返回的部件名具有下一个可用的数字后缀，以区分它与其他同类型的部件。
        `template`是一个printf(%)风格的模板字符串，包含一个单一的替换项，
        一个'%d'用于插入部件名的整数部分。示例："/word/header%d.xml"
        """
        partnames = {part.partname for part in self.iter_parts()}
        for n in range(1, len(partnames) + 2):
            candidate_partname = template % n
            if candidate_partname not in partnames:
                return PackURI(candidate_partname)

    @classmethod
    def open(cls, pkg_file: str | IO[bytes]) -> OpcPackage:
        """返回一个加载了`pkg_file`内容的|OpcPackage|实例。"""
        pkg_reader = PackageReader.from_file(pkg_file)
        package = cls()
        Unmarshaller.unmarshal(pkg_reader, package, PartFactory)
        return package

    def part_related_by(self, reltype: str) -> Part:
        """返回此包具有`reltype`关系的部件。

        如果找不到这样的关系则引发|KeyError|，如果找到多个这样的关系则引发|ValueError|。
        """
        return self.rels.part_with_reltype(reltype)

    @property
    def parts(self) -> list[Part]:
        """返回包含此包中每个部件引用的列表。"""
        return list(self.iter_parts())

    def relate_to(self, part: Part, reltype: str):
        """返回到`part`的新建或现有关系的rId键。

        如果到`part`的`reltype`关系已存在，则返回其rId。否则创建一个新关系并返回该rId。
        """
        rel = self.rels.get_or_add(reltype, part)
        return rel.rId

    @lazyproperty
    def rels(self):
        """返回持有此包的关系集合的|Relationships|实例的引用。"""
        return Relationships(PACKAGE_URI.baseURI)

    def save(self, pkg_file: str | IO[bytes]):
        """将此包保存到`pkg_file`。

        `pkg_file`可以是文件路径或类文件对象。
        """
        for part in self.parts:
            part.before_marshal()
        PackageWriter.write(pkg_file, self.rels, self.parts)

    @property
    def _core_properties_part(self) -> CorePropertiesPart:
        """与此包相关的|CorePropertiesPart|对象。

        如果不存在则创建一个默认的核心属性部件（不常见）。
        """
        try:
            return cast(CorePropertiesPart, self.part_related_by(RT.CORE_PROPERTIES))
        except KeyError:
            core_properties_part = CorePropertiesPart.default(self)
            self.relate_to(core_properties_part, RT.CORE_PROPERTIES)
            return core_properties_part


class Unmarshaller:
    """用于从|PackageReader|反序列化包的静态方法的宿主。"""

    @staticmethod
    def unmarshal(pkg_reader, package, part_factory):
        """基于`pkg_reader`的内容构造部件图和实现的关系，将每个部件的构造委托给`part_factory`。

        包关系被添加到`pkg`。
        """
        parts = Unmarshaller._unmarshal_parts(pkg_reader, package, part_factory)
        Unmarshaller._unmarshal_relationships(pkg_reader, package, parts)
        for part in parts.values():
            part.after_unmarshal()
        package.after_unmarshal()

    @staticmethod
    def _unmarshal_parts(pkg_reader, package, part_factory):
        """返回从`pkg_reader`反序列化的|Part|实例字典，以部件名为键。

        副作用是使用`part_factory`构造`pkg_reader`中的每个部件。
        """
        parts = {}
        for partname, content_type, reltype, blob in pkg_reader.iter_sparts():
            parts[partname] = part_factory(partname, content_type, reltype, blob, package)
        return parts

    @staticmethod
    def _unmarshal_relationships(pkg_reader, package, parts):
        """为`pkg_reader`中的每个关系添加一个关系到源对象，
        并将其target_part设置为`parts`中的实际目标部件。"""
        for source_uri, srel in pkg_reader.iter_srels():
            source = package if source_uri == "/" else parts[source_uri]
            target = srel.target_ref if srel.is_external else parts[srel.target_partname]
            source.load_rel(srel.reltype, target, srel.rId, srel.is_external)
