#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dev.to数据源

通过Dev.to API获取技术博客和教程信息
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from urllib.parse import quote_plus
import time

from .external_data_source import ExternalDataSource

logger = logging.getLogger(__name__)


class DevToDataSource(ExternalDataSource):
    """Dev.to数据源类"""
    
    def __init__(self, api_key: Optional[str] = None, timeout: int = 30):
        """
        初始化Dev.to数据源
        
        Args:
            api_key: Dev.to API密钥（可选）
            timeout: 请求超时时间
        """
        super().__init__(timeout)
        self.api_key = api_key
        self.base_url = "https://dev.to/api"
        self.request_delay = 0.5  # API请求间隔（秒）
        
    def get_data_source_name(self) -> str:
        """获取数据源名称"""
        return "DevTo"
    
    async def fetch_data(
        self, 
        project_name: str = None, 
        tags: List[str] = None,
        keywords: List[str] = None
    ) -> Dict[str, Any]:
        """
        获取Dev.to数据
        
        Args:
            project_name: 项目名称
            tags: 相关技术标签
            keywords: 搜索关键词
            
        Returns:
            Dict[str, Any]: Dev.to数据
        """
        try:
            if not project_name and not tags and not keywords:
                logger.warning("缺少搜索参数")
                return {}
            
            logger.info(f"开始获取Dev.to数据: {project_name}")
            
            # 构建搜索查询
            search_queries = self._build_search_queries(project_name, keywords)
            search_tags = tags or []
            
            # 获取各种数据
            data = {
                "articles": await self._fetch_articles(search_queries, search_tags),
                "tag_info": await self._fetch_tag_info(search_tags) if search_tags else [],
                "trending_articles": await self._fetch_trending_articles(search_tags),
                "user_articles": await self._fetch_user_articles(project_name) if project_name else []
            }
            
            # 分析和汇总数据
            data["analysis"] = self._analyze_data(data)
            
            # 缓存数据
            cache_key = f"{project_name}_{','.join(tags or [])}_{','.join(keywords or [])}"
            self.cache_data(cache_key, data)
            
            logger.info(f"Dev.to数据获取完成: {project_name}")
            return data
            
        except Exception as e:
            logger.error(f"获取Dev.to数据失败: {str(e)}")
            return {}
    
    def _build_search_queries(
        self, 
        project_name: Optional[str], 
        keywords: Optional[List[str]]
    ) -> List[str]:
        """构建搜索查询"""
        queries = []
        
        if project_name:
            queries.append(project_name)
            
            # 项目名称 + 常见教程词汇
            tutorial_terms = ["tutorial", "guide", "introduction", "getting started", "how to"]
            for term in tutorial_terms[:3]:
                queries.append(f"{project_name} {term}")
        
        if keywords:
            queries.extend(keywords[:3])
        
        return queries[:5]  # 限制查询数量
    
    async def _fetch_articles(
        self, 
        search_queries: List[str], 
        tags: List[str]
    ) -> List[Dict[str, Any]]:
        """获取相关文章"""
        try:
            all_articles = []
            
            # 通过标签搜索
            for tag in tags[:5]:  # 限制标签数量
                await asyncio.sleep(self.request_delay)
                
                url = f"{self.base_url}/articles"
                params = {
                    "tag": tag,
                    "per_page": 10,
                    "state": "fresh"
                }
                
                headers = {}
                if self.api_key:
                    headers["api-key"] = self.api_key
                
                data = await self._make_request(url, params=params, headers=headers)
                
                if data and isinstance(data, list):
                    for article in data:
                        article_info = {
                            "id": article.get("id"),
                            "title": article.get("title"),
                            "description": article.get("description", "")[:300],
                            "url": article.get("url"),
                            "canonical_url": article.get("canonical_url"),
                            "published_at": article.get("published_at"),
                            "positive_reactions_count": article.get("positive_reactions_count", 0),
                            "public_reactions_count": article.get("public_reactions_count", 0),
                            "comments_count": article.get("comments_count", 0),
                            "reading_time_minutes": article.get("reading_time_minutes", 0),
                            "tag_list": article.get("tag_list", []),
                            "tags": article.get("tags", ""),
                            "user": {
                                "name": article.get("user", {}).get("name"),
                                "username": article.get("user", {}).get("username"),
                                "profile_image": article.get("user", {}).get("profile_image")
                            },
                            "organization": article.get("organization"),
                            "cover_image": article.get("cover_image"),
                            "social_image": article.get("social_image"),
                            "type_of": article.get("type_of", "article")
                        }
                        all_articles.append(article_info)
            
            # 通过搜索查询获取更多文章
            for query in search_queries[:3]:  # 限制查询数量
                await asyncio.sleep(self.request_delay)
                
                # Dev.to没有直接的搜索API，我们通过标签和最新文章来模拟搜索
                url = f"{self.base_url}/articles"
                params = {
                    "per_page": 5,
                    "state": "fresh"
                }
                
                headers = {}
                if self.api_key:
                    headers["api-key"] = self.api_key
                
                data = await self._make_request(url, params=params, headers=headers)
                
                if data and isinstance(data, list):
                    # 过滤包含查询词的文章
                    for article in data:
                        title = article.get("title", "").lower()
                        description = article.get("description", "").lower()
                        tags = article.get("tags", "").lower()
                        
                        if query.lower() in title or query.lower() in description or query.lower() in tags:
                            article_info = {
                                "id": article.get("id"),
                                "title": article.get("title"),
                                "description": article.get("description", "")[:300],
                                "url": article.get("url"),
                                "published_at": article.get("published_at"),
                                "positive_reactions_count": article.get("positive_reactions_count", 0),
                                "comments_count": article.get("comments_count", 0),
                                "reading_time_minutes": article.get("reading_time_minutes", 0),
                                "tag_list": article.get("tag_list", []),
                                "user": {
                                    "name": article.get("user", {}).get("name"),
                                    "username": article.get("user", {}).get("username")
                                },
                                "type_of": article.get("type_of", "article")
                            }
                            all_articles.append(article_info)
            
            # 去重和排序
            unique_articles = {}
            for article in all_articles:
                article_id = article["id"]
                if article_id not in unique_articles:
                    unique_articles[article_id] = article
            
            # 按反应数排序
            sorted_articles = sorted(
                unique_articles.values(),
                key=lambda x: (x["positive_reactions_count"], x["comments_count"]),
                reverse=True
            )
            
            return sorted_articles[:20]
            
        except Exception as e:
            logger.error(f"获取Dev.to文章失败: {str(e)}")
            return []
    
    async def _fetch_tag_info(self, tags: List[str]) -> List[Dict[str, Any]]:
        """获取标签信息"""
        try:
            tag_info = []
            
            for tag in tags[:5]:  # 限制标签数量
                await asyncio.sleep(self.request_delay)
                
                url = f"{self.base_url}/tags/{tag}"
                headers = {}
                if self.api_key:
                    headers["api-key"] = self.api_key
                
                data = await self._make_request(url, headers=headers)
                
                if data:
                    info = {
                        "name": data.get("name"),
                        "id": data.get("id"),
                        "short_summary": data.get("short_summary", ""),
                        "rules_html": data.get("rules_html", ""),
                        "bg_color_hex": data.get("bg_color_hex"),
                        "text_color_hex": data.get("text_color_hex"),
                        "submission_template": data.get("submission_template", "")
                    }
                    tag_info.append(info)
            
            return tag_info
            
        except Exception as e:
            logger.error(f"获取标签信息失败: {str(e)}")
            return []
    
    async def _fetch_trending_articles(self, tags: List[str]) -> List[Dict[str, Any]]:
        """获取热门文章"""
        try:
            await asyncio.sleep(self.request_delay)
            
            url = f"{self.base_url}/articles"
            params = {
                "per_page": 10,
                "top": 7  # 本周热门
            }
            
            headers = {}
            if self.api_key:
                headers["api-key"] = self.api_key
            
            data = await self._make_request(url, params=params, headers=headers)
            
            if data and isinstance(data, list):
                trending_articles = []
                for article in data:
                    # 如果有指定标签，过滤相关文章
                    if tags:
                        article_tags = article.get("tag_list", [])
                        if not any(tag in article_tags for tag in tags):
                            continue
                    
                    article_info = {
                        "id": article.get("id"),
                        "title": article.get("title"),
                        "url": article.get("url"),
                        "positive_reactions_count": article.get("positive_reactions_count", 0),
                        "comments_count": article.get("comments_count", 0),
                        "published_at": article.get("published_at"),
                        "user": {
                            "name": article.get("user", {}).get("name"),
                            "username": article.get("user", {}).get("username")
                        },
                        "tag_list": article.get("tag_list", [])
                    }
                    trending_articles.append(article_info)
                
                return trending_articles[:10]
            
            return []
            
        except Exception as e:
            logger.error(f"获取热门文章失败: {str(e)}")
            return []
    
    async def _fetch_user_articles(self, project_name: str) -> List[Dict[str, Any]]:
        """获取用户相关文章"""
        try:
            # 尝试通过用户名搜索（如果项目名可能是用户名）
            await asyncio.sleep(self.request_delay)
            
            # 由于Dev.to API限制，我们通过最新文章来查找可能相关的用户
            url = f"{self.base_url}/articles"
            params = {
                "per_page": 20,
                "state": "fresh"
            }
            
            headers = {}
            if self.api_key:
                headers["api-key"] = self.api_key
            
            data = await self._make_request(url, params=params, headers=headers)
            
            if data and isinstance(data, list):
                user_articles = []
                for article in data:
                    # 检查用户名或文章内容是否与项目相关
                    username = article.get("user", {}).get("username", "").lower()
                    title = article.get("title", "").lower()
                    description = article.get("description", "").lower()
                    
                    if (project_name.lower() in username or 
                        project_name.lower() in title or 
                        project_name.lower() in description):
                        
                        article_info = {
                            "id": article.get("id"),
                            "title": article.get("title"),
                            "url": article.get("url"),
                            "positive_reactions_count": article.get("positive_reactions_count", 0),
                            "comments_count": article.get("comments_count", 0),
                            "published_at": article.get("published_at"),
                            "user": {
                                "name": article.get("user", {}).get("name"),
                                "username": article.get("user", {}).get("username")
                            }
                        }
                        user_articles.append(article_info)
                
                return user_articles[:5]
            
            return []
            
        except Exception as e:
            logger.error(f"获取用户文章失败: {str(e)}")
            return []
    
    def _analyze_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分析Dev.to数据"""
        try:
            articles = data.get("articles", [])
            trending_articles = data.get("trending_articles", [])
            tag_info = data.get("tag_info", [])
            
            analysis = {
                "content_availability": self._analyze_content_availability(articles),
                "community_engagement": self._analyze_community_engagement(articles),
                "educational_value": self._analyze_educational_value(articles),
                "trending_status": self._analyze_trending_status(trending_articles),
                "tag_popularity": self._analyze_tag_popularity(tag_info)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析Dev.to数据失败: {str(e)}")
            return {}
    
    def _analyze_content_availability(self, articles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析内容可用性"""
        if not articles:
            return {"availability_level": "none", "total_articles": 0}
        
        total_articles = len(articles)
        total_reactions = sum(article.get("positive_reactions_count", 0) for article in articles)
        avg_reactions = total_reactions / total_articles if total_articles > 0 else 0
        
        # 可用性评级
        if total_articles >= 10 and avg_reactions >= 20:
            availability_level = "high"
        elif total_articles >= 5 and avg_reactions >= 10:
            availability_level = "medium"
        elif total_articles >= 1:
            availability_level = "low"
        else:
            availability_level = "none"
        
        return {
            "availability_level": availability_level,
            "total_articles": total_articles,
            "total_reactions": total_reactions,
            "average_reactions": round(avg_reactions, 2)
        }
    
    def _analyze_community_engagement(self, articles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析社区参与度"""
        if not articles:
            return {"engagement_level": "none"}
        
        total_comments = sum(article.get("comments_count", 0) for article in articles)
        total_reactions = sum(article.get("positive_reactions_count", 0) for article in articles)
        total_articles = len(articles)
        
        avg_comments = total_comments / total_articles
        avg_reactions = total_reactions / total_articles
        
        # 参与度评级
        if avg_comments >= 10 and avg_reactions >= 50:
            engagement_level = "very_high"
        elif avg_comments >= 5 and avg_reactions >= 20:
            engagement_level = "high"
        elif avg_comments >= 2 and avg_reactions >= 10:
            engagement_level = "medium"
        elif avg_comments >= 1 or avg_reactions >= 5:
            engagement_level = "low"
        else:
            engagement_level = "very_low"
        
        return {
            "engagement_level": engagement_level,
            "total_comments": total_comments,
            "total_reactions": total_reactions,
            "average_comments_per_article": round(avg_comments, 2),
            "average_reactions_per_article": round(avg_reactions, 2)
        }
    
    def _analyze_educational_value(self, articles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析教育价值"""
        if not articles:
            return {"educational_level": "none"}
        
        # 教育相关关键词
        educational_keywords = [
            "tutorial", "guide", "how to", "introduction", "beginner", "learn",
            "getting started", "step by step", "example", "demo", "walkthrough"
        ]
        
        educational_articles = 0
        total_reading_time = 0
        
        for article in articles:
            title = article.get("title", "").lower()
            description = article.get("description", "").lower()
            content = title + " " + description
            
            if any(keyword in content for keyword in educational_keywords):
                educational_articles += 1
            
            total_reading_time += article.get("reading_time_minutes", 0)
        
        educational_ratio = educational_articles / len(articles)
        avg_reading_time = total_reading_time / len(articles)
        
        # 教育价值评级
        if educational_ratio >= 0.7 and avg_reading_time >= 5:
            educational_level = "very_high"
        elif educational_ratio >= 0.5 and avg_reading_time >= 3:
            educational_level = "high"
        elif educational_ratio >= 0.3:
            educational_level = "medium"
        elif educational_ratio >= 0.1:
            educational_level = "low"
        else:
            educational_level = "very_low"
        
        return {
            "educational_level": educational_level,
            "educational_articles": educational_articles,
            "total_articles": len(articles),
            "educational_ratio": round(educational_ratio, 2),
            "average_reading_time": round(avg_reading_time, 2)
        }
    
    def _analyze_trending_status(self, trending_articles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析趋势状态"""
        if not trending_articles:
            return {"trending_level": "none"}
        
        total_trending = len(trending_articles)
        total_reactions = sum(article.get("positive_reactions_count", 0) for article in trending_articles)
        
        # 趋势评级
        if total_trending >= 5 and total_reactions >= 500:
            trending_level = "viral"
        elif total_trending >= 3 and total_reactions >= 200:
            trending_level = "trending"
        elif total_trending >= 1 and total_reactions >= 50:
            trending_level = "popular"
        else:
            trending_level = "normal"
        
        return {
            "trending_level": trending_level,
            "trending_articles_count": total_trending,
            "total_trending_reactions": total_reactions
        }
    
    def _analyze_tag_popularity(self, tag_info: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析标签流行度"""
        if not tag_info:
            return {"popularity_level": "unknown"}
        
        # 基于标签数量和描述长度评估流行度
        tags_with_description = sum(1 for tag in tag_info if tag.get("short_summary"))
        
        if tags_with_description >= 3:
            popularity_level = "high"
        elif tags_with_description >= 1:
            popularity_level = "medium"
        else:
            popularity_level = "low"
        
        return {
            "popularity_level": popularity_level,
            "total_tags": len(tag_info),
            "tags_with_description": tags_with_description
        }
    
    async def is_available(self) -> bool:
        """检查Dev.to API是否可用"""
        try:
            url = f"{self.base_url}/articles"
            params = {"per_page": 1}
            
            headers = {}
            if self.api_key:
                headers["api-key"] = self.api_key
            
            data = await self._make_request(url, params=params, headers=headers)
            return data is not None
            
        except Exception as e:
            logger.error(f"检查Dev.to API可用性失败: {str(e)}")
            return False
