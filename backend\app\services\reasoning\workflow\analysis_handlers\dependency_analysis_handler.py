#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖分析处理器
负责分析项目的依赖关系和技术栈
"""
import structlog
from typing import Optional

from ..models import AnalysisConfig, AnalysisState
from .base_handler import BaseAnalysisHandler
from ....ai_agent_core import agent_manager
from ...analyzers.project_dependency_analyzer import ProjectDependencyAnalyzer

logger = structlog.get_logger(__name__)

class DependencyAnalysisHandler(BaseAnalysisHandler):
    """
    依赖分析处理器
    负责分析项目的依赖关系和技术栈
    """

    def __init__(self, config: AnalysisConfig, cache_dir: str = '.cache'):
        """
        初始化依赖分析处理器

        Args:
            config: 分析配置
            cache_dir: 缓存目录
        """
        self.dependency_analyzer: Optional[ProjectDependencyAnalyzer] = None
        super().__init__(config, cache_dir)

    def initialize(self) -> None:
        """
        初始化依赖分析器
        """
        try:
            self.dependency_analyzer = agent_manager.load("ProjectDependencyAnalyzerAgent")
            logger.info("依赖分析器初始化完成")
        except Exception as e:
            logger.error(f"依赖分析器初始化失败: {str(e)}")
            raise

    async def process(self, state: AnalysisState) -> AnalysisState:
        """
        处理依赖分析

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        # 1. 从缓存获取状态
        cached_state = self.get_cached_state()

        # 2. 执行依赖分析
        try:
            # 检查是否需要执行依赖分析
            if not self.config.analyze_dependencies:
                logger.info("配置指定不分析依赖，跳过依赖分析")
                return state

            # 检查是否可以使用缓存
            if cached_state and cached_state.dependency_analysis and state.is_incremental_analysis:
                # 检查变化比例
                total_files = len(state.files) if state.files else 1
                changed_files_count = len(state.changed_modules) + len(state.new_modules) + len(state.deleted_modules)
                change_ratio = changed_files_count / total_files

                # 如果变化比例小于10%，使用缓存的依赖分析结果
                if change_ratio < 0.1:
                    logger.info(f"变化比例为 {change_ratio:.2%}，低于阈值，使用缓存的依赖分析结果")
                    state.dependency_analysis = cached_state.dependency_analysis
                    state.analysis_progress = 0.8  # 依赖分析完成，进度80%
                    return state

            # 执行依赖分析
            state = await self._analyze_dependencies(state)

            logger.info("项目依赖分析完成")
        except Exception as e:
            logger.error(f"依赖分析失败: {str(e)}")
            state.errors["dependency_analysis"] = str(e)
            raise

        # 3. 设置缓存
        self.set_cached_state(state)

        # 4. 返回分析结果
        return state

    async def _analyze_dependencies(self, state: AnalysisState) -> AnalysisState:
        """
        分析项目依赖
        从high_level_orchestrator.py迁移的核心依赖分析逻辑

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        logger.info("开始项目依赖分析")

        try:
            # 检查依赖分析器是否已初始化
            if self.dependency_analyzer is None:
                raise ValueError("依赖分析器未初始化")

            # 准备输入数据
            input_data = {
                "parameters": {
                    "project_name": state.project_name,
                    "project_path": state.project_path,
                    "structure_analysis": state.structure_analysis,
                }
            }

            # 调用依赖分析器
            agent_input = await self.dependency_analyzer.prepare_input(input_data)
            output = await self.dependency_analyzer.process(agent_input)
            dependency_analysis = output.response.result

            # 保存分析结果
            state.dependency_analysis = dependency_analysis

            # 更新进度
            state.analysis_progress = 0.8
            logger.info("项目依赖分析完成")
        except Exception as e:
            logger.error(f"项目依赖分析失败: {str(e)}")
            state.errors["dependency_analysis"] = str(e)
            raise

        return state
