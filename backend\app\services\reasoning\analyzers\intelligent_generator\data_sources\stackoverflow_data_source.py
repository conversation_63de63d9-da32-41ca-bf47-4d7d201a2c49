#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Stack Overflow数据源

通过Stack Exchange API获取项目相关的问题和讨论信息
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from urllib.parse import quote_plus
import time

from .external_data_source import ExternalDataSource

logger = logging.getLogger(__name__)


class StackOverflowDataSource(ExternalDataSource):
    """Stack Overflow数据源类"""
    
    def __init__(self, api_key: Optional[str] = None, timeout: int = 30):
        """
        初始化Stack Overflow数据源
        
        Args:
            api_key: Stack Exchange API密钥（可选，用于提高请求限制）
            timeout: 请求超时时间
        """
        super().__init__(timeout)
        self.api_key = api_key
        self.base_url = "https://api.stackexchange.com/2.3"
        self.site = "stackoverflow"
        self.request_delay = 0.1  # API请求间隔（秒）
        
    def get_data_source_name(self) -> str:
        """获取数据源名称"""
        return "StackOverflow"
    
    async def fetch_data(
        self, 
        project_name: str = None, 
        tags: List[str] = None,
        keywords: List[str] = None
    ) -> Dict[str, Any]:
        """
        获取Stack Overflow数据
        
        Args:
            project_name: 项目名称
            tags: 相关技术标签
            keywords: 搜索关键词
            
        Returns:
            Dict[str, Any]: Stack Overflow数据
        """
        try:
            if not project_name and not tags and not keywords:
                logger.warning("缺少搜索参数")
                return {}
            
            logger.info(f"开始获取Stack Overflow数据: {project_name}")
            
            # 构建搜索查询
            search_queries = self._build_search_queries(project_name, tags, keywords)
            
            # 获取各种数据
            data = {
                "questions": await self._fetch_questions(search_queries),
                "tags_info": await self._fetch_tags_info(tags) if tags else [],
                "search_stats": await self._fetch_search_stats(search_queries),
                "related_technologies": await self._fetch_related_technologies(tags) if tags else []
            }
            
            # 分析和汇总数据
            data["analysis"] = self._analyze_data(data)
            
            # 缓存数据
            cache_key = f"{project_name}_{','.join(tags or [])}_{','.join(keywords or [])}"
            self.cache_data(cache_key, data)
            
            logger.info(f"Stack Overflow数据获取完成: {project_name}")
            return data
            
        except Exception as e:
            logger.error(f"获取Stack Overflow数据失败: {str(e)}")
            return {}
    
    def _build_search_queries(
        self, 
        project_name: Optional[str], 
        tags: Optional[List[str]], 
        keywords: Optional[List[str]]
    ) -> List[str]:
        """构建搜索查询"""
        queries = []
        
        if project_name:
            # 项目名称查询
            queries.append(project_name)
            
            # 项目名称 + 常见问题词汇
            common_terms = ["error", "issue", "problem", "how to", "tutorial", "example"]
            for term in common_terms[:3]:  # 限制查询数量
                queries.append(f"{project_name} {term}")
        
        if keywords:
            queries.extend(keywords[:3])  # 限制关键词数量
        
        return queries[:5]  # 总共限制查询数量
    
    async def _fetch_questions(self, search_queries: List[str]) -> List[Dict[str, Any]]:
        """获取相关问题"""
        try:
            all_questions = []
            
            for query in search_queries:
                await asyncio.sleep(self.request_delay)  # 速率限制
                
                url = f"{self.base_url}/search"
                params = {
                    "order": "desc",
                    "sort": "relevance",
                    "intitle": query,
                    "site": self.site,
                    "pagesize": 10,  # 限制每个查询的结果数量
                    "filter": "withbody"  # 包含问题内容
                }
                
                if self.api_key:
                    params["key"] = self.api_key
                
                data = await self._make_request(url, params=params)
                
                if data and "items" in data:
                    questions = []
                    for item in data["items"]:
                        question = {
                            "question_id": item.get("question_id"),
                            "title": item.get("title"),
                            "body": item.get("body", "")[:500],  # 限制内容长度
                            "score": item.get("score", 0),
                            "view_count": item.get("view_count", 0),
                            "answer_count": item.get("answer_count", 0),
                            "tags": item.get("tags", []),
                            "creation_date": item.get("creation_date"),
                            "last_activity_date": item.get("last_activity_date"),
                            "is_answered": item.get("is_answered", False),
                            "accepted_answer_id": item.get("accepted_answer_id"),
                            "link": item.get("link")
                        }
                        questions.append(question)
                    
                    all_questions.extend(questions)
            
            # 去重和排序
            unique_questions = {}
            for q in all_questions:
                qid = q["question_id"]
                if qid not in unique_questions or q["score"] > unique_questions[qid]["score"]:
                    unique_questions[qid] = q
            
            # 按评分排序，返回前20个
            sorted_questions = sorted(
                unique_questions.values(), 
                key=lambda x: (x["score"], x["view_count"]), 
                reverse=True
            )
            
            return sorted_questions[:20]
            
        except Exception as e:
            logger.error(f"获取Stack Overflow问题失败: {str(e)}")
            return []
    
    async def _fetch_tags_info(self, tags: List[str]) -> List[Dict[str, Any]]:
        """获取标签信息"""
        try:
            if not tags:
                return []
            
            await asyncio.sleep(self.request_delay)
            
            # 限制标签数量
            limited_tags = tags[:10]
            tags_param = ";".join(limited_tags)
            
            url = f"{self.base_url}/tags/{tags_param}/info"
            params = {
                "site": self.site,
                "filter": "default"
            }
            
            if self.api_key:
                params["key"] = self.api_key
            
            data = await self._make_request(url, params=params)
            
            if data and "items" in data:
                tags_info = []
                for item in data["items"]:
                    tag_info = {
                        "name": item.get("name"),
                        "count": item.get("count", 0),
                        "excerpt": item.get("excerpt"),
                        "wiki_post_id": item.get("wiki_post_id"),
                        "is_moderator_only": item.get("is_moderator_only", False),
                        "is_required": item.get("is_required", False)
                    }
                    tags_info.append(tag_info)
                
                return tags_info
            
            return []
            
        except Exception as e:
            logger.error(f"获取标签信息失败: {str(e)}")
            return []
    
    async def _fetch_search_stats(self, search_queries: List[str]) -> Dict[str, Any]:
        """获取搜索统计信息"""
        try:
            total_results = 0
            avg_score = 0
            total_views = 0
            
            for query in search_queries[:3]:  # 限制查询数量
                await asyncio.sleep(self.request_delay)
                
                url = f"{self.base_url}/search"
                params = {
                    "order": "desc",
                    "sort": "relevance",
                    "intitle": query,
                    "site": self.site,
                    "pagesize": 1,  # 只获取统计信息
                    "filter": "total"
                }
                
                if self.api_key:
                    params["key"] = self.api_key
                
                data = await self._make_request(url, params=params)
                
                if data:
                    total_results += data.get("total", 0)
                    if "items" in data and data["items"]:
                        item = data["items"][0]
                        avg_score += item.get("score", 0)
                        total_views += item.get("view_count", 0)
            
            return {
                "total_questions": total_results,
                "average_score": avg_score / len(search_queries) if search_queries else 0,
                "total_views": total_views,
                "search_queries_count": len(search_queries)
            }
            
        except Exception as e:
            logger.error(f"获取搜索统计失败: {str(e)}")
            return {}
    
    async def _fetch_related_technologies(self, tags: List[str]) -> List[Dict[str, Any]]:
        """获取相关技术标签"""
        try:
            if not tags:
                return []
            
            await asyncio.sleep(self.request_delay)
            
            # 获取与给定标签相关的其他标签
            primary_tag = tags[0]  # 使用第一个标签作为主要标签
            
            url = f"{self.base_url}/tags/{primary_tag}/related"
            params = {
                "site": self.site,
                "pagesize": 10
            }
            
            if self.api_key:
                params["key"] = self.api_key
            
            data = await self._make_request(url, params=params)
            
            if data and "items" in data:
                related_tags = []
                for item in data["items"]:
                    tag_info = {
                        "name": item.get("name"),
                        "count": item.get("count", 0)
                    }
                    related_tags.append(tag_info)
                
                return related_tags
            
            return []
            
        except Exception as e:
            logger.error(f"获取相关技术失败: {str(e)}")
            return []
    
    def _analyze_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分析Stack Overflow数据"""
        try:
            questions = data.get("questions", [])
            tags_info = data.get("tags_info", [])
            search_stats = data.get("search_stats", {})
            
            analysis = {
                "community_activity": self._analyze_community_activity(questions),
                "common_issues": self._analyze_common_issues(questions),
                "technology_popularity": self._analyze_technology_popularity(tags_info),
                "difficulty_indicators": self._analyze_difficulty_indicators(questions),
                "community_support": self._analyze_community_support(questions, search_stats)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析Stack Overflow数据失败: {str(e)}")
            return {}
    
    def _analyze_community_activity(self, questions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析社区活跃度"""
        if not questions:
            return {"activity_level": "low", "total_questions": 0}
        
        total_questions = len(questions)
        total_views = sum(q.get("view_count", 0) for q in questions)
        avg_score = sum(q.get("score", 0) for q in questions) / total_questions
        answered_ratio = sum(1 for q in questions if q.get("is_answered", False)) / total_questions
        
        # 活跃度评级
        if total_questions >= 50 and avg_score >= 5:
            activity_level = "high"
        elif total_questions >= 20 and avg_score >= 2:
            activity_level = "medium"
        else:
            activity_level = "low"
        
        return {
            "activity_level": activity_level,
            "total_questions": total_questions,
            "total_views": total_views,
            "average_score": round(avg_score, 2),
            "answered_ratio": round(answered_ratio, 2)
        }
    
    def _analyze_common_issues(self, questions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析常见问题"""
        if not questions:
            return []
        
        # 按评分和浏览量排序，获取热门问题
        top_questions = sorted(
            questions, 
            key=lambda x: (x.get("score", 0) + x.get("view_count", 0) / 1000), 
            reverse=True
        )[:5]
        
        common_issues = []
        for q in top_questions:
            issue = {
                "title": q.get("title", ""),
                "score": q.get("score", 0),
                "view_count": q.get("view_count", 0),
                "answer_count": q.get("answer_count", 0),
                "is_answered": q.get("is_answered", False),
                "tags": q.get("tags", []),
                "link": q.get("link", "")
            }
            common_issues.append(issue)
        
        return common_issues
    
    def _analyze_technology_popularity(self, tags_info: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析技术流行度"""
        if not tags_info:
            return {"popularity_level": "unknown", "total_usage": 0}
        
        total_usage = sum(tag.get("count", 0) for tag in tags_info)
        avg_usage = total_usage / len(tags_info)
        
        # 流行度评级（基于Stack Overflow的标签使用量）
        if avg_usage >= 10000:
            popularity_level = "very_high"
        elif avg_usage >= 5000:
            popularity_level = "high"
        elif avg_usage >= 1000:
            popularity_level = "medium"
        elif avg_usage >= 100:
            popularity_level = "low"
        else:
            popularity_level = "very_low"
        
        return {
            "popularity_level": popularity_level,
            "total_usage": total_usage,
            "average_usage": round(avg_usage, 2),
            "tags_analyzed": len(tags_info)
        }
    
    def _analyze_difficulty_indicators(self, questions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析技术难度指标"""
        if not questions:
            return {"difficulty_level": "unknown"}
        
        # 分析问题的复杂度指标
        unanswered_ratio = sum(1 for q in questions if not q.get("is_answered", False)) / len(questions)
        low_score_ratio = sum(1 for q in questions if q.get("score", 0) <= 0) / len(questions)
        avg_answer_count = sum(q.get("answer_count", 0) for q in questions) / len(questions)
        
        # 难度评级
        if unanswered_ratio >= 0.5 or low_score_ratio >= 0.4:
            difficulty_level = "high"
        elif unanswered_ratio >= 0.3 or avg_answer_count <= 1:
            difficulty_level = "medium"
        else:
            difficulty_level = "low"
        
        return {
            "difficulty_level": difficulty_level,
            "unanswered_ratio": round(unanswered_ratio, 2),
            "low_score_ratio": round(low_score_ratio, 2),
            "average_answer_count": round(avg_answer_count, 2)
        }
    
    def _analyze_community_support(
        self, 
        questions: List[Dict[str, Any]], 
        search_stats: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析社区支持度"""
        if not questions:
            return {"support_level": "unknown"}
        
        total_questions = search_stats.get("total_questions", len(questions))
        answered_questions = sum(1 for q in questions if q.get("is_answered", False))
        avg_answer_count = sum(q.get("answer_count", 0) for q in questions) / len(questions)
        
        # 支持度评级
        if total_questions >= 100 and answered_questions / len(questions) >= 0.7:
            support_level = "excellent"
        elif total_questions >= 50 and answered_questions / len(questions) >= 0.5:
            support_level = "good"
        elif total_questions >= 20:
            support_level = "moderate"
        else:
            support_level = "limited"
        
        return {
            "support_level": support_level,
            "total_questions": total_questions,
            "answered_questions": answered_questions,
            "answer_ratio": round(answered_questions / len(questions), 2) if questions else 0,
            "average_answers_per_question": round(avg_answer_count, 2)
        }
    
    async def is_available(self) -> bool:
        """检查Stack Exchange API是否可用"""
        try:
            url = f"{self.base_url}/info"
            params = {"site": self.site}
            
            if self.api_key:
                params["key"] = self.api_key
            
            data = await self._make_request(url, params=params)
            return data is not None
            
        except Exception as e:
            logger.error(f"检查Stack Exchange API可用性失败: {str(e)}")
            return False
