import os
from typing import Dict, List, Optional, Tuple

import aiohttp
import structlog
from github import Github
from github.ContentFile import ContentFile
from github.Repository import Repository

from app.core.config import settings
from app.exceptions.git import GitHubError
from app.schemas.git import GitFileInfo, GitFileContent

logger = structlog.get_logger(__name__)


class GitHubService:
    """GitHub服务"""
    
    def __init__(self, token: Optional[str] = None):
        self.token = token or settings.github.GITHUB_TOKEN
        if not self.token:
            logger.warning("GitHub令牌未配置")
        self.github = Github(self.token) if self.token else Github()
    
    def get_repository(self, repo_url: str) -> Repository:
        """获取GitHub仓库"""
        try:
            # 从URL中提取仓库名称
            # 格式: https://github.com/username/repo
            parts = repo_url.strip("/").split("/")
            if "github.com" not in parts:
                raise GitHubError(message="无效的GitHub仓库URL")
            
            # 获取用户名和仓库名
            github_index = parts.index("github.com")
            if len(parts) < github_index + 3:
                raise GitHubError(message="无效的GitHub仓库URL")
            
            username = parts[github_index + 1]
            repo_name = parts[github_index + 2]
            full_name = f"{username}/{repo_name}"
            
            logger.info(f"获取GitHub仓库", repo=full_name)
            return self.github.get_repo(full_name)
        except Exception as e:
            logger.error("获取GitHub仓库失败", error=str(e))
            raise GitHubError(message=f"获取GitHub仓库失败: {str(e)}")
    
    def get_repository_files(self, repo: Repository, path: str = "") -> List[GitFileInfo]:
        """获取仓库文件列表"""
        try:
            contents = repo.get_contents(path)
            result = []
            
            for content in contents:
                if content.type == "file":
                    item = GitFileInfo(
                        name=content.name,
                        path=content.path,
                        type="file",
                        size=content.size,
                    )
                else:  # 目录
                    children = self.get_repository_files(repo, content.path)
                    item = GitFileInfo(
                        name=content.name,
                        path=content.path,
                        type="dir",
                        children=children
                    )
                
                result.append(item)
            
            return result
        except Exception as e:
            logger.error("获取仓库文件列表失败", error=str(e))
            raise GitHubError(message=f"获取仓库文件列表失败: {str(e)}")
    
    def get_file_content(self, repo: Repository, path: str) -> GitFileContent:
        """获取文件内容"""
        try:
            content_file = repo.get_contents(path)
            if isinstance(content_file, list):
                raise GitHubError(message=f"路径 {path} 指向一个目录，而不是文件")
            
            content = content_file.decoded_content.decode("utf-8", errors="replace")
            return GitFileContent(
                name=content_file.name,
                content=content,
                path=content_file.path
            )
        except Exception as e:
            logger.error("获取文件内容失败", error=str(e), path=path)
            raise GitHubError(message=f"获取文件内容失败: {str(e)}")
    
    async def clone_repository(self, repo_url: str, target_dir: str) -> str:
        """克隆仓库到本地"""
        try:
            repo = self.get_repository(repo_url)
            clone_url = repo.clone_url
            
            if self.token:
                # 将令牌添加到克隆URL
                clone_url = clone_url.replace("https://", f"https://{self.token}@")
            
            # 确保目标目录存在
            os.makedirs(target_dir, exist_ok=True)
            
            # 构建克隆命令
            repo_dir = os.path.join(target_dir, repo.name)
            
            # 使用git命令克隆
            import subprocess
            
            logger.info("克隆仓库", repo=repo.full_name, target=repo_dir)
            process = subprocess.Popen(
                ["git", "clone", clone_url, repo_dir],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
            stdout, stderr = process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode("utf-8", errors="replace")
                logger.error("克隆仓库失败", error=error_msg)
                raise GitHubError(message=f"克隆仓库失败: {error_msg}")
            
            return repo_dir
        except Exception as e:
            logger.error("克隆仓库失败", error=str(e))
            raise GitHubError(message=f"克隆仓库失败: {str(e)}")
