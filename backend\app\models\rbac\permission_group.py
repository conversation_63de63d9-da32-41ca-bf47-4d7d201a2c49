"""
权限相关模型
包含权限组(PermissionGroupModel)和权限(PermissionModel)
"""
from datetime import datetime, timezone
from sqlalchemy import ForeignKey, UniqueConstraint, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.model_base import ModelBase
from app.models.rbac.associations import role_permission_group

class PermissionGroupModel(ModelBase):
    """
    权限组模型
    用于对权限进行分类管理，例如：用户管理、订单管理等
    """
    __tablename__ = 'permission_groups'
    
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False, index=True, comment='权限组名称')
    code: Mapped[str] = mapped_column(String(100), unique=True, nullable=False, index=True, comment='权限组标识符(小写英文+下划线)')
    description: Mapped[str | None] = mapped_column(String(200), default=None, comment='描述信息')
    sort_order: Mapped[int] = mapped_column(default=0, comment='排序号')
    is_active: Mapped[bool] = mapped_column(default=True, comment='是否启用')

    # 关系定义
    permissions: Mapped[list['PermissionModel']] = relationship(
        back_populates='group',
        cascade='all, delete-orphan',
        order_by='PermissionModel.sort_order'
    )
    roles: Mapped[list['RoleModel']] = relationship(
        'RoleModel',
        secondary=role_permission_group,
        back_populates='permission_groups'
    )