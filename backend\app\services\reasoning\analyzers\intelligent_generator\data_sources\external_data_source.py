#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外部数据源基类

为各种外部数据源提供统一接口
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import asyncio
import aiohttp

logger = logging.getLogger(__name__)


class ExternalDataSource(ABC):
    """外部数据源基类"""
    
    def __init__(self, timeout: int = 30):
        """
        初始化外部数据源
        
        Args:
            timeout: 请求超时时间（秒）
        """
        self.timeout = timeout
        self.session: Optional[aiohttp.ClientSession] = None
        self.data_cache: Dict[str, Any] = {}
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._create_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self._close_session()
    
    async def _create_session(self):
        """创建HTTP会话"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def _close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
            self.session = None
    
    @abstractmethod
    async def fetch_data(self, **kwargs) -> Dict[str, Any]:
        """
        获取外部数据
        
        Returns:
            Dict[str, Any]: 获取的数据
        """
        pass
    
    @abstractmethod
    def get_data_source_name(self) -> str:
        """获取数据源名称"""
        pass
    
    async def _make_request(
        self, 
        url: str, 
        method: str = "GET", 
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        发起HTTP请求
        
        Args:
            url: 请求URL
            method: HTTP方法
            headers: 请求头
            params: 查询参数
            json_data: JSON数据
            
        Returns:
            Optional[Dict[str, Any]]: 响应数据
        """
        try:
            if not self.session:
                await self._create_session()
            
            async with self.session.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                json=json_data
            ) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 404:
                    logger.warning(f"资源未找到: {url}")
                    return None
                elif response.status == 403:
                    logger.warning(f"访问被拒绝: {url}")
                    return None
                elif response.status == 429:
                    logger.warning(f"请求频率限制: {url}")
                    return None
                else:
                    logger.error(f"请求失败: {url}, 状态码: {response.status}")
                    return None
                    
        except asyncio.TimeoutError:
            logger.error(f"请求超时: {url}")
            return None
        except Exception as e:
            logger.error(f"请求异常: {url}, 错误: {str(e)}")
            return None
    
    def cache_data(self, key: str, data: Any):
        """缓存数据"""
        self.data_cache[key] = data
    
    def get_cached_data(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        return self.data_cache.get(key)
    
    def clear_cache(self):
        """清空缓存"""
        self.data_cache.clear()
    
    async def is_available(self) -> bool:
        """检查数据源是否可用"""
        try:
            # 子类可以重写此方法进行特定的可用性检查
            return True
        except Exception as e:
            logger.error(f"检查数据源可用性失败: {str(e)}")
            return False
