#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多数据源集成服务

整合项目内部数据和外部数据源，为README生成提供全面的信息支持
"""

from .internal_data_source import InternalDataSource
from .external_data_source import ExternalDataSource
from .github_data_source import GitHubDataSource
from .package_registry_source import PackageRegistrySource
from .stackoverflow_data_source import StackOverflowDataSource
from .reddit_data_source import RedditDataSource
from .hackernews_data_source import HackerNewsDataSource
from .devto_data_source import DevToDataSource
from .producthunt_data_source import ProductHuntDataSource
from .data_source_manager import DataSourceManager

__all__ = [
    "InternalDataSource",
    "ExternalDataSource",
    "GitHubDataSource",
    "PackageRegistrySource",
    "StackOverflowDataSource",
    "RedditDataSource",
    "HackerNewsDataSource",
    "DevToDataSource",
    "ProductHuntDataSource",
    "DataSourceManager"
]
