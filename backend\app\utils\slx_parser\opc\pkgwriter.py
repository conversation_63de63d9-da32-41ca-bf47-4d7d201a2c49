"""提供序列化（OPC）包的低级只写API。

OPC代表开放包装约定（Open Packaging Convention）。
本质上，这是OpcPackage.save()的一个实现。
"""

from __future__ import annotations

from typing import TYPE_CHECKING, Iterable

from app.utils.slx_parser.opc.constants import CONTENT_TYPE as CT
from app.utils.slx_parser.opc.oxml import CT_Types, serialize_part_xml
from app.utils.slx_parser.opc.packuri import CONTENT_TYPES_URI, PACKAGE_URI
from app.utils.slx_parser.opc.phys_pkg import PhysPkgWriter
from app.utils.slx_parser.opc.shared import CaseInsensitiveDict
from app.utils.slx_parser.opc.spec import default_content_types

if TYPE_CHECKING:
    from app.utils.slx_parser.opc.part import Part


class PackageWriter:
    """将zip格式的OPC包写入`pkg_file`，其中`pkg_file`可以是zip文件的路径（字符串）
    或类文件对象。

    其唯一的API方法:meth:`write`是静态的，因此此类不打算被实例化。
    """

    @staticmethod
    def write(pkg_file, pkg_rels, parts):
        """将包含`pkg_rels`和`parts`的物理包（.pptx文件）写入`pkg_file`，
        并基于部件的内容类型写入内容类型流。"""
        phys_writer = PhysPkgWriter(pkg_file)
        PackageWriter._write_content_types_stream(phys_writer, parts)
        PackageWriter._write_pkg_rels(phys_writer, pkg_rels)
        PackageWriter._write_parts(phys_writer, parts)
        phys_writer.close()

    @staticmethod
    def _write_content_types_stream(phys_writer, parts):
        """将``[Content_Types].xml``部件写入物理包，为`parts`中的每个部件
        提供适当的内容类型查找目标。"""
        cti = _ContentTypesItem.from_parts(parts)
        phys_writer.write(CONTENT_TYPES_URI, cti.blob)

    @staticmethod
    def _write_parts(phys_writer: PhysPkgWriter, parts: Iterable[Part]):
        """将`parts`中每个部件的二进制数据写入包中，如果它有任何关系，
        则同时写入其关系的rels项。"""
        for part in parts:
            phys_writer.write(part.partname, part.blob)
            if len(part.rels):
                phys_writer.write(part.partname.rels_uri, part.rels.xml)

    @staticmethod
    def _write_pkg_rels(phys_writer, pkg_rels):
        """将`pkg_rels`的XML关系项（'/_rels/.rels'）写入包中。"""
        phys_writer.write(PACKAGE_URI.rels_uri, pkg_rels.xml)


class _ContentTypesItem:
    """基于部件列表组合内容类型项（[Content_Types].xml）的服务类。

    不打算直接实例化，其唯一的接口方法是xml_for()，
    例如``_ContentTypesItem.xml_for(parts)``。
    """

    def __init__(self):
        self._defaults = CaseInsensitiveDict()
        self._overrides = {}

    @property
    def blob(self):
        """返回此内容类型项的XML形式，适合作为``[Content_Types].xml``存储在OPC包中。"""
        return serialize_part_xml(self._element)

    @classmethod
    def from_parts(cls, parts):
        """返回内容类型XML，将`parts`中的每个部件映射到适当的内容类型，
        适合作为``[Content_Types].xml``存储在OPC包中。"""
        cti = cls()
        cti._defaults["rels"] = CT.OPC_RELATIONSHIPS
        cti._defaults["xml"] = CT.XML
        for part in parts:
            cti._add_content_type(part.partname, part.content_type)
        return cti

    def _add_content_type(self, partname, content_type):
        """为具有`partname`和`content_type`的部件添加内容类型，
        根据需要使用默认值或覆盖值。"""
        ext = partname.ext
        if (ext.lower(), content_type) in default_content_types:
            self._defaults[ext] = content_type
        else:
            self._overrides[partname] = content_type

    @property
    def _element(self):
        """返回此内容类型项的XML形式，适合作为``[Content_Types].xml``存储在OPC包中。

        虽然元素的顺序不是严格重要的，但为了便于测试和可读性，
        Default元素按扩展名排序，Override元素按部件名排序。
        """
        _types_elm = CT_Types.new()
        for ext in sorted(self._defaults.keys()):
            _types_elm.add_default(ext, self._defaults[ext])
        for partname in sorted(self._overrides.keys()):
            _types_elm.add_override(partname, self._overrides[partname])
        return _types_elm
