"""
GitHub项目API处理器
"""
from typing import Dict, Any, Optional
import structlog
from tornado.web import HTT<PERSON><PERSON>rror

from app.api.base import BaseHandler
from app.schemas.github.github_project import GitHubProjectCreate, GitHubProject
from app.services.github.github_project import GitHubProjectService
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide
from app.core.middleware import require_auth

logger = structlog.get_logger(__name__)


class GitHubProjectCardGenerateHandler(BaseHandler):
    """GitHub项目卡片生成处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    # @require_auth(required=True, permissions=["github:project:generate"])
    async def post(self) -> None:
        """生成GitHub项目卡片"""
        try:
            # 从请求体获取项目ID列表
            request_data = self.json_body
            project_ids = request_data.get("project_ids", [])

            if not project_ids:
                raise ValueError("project_ids 列表不能为空")

            # 验证项目ID格式
            if not all(isinstance(pid, str) for pid in project_ids):
                raise ValueError("所有项目ID必须是字符串类型")

            # 调用服务生成卡片
            results = await self.github_project_service.generate_cards(project_ids)

            # 返回结果
            self.success_response(results)

        except ValueError as e:
            logger.error("请求数据无效", error=str(e))
            self.write_error(500, error_message=f"请求数据无效: {str(e)}")
        except Exception as e:
            logger.error("处理生成GitHub项目卡片请求时发生错误", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")


class GitHubProjectDownloadHandler(BaseHandler):
    """GitHub项目处理器"""
    
    @inject
    def initialize(
        self,
        github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器
        
        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    
    # @require_auth(required=True, permissions=["github:project:download"])
    async def post(self) -> None:
        """下载GitHub项目
        
        请求体:
        {
            "repository_url": "https://github.com/username/repo",
            "name": "项目名称" (可选),
            "description": "项目描述" (可选),
            "tags": ["标签1", "标签2"] (可选)
        }
        """
        try:
            projects_data = self.json_body.get("projects", [])
            if not projects_data:
                raise ValueError("projects 列表不能为空")
            # 验证每个项目数据
            projects = [GitHubProjectCreate(**project) for project in projects_data]
            ans = await self.github_project_service.download_projects(projects)
            # 返回结果
            self.success_response(str(ans) if ans else None)
        except ValueError as e:
            logger.error("请求数据无效", error=str(e))
            self.write_error(500, error_message="请求数据无效" + str(e))
        except Exception as e:
            logger.error("处理下载GitHub项目请求时发生错误", error=str(e))
            self.write_error(500, error_message="服务器内部错误" + str(e))


class GitHubProjectHandler(BaseHandler):
    """GitHub项目处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    # @require_auth(required=True, permissions=["github:project:list"])
    async def get(self) -> None:
        """获取GitHub项目列表"""
        try:
            # 获取分页参数
            page = int(self.get_argument("page", "1"))
            page_size = int(self.get_argument("page_size", "10"))
            
            # 获取搜索参数
            status = self.get_argument("status", None)
            name = self.get_argument("name", None)
            recommend_description = self.get_argument("recommend_description", None)

            # 调用服务获取项目列表
            result, total = await self.github_project_service.get_projects(
                page=page,
                page_size=page_size,
                status=status,
                name=name,
                recommend_description=recommend_description  # 传递搜索参数
            )
            
            # 返回结果
            self.success_response({
                "github_projects": [item.dict() for item in result],
                "total": total,
                "page": page,
                "page_size": page_size
            })

        except ValueError as e:
            logger.error("请求参数无效", error=str(e))
            self.write_error(500, error_message="请求参数无效" + str(e))
        except Exception as e:
            logger.error("获取GitHub项目列表时发生错误", error=str(e))
            self.write_error(500, error_message="获取GitHub项目列表时发生错误" + str(e))
