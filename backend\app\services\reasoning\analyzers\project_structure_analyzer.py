#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目结构分析器 - 表层特征轻量级分析版本
基于文件名、路径、扩展名等表层特征进行项目结构分析，无需解析文件内容
"""
import logging
import os
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone

from langchain.chains import <PERSON>MChain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser
from .common_models import AttentionPointInfo
from ....utils.retry import retry_async

from .project_structure_models import (
    ProjectStructureAnalysis
)
from ...ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser

logger = logging.getLogger(__name__)


@AgentManager.register("ProjectStructureAnalyzerAgent")
class ProjectStructureAnalyzer(BaseAgent):
    """
    项目结构分析器 - 表层特征轻量级分析版本
    基于文件名、路径、扩展名等表层特征进行快速项目结构分析
    专注于组件识别和分类，避免深度文件内容解析
    """

    def __init__(self):
        """初始化项目结构分析器"""
        super().__init__()
        self.name = "ProjectStructureAnalyzer"
        self.chain = None

    async def _initialize(self) -> None:
        """初始化分析器特定资源"""
        logger.info("初始化项目结构分析器特定资源")
        # 在这里可以添加特定于分析器的初始化逻辑

    async def _create_chain(self) -> None:
        """初始化处理链"""
        try:
            # 表层特征分析提示模板
            structure_template = """
            # 项目结构分析 - 表层特征轻量级分析

            作为专业的项目结构分析专家，请基于文件名、路径、扩展名等表层特征对项目进行快速结构分析。

            ## 项目信息
            项目名称: {{ project_name }}
            项目路径: {{ project_path }}

            {% if project_structure %}
            ## 目录结构
            ```
            {{ project_structure }}
            ```
            {% endif %}

            ## 表层特征分析框架

            请严格按照以下框架进行表层特征分析，只基于文件名、路径、扩展名进行判断：

            ### 1. 项目类型识别
            基于目录结构和关键文件名识别项目类型：
            - web_application: 存在 app.py, main.py, server.js, index.html 等
            - library: 存在 setup.py, __init__.py, lib/ 目录等
            - cli_tool: 存在 cli.py, command.py, bin/ 目录等
            - framework: 存在复杂的目录结构和框架特征文件
            - microservice: 存在 docker/, k8s/, service/ 等目录
            - desktop_app: 存在 gui/, ui/, desktop/ 等目录
            - mobile_app: 存在 android/, ios/, mobile/ 等目录
            - data_pipeline: 存在 pipeline/, etl/, data/ 等目录

            ### 2. 核心组件识别 (core)
            基于文件名和路径识别核心业务组件：
            - 主要业务逻辑文件（如 main.py, app.py, index.js）
            - 核心模块目录（如 src/, lib/, core/）
            - component_kind: 组件类型（file, directory, module, package）
            - business_importance: 重要性评分基于文件位置和命名重要性（0.0-1.0）

            ### 3. 配置组件识别 (configs)
            基于文件扩展名和命名模式识别配置文件：
            - config_format:
              * JSON格式: *.json
              * YAML格式: *.yml, *.yaml
              * TOML格式: *.toml
              * INI格式: *.ini, *.cfg
              * ENV格式: *.env, .env*
              * XML格式: *.xml
              * Properties格式: *.properties
              * HCL格式: *.hcl, *.tf (Terraform, HashiCorp工具)
              * HOCON格式: *.conf, *.hocon (Akka, Play Framework)
              * HJSON格式: *.hjson (Human JSON)
              * JSONC格式: *.jsonc (JSON with Comments)
              * CFG格式: *.cfg, *.config (通用配置)
              * CONF格式: *.conf (Nginx, Apache等服务器配置)
            - config_scope: 作用域基于文件名推断（global, environment, feature, local）
            - is_required: 是否为必需配置
            - config_priority: 配置优先级（low, medium, high, critical）

            ### 4. 依赖管理组件识别 (dependencies)
            基于特定文件名识别依赖管理文件：
            - package_manager: Python(pip, poetry), Node.js(npm, yarn, pnpm), Java(maven, gradle), Rust(cargo), Go(go_mod), PHP(composer)
            - dependency_category: 依赖类型（runtime_dependencies, dev_dependencies, build_dependencies, test_dependencies, optional_dependencies, peer_dependencies）
            - has_lockfile: 是否有对应的锁定文件
            - supports_versioning: 是否支持版本管理
            - is_monorepo: 是否为单体仓库配置

            ### 5. 测试组件识别 (tests)
            基于文件名和目录名识别测试文件：
            - test_category: 测试类型（unit, integration, e2e, performance, security, smoke, api, ui, example）
              * example: 示例代码、演示代码、教程代码的测试文件
            - test_framework: 使用的测试框架
            - test_scope: 测试范围级别（component_level, service_level, system_level, acceptance_level）
            - coverage_target: 代码覆盖率目标
            - is_automated: 是否为自动化测试
            - execution_environment: 执行环境（local, ci_cd, staging, production）
            - test_priority: 测试优先级（low, medium, high, critical）

            ### 6. 构建部署组件识别 (builds)
            基于文件名识别构建和部署文件：
            - build_tool: 构建类型（dockerfile, docker_compose, makefile, ci_cd, kubernetes, terraform, script, helm）
            - deployment_stage: 部署阶段（development, testing, staging, production, multi_stage）
            - target_platforms: 目标部署平台列表
            - build_stages: 构建阶段列表
            - supports_multi_arch: 是否支持多架构
            - automation_level: 自动化程度（manual, semi_automated, fully_automated）

            ### 7. 文档组件识别 (docs)
            基于文件名和扩展名识别文档文件：
            - doc_category: 文档类型（readme, api_docs, user_guide, developer_guide, changelog, license, contributing）
            - doc_format: 文档格式（markdown, rst, html, pdf, txt, wiki）
            - target_audience: 目标受众（end_users, developers, administrators, contributors, general）
            - maintenance_status: 维护程度（active, occasional, minimal, deprecated）
            - doc_importance: 文档重要性评分（0.0-1.0）
            - completeness_level: 完整性级别（comprehensive, basic, minimal, placeholder）

            ### 8. 入口点组件识别 (entries)
            基于文件名和位置识别项目入口点：
            - entry_category: 入口点类型（main_script, web_server, cli_command, api_gateway, worker_process, batch_job, service_daemon）
            - execution_context: 执行环境上下文（standalone, web_framework, container, serverless, microservice）
            - is_primary: 是否为主要入口点
            - startup_priority: 启动优先级（low, medium, high, critical）

            ## 表层特征推断规则

            ### 文件重要性推断（0.0-1.0）
            - 根目录主文件: 0.9-1.0 (main.py, app.py, index.js)
            - 根目录配置文件: 0.8-0.9 (setup.py, package.json)
            - src/主目录文件: 0.6-0.8
            - 核心模块文件: 0.5-0.7
            - 子模块文件: 0.3-0.5
            - 工具脚本: 0.2-0.4
            - 测试文件: 0.2-0.3
            - 示例文件: 0.1-0.2

            ### 配置作用域推断
            - global: config.*, settings.*, 根目录配置文件
            - environment: dev.*, prod.*, staging.*, test.*, .env.*
            - feature: 特定功能目录下的配置文件
            - local: local.*, .env.local, *local*

            ### 配置优先级推断
            - critical: 主配置文件（config.json, settings.py）
            - high: 环境配置（prod.*, staging.*）
            - medium: 功能配置（feature.*, module.*）
            - low: 本地配置（local.*, dev.*）

            ### 配置格式推断
            - json: *.json
            - yaml: *.yml, *.yaml
            - toml: *.toml
            - ini: *.ini, *.cfg
            - env: *.env, .env*
            - xml: *.xml
            - properties: *.properties
            - hcl: *.hcl, *.tf (Terraform配置)
            - hocon: *.conf, *.hocon (Akka/Play Framework)
            - hjson: *.hjson (Human JSON)
            - jsonc: *.jsonc (JSON with Comments, VS Code配置)
            - cfg: *.cfg, *.config (通用配置文件)
            - conf: *.conf (Nginx, Apache等服务器配置)

            ### 测试范围推断
            - component_level: test_*.py, *_test.js, 单文件测试
            - service_level: tests/unit/, tests/service/
            - system_level: tests/integration/, tests/system/
            - acceptance_level: tests/e2e/, tests/acceptance/

            ### 测试类型推断
            - unit: test_*, *_test.*, tests/unit/
            - integration: tests/integration/, integration_test*
            - e2e: tests/e2e/, e2e/, cypress/, selenium/
            - performance: tests/performance/, benchmark/, load_test*
            - security: tests/security/, security_test*
            - smoke: tests/smoke/, smoke_test*
            - api: tests/api/, api_test*
            - ui: tests/ui/, ui_test*
            - example: examples/, demo/, tutorial/, samples/, *example*, example_*, demo_*, tutorial_*

            ### 构建类型推断
            - dockerfile: Dockerfile, *.dockerfile
            - docker_compose: docker-compose.yml, docker-compose.yaml
            - makefile: Makefile, makefile, *.mk
            - ci_cd: .github/workflows/, .gitlab-ci.yml, Jenkinsfile, .travis.yml
            - kubernetes: k8s/, kubernetes/, *.k8s.yaml
            - terraform: *.tf, terraform/
            - script: build.sh, deploy.sh, *.sh
            - helm: charts/, helm/, Chart.yaml

            ### 部署阶段推断
            - development: dev.*, development.*, local.*
            - testing: test.*, testing.*, qa.*
            - staging: staging.*, stage.*, pre.*
            - production: prod.*, production.*, live.*
            - multi_stage: 包含多个阶段的配置

            ### 入口点类型推断
            - main_script: main.py, __main__.py, index.js, app.js
            - web_server: server.py, wsgi.py, asgi.py, app.py
            - cli_command: cli.py, command.py, cmd.py
            - api_gateway: gateway.py, api.py, router.py
            - worker_process: worker.py, celery.py, task.py
            - batch_job: batch.py, job.py, cron.py
            - service_daemon: daemon.py, service.py

            ### 执行环境推断
            - standalone: 独立脚本文件
            - web_framework: 在web目录或包含web框架特征
            - container: 存在Docker相关文件
            - serverless: 包含lambda, function等关键词
            - microservice: 存在service目录或微服务特征

            ### 文档类型推断
            - readme: README.*, readme.*
            - api_docs: api.*, swagger.*, openapi.*
            - user_guide: guide.*, manual.*, tutorial.*
            - developer_guide: dev.*, development.*, contributing.*
            - changelog: CHANGELOG.*, HISTORY.*, CHANGES.*
            - license: LICENSE.*, COPYING.*, COPYRIGHT.*
            - contributing: CONTRIBUTING.*, CONTRIBUTE.*

            ### 文档格式推断
            - markdown: *.md, *.markdown
            - rst: *.rst, *.rest
            - html: *.html, *.htm
            - pdf: *.pdf
            - txt: *.txt
            - wiki: *.wiki

            ### 目标受众推断
            - end_users: user.*, manual.*, guide.*
            - developers: dev.*, api.*, technical.*
            - administrators: admin.*, deploy.*, ops.*
            - contributors: contributing.*, develop.*
            - general: readme.*, overview.*

            ## 常见文件模式识别

            ### Python项目模式
            - 入口点: main.py, app.py, __main__.py, run.py
            - 核心模块: src/, lib/, 包含__init__.py的目录
            - 配置: settings.py, config.py, *.ini, *.yaml
            - 依赖: requirements.txt, setup.py, pyproject.toml, Pipfile
            - 测试: test_*.py, tests/, pytest.ini, conftest.py
            - 构建: setup.py, Dockerfile, tox.ini

            ### JavaScript/Node.js项目模式
            - 入口点: index.js, app.js, server.js, main.js
            - 核心模块: src/, lib/, components/
            - 配置: config.js, *.json, .env
            - 依赖: package.json, yarn.lock, package-lock.json
            - 测试: *.test.js, *.spec.js, __tests__/
            - 构建: webpack.config.js, rollup.config.js, Dockerfile

            ### Java项目模式
            - 入口点: Main.java, Application.java, *Application.java
            - 核心模块: src/main/java/, src/main/kotlin/
            - 配置: application.properties, application.yml, config/
            - 依赖: pom.xml, build.gradle, gradle.properties
            - 测试: src/test/, *Test.java, *Tests.java
            - 构建: Dockerfile, Jenkinsfile, build.gradle

            ### 通用项目模式
            - 文档: README.*, docs/, documentation/
            - 脚本: scripts/, bin/, tools/
            - 资源: assets/, resources/, static/
            - 示例: examples/, samples/, demo/

            ## 分析要求与原则

            ### 核心分析原则
            1. **仅基于表层特征**：文件名、路径、扩展名、目录结构
            2. **禁止内容推测**：严禁推测文件内容或进行深度解析
            3. **使用预定义规则**：严格按照上述推断规则进行分类
            4. **保持一致性**：相同模式的文件应归类一致
            5. **避免过度分析**：不要添加不存在的信息
            6. **描述字段必填**：每个组件都必须提供有意义的功能描述
            7. **路径准确性**：所有路径都是相对路径，不允许包含根目录名

            ### 输出语言要求
            所有分析结果使用中文(简体中文)输出，技术名称和代码保持原文。

            ### 输出质量要求
            1. **表层特征优先**：只基于文件名、路径、扩展名进行分析
            2. **避免内容推测**：不要基于文件内容进行任何推断
            3. **快速分类**：使用预定义的分类规则进行快速组件识别
            4. **准确性优先**：宁可少报也不要误报
            5. **完整性保证**：确保每个组件包含所有必需属性

            ### 必需输出组件类型
            请确保输出包含以下7种组件类型的分析结果：

            1. **核心组件 (core)**: 主要业务逻辑文件和目录
            2. **配置组件 (configs)**: 各种格式的配置文件
            3. **依赖管理 (dependencies)**: 依赖管理和包管理文件
            4. **测试组件 (tests)**: 测试文件和测试目录
            5. **构建部署 (builds)**: 构建、部署、CI/CD相关文件
            6. **文档组件 (docs)**: 文档和说明文件
            7. **入口点组件 (entries)**: 项目入口点和启动文件

            **注意**：如果某个组件类型在项目中不存在，请返回空列表，不要创造不存在的组件。

            {{ format_instructions }}
            """

            # 创建Parser
            structure_parser = AnalysisOutputParser(pydantic_object=ProjectStructureAnalysis)
            parser = OutputFixingParser.from_llm(parser=structure_parser, llm=self.llm)

            # 获取格式指令
            structure_format_instructions = structure_parser.get_format_instructions()

            # 创建提示模板
            structure_prompt = PromptTemplate.from_template(
                structure_template,
                template_format="jinja2"
            ).partial(format_instructions=structure_format_instructions)

            # 创建处理链
            self.chain = (
                structure_prompt
                | self.llm
                | parser
            )

            logger.info("项目结构分析器处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    @retry_async(max_retries=3)
    async def _process(
        self,
        input_data: AgentInput
    ) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询

        Args:
            input_data: 输入数据

        Returns:
            Tuple[ProjectStructureAnalysis, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 调用LLM处理链
            # result = await self.chain.first.ainvoke(input_data.parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(input_data.parameters)

            return result, {"analyzer": "project structure analyzer", "version": "1.0"}

        except Exception as e:
            logger.error(f"项目结构分析失败: {str(e)}")
            raise

    async def _shutdown(self) -> None:
        """关闭分析器资源"""
        logger.info("关闭项目结构分析器资源")
        # 释放可能的资源
        self.chain = None
        self.llm = None
