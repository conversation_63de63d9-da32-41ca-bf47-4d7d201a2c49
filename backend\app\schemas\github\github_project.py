"""
GitHub项目相关模型
"""
import random
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field


class GitHubProjectBase(BaseModel):
    """GitHub项目基础信息"""
    name: Optional[str] = Field(None, description="项目名称")
    description_project: Optional[str] = Field(None, description="项目详情描述")
    description_recommend: Optional[str] = Field(None, description="项目推荐描述")
    tags: List[str] = Field(default_factory=list, description="项目标签")
    status: str = Field(default="false", description="项目状态")
    project_phase: Optional[str] = Field(default="false", description="项目进度")
    stars: Optional[str] = Field(default="0", description="星数")
    local_path: Optional[str] = Field(default="test", description="项目在硬盘中的地址")
    image_url: Optional[str] = Field(None, description="项目图片URL")
    image_list: Optional[str] = Field(None, description="项目图片备选列表")
    icon_url: Optional[str] = Field(None, description="项目小图标URL")
    background_color: Optional[str] = Field(default='#ffe6e6', description="背景颜色")
    button_color: Optional[str] = Field(default='#07c4fe',  description="按钮颜色")
    architecture_mermaid: Optional[str] = Field(None, description="架构图")
    dependency_mermaid: Optional[str] = Field(None, description="依赖图")
    shared_count_qrcode: Optional[str] = Field(None, description="分享qrcode")
    shared_count_link: Optional[str] = Field(None, description="分享link")
    shared_pic: Optional[str] = Field(None, description="分享图片链接")
    shared_data: Optional[str] = Field(None, description="手机分享数据")


class GitHubProjectCreate(GitHubProjectBase):
    """创建GitHub项目请求"""
    repository_url: str = Field(..., description="GitHub仓库URL")

    def dict(self, *args, **kwargs):
        # 确保字典中包含随机生成的颜色
        result = super().dict(*args, **kwargs)
        return result

class GitHubProject(GitHubProjectBase):
    """GitHub项目完整信息"""
    id: str = Field(..., description="项目ID")
    repository_url: str = Field(..., description="GitHub仓库URL")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


    # class Config:
    #     orm_mode = True

    def __init__(self, **data):
        super().__init__(**data)
        # 随机生成背景色和按钮色
        # background_colors = ["#ffe6e6", "#cef9fd", "#f9e9ff"]
        # button_colors = ["#07c4fe", "#f4acff", "#ffbaa0"]
        # self.background_color = random.choice(background_colors)
        # self.button_color = random.choice(button_colors)

    def dict(self, *args, **kwargs):
        # 确保字典中包含随机生成的颜色
        result = super().dict(*args, **kwargs)
        return result
