"""
GitHub待处理仓库服务

用于管理从URL验证到最终索引的整个待处理仓库生命周期
"""
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, List, Any
from urllib.parse import urlparse

import structlog
from sqlalchemy import select, and_, func, or_
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import selectinload

from app.core.di.providers import SessionProvider, AsyncSessionProvider
from app.models.github.github_pending_repository import (
    GitHubPendingRepositoryModel,
    GitHubRepositorySubmissionModel,
    SubmissionSourceType
)

logger = structlog.get_logger(__name__)


class PendingRepositoryService:
    """GitHub待处理仓库服务"""

    def __init__(
        self,
        session: SessionProvider,
        async_session: AsyncSessionProvider
    ):
        self.session = session
        self.async_session = async_session
        logger.debug("GitHub待处理仓库服务初始化完成")

    async def create_pending_repository(
        self,
        repository_url: str,
        platform: str,
        username: str,
        repository_name: str,
        *,
        source_type: SubmissionSourceType = SubmissionSourceType.URL_VALIDATION,
        submitted_by_ip: Optional[str] = None,
        submitted_by_user: Optional[str] = None,
        submitted_by_user_agent: Optional[str] = None,
        submission_metadata: Optional[Dict[str, Any]] = None,
        validation_status: str = "pending"
    ) -> Optional[GitHubPendingRepositoryModel]:
        """创建待处理仓库记录

        Args:
            repository_url: 仓库URL
            platform: 代码托管平台
            username: 用户名或组织名
            repository_name: 仓库名称
            source_type: 提交来源类型
            submitted_by_ip: 提交者IP地址
            submitted_by_user: 提交者用户名
            submitted_by_user_agent: 提交者User Agent
            submission_metadata: 提交上下文信息
            validation_status: 验证状态

        Returns:
            Optional[GitHubPendingRepositoryModel]: 创建的记录，失败时返回None
        """
        try:
            async with self.async_session() as session:
                # 检查是否已存在相同URL的记录
                existing = await self.check_repository_exists(repository_url)
                if existing:
                    logger.info("待处理仓库已存在，添加新的提交记录",
                               repository_url=repository_url,
                               existing_id=existing.id,
                               source_type=source_type.value)

                    # 为已存在的仓库添加新的提交记录
                    submission_record = await self.add_submission_record(
                        repository_id=existing.id,
                        source_type=source_type,
                        submitted_by_ip=submitted_by_ip,
                        submitted_by_user=submitted_by_user,
                        submitted_by_user_agent=submitted_by_user_agent,
                        submission_metadata=submission_metadata
                    )

                    if submission_record:
                        logger.info("为已存在仓库添加提交记录成功",
                                   repository_id=existing.id,
                                   submission_id=submission_record.id,
                                   source_type=source_type.value)

                    return existing

                # 创建新记录
                pending_repo = GitHubPendingRepositoryModel.create_from_url(
                    url=repository_url,
                    platform=platform,
                    username=username,
                    repo_name=repository_name,
                    source_type=source_type,
                    submitted_by_ip=submitted_by_ip,
                    submitted_by_user=submitted_by_user,
                    submitted_by_user_agent=submitted_by_user_agent,
                    submission_metadata=submission_metadata,
                    validation_status=validation_status
                )

                session.add(pending_repo)
                await session.commit()
                await session.refresh(pending_repo)

                logger.info("待处理仓库创建成功",
                           repository_url=repository_url,
                           pending_id=pending_repo.id,
                           source_type=source_type.value)
                return pending_repo

        except IntegrityError as e:
            logger.warning("待处理仓库创建失败：URL已存在，尝试添加提交记录",
                          repository_url=repository_url, error=str(e))
            # 获取已存在的记录并添加新的提交记录
            existing = await self.check_repository_exists(repository_url)
            if existing:
                submission_record = await self.add_submission_record(
                    repository_id=existing.id,
                    source_type=source_type,
                    submitted_by_ip=submitted_by_ip,
                    submitted_by_user=submitted_by_user,
                    submitted_by_user_agent=submitted_by_user_agent,
                    submission_metadata=submission_metadata
                )

                if submission_record:
                    logger.info("IntegrityError处理：为已存在仓库添加提交记录成功",
                               repository_id=existing.id,
                               submission_id=submission_record.id,
                               source_type=source_type.value)

                return existing
            else:
                logger.error("IntegrityError处理失败：无法找到已存在的仓库记录",
                            repository_url=repository_url)
                return None
        except Exception as e:
            logger.error("创建待处理仓库失败", 
                        repository_url=repository_url, error=str(e))
            return None

    async def check_repository_exists(self, repository_url: str) -> Optional[GitHubPendingRepositoryModel]:
        """检查仓库URL是否已存在

        Args:
            repository_url: 仓库URL

        Returns:
            Optional[GitHubPendingRepositoryModel]: 存在的记录，不存在时返回None
        """
        try:
            async with self.async_session() as session:
                stmt = select(GitHubPendingRepositoryModel).where(
                    GitHubPendingRepositoryModel.repository_url == repository_url
                )
                result = await session.execute(stmt)
                return result.scalar_one_or_none()
        except Exception as e:
            logger.error("检查仓库存在性失败", repository_url=repository_url, error=str(e))
            return None

    async def get_pending_repository(self, pending_id: str) -> Optional[GitHubPendingRepositoryModel]:
        """获取待处理仓库记录

        Args:
            pending_id: 待处理仓库ID

        Returns:
            Optional[GitHubPendingRepositoryModel]: 仓库记录，不存在时返回None
        """
        try:
            async with self.async_session() as session:
                stmt = select(GitHubPendingRepositoryModel).where(
                    GitHubPendingRepositoryModel.id == pending_id
                )
                result = await session.execute(stmt)
                return result.scalar_one_or_none()
        except Exception as e:
            logger.error("获取待处理仓库失败", pending_id=pending_id, error=str(e))
            return None

    async def query_pending_repositories(
        self,
        *,
        page: int = 1,
        page_size: int = 10,
        validation_status: Optional[str] = None,
        processing_status: Optional[str] = None,
        platform: Optional[str] = None,
        source_type: Optional[SubmissionSourceType] = None,
        username: Optional[str] = None
    ) -> Dict[str, Any]:
        """查询待处理仓库列表

        Args:
            page: 页码
            page_size: 每页记录数
            validation_status: 验证状态筛选
            processing_status: 处理状态筛选
            platform: 平台筛选
            source_type: 提交来源类型筛选
            username: 用户名筛选

        Returns:
            Dict[str, Any]: 包含记录列表和分页信息的字典
        """
        try:
            async with self.async_session() as session:
                # 构建基础查询
                stmt = select(GitHubPendingRepositoryModel)

                # 如果需要按提交来源筛选，需要JOIN submissions表
                if source_type is not None:
                    stmt = stmt.join(GitHubRepositorySubmissionModel)

                # 构建查询条件
                conditions = []

                if validation_status is not None:
                    conditions.append(GitHubPendingRepositoryModel.validation_status == validation_status)

                if processing_status is not None:
                    conditions.append(GitHubPendingRepositoryModel.processing_status == processing_status)

                if platform is not None:
                    conditions.append(GitHubPendingRepositoryModel.platform == platform)

                if source_type is not None:
                    conditions.append(GitHubRepositorySubmissionModel.source_type == source_type)

                if username is not None:
                    conditions.append(GitHubPendingRepositoryModel.username.ilike(f"%{username}%"))

                # 应用查询条件
                if conditions:
                    stmt = stmt.where(and_(*conditions))

                # 如果有JOIN，需要去重
                if source_type is not None:
                    stmt = stmt.distinct()

                # 获取总记录数
                count_stmt = select(func.count(GitHubPendingRepositoryModel.id.distinct()))
                if source_type is not None:
                    count_stmt = count_stmt.select_from(
                        GitHubPendingRepositoryModel.__table__.join(GitHubRepositorySubmissionModel.__table__)
                    )
                else:
                    count_stmt = count_stmt.select_from(GitHubPendingRepositoryModel)

                if conditions:
                    count_stmt = count_stmt.where(and_(*conditions))

                total = await session.scalar(count_stmt)

                # 计算分页
                skip = (page - 1) * page_size
                stmt = stmt.order_by(GitHubPendingRepositoryModel.created_at.desc())
                stmt = stmt.offset(skip).limit(page_size)

                # 执行查询
                result = await session.execute(stmt)
                repositories = result.scalars().all()

                return {
                    "repositories": [repo.to_dict(include_submissions=True) for repo in repositories],
                    "total": total,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total + page_size - 1) // page_size if total > 0 else 0
                }

        except Exception as e:
            logger.error("查询待处理仓库失败", error=str(e))
            return {
                "repositories": [],
                "total": 0,
                "page": page,
                "page_size": page_size,
                "total_pages": 0
            }

    async def update_validation_status(
        self,
        pending_id: str,
        validation_status: str,
        validation_message: Optional[str] = None
    ) -> bool:
        """更新验证状态

        Args:
            pending_id: 待处理仓库ID
            validation_status: 新的验证状态
            validation_message: 验证消息

        Returns:
            bool: 是否更新成功
        """
        try:
            async with self.async_session() as session:
                stmt = select(GitHubPendingRepositoryModel).where(
                    GitHubPendingRepositoryModel.id == pending_id
                )
                result = await session.execute(stmt)
                pending_repo = result.scalar_one_or_none()

                if not pending_repo:
                    logger.error("更新验证状态失败：记录不存在", pending_id=pending_id)
                    return False

                # 更新验证状态
                pending_repo.validation_status = validation_status
                pending_repo.validation_message = validation_message
                pending_repo.last_validation_at = datetime.now(timezone.utc)

                await session.commit()

                logger.info("验证状态更新成功",
                           pending_id=pending_id,
                           validation_status=validation_status)
                return True

        except Exception as e:
            logger.error("更新验证状态失败",
                        pending_id=pending_id,
                        validation_status=validation_status,
                        error=str(e))
            return False

    async def update_processing_status(
        self,
        pending_id: str,
        processing_status: str,
        processing_message: Optional[str] = None
    ) -> bool:
        """更新处理状态

        Args:
            pending_id: 待处理仓库ID
            processing_status: 新的处理状态
            processing_message: 处理消息

        Returns:
            bool: 是否更新成功
        """
        try:
            async with self.async_session() as session:
                stmt = select(GitHubPendingRepositoryModel).where(
                    GitHubPendingRepositoryModel.id == pending_id
                )
                result = await session.execute(stmt)
                pending_repo = result.scalar_one_or_none()

                if not pending_repo:
                    logger.error("更新处理状态失败：记录不存在", pending_id=pending_id)
                    return False

                # 更新处理状态
                pending_repo.processing_status = processing_status
                pending_repo.processing_message = processing_message
                pending_repo.last_processing_at = datetime.now(timezone.utc)

                await session.commit()

                logger.info("处理状态更新成功",
                           pending_id=pending_id,
                           processing_status=processing_status)
                return True

        except Exception as e:
            logger.error("更新处理状态失败",
                        pending_id=pending_id,
                        processing_status=processing_status,
                        error=str(e))
            return False

    async def get_repositories_by_status(
        self,
        validation_status: Optional[str] = None,
        processing_status: Optional[str] = None,
        limit: int = 100
    ) -> List[GitHubPendingRepositoryModel]:
        """根据状态获取待处理仓库列表

        Args:
            validation_status: 验证状态
            processing_status: 处理状态
            limit: 最大返回数量

        Returns:
            List[GitHubPendingRepositoryModel]: 仓库记录列表
        """
        try:
            async with self.async_session() as session:
                stmt = select(GitHubPendingRepositoryModel)

                conditions = []
                if validation_status is not None:
                    conditions.append(GitHubPendingRepositoryModel.validation_status == validation_status)
                if processing_status is not None:
                    conditions.append(GitHubPendingRepositoryModel.processing_status == processing_status)

                if conditions:
                    stmt = stmt.where(and_(*conditions))

                stmt = stmt.order_by(GitHubPendingRepositoryModel.created_at.asc()).limit(limit)

                result = await session.execute(stmt)
                return list(result.scalars().all())

        except Exception as e:
            logger.error("根据状态获取待处理仓库失败", error=str(e))
            return []

    async def delete_pending_repository(self, pending_id: str) -> bool:
        """删除待处理仓库记录

        Args:
            pending_id: 待处理仓库ID

        Returns:
            bool: 是否删除成功
        """
        try:
            async with self.async_session() as session:
                stmt = select(GitHubPendingRepositoryModel).where(
                    GitHubPendingRepositoryModel.id == pending_id
                )
                result = await session.execute(stmt)
                pending_repo = result.scalar_one_or_none()

                if not pending_repo:
                    logger.error("删除待处理仓库失败：记录不存在", pending_id=pending_id)
                    return False

                await session.delete(pending_repo)
                await session.commit()

                logger.info("待处理仓库删除成功", pending_id=pending_id)
                return True

        except Exception as e:
            logger.error("删除待处理仓库失败", pending_id=pending_id, error=str(e))
            return False

    async def get_statistics(self) -> Dict[str, Any]:
        """获取待处理仓库统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            async with self.async_session() as session:
                # 总数统计
                total_stmt = select(func.count()).select_from(GitHubPendingRepositoryModel)
                total = await session.scalar(total_stmt)

                # 按验证状态统计
                validation_stats = {}
                for status in ["pending", "validated", "failed"]:
                    stmt = select(func.count()).select_from(GitHubPendingRepositoryModel).where(
                        GitHubPendingRepositoryModel.validation_status == status
                    )
                    count = await session.scalar(stmt)
                    validation_stats[status] = count

                # 按处理状态统计
                processing_stats = {}
                for status in ["pending", "processing", "completed", "failed"]:
                    stmt = select(func.count()).select_from(GitHubPendingRepositoryModel).where(
                        GitHubPendingRepositoryModel.processing_status == status
                    )
                    count = await session.scalar(stmt)
                    processing_stats[status] = count

                # 按平台统计
                platform_stats = {}
                platforms = ["github.com", "gitlab.com", "gitee.com", "bitbucket.org"]
                for platform in platforms:
                    stmt = select(func.count()).select_from(GitHubPendingRepositoryModel).where(
                        GitHubPendingRepositoryModel.platform == platform
                    )
                    count = await session.scalar(stmt)
                    if count > 0:
                        platform_stats[platform] = count

                # 按提交来源统计
                source_stats = {}
                for source_type in SubmissionSourceType:
                    stmt = select(func.count()).select_from(GitHubRepositorySubmissionModel).where(
                        GitHubRepositorySubmissionModel.source_type == source_type
                    )
                    count = await session.scalar(stmt)
                    if count > 0:
                        source_stats[source_type.value] = count

                return {
                    "total": total,
                    "validation_status": validation_stats,
                    "processing_status": processing_stats,
                    "platform": platform_stats,
                    "submission_source": source_stats
                }

        except Exception as e:
            logger.error("获取统计信息失败", error=str(e))
            return {
                "total": 0,
                "validation_status": {},
                "processing_status": {},
                "platform": {},
                "submission_source": {}
            }

    @classmethod
    def parse_repository_url(cls, url: str) -> Optional[Dict[str, str]]:
        """解析仓库URL，提取平台、用户名和仓库名

        Args:
            url: 仓库URL

        Returns:
            Optional[Dict[str, str]]: 包含platform, username, repository_name的字典，解析失败时返回None
        """
        try:
            parsed = urlparse(url.strip())
            hostname = parsed.hostname

            if not hostname:
                return None

            # 标准化主机名
            platform_mapping = {
                "github.com": "github.com",
                "www.github.com": "github.com",
                "gitlab.com": "gitlab.com",
                "www.gitlab.com": "gitlab.com",
                "gitee.com": "gitee.com",
                "www.gitee.com": "gitee.com",
                "bitbucket.org": "bitbucket.org",
                "www.bitbucket.org": "bitbucket.org"
            }

            platform = platform_mapping.get(hostname.lower())
            if not platform:
                return None

            # 解析路径
            path_parts = [part for part in parsed.path.strip('/').split('/') if part]
            if len(path_parts) < 2:
                return None

            username = path_parts[0]
            repository_name = path_parts[1]

            # 移除.git后缀
            if repository_name.endswith('.git'):
                repository_name = repository_name[:-4]

            return {
                "platform": platform,
                "username": username,
                "repository_name": repository_name
            }

        except Exception as e:
            logger.error("解析仓库URL失败", url=url, error=str(e))
            return None

    async def create_from_url_validation(
        self,
        repository_url: str,
        *,
        submitted_by_ip: Optional[str] = None,
        submitted_by_user: Optional[str] = None,
        submitted_by_user_agent: Optional[str] = None,
        submission_metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[GitHubPendingRepositoryModel]:
        """从URL验证结果创建待处理记录（便捷方法）

        Args:
            repository_url: 仓库URL
            submitted_by_ip: 提交者IP地址
            submitted_by_user: 提交者用户名
            submitted_by_user_agent: 提交者User Agent
            submission_metadata: 提交上下文信息

        Returns:
            Optional[GitHubPendingRepositoryModel]: 创建的记录，失败时返回None
        """
        # 解析URL
        parsed_info = self.parse_repository_url(repository_url)
        if not parsed_info:
            logger.error("URL解析失败，无法创建待处理记录", repository_url=repository_url)
            return None

        # 创建记录
        return await self.create_pending_repository(
            repository_url=repository_url,
            platform=parsed_info["platform"],
            username=parsed_info["username"],
            repository_name=parsed_info["repository_name"],
            source_type=SubmissionSourceType.URL_VALIDATION,
            submitted_by_ip=submitted_by_ip,
            submitted_by_user=submitted_by_user,
            submitted_by_user_agent=submitted_by_user_agent,
            submission_metadata=submission_metadata,
            validation_status="validated"  # 从URL验证来的默认为已验证
        )



    async def add_submission_record(
        self,
        repository_id: str,
        source_type: SubmissionSourceType,
        *,
        submitted_by_ip: Optional[str] = None,
        submitted_by_user: Optional[str] = None,
        submitted_by_user_agent: Optional[str] = None,
        submission_metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[GitHubRepositorySubmissionModel]:
        """为现有仓库添加新的提交来源记录

        Args:
            repository_id: 仓库ID
            source_type: 提交来源类型
            submitted_by_ip: 提交者IP地址
            submitted_by_user: 提交者用户名
            submitted_by_user_agent: 提交者User Agent
            submission_metadata: 提交上下文信息

        Returns:
            Optional[GitHubRepositorySubmissionModel]: 创建的提交记录，如果检测到重复则返回已存在的记录，失败时返回None

        重复检测策略：
        - 在1小时内，相同仓库+来源类型+提交者信息的提交视为重复
        - 如果提交者信息不完整，采用宽松匹配策略
        """
        try:
            async with self.async_session() as session:
                # 检查仓库是否存在
                repo_stmt = select(GitHubPendingRepositoryModel).where(
                    GitHubPendingRepositoryModel.id == repository_id
                )
                repo_result = await session.execute(repo_stmt)
                repository = repo_result.scalar_one_or_none()

                if not repository:
                    logger.error("添加提交记录失败：仓库不存在", repository_id=repository_id)
                    return None

                # 智能重复检测（1小时内，严格模式）
                existing_submission = await self._check_duplicate_submission(
                    session=session,
                    repository_id=repository_id,
                    source_type=source_type,
                    submitted_by_ip=submitted_by_ip,
                    submitted_by_user=submitted_by_user,
                    duplicate_check_window_minutes=60,
                    enable_strict_duplicate_check=True
                )

                if existing_submission:
                    logger.info("检测到重复提交记录，返回已有记录",
                               repository_id=repository_id,
                               source_type=source_type.value,
                               existing_submission_id=existing_submission.id,
                               submitted_by_ip=submitted_by_ip,
                               submitted_by_user=submitted_by_user)
                    return existing_submission

                # 创建提交记录
                submission = GitHubRepositorySubmissionModel(
                    repository_id=repository_id,
                    source_type=source_type,
                    submitted_by_ip=submitted_by_ip,
                    submitted_by_user=submitted_by_user,
                    submitted_by_user_agent=submitted_by_user_agent,
                    submission_metadata=submission_metadata
                )

                session.add(submission)
                await session.commit()
                await session.refresh(submission)

                logger.info("提交记录添加成功",
                           repository_id=repository_id,
                           submission_id=submission.id,
                           source_type=source_type.value)
                return submission

        except Exception as e:
            logger.error("添加提交记录失败",
                        repository_id=repository_id,
                        source_type=source_type.value,
                        error=str(e))
            return None

    async def get_submission_history(
        self,
        repository_id: str,
        *,
        limit: int = 50
    ) -> List[GitHubRepositorySubmissionModel]:
        """获取仓库的提交历史记录

        Args:
            repository_id: 仓库ID
            limit: 最大返回数量

        Returns:
            List[GitHubRepositorySubmissionModel]: 提交记录列表
        """
        try:
            async with self.async_session() as session:
                stmt = select(GitHubRepositorySubmissionModel).where(
                    GitHubRepositorySubmissionModel.repository_id == repository_id
                ).order_by(
                    GitHubRepositorySubmissionModel.created_at.desc()
                ).limit(limit)

                result = await session.execute(stmt)
                return list(result.scalars().all())

        except Exception as e:
            logger.error("获取提交历史失败",
                        repository_id=repository_id,
                        error=str(e))
            return []

    async def get_repositories_with_submissions(
        self,
        *,
        page: int = 1,
        page_size: int = 10
    ) -> Dict[str, Any]:
        """获取包含提交记录的仓库列表

        Args:
            page: 页码
            page_size: 每页记录数

        Returns:
            Dict[str, Any]: 包含仓库和提交记录的数据
        """
        try:
            async with self.async_session() as session:
                # 获取总记录数
                total_stmt = select(func.count()).select_from(GitHubPendingRepositoryModel)
                total = await session.scalar(total_stmt)

                # 计算分页
                skip = (page - 1) * page_size

                # 查询仓库记录，预加载提交记录
                stmt = select(GitHubPendingRepositoryModel).options(
                    selectinload(GitHubPendingRepositoryModel.submissions)
                ).order_by(
                    GitHubPendingRepositoryModel.created_at.desc()
                ).offset(skip).limit(page_size)

                result = await session.execute(stmt)
                repositories = result.scalars().all()

                return {
                    "repositories": [repo.to_dict(include_submissions=True) for repo in repositories],
                    "total": total,
                    "page": page,
                    "page_size": page_size,
                    "total_pages": (total + page_size - 1) // page_size if total > 0 else 0
                }

        except Exception as e:
            logger.error("获取包含提交记录的仓库列表失败", error=str(e))
            return {
                "repositories": [],
                "total": 0,
                "page": page,
                "page_size": page_size,
                "total_pages": 0
            }

    async def _check_duplicate_submission(
        self,
        session,
        repository_id: str,
        source_type: SubmissionSourceType,
        submitted_by_ip: Optional[str] = None,
        submitted_by_user: Optional[str] = None,
        duplicate_check_window_minutes: int = 60,
        enable_strict_duplicate_check: bool = True
    ) -> Optional[GitHubRepositorySubmissionModel]:
        """内部方法：智能重复检测

        Args:
            session: 数据库会话
            repository_id: 仓库ID
            source_type: 提交来源类型
            submitted_by_ip: 提交者IP地址
            submitted_by_user: 提交者用户名
            duplicate_check_window_minutes: 重复检测时间窗口（分钟）
            enable_strict_duplicate_check: 是否启用严格重复检测

        Returns:
            Optional[GitHubRepositorySubmissionModel]: 如果检测到重复则返回已存在的记录，否则返回None
        """
        try:
            # 计算时间窗口
            time_threshold = datetime.now(timezone.utc) - timedelta(minutes=duplicate_check_window_minutes)

            # 基础查询条件：相同仓库 + 相同来源类型 + 时间窗口内
            base_conditions = [
                GitHubRepositorySubmissionModel.repository_id == repository_id,
                GitHubRepositorySubmissionModel.source_type == source_type,
                GitHubRepositorySubmissionModel.created_at >= time_threshold
            ]

            if enable_strict_duplicate_check:
                # 严格模式：需要考虑提交者信息
                strict_conditions = []

                # 构建严格检测条件
                if submitted_by_ip and submitted_by_user:
                    # 同时有IP和用户信息：必须完全匹配
                    strict_conditions.append(
                        and_(
                            GitHubRepositorySubmissionModel.submitted_by_ip == submitted_by_ip,
                            GitHubRepositorySubmissionModel.submitted_by_user == submitted_by_user
                        )
                    )
                elif submitted_by_ip:
                    # 只有IP信息：IP匹配或用户为空
                    strict_conditions.append(
                        or_(
                            GitHubRepositorySubmissionModel.submitted_by_ip == submitted_by_ip,
                            and_(
                                GitHubRepositorySubmissionModel.submitted_by_ip.is_(None),
                                GitHubRepositorySubmissionModel.submitted_by_user.is_(None)
                            )
                        )
                    )
                elif submitted_by_user:
                    # 只有用户信息：用户匹配或IP为空
                    strict_conditions.append(
                        or_(
                            GitHubRepositorySubmissionModel.submitted_by_user == submitted_by_user,
                            and_(
                                GitHubRepositorySubmissionModel.submitted_by_ip.is_(None),
                                GitHubRepositorySubmissionModel.submitted_by_user.is_(None)
                            )
                        )
                    )
                else:
                    # 没有提交者信息：只匹配同样没有提交者信息的记录
                    strict_conditions.append(
                        and_(
                            GitHubRepositorySubmissionModel.submitted_by_ip.is_(None),
                            GitHubRepositorySubmissionModel.submitted_by_user.is_(None)
                        )
                    )

                # 组合基础条件和严格条件
                if strict_conditions:
                    final_conditions = base_conditions + [or_(*strict_conditions)]
                else:
                    final_conditions = base_conditions
            else:
                # 宽松模式：只检查基础条件
                final_conditions = base_conditions

            # 执行查询
            stmt = select(GitHubRepositorySubmissionModel).where(
                and_(*final_conditions)
            ).order_by(GitHubRepositorySubmissionModel.created_at.desc())

            result = await session.execute(stmt)
            existing = result.scalar_one_or_none()

            if existing:
                logger.debug("重复检测命中",
                           repository_id=repository_id,
                           source_type=source_type.value,
                           existing_id=existing.id,
                           time_window_minutes=duplicate_check_window_minutes,
                           strict_mode=enable_strict_duplicate_check,
                           submitted_by_ip=submitted_by_ip,
                           submitted_by_user=submitted_by_user)

            return existing

        except Exception as e:
            logger.error("重复检测失败",
                        repository_id=repository_id,
                        source_type=source_type.value,
                        error=str(e))
            # 检测失败时返回None，允许创建新记录
            return None


