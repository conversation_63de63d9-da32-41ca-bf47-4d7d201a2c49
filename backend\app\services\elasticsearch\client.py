"""
Elasticsearch客户端
"""
from typing import Dict, Any, List, Optional
import logging
from elasticsearch import Elasticsearch
from elasticsearch.exceptions import ApiError
from app.core.config.elasticsearch import ElasticsearchSettings

logger = logging.getLogger(__name__)


class ElasticsearchClient:
    """Elasticsearch客户端"""

    def __init__(self, settings: ElasticsearchSettings = None):
        if settings is None:
            settings = ElasticsearchSettings()
        
        self.es_url = settings.elasticsearch_url
        
        # 增强的客户端配置
        self.client = Elasticsearch(
            hosts=[self.es_url],
            # 重试配置
            retry_on_timeout=True,
            max_retries=3,
            # 超时设置
            request_timeout=30,
            # 连接池设置
            connections_per_node=10,
            # 探活和恢复设置
            dead_node_backoff_factor=2,
            # 健康检查
            sniff_on_start=False,
            sniff_on_node_failure=False
        )
        logger.info(f"初始化Elasticsearch客户端: {self.es_url}")

    def create_index(self, index_name: str, settings: Dict[str, Any] = None, mappings: Dict[str, Any] = None) -> bool:
        """
        创建索引
        
        Args:
            index_name: 索引名称
            settings: 索引设置
            mappings: 索引映射
            
        Returns:
            bool: 创建结果
        """
        try:
            # 检查索引是否已存在
            if self.client.indices.exists(index=index_name):
                logger.warning(f"索引 {index_name} 已存在")
                return False
            
            index_config = {}
            if settings:
                index_config["settings"] = settings
            if mappings:
                index_config["mappings"] = mappings
            
            response = self.client.indices.create(index=index_name, **index_config)
            return response.get("acknowledged", False)
        except ApiError as e:
            logger.error(f"创建索引 {index_name} 失败: {str(e)}")
            return False

    def index_document(self, index_name: str, doc_id: str, document: Dict[str, Any]) -> bool:
        """
        索引文档
        
        Args:
            index_name: 索引名称
            doc_id: 文档ID
            document: 文档内容
            
        Returns:
            bool: 索引结果
        """
        try:
            response = self.client.index(index=index_name, id=doc_id, document=document)
            return response.get("result") in ["created", "updated"]
        except ApiError as e:
            logger.error(f"索引文档失败 {index_name}/{doc_id}: {str(e)}")
            return False

    def bulk_index_documents(self, index_name: str, documents: List[Dict[str, Any]]) -> bool:
        """
        批量索引文档
        
        Args:
            index_name: 索引名称
            documents: 文档列表，每个文档都应该包含id字段
            
        Returns:
            bool: 批量索引结果
        """
        if not documents:
            return True
            
        try:
            operations = []
            for doc in documents:
                doc_id = doc.pop("_id", None)
                if not doc_id:
                    logger.warning("文档缺少ID字段，将被跳过")
                    continue
                    
                operations.append({"index": {"_index": index_name, "_id": doc_id}})
                operations.append(doc)
            
            if not operations:
                return True
                
            response = self.client.bulk(operations=operations)
            return not response.get("errors", False)
        except ApiError as e:
            logger.error(f"批量索引文档失败 {index_name}: {str(e)}")
            return False

    def search(self, index_name: str, query: Dict[str, Any], size: int = 10, from_: int = 0) -> Dict[str, Any]:
        """
        搜索文档
        
        Args:
            index_name: 索引名称
            query: 查询条件
            size: 返回结果数量
            from_: 分页起始位置
            
        Returns:
            Dict: 搜索结果
        """
        try:
            return self.client.search(
                index=index_name,
                query=query.get("query"),
                size=size,
                from_=from_
            )
        except ApiError as e:
            logger.error(f"搜索文档失败 {index_name}: {str(e)}")
            return {"error": str(e)}

    def delete_document(self, index_name: str, doc_id: str) -> bool:
        """
        删除文档
        
        Args:
            index_name: 索引名称
            doc_id: 文档ID
            
        Returns:
            bool: 删除结果
        """
        try:
            response = self.client.delete(index=index_name, id=doc_id)
            return response.get("result") == "deleted"
        except ApiError as e:
            if "404" in str(e):
                return False  # 文档不存在视为成功
            logger.error(f"删除文档失败 {index_name}/{doc_id}: {str(e)}")
            return False

    def update_document(self, index_name: str, doc_id: str, document: Dict[str, Any]) -> bool:
        """
        更新文档
        
        Args:
            index_name: 索引名称
            doc_id: 文档ID
            document: 文档内容
            
        Returns:
            bool: 更新结果
        """
        try:
            response = self.client.update(
                index=index_name,
                id=doc_id,
                doc=document
            )
            return response.get("result") == "updated"
        except ApiError as e:
            logger.error(f"更新文档失败 {index_name}/{doc_id}: {str(e)}")
            return False

    def delete_index(self, index_name: str) -> bool:
        """
        删除索引
        
        Args:
            index_name: 索引名称
            
        Returns:
            bool: 删除结果
        """
        try:
            if not self.client.indices.exists(index=index_name):
                return True  # 索引不存在视为成功
                
            response = self.client.indices.delete(index=index_name)
            return response.get("acknowledged", False)
        except ApiError as e:
            logger.error(f"删除索引失败 {index_name}: {str(e)}")
            return False
