#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源管理器

统一管理和协调各种数据源，提供数据聚合和缓存功能
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from pathlib import Path

from .internal_data_source import InternalDataSource
from .github_data_source import GitHubDataSource
from .package_registry_source import PackageRegistrySource
from .stackoverflow_data_source import StackOverflowDataSource
from .reddit_data_source import RedditDataSource
from .hackernews_data_source import HackerNewsDataSource
from .devto_data_source import DevToDataSource
from .producthunt_data_source import ProductHuntDataSource

# 导入工作流模型
from ....workflow.models import AnalysisState

logger = logging.getLogger(__name__)


class DataSourceManager:
    """数据源管理器"""
    
    def __init__(
        self,
        analysis_state: AnalysisState,
        github_token: Optional[str] = None,
        stackoverflow_api_key: Optional[str] = None,
        reddit_client_id: Optional[str] = None,
        reddit_client_secret: Optional[str] = None,
        devto_api_key: Optional[str] = None,
        producthunt_access_token: Optional[str] = None
    ):
        """
        初始化数据源管理器

        Args:
            analysis_state: 分析状态对象（必需）
            github_token: GitHub API令牌
            stackoverflow_api_key: Stack Overflow API密钥
            reddit_client_id: Reddit客户端ID
            reddit_client_secret: Reddit客户端密钥
            devto_api_key: Dev.to API密钥
            producthunt_access_token: Product Hunt访问令牌
        """
        # 使用传入的分析状态
        self.analysis_state = analysis_state
        self.project_path = Path(analysis_state.project_path)
        self.github_token = github_token

        # 初始化内部数据源
        self.internal_source = InternalDataSource(self.analysis_state)

        # 初始化外部数据源
        self.github_source = GitHubDataSource(github_token)
        self.package_source = PackageRegistrySource()

        # 初始化社区平台数据源
        self.stackoverflow_source = StackOverflowDataSource(stackoverflow_api_key) if stackoverflow_api_key else None
        self.reddit_source = RedditDataSource(
            reddit_client_id, reddit_client_secret
        ) if reddit_client_id and reddit_client_secret else None
        self.hackernews_source = HackerNewsDataSource()
        self.devto_source = DevToDataSource(devto_api_key) if devto_api_key else None
        self.producthunt_source = ProductHuntDataSource(producthunt_access_token) if producthunt_access_token else None

        # 数据缓存
        self.aggregated_data: Dict[str, Any] = {}
        self.data_freshness: Dict[str, float] = {}

        # 配置
        self.cache_ttl = 3600  # 缓存有效期（秒）
        self.max_concurrent_requests = 5  # 最大并发请求数
        
    async def collect_all_data(
        self,
        include_external: bool = True,
        include_community: bool = True
    ) -> Dict[str, Any]:
        """
        收集所有数据源的数据

        Args:
            include_external: 是否包含外部数据源
            include_community: 是否包含社区平台数据源

        Returns:
            Dict[str, Any]: 聚合的数据
        """
        try:
            logger.info("开始收集所有数据源的数据")
            
            # 收集内部数据
            internal_data = self.internal_source.extract_all_data()
            
            aggregated_data = {
                "internal": internal_data,
                "external": {},
                "community": {}
            }
            
            if include_external:
                # 收集外部数据
                external_tasks = []
                
                # GitHub数据
                github_url = self._extract_github_url(internal_data)
                if github_url:
                    external_tasks.append(self._collect_github_data(github_url))
                
                # 包注册表数据
                package_names = self._extract_package_names(internal_data)
                if package_names:
                    external_tasks.append(self._collect_package_data(package_names))
                
                # 并发执行外部数据收集
                if external_tasks:
                    external_results = await asyncio.gather(*external_tasks, return_exceptions=True)
                    
                    for i, result in enumerate(external_results):
                        if isinstance(result, Exception):
                            logger.error(f"外部数据收集失败 {i}: {str(result)}")
                        elif result:
                            aggregated_data["external"].update(result)

            # 收集社区数据
            if include_community:
                community_data = await self._collect_community_data(
                    internal_data, aggregated_data["external"]
                )
                aggregated_data["community"] = community_data

            # 缓存聚合数据
            self.aggregated_data = aggregated_data
            
            logger.info("数据收集完成")
            return aggregated_data
            
        except Exception as e:
            logger.error(f"收集数据失败: {str(e)}")
            return {"internal": {}, "external": {}}
    
    async def _collect_github_data(self, github_url: str) -> Dict[str, Any]:
        """收集GitHub数据"""
        try:
            async with self.github_source as github:
                github_data = await github.fetch_data(repository_url=github_url)
                return {"github": github_data} if github_data else {}
        except Exception as e:
            logger.error(f"收集GitHub数据失败: {str(e)}")
            return {}
    
    async def _collect_package_data(self, package_names: List[str]) -> Dict[str, Any]:
        """收集包注册表数据"""
        try:
            package_data = {}
            
            # 限制并发数量
            semaphore = asyncio.Semaphore(self.max_concurrent_requests)
            
            async def fetch_package(package_name: str):
                async with semaphore:
                    async with self.package_source as pkg_source:
                        return await pkg_source.fetch_data(package_name)
            
            # 并发获取包数据
            tasks = [fetch_package(name) for name in package_names[:5]]  # 限制数量
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"获取包数据失败 {package_names[i]}: {str(result)}")
                elif result:
                    package_data[package_names[i]] = result
            
            return {"packages": package_data} if package_data else {}
            
        except Exception as e:
            logger.error(f"收集包数据失败: {str(e)}")
            return {}
    
    def _extract_github_url(self, internal_data: Dict[str, Any]) -> Optional[str]:
        """从内部数据中提取GitHub URL"""
        try:
            # 从项目元数据中提取
            project_metadata = internal_data.get("project_metadata", {})

            # 从依赖信息中提取
            dependencies = internal_data.get("dependencies", {})
            external_deps = dependencies.get("external_dependencies", [])

            # 查找可能的GitHub URL
            for dep in external_deps:
                if isinstance(dep, dict):
                    source_url = dep.get("source_url", "")
                    if "github.com" in source_url:
                        return source_url

            # 从文件组织信息中查找配置文件
            file_org = internal_data.get("file_organization", {})
            important_files = file_org.get("important_files", [])

            # 查找package.json等配置文件中的repository信息
            for file_info in important_files:
                if isinstance(file_info, dict):
                    file_name = file_info.get("name", "").lower()
                    if file_name in ["package.json", "pyproject.toml", "cargo.toml"]:
                        # 这里可以进一步解析文件内容，但需要实际文件访问
                        # 暂时返回None，让外部数据源处理
                        pass

            return None

        except Exception as e:
            logger.error(f"提取GitHub URL失败: {str(e)}")
            return None
    
    def _extract_package_names(self, internal_data: Dict[str, Any]) -> List[str]:
        """从内部数据中提取包名称"""
        try:
            package_names = []

            # 从项目元数据中提取项目名称
            project_metadata = internal_data.get("project_metadata", {})
            project_name = project_metadata.get("project_name", "")
            if project_name:
                package_names.append(project_name)

            # 从依赖信息中提取主要依赖包名
            dependencies = internal_data.get("dependencies", {})
            external_deps = dependencies.get("external_dependencies", [])

            # 提取前几个主要依赖作为相关包
            for dep in external_deps[:3]:  # 只取前3个主要依赖
                if isinstance(dep, dict):
                    dep_name = dep.get("name", "")
                    if dep_name and dep.get("is_direct", False):
                        package_names.append(dep_name)

            # 从结构信息中提取包管理器相关信息
            structure = internal_data.get("structure", {})
            dependency_components = structure.get("dependency_components", [])

            for dep_comp in dependency_components:
                if isinstance(dep_comp, dict):
                    comp_name = dep_comp.get("name", "")
                    if comp_name and comp_name not in ["requirements.txt", "package.json", "Cargo.toml"]:
                        # 从文件名推断包名（简化处理）
                        if "." not in comp_name:
                            package_names.append(comp_name)

            # 去重并返回
            return list(set(package_names))

        except Exception as e:
            logger.error(f"提取包名称失败: {str(e)}")
            return []
    
    def get_data_for_section(self, section_id: str) -> Dict[str, Any]:
        """
        获取特定章节所需的数据
        
        Args:
            section_id: 章节标识符
            
        Returns:
            Dict[str, Any]: 章节相关数据
        """
        try:
            # 定义各章节所需的数据映射
            section_data_mapping = {
                "introduction": ["project_structure", "package_info", "github.repository_info"],
                "features": ["project_structure", "package_info", "github.repository_info"],
                "installation": ["package_info", "dependency_files", "build_info"],
                "usage": ["entry_points", "config_files", "documentation"],
                "api_reference": ["project_structure", "documentation"],
                "architecture": ["project_structure", "config_files"],
                "dependencies": ["dependency_files", "packages"],
                "contributing": ["documentation", "github.contributors"],
                "license": ["documentation", "github.license"]
            }
            
            required_data_paths = section_data_mapping.get(section_id, [])
            section_data = {}
            
            for data_path in required_data_paths:
                data_value = self._get_nested_data(self.aggregated_data, data_path)
                if data_value is not None:
                    section_data[data_path] = data_value
            
            return section_data
            
        except Exception as e:
            logger.error(f"获取章节数据失败 {section_id}: {str(e)}")
            return {}
    
    def _get_nested_data(self, data: Dict[str, Any], path: str) -> Any:
        """获取嵌套数据"""
        try:
            keys = path.split('.')
            current_data = data
            
            for key in keys:
                if isinstance(current_data, dict) and key in current_data:
                    current_data = current_data[key]
                else:
                    return None
            
            return current_data
            
        except Exception:
            return None
    
    def get_project_characteristics(self) -> Dict[str, Any]:
        """获取项目特征信息"""
        try:
            internal_data = self.aggregated_data.get("internal", {})
            external_data = self.aggregated_data.get("external", {})
            
            # 基础特征
            characteristics = {
                "has_web_interface": False,
                "has_api_endpoints": False,
                "has_cli_interface": False,
                "has_database": False,
                "has_frontend": False,
                "has_backend": False,
                "has_docker": False,
                "has_cloud_config": False,
                "has_ci_cd": False,
                "has_unit_tests": False,
                "has_api_docs": False,
                "has_user_guide": False,
                "has_dev_guide": False,
                "has_contributing_guide": False,
                "has_license": False,
                "primary_language": "",
                "frameworks": [],
                "build_tools": [],
                "package_managers": []
            }
            
            # 从内部数据推断特征
            self._infer_characteristics_from_internal(characteristics, internal_data)
            
            # 从外部数据补充特征
            self._infer_characteristics_from_external(characteristics, external_data)
            
            return characteristics
            
        except Exception as e:
            logger.error(f"获取项目特征失败: {str(e)}")
            return {}
    
    def _infer_characteristics_from_internal(self, characteristics: Dict[str, Any], internal_data: Dict[str, Any]):
        """从内部数据推断特征"""
        try:
            # 从技术栈信息推断特征
            tech_stack = internal_data.get("tech_stack", {})
            characteristics["primary_language"] = tech_stack.get("primary_language", "")
            characteristics["frameworks"] = tech_stack.get("frameworks", [])
            characteristics["build_tools"] = tech_stack.get("build_tools", [])

            # 从文件组织信息推断特征
            file_org = internal_data.get("file_organization", {})
            files_by_type = file_org.get("files_by_type", {})

            # 检查Docker相关文件
            characteristics["has_docker"] = any(
                "dockerfile" in ext.lower() or "docker" in ext.lower()
                for ext in files_by_type.keys()
            )

            # 检查许可证文件
            characteristics["has_license"] = any(
                "license" in ext.lower() for ext in files_by_type.keys()
            )

            # 从结构信息推断特征
            structure = internal_data.get("structure", {})

            # 检查测试组件
            test_components = structure.get("test_components", [])
            characteristics["has_unit_tests"] = len(test_components) > 0

            # 检查构建部署组件
            build_components = structure.get("build_components", [])
            characteristics["has_ci_cd"] = any(
                comp.get("build_tool") == "ci_cd" for comp in build_components
            )
            characteristics["has_docker"] = characteristics["has_docker"] or any(
                comp.get("build_tool") in ["dockerfile", "docker_compose"] for comp in build_components
            )

            # 检查文档组件
            doc_components = structure.get("doc_components", [])
            characteristics["has_contributing_guide"] = any(
                comp.get("doc_category") == "contributing" for comp in doc_components
            )
            characteristics["has_user_guide"] = any(
                comp.get("doc_category") == "user_guide" for comp in doc_components
            )
            characteristics["has_dev_guide"] = any(
                comp.get("doc_category") == "developer_guide" for comp in doc_components
            )
            characteristics["has_api_docs"] = any(
                comp.get("doc_category") == "api_docs" for comp in doc_components
            )

            # 从API设计信息推断特征
            api_design = internal_data.get("api_design", {})
            characteristics["has_api_endpoints"] = api_design.get("total_endpoints", 0) > 0
            api_types = api_design.get("api_types", [])
            characteristics["has_web_interface"] = "rest" in api_types or "graphql" in api_types

            # 从模块信息推断特征
            modules = internal_data.get("modules", {})
            api_modules = modules.get("api_modules", [])
            characteristics["has_api_endpoints"] = characteristics["has_api_endpoints"] or len(api_modules) > 0

        except Exception as e:
            logger.error(f"从内部数据推断特征失败: {str(e)}")
    
    def _infer_characteristics_from_external(self, characteristics: Dict[str, Any], external_data: Dict[str, Any]):
        """从外部数据补充特征"""
        try:
            # GitHub数据
            github_data = external_data.get("github", {})
            repo_info = github_data.get("repository_info", {})
            
            if repo_info:
                characteristics["has_license"] = characteristics["has_license"] or bool(repo_info.get("license"))
                if repo_info.get("language"):
                    characteristics["primary_language"] = characteristics["primary_language"] or repo_info["language"]
            
        except Exception as e:
            logger.error(f"从外部数据补充特征失败: {str(e)}")
    
    def clear_cache(self):
        """清空所有缓存"""
        self.aggregated_data.clear()
        self.data_freshness.clear()

        # 清空内部数据源缓存
        if hasattr(self.internal_source, 'clear_cache'):
            self.internal_source.clear_cache()

        # 清空外部数据源缓存（如果有clear_cache方法）
        for source in [self.github_source, self.package_source]:
            if hasattr(source, 'clear_cache'):
                source.clear_cache()

        # 清空社区数据源缓存
        for source in [self.stackoverflow_source, self.reddit_source,
                      self.hackernews_source, self.devto_source, self.producthunt_source]:
            if source and hasattr(source, 'clear_cache'):
                source.clear_cache()

        logger.info("数据缓存已清空")

    def set_analysis_state(self, analysis_state: AnalysisState) -> None:
        """
        设置分析状态到内部数据源

        Args:
            analysis_state: AnalysisState对象
        """
        try:
            # 更新分析状态
            self.analysis_state = analysis_state
            self.project_path = Path(analysis_state.project_path)

            # 重新创建内部数据源
            self.internal_source = InternalDataSource(analysis_state)
            logger.info("分析状态已设置到内部数据源")
        except Exception as e:
            logger.error(f"设置分析状态失败: {str(e)}")

    def get_analysis_summary(self) -> Dict[str, Any]:
        """
        获取分析数据摘要

        Returns:
            Dict[str, Any]: 分析摘要
        """
        try:
            # 从缓存的聚合数据中提取摘要
            internal_data = self.aggregated_data.get("internal", {})
            if not internal_data:
                # 如果没有缓存数据，提取基本摘要
                internal_data = self.internal_source.extract_all_data()

            # 构建摘要
            summary = {
                "project_name": internal_data.get("project_metadata", {}).get("project_name", ""),
                "primary_language": internal_data.get("tech_stack", {}).get("primary_language", ""),
                "total_modules": internal_data.get("modules", {}).get("total_modules", 0),
                "total_dependencies": internal_data.get("dependencies", {}).get("dependency_count", 0),
                "has_api": internal_data.get("api_design", {}).get("total_endpoints", 0) > 0,
                "overall_quality": internal_data.get("code_quality", {}).get("overall_quality_score", 0.0)
            }

            return summary
        except Exception as e:
            logger.error(f"获取分析摘要失败: {str(e)}")
            return {}
    
    async def refresh_data(self, force: bool = False) -> Dict[str, Any]:
        """
        刷新数据
        
        Args:
            force: 是否强制刷新
            
        Returns:
            Dict[str, Any]: 刷新后的数据
        """
        if force:
            self.clear_cache()
        
        return await self.collect_all_data()

    async def _collect_community_data(
        self,
        internal_data: Dict[str, Any],
        external_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """收集社区平台数据"""
        try:
            logger.info("开始收集社区平台数据")

            # 提取项目信息用于搜索
            project_info = self._extract_project_info(internal_data, external_data)

            community_tasks = []

            # Stack Overflow数据
            if self.stackoverflow_source:
                community_tasks.append(
                    self._collect_stackoverflow_data(project_info)
                )

            # Reddit数据
            if self.reddit_source:
                community_tasks.append(
                    self._collect_reddit_data(project_info)
                )

            # Hacker News数据
            if self.hackernews_source:
                community_tasks.append(
                    self._collect_hackernews_data(project_info)
                )

            # Dev.to数据
            if self.devto_source:
                community_tasks.append(
                    self._collect_devto_data(project_info)
                )

            # Product Hunt数据
            if self.producthunt_source:
                community_tasks.append(
                    self._collect_producthunt_data(project_info)
                )

            # 并发执行社区数据收集
            community_data = {}
            if community_tasks:
                community_results = await asyncio.gather(*community_tasks, return_exceptions=True)

                for i, result in enumerate(community_results):
                    if isinstance(result, Exception):
                        logger.error(f"社区数据收集失败 {i}: {str(result)}")
                    elif result:
                        community_data.update(result)

            # 分析和汇总社区数据
            if community_data:
                community_data["analysis"] = self._analyze_community_data(community_data)

            logger.info("社区平台数据收集完成")
            return community_data

        except Exception as e:
            logger.error(f"收集社区数据失败: {str(e)}")
            return {}

    def _extract_project_info(
        self,
        internal_data: Dict[str, Any],
        external_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """提取项目信息用于社区搜索"""
        try:
            project_info = {}

            # 从项目元数据提取
            project_metadata = internal_data.get("project_metadata", {})
            project_info["project_name"] = project_metadata.get("project_name", "")
            project_info["project_type"] = project_metadata.get("project_type", "")

            # 从技术栈信息提取
            tech_stack = internal_data.get("tech_stack", {})
            project_info["language"] = tech_stack.get("primary_language", "")
            project_info["frameworks"] = tech_stack.get("frameworks", [])
            project_info["databases"] = tech_stack.get("databases", [])

            # 从结构信息提取描述
            structure = internal_data.get("structure", {})
            doc_components = structure.get("doc_components", [])
            for doc in doc_components:
                if isinstance(doc, dict) and doc.get("doc_category") == "readme":
                    project_info["description"] = doc.get("description", "")
                    break

            # 从GitHub数据提取
            if "github" in external_data:
                github_data = external_data["github"]
                if "repository_info" in github_data:
                    repo_info = github_data["repository_info"]
                    project_info["project_name"] = repo_info.get("name", project_info.get("project_name", ""))
                    project_info["description"] = repo_info.get("description", project_info.get("description", ""))
                    project_info["github_url"] = repo_info.get("html_url", "")
                    project_info["website_url"] = repo_info.get("homepage", "")
                    project_info["language"] = repo_info.get("language", project_info.get("language", ""))
                    project_info["topics"] = repo_info.get("topics", [])

            # 构建搜索关键词
            keywords = set()

            # 添加框架关键词
            if project_info.get("frameworks"):
                keywords.update([fw.lower() for fw in project_info["frameworks"]])

            # 添加数据库关键词
            if project_info.get("databases"):
                keywords.update([db.lower() for db in project_info["databases"]])

            # 添加GitHub topics
            if project_info.get("topics"):
                keywords.update(project_info["topics"])

            # 添加语言关键词
            if project_info.get("language"):
                keywords.add(project_info["language"].lower())

            # 添加项目类型关键词
            if project_info.get("project_type"):
                keywords.add(project_info["project_type"].lower())

            project_info["search_keywords"] = list(keywords)[:10]  # 限制关键词数量

            return project_info

        except Exception as e:
            logger.error(f"提取项目信息失败: {str(e)}")
            return {"project_name": "unknown", "search_keywords": []}

    async def _collect_stackoverflow_data(self, project_info: Dict[str, Any]) -> Dict[str, Any]:
        """收集Stack Overflow数据"""
        try:
            data = await self.stackoverflow_source.fetch_data(
                project_name=project_info.get("project_name"),
                tags=project_info.get("search_keywords", [])[:5],
                keywords=project_info.get("search_keywords", [])[:3]
            )
            return {"stackoverflow": data} if data else {}
        except Exception as e:
            logger.error(f"收集Stack Overflow数据失败: {str(e)}")
            return {}

    async def _collect_reddit_data(self, project_info: Dict[str, Any]) -> Dict[str, Any]:
        """收集Reddit数据"""
        try:
            data = await self.reddit_source.fetch_data(
                project_name=project_info.get("project_name"),
                keywords=project_info.get("search_keywords", [])[:3]
            )
            return {"reddit": data} if data else {}
        except Exception as e:
            logger.error(f"收集Reddit数据失败: {str(e)}")
            return {}

    async def _collect_hackernews_data(self, project_info: Dict[str, Any]) -> Dict[str, Any]:
        """收集Hacker News数据"""
        try:
            data = await self.hackernews_source.fetch_data(
                project_name=project_info.get("project_name"),
                keywords=project_info.get("search_keywords", [])[:3],
                github_url=project_info.get("github_url")
            )
            return {"hackernews": data} if data else {}
        except Exception as e:
            logger.error(f"收集Hacker News数据失败: {str(e)}")
            return {}

    async def _collect_devto_data(self, project_info: Dict[str, Any]) -> Dict[str, Any]:
        """收集Dev.to数据"""
        try:
            data = await self.devto_source.fetch_data(
                project_name=project_info.get("project_name"),
                tags=project_info.get("search_keywords", [])[:5],
                keywords=project_info.get("search_keywords", [])[:3]
            )
            return {"devto": data} if data else {}
        except Exception as e:
            logger.error(f"收集Dev.to数据失败: {str(e)}")
            return {}

    async def _collect_producthunt_data(self, project_info: Dict[str, Any]) -> Dict[str, Any]:
        """收集Product Hunt数据"""
        try:
            data = await self.producthunt_source.fetch_data(
                project_name=project_info.get("project_name"),
                keywords=project_info.get("search_keywords", [])[:3],
                website_url=project_info.get("website_url")
            )
            return {"producthunt": data} if data else {}
        except Exception as e:
            logger.error(f"收集Product Hunt数据失败: {str(e)}")
            return {}

    def _analyze_community_data(self, community_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析社区数据"""
        try:
            analysis = {
                "overall_sentiment": self._calculate_overall_sentiment(community_data),
                "community_activity": self._calculate_community_activity(community_data),
                "influence_score": self._calculate_influence_score(community_data),
                "trending_status": self._calculate_trending_status(community_data),
                "support_level": self._calculate_support_level(community_data)
            }

            return analysis

        except Exception as e:
            logger.error(f"分析社区数据失败: {str(e)}")
            return {}

    def _calculate_overall_sentiment(self, community_data: Dict[str, Any]) -> str:
        """计算整体情感倾向"""
        try:
            sentiments = []

            # 收集各平台的情感分析结果
            for platform, data in community_data.items():
                if platform == "analysis":
                    continue

                analysis = data.get("analysis", {})
                if "community_sentiment" in analysis:
                    sentiment = analysis["community_sentiment"].get("sentiment", "neutral")
                    confidence = analysis["community_sentiment"].get("confidence", 0.0)
                    sentiments.append((sentiment, confidence))

            if not sentiments:
                return "unknown"

            # 加权计算整体情感
            positive_weight = sum(conf for sent, conf in sentiments if sent == "positive")
            negative_weight = sum(conf for sent, conf in sentiments if sent == "negative")
            neutral_weight = sum(conf for sent, conf in sentiments if sent == "neutral")

            if positive_weight > negative_weight and positive_weight > neutral_weight:
                return "positive"
            elif negative_weight > positive_weight and negative_weight > neutral_weight:
                return "negative"
            else:
                return "neutral"

        except Exception as e:
            logger.error(f"计算整体情感失败: {str(e)}")
            return "unknown"

    def _calculate_community_activity(self, community_data: Dict[str, Any]) -> str:
        """计算社区活跃度"""
        try:
            activity_scores = []

            for platform, data in community_data.items():
                if platform == "analysis":
                    continue

                analysis = data.get("analysis", {})

                # 根据不同平台的活跃度指标计算分数
                if platform == "stackoverflow":
                    activity = analysis.get("community_activity", {})
                    level = activity.get("activity_level", "low")
                    score = {"high": 3, "medium": 2, "low": 1}.get(level, 0)
                    activity_scores.append(score)

                elif platform == "reddit":
                    engagement = analysis.get("user_engagement", {})
                    level = engagement.get("engagement_level", "low")
                    score = {"high": 3, "medium": 2, "low": 1}.get(level, 0)
                    activity_scores.append(score)

                elif platform == "hackernews":
                    interest = analysis.get("community_interest", {})
                    level = interest.get("interest_level", "very_low")
                    score = {"very_high": 4, "high": 3, "medium": 2, "low": 1, "very_low": 0}.get(level, 0)
                    activity_scores.append(score)

            if not activity_scores:
                return "unknown"

            avg_score = sum(activity_scores) / len(activity_scores)

            if avg_score >= 3:
                return "very_high"
            elif avg_score >= 2.5:
                return "high"
            elif avg_score >= 1.5:
                return "medium"
            elif avg_score >= 0.5:
                return "low"
            else:
                return "very_low"

        except Exception as e:
            logger.error(f"计算社区活跃度失败: {str(e)}")
            return "unknown"

    def _calculate_influence_score(self, community_data: Dict[str, Any]) -> float:
        """计算影响力评分"""
        try:
            total_score = 0.0
            platform_count = 0

            for platform, data in community_data.items():
                if platform == "analysis":
                    continue

                analysis = data.get("analysis", {})
                platform_score = 0.0

                if platform == "stackoverflow":
                    activity = analysis.get("community_activity", {})
                    total_questions = activity.get("total_questions", 0)
                    avg_score = activity.get("average_score", 0)
                    platform_score = min(100, (total_questions * 2 + avg_score * 5))

                elif platform == "reddit":
                    engagement = analysis.get("user_engagement", {})
                    total_posts = engagement.get("total_posts", 0)
                    avg_score = engagement.get("average_score_per_post", 0)
                    platform_score = min(100, (total_posts * 3 + avg_score * 2))

                elif platform == "hackernews":
                    influence = analysis.get("influence_score", 0)
                    platform_score = min(100, influence)

                elif platform == "devto":
                    availability = analysis.get("content_availability", {})
                    total_articles = availability.get("total_articles", 0)
                    avg_reactions = availability.get("average_reactions", 0)
                    platform_score = min(100, (total_articles * 5 + avg_reactions))

                elif platform == "producthunt":
                    reception = analysis.get("market_reception", {})
                    total_votes = reception.get("total_votes", 0)
                    platform_score = min(100, total_votes / 10)

                total_score += platform_score
                platform_count += 1

            if platform_count == 0:
                return 0.0

            return round(total_score / platform_count, 2)

        except Exception as e:
            logger.error(f"计算影响力评分失败: {str(e)}")
            return 0.0

    def _calculate_trending_status(self, community_data: Dict[str, Any]) -> str:
        """计算趋势状态"""
        try:
            trending_indicators = []

            for platform, data in community_data.items():
                if platform == "analysis":
                    continue

                analysis = data.get("analysis", {})

                if platform == "hackernews":
                    trending = analysis.get("trending_status", {})
                    level = trending.get("trending_level", "none")
                    trending_indicators.append(level)

                elif platform == "reddit":
                    trending = analysis.get("trending_status", {})
                    level = trending.get("trending_level", "none")
                    trending_indicators.append(level)

                elif platform == "devto":
                    trending = analysis.get("trending_status", {})
                    level = trending.get("trending_level", "none")
                    trending_indicators.append(level)

                elif platform == "producthunt":
                    trending = analysis.get("trending_status", {})
                    level = trending.get("trending_level", "none")
                    trending_indicators.append(level)

            if not trending_indicators:
                return "unknown"

            # 统计趋势级别
            viral_count = trending_indicators.count("viral")
            trending_count = trending_indicators.count("trending")
            popular_count = trending_indicators.count("popular")

            if viral_count >= 1:
                return "viral"
            elif trending_count >= 2:
                return "trending"
            elif trending_count >= 1 or popular_count >= 2:
                return "popular"
            else:
                return "normal"

        except Exception as e:
            logger.error(f"计算趋势状态失败: {str(e)}")
            return "unknown"

    def _calculate_support_level(self, community_data: Dict[str, Any]) -> str:
        """计算支持水平"""
        try:
            support_scores = []

            for platform, data in community_data.items():
                if platform == "analysis":
                    continue

                analysis = data.get("analysis", {})

                if platform == "stackoverflow":
                    support = analysis.get("community_support", {})
                    level = support.get("support_level", "unknown")
                    score = {"excellent": 4, "good": 3, "moderate": 2, "limited": 1}.get(level, 0)
                    support_scores.append(score)

                elif platform == "reddit":
                    engagement = analysis.get("user_engagement", {})
                    level = engagement.get("engagement_level", "none")
                    score = {"very_high": 4, "high": 3, "medium": 2, "low": 1, "very_low": 0}.get(level, 0)
                    support_scores.append(score)

                elif platform == "devto":
                    educational = analysis.get("educational_value", {})
                    level = educational.get("educational_level", "none")
                    score = {"very_high": 4, "high": 3, "medium": 2, "low": 1, "very_low": 0}.get(level, 0)
                    support_scores.append(score)

            if not support_scores:
                return "unknown"

            avg_score = sum(support_scores) / len(support_scores)

            if avg_score >= 3.5:
                return "excellent"
            elif avg_score >= 2.5:
                return "good"
            elif avg_score >= 1.5:
                return "moderate"
            elif avg_score >= 0.5:
                return "limited"
            else:
                return "poor"

        except Exception as e:
            logger.error(f"计算支持水平失败: {str(e)}")
            return "unknown"
