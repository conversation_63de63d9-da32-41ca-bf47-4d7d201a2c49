"""
依赖注入容器模块
使用 dependency_injector 实现依赖注入容器
"""
from dependency_injector import containers, providers
import structlog
import redis
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import MongoClient

from app.core.config import settings
from app.core.database import Database
from app.services.rbac.auth import AuthService
from app.services.rbac.user import UserService
from app.services.rbac.role import RoleService
from app.services.rbac.permission import PermissionService
from app.services.rbac.permission_group import PermissionGroupService
from app.services.rbac.oauth import OAuthService
from app.services.rbac.email import EmailService
from .providers import SessionProvider, AsyncSessionProvider, RedisProvider, AsyncMongoProvider, SyncMongoProvider
from ...services.github.github_downloader import GitHubDownloader
from ...services.github.github_project import GitHubProjectService
from ...services.github.github_readme_generater import GitHubReadmeGenerate
from ...services.github.pending_repository_service import PendingRepositoryService
from ...services.rbac.phone import PhoneService
from ...services.statistics import StatisticsService
from ...services.elasticsearch.client import ElasticsearchClient
from ...services.elasticsearch.project_indexer import ProjectIndexer

logger = structlog.get_logger(__name__)

class Container(containers.DeclarativeContainer):
    """依赖注入容器"""

    # 连接配置,需要将容器写入到需要用到容器的包或者模块
    wiring_config = containers.WiringConfiguration(
        packages=[
            'app.core.middleware',
            'app.api.v1',
            'app.services.reasoning.workflow',
        ],
    )

    # 配置
    config = providers.Configuration()

    # # 日志提供者
    # logger_provider = providers.Factory(
    #     LoggerProvider,
    #     name="app"
    # )

    # 数据库服务
    database = providers.Singleton(
        Database,
        url=settings.postgresql.DATABASE_URL,
        echo=settings.postgresql.POSTGRESQL_ECHO_SQL,
        pool_size=settings.postgresql.POSTGRESQL_POOL_SIZE,
        max_overflow=settings.postgresql.POSTGRESQL_MAX_OVERFLOW,
        pool_timeout=settings.postgresql.POSTGRESQL_POOL_TIMEOUT,
        pool_recycle=settings.postgresql.POSTGRESQL_POOL_RECYCLE,
        pool_pre_ping=settings.postgresql.POSTGRESQL_POOL_PRE_PING
    )

    # 同步数据库提供者
    session_provider = providers.Singleton(
        SessionProvider,
        database=database
    )

    # 异步数据库提供者
    async_session_provider = providers.Singleton(
        AsyncSessionProvider,
        database=database
    )



    # Redis连接池
    redis_pool = providers.Singleton(
        redis.ConnectionPool.from_url,
        url=settings.redis.REDIS_URL,
        decode_responses=True
    )

    # Redis客户端
    redis_client = providers.Singleton(
        redis.Redis,
        connection_pool=redis_pool
    )

    # Redis提供者
    redis_provider = providers.Singleton(
        RedisProvider,
        redis_client=redis_client
    )

    # MongoDB客户端
    mongodb_client = providers.Singleton(
        AsyncIOMotorClient,
        settings.mongodb.MONGODB_URL
    )

    # MongoDB异步提供者
    mongodb_provider = providers.Singleton(
        AsyncMongoProvider,
        mongo_client=mongodb_client,
        database_name=settings.mongodb.MONGODB_DB
    )

    # MongoDB同步客户端
    sync_mongodb_client = providers.Singleton(
        MongoClient,
        settings.mongodb.MONGODB_URL
    )

    # MongoDB同步提供者
    sync_mongodb_provider = providers.Singleton(
        SyncMongoProvider,
        mongo_client=sync_mongodb_client,
        database_name=settings.mongodb.MONGODB_DB
    )

    # 邮件服务
    email_service = providers.Singleton(EmailService)

    # 手机服务
    phone_service = providers.Singleton(PhoneService)


    github_downloader = providers.Singleton(
        GitHubDownloader,
        session=session_provider,
        async_session=async_session_provider
    )

    github_readme_generate = providers.Singleton(
        GitHubReadmeGenerate,
        session=session_provider,
        async_session=async_session_provider
    )

    # 用户服务
    user_service = providers.Factory(
        UserService,
        session=session_provider,
        async_session=async_session_provider
    )

    # 认证服务
    auth_service = providers.Factory(
        AuthService,
        session=session_provider,
        async_session=async_session_provider,
        redis=redis_provider,
        user_service=user_service
    )

    # Elasticsearch客户端
    elasticsearch_client = providers.Singleton(
        ElasticsearchClient
    )

    # GitHub Elasticsearch搜索服务
    project_indexer = providers.Factory(
        ProjectIndexer,
        es_client=elasticsearch_client
    )

    # GitHub项目服务
    github_project_service = providers.Factory(
        GitHubProjectService,
        session=session_provider,
        async_session=async_session_provider,
        indexer=project_indexer
    )

    # GitHub待处理仓库服务
    pending_repository_service = providers.Factory(
        PendingRepositoryService,
        session=session_provider,
        async_session=async_session_provider
    )

    # OAuth服务
    oauth_service = providers.Factory(
        OAuthService,
        session=session_provider,
        async_session=async_session_provider,
        redis=redis_provider,
        auth_service=auth_service,
        user_service=user_service
    )

    # 角色服务
    role_service = providers.Factory(
        RoleService,
        session=session_provider,
        async_session=async_session_provider,
    )

    # 权限服务
    permission_service = providers.Factory(
        PermissionService,
        session=session_provider,
        async_session=async_session_provider,
    )

    # 权限组服务
    permission_group_service = providers.Factory(
        PermissionGroupService,
        session=session_provider,
        async_session=async_session_provider,
    )

    statistics_service = providers.Factory(
        StatisticsService,
        session=session_provider,
        async_session=async_session_provider
    )