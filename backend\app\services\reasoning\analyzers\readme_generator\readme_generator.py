#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
README生成器

负责并发调用所有子生成器生成完整的README文档
"""
import logging
import os
import asyncio
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone

from langchain.chains import <PERSON><PERSON><PERSON><PERSON>
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser

from .section_models import (
    ReadmeDocument,
    ReadmeSection,
    IntroductionSection,
    FeaturesSection,
    InstallationSection,
    UsageSection,
    ApiReferenceSection,
    ArchitectureSection,
    DependenciesSection,
    ContributionSection,
    LicenseSection
)

from ....ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser
from .generators.introduction_generator import IntroductionGenerator
from .generators.features_generator import FeaturesGenerator
from .generators.installation_generator import InstallationGenerator
from .generators.usage_generator import UsageGenerator
from .generators.api_reference_generator import ApiReferenceGenerator
from .generators.architecture_generator import ArchitectureGenerator
from .generators.dependencies_generator import DependenciesGenerator
from .generators.contribution_generator import ContributionGenerator
from .generators.license_generator import LicenseGenerator
from .generators import Generator

logger = logging.getLogger(__name__)


@AgentManager.register("ReadmeGeneratorAgent")
class ReadmeGenerator(BaseAgent):
    """
    README生成器
    负责并发调用所有子生成器生成完整的README文档
    """

    def __init__(self):
        """初始化README生成器"""
        super().__init__()
        self.name = "ReadmeGenerator"
        self.section_generators: Dict[str, Generator] = {}
        self.section_tasks: Dict[str, Any] = {}

    async def _initialize(self) -> None:
        """初始化生成器特定资源"""
        logger.info("初始化README生成器特定资源")

        # 初始化所有子生成器
        self.section_generators = {
            "introduction": IntroductionGenerator(llm=self.llm),
            "features": FeaturesGenerator(llm=self.llm),
            "installation": InstallationGenerator(llm=self.llm),
            "usage": UsageGenerator(llm=self.llm),
            "api_reference": ApiReferenceGenerator(llm=self.llm),
            "architecture": ArchitectureGenerator(llm=self.llm),
            "dependencies": DependenciesGenerator(llm=self.llm),
            "contribution": ContributionGenerator(llm=self.llm),
            "license": LicenseGenerator(llm=self.llm)
        }

    async def _create_chain(self) -> None:
        """初始化处理链"""
        logger.info("初始化README生成器处理链")

        # 初始化每个子生成器的处理链
        for name, generator in self.section_generators.items():
            try:
                await generator.create_chain()
                logger.info(f"初始化了 {name} 生成器的处理链")
            except Exception as e:
                logger.error(f"初始化 {name} 生成器的处理链失败: {str(e)}")
                # 继续初始化其他生成器，不要因为一个失败就中断整个过程

    async def prepare_input(self, input_data: Dict[str, Any]) -> AgentInput:
        """
        准备输入数据

        Args:
            input_data: 原始输入数据

        Returns:
            AgentInput: 准备后的输入数据
        """
        input_data: AgentInput = await super().prepare_input(input_data)

        # 这里可以添加一些通用的输入数据处理逻辑
        # 例如，从项目结构中提取项目名称、描述等

        # 按顺序为每个子生成器准备输入数据
        for section_type, generator in self.section_generators.items():
            try:
                logger.info(f"开始为 {section_type} 生成器准备输入数据")
                await generator.prepare_input(input_data)
                logger.info(f"{section_type} 生成器输入数据准备完成")
            except Exception as e:
                logger.error(f"为 {section_type} 生成器准备输入数据失败: {str(e)}")

        logger.info(f"所有子生成器的输入数据准备完成")

        return input_data

    async def _process(
        self,
        input_data: AgentInput
    ) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询，并发调用所有子生成器

        Args:
            input_data: 输入数据

        Returns:
            Tuple[Any, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 创建README文档对象
            readme_document = ReadmeDocument(
                project_id=input_data.parameters.get("project_id", ""),
            )

            # 创建任务列表，并发执行所有子生成器的处理
            process_tasks = []
            for section_type, generator in self.section_generators.items():
                try:
                    task = asyncio.create_task(generator.process())
                    process_tasks.append((section_type, task))
                    self.section_tasks[section_type] = task
                    logger.info(f"开始处理 {section_type} 章节")
                except Exception as e:
                    logger.error(f"启动 {section_type} 章节处理失败: {str(e)}")

            # 等待所有子生成器完成
            for section_type, task in process_tasks:
                try:
                    result = await task
                    # 解包结果，处理可能的不同返回格式
                    section_result, section_metadata = result

                    if section_result:
                        # 将生成的章节添加到README文档中
                        readme_document.add_section(section_type, section_result)
                        logger.info(f"添加了 {section_type} 章节到README文档")
                except Exception as e:
                    logger.error(f"{section_type} 章节生成失败: {str(e)}")

            return readme_document, {
                "generator": "readme_generator"
            }

        except Exception as e:
            logger.error(f"README生成失败: {str(e)}")
            return None, {"error": str(e)}

    async def _shutdown(self) -> None:
        """关闭生成器资源"""
        logger.info("关闭README生成器资源")
        # 释放其他资源
        self.section_generators = {}
        self.section_tasks = {}
        self.llm = None
