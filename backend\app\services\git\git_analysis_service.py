"""
Git版本控制分析服务

提供基于Git提交记录的高级分析功能，包括：
1. 提交活跃度分析
2. 代码变更趋势分析
3. 贡献者分析
4. 文件修改热点分析
5. 提交行为模式分析
"""
import os
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import Counter, defaultdict

import structlog
from pydantic import Field

from app.exceptions.git import GitError
from app.services.git.git_service import GitService
from app.schemas.git import GitCommitWithStats, GitAuthor, GitCommit
from app.schemas.git.git_analysis import (
    GitActivityAnalysis, CommitFrequency, AuthorContribution, 
    ChangeTrend, FileHotspot, CommitPatternAnalysis,
    GitRepositoryAnalysis, TimeDistribution
)

logger = structlog.get_logger(__name__)


class GitAnalysisService:
    """Git版本控制分析服务
    
    提供基于Git提交记录的高级分析功能
    """
    
    def __init__(self, git_service: Optional[GitService] = None, repo_path: Optional[str] = None):
        """初始化Git分析服务
        
        Args:
            git_service: 已初始化的GitService实例
            repo_path: Git仓库路径，如果没有提供git_service则使用此路径创建新的GitService
        """
        if git_service:
            self.git_service = git_service
        elif repo_path:
            self.git_service = GitService(repo_path)
        else:
            self.git_service = None
    
    def load_repository(self, repo_path: str) -> None:
        """加载Git仓库
        
        Args:
            repo_path: Git仓库路径
            
        Raises:
            GitError: 如果仓库路径无效或不是Git仓库
        """
        if not self.git_service:
            self.git_service = GitService(repo_path)
        else:
            self.git_service.load_repository(repo_path)
    
    def get_complete_repository_analysis(self, branch: Optional[str] = None, 
                                         days: int = 365, max_commits: int = 1000) -> GitRepositoryAnalysis:
        """获取完整的仓库分析结果
        
        Args:
            branch: 分支名称，默认为当前分支
            days: 分析的时间范围（天数），默认为365天
            max_commits: 最大分析提交数量，默认为1000
            
        Returns:
            GitRepositoryAnalysis: 仓库分析结果
            
        Raises:
            GitError: 如果仓库未加载或操作失败
        """
        if not self.git_service or not self.git_service.repo:
            raise GitError(message="仓库未加载")
        
        try:
            # 获取仓库基本信息
            repo_info = self.git_service.get_repository_info()
            
            # 获取提交历史
            commits = self.git_service.get_commits(branch=branch, limit=max_commits)
            
            # 执行各项分析
            activity_analysis = self.analyze_commit_activity(commits, days)
            change_trends = self.analyze_code_change_trends(commits)
            contributor_analysis = self.analyze_contributors(commits)
            file_hotspots = self.analyze_file_hotspots(branch, days)
            commit_patterns = self.analyze_commit_patterns(commits)
            
            # 构建综合分析结果
            return GitRepositoryAnalysis(
                repository_name=repo_info.name,
                repository_path=repo_info.path,
                analysis_time=datetime.now(timezone.utc),
                branch_analyzed=branch or repo_info.current_branch,
                commit_count=len(commits),
                time_period_days=days,
                activity_analysis=activity_analysis,
                change_trends=change_trends,
                contributor_analysis=contributor_analysis,
                file_hotspots=file_hotspots,
                commit_patterns=commit_patterns
            )
        except Exception as e:
            logger.error("获取仓库分析失败", error=str(e), branch=branch)
            raise GitError(message=f"获取仓库分析失败: {str(e)}")

    def analyze_commit_activity(self, commits: List[GitCommitWithStats], days: int = 365) -> GitActivityAnalysis:
        """分析提交活跃度
        
        Args:
            commits: 提交记录列表
            days: 分析的时间范围（天数）
            
        Returns:
            GitActivityAnalysis: 提交活跃度分析结果
        """
        # 计算开始日期
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days)
        
        # 按时间段计数
        daily_counts = defaultdict(int)
        weekly_counts = defaultdict(int)
        monthly_counts = defaultdict(int)
        
        # 按小时分布
        hour_distribution = defaultdict(int)
        
        # 按星期几分布
        weekday_distribution = defaultdict(int)
        
        total_additions = 0
        total_deletions = 0
        
        for commit in commits:
            commit_time = commit.committed_date
            
            # 仅统计指定时间范围内的提交
            if commit_time < start_date:
                continue
                
            # 按日期计数
            date_str = commit_time.strftime("%Y-%m-%d")
            daily_counts[date_str] += 1
            
            # 按周计数 (以周一为一周的开始)
            year, week, _ = commit_time.isocalendar()
            weekly_key = f"{year}-W{week:02d}"
            weekly_counts[weekly_key] += 1
            
            # 按月计数
            month_str = commit_time.strftime("%Y-%m")
            monthly_counts[month_str] += 1
            
            # 按小时分布
            hour = commit_time.hour
            hour_distribution[hour] += 1
            
            # 按星期几分布 (0=周一, 6=周日)
            weekday = commit_time.weekday()
            weekday_distribution[weekday] += 1
            
            # 统计代码变更行数
            total_additions += commit.stats.additions
            total_deletions += commit.stats.deletions
        
        # 格式化每日提交频率
        daily_frequency = [
            CommitFrequency(date=key, count=value)
            for key, value in sorted(daily_counts.items())
        ]
        
        # 格式化每周提交频率
        weekly_frequency = [
            CommitFrequency(date=key, count=value)
            for key, value in sorted(weekly_counts.items())
        ]
        
        # 格式化每月提交频率
        monthly_frequency = [
            CommitFrequency(date=key, count=value)
            for key, value in sorted(monthly_counts.items())
        ]
        
        # 格式化时间分布
        hour_dist = [hour_distribution[hour] for hour in range(24)]
        weekday_dist = [weekday_distribution[day] for day in range(7)]
        
        time_distribution = TimeDistribution(
            hourly_distribution=hour_dist,
            weekday_distribution=weekday_dist
        )
        
        # 计算统计数据
        commit_count = len(daily_counts)
        
        if commit_count > 0:
            avg_commits_per_day = sum(daily_counts.values()) / len(daily_counts)
            max_commits_in_day = max(daily_counts.values()) if daily_counts else 0
        else:
            avg_commits_per_day = 0
            max_commits_in_day = 0
        
        # 构建活跃度分析结果
        return GitActivityAnalysis(
            total_commits=commit_count,
            time_period_days=days,
            avg_commits_per_day=avg_commits_per_day,
            max_commits_in_day=max_commits_in_day,
            total_additions=total_additions,
            total_deletions=total_deletions,
            daily_commit_frequency=daily_frequency,
            weekly_commit_frequency=weekly_frequency,
            monthly_commit_frequency=monthly_frequency,
            time_distribution=time_distribution
        )
    
    def analyze_code_change_trends(self, commits: List[GitCommitWithStats]) -> List[ChangeTrend]:
        """分析代码变更趋势
        
        Args:
            commits: 提交记录列表
            
        Returns:
            List[ChangeTrend]: 代码变更趋势分析结果
        """
        # 按月份聚合变更
        monthly_changes = defaultdict(lambda: {"additions": 0, "deletions": 0, "commits": 0})
        
        # 遍历提交记录
        for commit in commits:
            # 跳过没有统计信息的提交
            if not hasattr(commit, 'stats'):
                continue
                
            # 按月聚合
            month_key = commit.committed_date.strftime("%Y-%m")
            monthly_changes[month_key]["additions"] += commit.stats.additions
            monthly_changes[month_key]["deletions"] += commit.stats.deletions
            monthly_changes[month_key]["commits"] += 1
        
        # 构建趋势数据
        trends = []
        for month, stats in sorted(monthly_changes.items()):
            trend = ChangeTrend(
                time_period=month,
                additions=stats["additions"],
                deletions=stats["deletions"],
                commits=stats["commits"],
                net_change=stats["additions"] - stats["deletions"]
            )
            trends.append(trend)
        
        return trends
    
    def analyze_contributors(self, commits: List[GitCommitWithStats]) -> List[AuthorContribution]:
        """分析贡献者情况
        
        Args:
            commits: 提交记录列表
            
        Returns:
            List[AuthorContribution]: 贡献者分析结果
        """
        # 按作者聚合提交数据
        author_stats = defaultdict(lambda: {
            "name": "", 
            "email": "",
            "commits": 0, 
            "additions": 0, 
            "deletions": 0,
            "first_commit": None,
            "latest_commit": None
        })
        
        # 遍历提交记录
        for commit in commits:
            author_key = commit.author.email
            
            # 更新作者信息
            if not author_stats[author_key]["name"]:
                author_stats[author_key]["name"] = commit.author.name
                author_stats[author_key]["email"] = commit.author.email
            
            # 更新提交计数
            author_stats[author_key]["commits"] += 1
            
            # 更新代码变更统计
            if hasattr(commit, 'stats'):
                author_stats[author_key]["additions"] += commit.stats.additions
                author_stats[author_key]["deletions"] += commit.stats.deletions
            
            # 更新首次提交时间
            if (author_stats[author_key]["first_commit"] is None or 
                commit.committed_date < author_stats[author_key]["first_commit"]):
                author_stats[author_key]["first_commit"] = commit.committed_date
            
            # 更新最近提交时间
            if (author_stats[author_key]["latest_commit"] is None or 
                commit.committed_date > author_stats[author_key]["latest_commit"]):
                author_stats[author_key]["latest_commit"] = commit.committed_date
        
        # 构建贡献者列表
        contributors = []
        for _, stats in author_stats.items():
            contributor = AuthorContribution(
                name=stats["name"],
                email=stats["email"],
                commit_count=stats["commits"],
                additions=stats["additions"],
                deletions=stats["deletions"],
                first_commit=stats["first_commit"],
                latest_commit=stats["latest_commit"],
                active_days=(stats["latest_commit"] - stats["first_commit"]).days if stats["first_commit"] else 0
            )
            contributors.append(contributor)
        
        # 按提交数量排序
        contributors.sort(key=lambda x: x.commit_count, reverse=True)
        
        return contributors
    
    def analyze_file_hotspots(self, branch: Optional[str] = None, days: int = 365) -> List[FileHotspot]:
        """分析文件修改热点
        
        Args:
            branch: 分支名称，默认为当前分支
            days: 分析的时间范围（天数）
            
        Returns:
            List[FileHotspot]: 文件热点分析结果
        """
        if not self.git_service or not self.git_service.repo:
            return []
        
        try:
            # 获取仓库对象
            repo = self.git_service.repo
            
            # 计算开始日期
            end_date = datetime.now(timezone.utc)
            start_timestamp = (end_date - timedelta(days=days)).timestamp()
            
            # 确定要查询的分支
            commit_ref = branch if branch else "HEAD"
            
            # 使用Git命令获取文件修改统计
            # 命令格式：git log --pretty=format: --name-only --since=<date> <branch>
            git_cmd = repo.git
            raw_files = git_cmd.log(
                pretty="format:", 
                name_only=True, 
                since=int(start_timestamp), 
                branch=commit_ref
            ).split("\n")
            
            # 过滤空行并计数
            file_counter = Counter(filter(None, raw_files))
            
            # 构建热点数据
            hotspots = []
            for file_path, count in file_counter.most_common(50):  # 取前50个热点文件
                try:
                    # 获取文件的最后修改时间
                    file_commits = list(repo.iter_commits(paths=file_path, max_count=1))
                    latest_commit = file_commits[0] if file_commits else None
                    
                    hotspot = FileHotspot(
                        file_path=file_path,
                        change_frequency=count,
                        latest_change=datetime.fromtimestamp(latest_commit.committed_date, timezone.utc) if latest_commit else None,
                        file_type=os.path.splitext(file_path)[1][1:] if os.path.splitext(file_path)[1] else ""
                    )
                    hotspots.append(hotspot)
                except Exception as e:
                    logger.warning(f"分析文件 {file_path} 热点信息时出错: {str(e)}")
            
            return hotspots
        except Exception as e:
            logger.error("分析文件热点失败", error=str(e))
            return []
    
    def analyze_commit_patterns(self, commits: List[GitCommitWithStats]) -> CommitPatternAnalysis:
        """分析提交行为模式
        
        Args:
            commits: 提交记录列表
            
        Returns:
            CommitPatternAnalysis: 提交行为模式分析结果
        """
        # 初始化统计变量
        message_lengths = []
        has_emoji_count = 0
        has_issue_ref_count = 0
        
        # 常用提交类型前缀统计
        type_prefixes = Counter()
        
        # 正常工作时间的提交计数
        work_hour_commits = 0
        weekend_commits = 0
        
        # 分析提交信息
        for commit in commits:
            # 提交信息长度
            message = commit.message.strip()
            message_lengths.append(len(message))
            
            # 检查是否包含emoji
            if any(c for c in message if ord(c) > 127):  # 简单检查非ASCII字符
                has_emoji_count += 1
            
            # 检查是否引用Issue (#数字)
            if "#" in message and any(part.strip("#").isdigit() for part in message.split() if part.startswith("#")):
                has_issue_ref_count += 1
            
            # 检查常见提交类型前缀
            first_word = message.split(":", 1)[0].lower() if ":" in message else ""
            if first_word in ["feat", "fix", "docs", "style", "refactor", "test", "chore"]:
                type_prefixes[first_word] += 1
            
            # 检查工作时间
            commit_time = commit.committed_date
            # 工作时间: 周一至周五 (0-4) 的9点至18点 (9-17)
            is_weekday = commit_time.weekday() < 5
            is_work_hour = 9 <= commit_time.hour < 18
            
            if is_weekday and is_work_hour:
                work_hour_commits += 1
            
            if not is_weekday:  # 周末
                weekend_commits += 1
        
        # 计算统计值
        total_commits = len(commits)
        avg_message_length = sum(message_lengths) / total_commits if message_lengths else 0
        
        # 构建结果
        return CommitPatternAnalysis(
            avg_commit_message_length=avg_message_length,
            emoji_usage_rate=has_emoji_count / total_commits if total_commits else 0,
            issue_reference_rate=has_issue_ref_count / total_commits if total_commits else 0,
            common_prefixes={k: v / total_commits for k, v in type_prefixes.items()} if total_commits else {},
            work_hour_commit_rate=work_hour_commits / total_commits if total_commits else 0,
            weekend_commit_rate=weekend_commits / total_commits if total_commits else 0
        )
