#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
README生成器子模块
"""
from abc import ABC, abstractmethod

from .....ai_agent_core import AgentInput

class Generator(ABC):
    """
    生成器基类
    """
    def __init__(self, llm):
        self.llm = llm
        self.chain = None
        self.input_parameters = {}

    @abstractmethod
    async def create_chain(self):
        """
        初始化处理链
        """
        pass

    @abstractmethod
    async def prepare_input(self, input_data: AgentInput):
        """
        准备输入数据
        """
        pass

    @abstractmethod
    async def process(self):
        """
        处理查询
        """
        pass
