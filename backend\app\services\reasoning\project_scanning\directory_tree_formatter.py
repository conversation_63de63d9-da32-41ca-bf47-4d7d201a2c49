#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
目录树字符生成器
基于分片后的子树生成可视化的目录树字符表示
"""
from __future__ import annotations
from typing import List, Optional

from .project_info_models import TreeNode


class DirectoryTreeFormatter:
    """
    目录树字符生成器
    将TreeNode结构转换为可视化的目录树字符表示
    """
    
    def __init__(self, indent_size: int = 2, use_unicode: bool = True):
        """
        初始化目录树字符生成器
        
        Args:
            indent_size: 缩进大小，默认为2个空格
            use_unicode: 是否使用Unicode字符美化树形结构
        """
        self.indent_size = indent_size
        self.use_unicode = use_unicode
        
        # Unicode字符用于美化树形结构
        if use_unicode:
            self.branch = "├── "
            self.last_branch = "└── "
            self.vertical = "│   "
            self.empty = "    "
        else:
            self.branch = "|-- "
            self.last_branch = "`-- "
            self.vertical = "|   "
            self.empty = "    "
    
    def format_tree(self, tree: TreeNode, include_root: bool = True) -> str:
        """
        格式化目录树为字符表示
        
        Args:
            tree: 要格式化的目录树
            include_root: 是否包含根节点
            
        Returns:
            格式化后的目录树字符表示
        """
        result = []
        
        if include_root:
            result.append(tree.name)
            
        self._format_node(tree, "", include_root, result)
        return "\n".join(result)
    
    def _format_node(self, node: TreeNode, prefix: str, is_root: bool, result: List[str]) -> None:
        """
        递归格式化节点
        
        Args:
            node: 当前节点
            prefix: 当前前缀
            is_root: 是否为根节点
            result: 结果列表
        """
        # 如果是根节点且不包含根节点，则跳过当前节点，直接处理子节点
        if is_root:
            children_prefix = ""
        else:
            # 不是根节点，添加正常的前缀
            children_prefix = prefix
            
        # 处理子节点
        if node.children:
            # 对子节点进行排序，目录在前，文件在后
            sorted_children = sorted(
                node.children,
                key=lambda x: (x.type != "directory", x.name.lower())
            )
            
            for i, child in enumerate(sorted_children):
                is_last = i == len(sorted_children) - 1
                
                # 生成当前行的前缀和节点名
                if not is_root:
                    if is_last:
                        line_prefix = f"{prefix}{self.last_branch}"
                        next_prefix = f"{prefix}{self.empty}"
                    else:
                        line_prefix = f"{prefix}{self.branch}"
                        next_prefix = f"{prefix}{self.vertical}"
                else:
                    # 根节点的子节点
                    if is_last:
                        line_prefix = self.last_branch
                        next_prefix = self.empty
                    else:
                        line_prefix = self.branch
                        next_prefix = self.vertical
                
                # 添加当前节点
                result.append(f"{line_prefix}{child.name}")
                
                # 递归处理子节点
                self._format_node(child, next_prefix, False, result)


def format_tree(tree: TreeNode, indent_size: int = 2, use_unicode: bool = True, include_root: bool = True) -> str:
    """
    将目录树格式化为字符表示的便捷函数
    
    Args:
        tree: 要格式化的目录树
        indent_size: 缩进大小
        use_unicode: 是否使用Unicode字符美化树形结构
        include_root: 是否包含根节点
        
    Returns:
        格式化后的目录树字符表示
    """
    formatter = DirectoryTreeFormatter(indent_size, use_unicode)
    return formatter.format_tree(tree, include_root)


def format_chunked_trees(trees: List[TreeNode], indent_size: int = 2, use_unicode: bool = True) -> List[str]:
    """
    格式化多个分片后的子树
    
    Args:
        trees: 分片后的子树列表
        indent_size: 缩进大小
        use_unicode: 是否使用Unicode字符美化树形结构
        
    Returns:
        格式化后的目录树字符表示列表
    """
    formatter = DirectoryTreeFormatter(indent_size, use_unicode)
    return [formatter.format_tree(tree) for tree in trees]
