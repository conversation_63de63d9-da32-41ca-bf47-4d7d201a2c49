"""
GitHub项目初始化器
用于初始化系统默认的GitHub项目示例
"""
import os
import uuid
from datetime import datetime, timezone
import random
from typing import Optional
from urllib.parse import urlparse

import aiohttp
import structlog
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from app.core.config import settings
from app.models.github.github_project import GitHubProjectModel
from app.models.github.github_project_card import GitHubProjectCardModel
from app.services.github.github_downloader import GitHubDownloader
from app.utils.status_enum import ProjectStatusEnum

logger = structlog.get_logger(__name__)

class GitHubInitializer:
    """GitHub项目系统初始化器"""

    def __init__(self, async_session: AsyncSession) -> None:
        self.now = datetime.now(timezone.utc)
        # 添加一个不带时区信息的时间对象
        self.now_naive = self.now.replace(tzinfo=None)
        self.async_session = async_session
        # 初始化下载器

    async def initialize(self) -> None:
        """初始化GitHub项目系统"""
        try:
            logger.info("开始初始化GitHub项目系统")

            # 初始化默认的20个项目
            await self._init_default_projects()
            # 初始化默认的20个项目的卡片模板
            # await self._init_default_projects_cards()

            logger.info("GitHub项目系统初始化完成")
        except Exception as e:
            logger.error("GitHub项目系统初始化失败", error=str(e))
            raise

    async def _init_default_projects(self) -> None:
        """初始化默认GitHub项目，确保至少有23个示例项目"""
        # 获取当前项目数量
        result = await self.async_session.execute(select(func.count()).select_from(GitHubProjectModel))
        current_count = result.scalar_one()

        # 如果已有足够的项目，则无需添加
        # required_count = 23
        required_count = 0
        if current_count >= required_count:
            logger.info(f"GitHub项目数量已满足要求，现有{current_count}个项目")
            return

        # 计算需要添加的项目数量
        to_add_count = required_count - current_count
        logger.info(f"需要添加{to_add_count}个GitHub项目")
        # background_colors = ["#ffe6e6", "#cef9fd", "#f9e9ff"]
        # 250617
        background_colors = ["#e3ecf0", "#e3e5f5", "#f7f4eb", "#ebf6f0"]
        button_colors = ["#07c4fe", "#f4acff", "#ffbaa0"]

        example_projects = [
            # {
            #     "repository_url": "https://github.com/ytdl-org/youtube-dl",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/Jeric-X/SyncClipboard",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/Yu-Core/SwashbucklerDiary",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/danvergara/dblab",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/lxzan/gws",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/codeplea/genann",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/gunnarmorling/1brc",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/PrismLauncher/PrismLauncher",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/donknap/dpanel",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/Melkeydev/go-blueprint",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/88250/lute",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/remvze/moodist",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/didi/xiaoju-survey",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/vietnh1009/ASCII-generator",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/mwmbl/mwmbl",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/hellzerg/optimizer",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/scottbez1/smartknob",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/simeydotme/pokemon-cards-css",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/hibiken/asynq",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/Stirling-Tools/Stirling-PDF",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/codemix/deprank",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # },
            # {
            #     "repository_url": "https://github.com/typicode/json-server",
            #     "background_color": random.choice(background_colors),
            #     "button_color": random.choice(button_colors),
            # }
        ]

        # 获取已存在项目的名称和仓库URL
        result = await self.async_session.execute(
            select(GitHubProjectModel.name, GitHubProjectModel.repository_url)
        )
        existing_projects = {p.repository_url for p in result.all()}
        # 添加不存在的项目
        added_count = 0
        for project in example_projects:
            # 检查项目是否已存在
            if project["repository_url"] not in existing_projects:
                if added_count >= to_add_count:
                    break

                logger.info(
                    f"添加GitHub项目 到下载队列",
                    project["repository_url"]
                )
                # success, local_path, project_info = downloader.download_repository(str(project["repository_url"]))
                # logger.info(
                #     f"添加结果:",
                #     str(success),
                #     " path : ",
                #     str(local_path),
                #     str(project_info),
                # )
                # if success:
                #     project["local_path"] = local_path
                # else:
                #     continue


                # project["image_url"] = image_url
                # if project_info.get("image_url"):
                #     local_image_url = await self.download_and_store_github_image(project_info["image_url"])
                # else:
                #     local_image_url = None
                # project_info["image_url"] = local_image_url
                # project_model = GitHubProjectModel(
                #     name=urlparse(project["repository_url"]).path.strip('/').split('/')[-1],
                #     repository_url=project["repository_url"],
                #     description_recommend=project_info["description_recommend"],
                #     description_project=project_info["description_recommend"],
                #     tags=project_info["tags"],
                #     status="true",
                #     local_path=project_info["local_path"],
                #     image_url=local_image_url,
                #     background_color=project["background_color"],
                #     button_color=project["button_color"],
                #     created_at=self.now_naive,
                #     updated_at=self.now_naive
                # )
                # self.async_session.add(project_model)
                # added_count += 1

                logger.info(
                    f"添加GitHub项目到下载队列",
                    project["repository_url"]
                )

                # 创建项目记录，初始状态为 pending
                project_model = GitHubProjectModel(
                    name=urlparse(project["repository_url"]).path.strip('/').split('/')[-1],
                    repository_url=project["repository_url"],
                    background_color=project["background_color"],
                    button_color=project["button_color"],
                    status=ProjectStatusEnum.NOT_DOWNLOAD.value,  # 初始状态
                    created_at=self.now_naive,
                    updated_at=self.now_naive
                )
                self.async_session.add(project_model)
                await self.async_session.commit()
                from app.core.di import container_manager
                downloader = container_manager.container.github_downloader()
                await downloader.add_to_download_queue(project_model.id, project["repository_url"])
                added_count += 1
                existing_projects.add((project["repository_url"], project["repository_url"]))

        # 如果示例项目不够，生成随机项目
        if added_count < to_add_count:
            await self._generate_random_projects(to_add_count - added_count, existing_projects)

        await self.async_session.commit()
        logger.info(f"GitHub项目初始化完成，添加了{added_count}个示例项目")

    async def _generate_random_projects(self, count: int, existing_projects: set) -> None:
        """生成随机项目

        Args:
            count: 需要生成的项目数量
            existing_projects: 已存在的项目集合（name, repository_url）
        """
        # 常用的项目前缀和后缀
        prefixes = ["awesome", "super", "my", "open", "simple", "mini", "light", "smart", "fast", "easy"]
        suffixes = ["app", "framework", "lib", "tool", "sdk", "api", "service", "platform", "system", "project"]

        # 常用的技术栈
        tech_stacks = ["react", "vue", "angular", "nodejs", "python", "django", "flask", "spring", "java", "go"]

        background_colors = ["#ffe6e6", "#cef9fd", "#f9e9ff"]
        button_colors = ["#07c4fe", "#f4acff", "#ffbaa0"]

        # 随机生成项目
        for i in range(count):
            # 生成一个唯一的项目名称
            while True:
                prefix = random.choice(prefixes)
                tech = random.choice(tech_stacks)
                suffix = random.choice(suffixes)
                variant = random.randint(1, 999)

                name = f"{prefix}-{tech}-{suffix}-{variant}"
                repo_url = f"https://github.com/example/{name}"

                if (name, repo_url) not in existing_projects:
                    break

            # 生成项目描述和标签
            description = f"这是一个基于{tech}的{suffix}项目"
            tags = [tech, suffix, random.choice(["opensource", "community", "personal"])]

            # 创建项目模型
            project_model = GitHubProjectModel(
                name=name,
                repository_url=repo_url,
                description_recommend=description,
                description_project=description,
                tags=tags,
                status="false",
                local_path=f"/tmp/github/example/{name}",
                image_url=None,
                background_color=random.choice(background_colors),
                button_color=random.choice(button_colors),
                created_at=self.now_naive,
                updated_at=self.now_naive
            )

            self.async_session.add(project_model)
            existing_projects.add((name, repo_url))

            logger.info(f"添加随机GitHub项目", name=name)

    async def _init_default_projects_cards(self) -> None:
        """初始化GitHub项目卡片
        
        为每个没有卡片的项目随机生成0-5张卡片，每张卡片包含随机生成的内容和网址
        """
        logger.info("开始初始化GitHub项目卡片")
        
        # 获取所有项目ID
        result = await self.async_session.execute(
            select(GitHubProjectModel.id)
        )
        project_ids = [p[0] for p in result.all()]
        
        # 获取已经有卡片的项目ID
        result = await self.async_session.execute(
            select(GitHubProjectCardModel.project_id).distinct()
        )
        projects_with_cards = [p[0] for p in result.all()]
        
        # 找出没有卡片的项目
        projects_without_cards = [pid for pid in project_ids if pid not in projects_with_cards]
        
        if not projects_without_cards:
            logger.info("所有项目已有卡片，无需添加新卡片")
            return
        
        # 获取需要添加卡片的项目详情
        result = await self.async_session.execute(
            select(GitHubProjectModel).where(GitHubProjectModel.id.in_(projects_without_cards))
        )
        # projects = result.scalars().all()
        projects = None
        # 句子片段，用于随机组合生成卡片内容
        sentence_parts = [
            "这个项目提供了强大的功能支持，可用于各类开发场景。",
            "代码结构清晰，模块化设计使得扩展变得非常容易。",
            "该库的API设计简洁直观，上手难度低，适合初学者。",
            "性能优化做得相当出色，即使在大规模数据处理时也很高效。",
            "社区活跃度高，问题能够得到及时解答和支持。",
            "文档详尽，包含丰富的示例代码和使用场景说明。",
            "持续集成和自动化测试覆盖率高，保证了代码质量。",
            "跨平台兼容性好，支持多种运行环境和操作系统。",
            "安全性考虑周全，内置多种防护机制。",
            "更新迭代频率高，紧跟技术发展趋势。",
            "该项目解决了行业中的关键痛点问题，受到广泛关注。",
            "代码风格一致，遵循最佳实践，适合学习和参考。",
            "扩展插件丰富，可满足各种定制化需求。",
            "错误处理机制完善，提供清晰的调试信息。",
            "性能监控工具齐全，便于分析和优化。",
            "内存管理高效，资源占用率低。",
            "支持热更新，不需要重启即可应用新的配置。",
            "提供完整的迁移方案，版本升级平滑无缝。",
            "国际化支持全面，适用于全球化应用场景。",
            "社区贡献者众多，维护活跃度高。"
        ]
        
        # 域名列表，用于生成随机网址
        domains = [
            "github.com", "dev.to", "medium.com", "stackoverflow.com",
            "npmjs.com", "pypi.org", "docker.com", "kubernetes.io",
            "reactjs.org", "vuejs.org", "angular.io", "tensorflow.org",
            "python.org", "java.com", "golang.org", "rust-lang.org",
            "developer.mozilla.org", "w3schools.com", "codecademy.com",
            "udemy.com", "coursera.org", "edx.org", "freecodecamp.org"
        ]
        
        # 路径前缀，用于生成随机网址路径
        path_prefixes = [
            "docs", "tutorial", "guide", "api", "examples",
            "blog", "article", "resource", "learn", "reference",
            "community", "download", "install", "setup", "getting-started"
        ]
        
        card_count = 0
        
        # 为每个没有卡片的项目创建卡片
        # for project in projects:
        #     # 随机决定创建多少张卡片(3-5张)
        #     num_cards = random.randint(3, 5)
        #
        #     for _ in range(num_cards):
        #         # 随机生成约100字的内容
        #         content_parts = random.sample(sentence_parts, 5)
        #         content = " ".join(content_parts)
        #
        #         # 随机生成网址
        #         domain = random.choice(domains)
        #         path_prefix = random.choice(path_prefixes)
        #         random_path = "".join(random.choices("abcdefghijklmnopqrstuvwxyz0123456789", k=8))
        #         url = f"https://{domain}/{path_prefix}/{random_path}"
        #
        #         # 完整的卡片内容
        #         full_content = f"{content}\n\n{url}"
        #
        #         # 创建卡片模型
        #         card = GitHubProjectCardModel(
        #             project_id=project.id,
        #             title=" ".join(random.sample(sentence_parts, 1)),
        #             content=full_content,
        #             sort_order=int(num_cards)
        #         )
        #
        #         self.async_session.add(card)
        #         card_count += 1
        
        await self.async_session.commit()
        logger.info(f"GitHub项目卡片初始化完成，为{len(projects)}个项目添加了{card_count}张卡片")