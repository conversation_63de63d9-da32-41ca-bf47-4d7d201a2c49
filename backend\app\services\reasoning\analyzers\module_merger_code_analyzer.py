#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块合并编码分析器
纯程序化分析模块间的代码关系，不涉及AI推理
"""
import logging
from typing import Any, Dict, List, Optional, Tuple, Set
from pathlib import Path
from collections import defaultdict, Counter

from .module_merger_models import (
    CodeAnalysisResult, 
    ModuleCluster, 
    DependencyMatrix, 
    CodeRelationshipAnalysis,
    APIAnalysis
)
from .module_models import ModuleAnalysis, DependencyGraph, DependencyEdge, APIQuality

logger = logging.getLogger(__name__)


class ModuleMergerCodeAnalyzer:
    """
    模块合并编码分析器
    纯程序化分析模块间的代码关系，包括集群化、依赖分析等
    """

    def __init__(self):
        """初始化编码分析器"""
        self.name = "ModuleMergerCodeAnalyzer"

    def analyze(self, module_analyses: Dict[str, ModuleAnalysis], 
                project_name: str = "", analysis_scope: str = "") -> CodeAnalysisResult:
        """
        分析模块间的代码关系

        Args:
            module_analyses: 模块分析结果字典
            project_name: 项目名称
            analysis_scope: 分析范围

        Returns:
            CodeAnalysisResult: 编码分析结果
        """
        logger.info(f"开始编码分析，包含{len(module_analyses)}个模块")

        # 1. 生成模块集群
        module_clusters = self._generate_module_clusters(module_analyses)
        
        # 2. 分析依赖关系矩阵
        dependency_matrix = self._analyze_dependency_matrix(module_analyses)
        
        # 3. 分析代码关系
        code_relationships = self._analyze_code_relationships(
            module_clusters, dependency_matrix, module_analyses
        )
        
        # 4. 分析API设计
        api_analysis = self._analyze_api_design(module_analyses, project_name)

        # 5. 构造结果
        result = CodeAnalysisResult(
            project_name=project_name,
            analysis_scope=analysis_scope,
            source_module_analyses=module_analyses,
            code_relationships=code_relationships,
            api_analysis=api_analysis
        )

        logger.info("编码分析完成")
        return result

    def _generate_module_clusters(self, module_analyses: Dict[str, ModuleAnalysis]) -> List[ModuleCluster]:
        """
        基于目录结构生成模块集群

        Args:
            module_analyses: 模块分析结果

        Returns:
            List[ModuleCluster]: 模块集群列表
        """
        clusters = []
        directory_groups = defaultdict(list)

        # 按目录分组
        for module_name, analysis in module_analyses.items():
            module_path = Path(analysis.module_path)
            directory_path = str(module_path.parent)
            directory_groups[directory_path].append(analysis)

        # 生成集群
        for directory_path, modules in directory_groups.items():
            if len(modules) > 0:
                # 确定主要编程语言
                languages = [m.language for m in modules if m.language]
                primary_language = max(set(languages), key=languages.count) if languages else "unknown"
                
                # 生成集群名称
                cluster_name = Path(directory_path).name or "root"
                if cluster_name == ".":
                    cluster_name = "root"

                cluster = ModuleCluster(
                    cluster_name=cluster_name,
                    directory_path=directory_path,
                    module_paths=[m.module_path for m in modules],
                    primary_language=primary_language
                )
                clusters.append(cluster)

        logger.info(f"生成了{len(clusters)}个模块集群")
        return clusters

    def _analyze_dependency_matrix(self, module_analyses: Dict[str, ModuleAnalysis]) -> DependencyMatrix:
        """
        分析依赖关系矩阵

        Args:
            module_analyses: 模块分析结果

        Returns:
            DependencyMatrix: 依赖关系矩阵
        """
        internal_edges = []
        external_edges = []
        internal_nodes = []
        external_nodes = []
        all_circular_groups = []

        # 收集所有依赖边和节点
        for module_name, analysis in module_analyses.items():
            # 收集节点
            for node in analysis.dependency_graph.nodes:
                if node.dependency_scope == "internal":
                    internal_nodes.append(node)
                else:
                    external_nodes.append(node)
            
            # 收集边
            for edge in analysis.dependency_graph.edges:
                if edge.dependency_scope == "internal":
                    internal_edges.append(edge)
                else:
                    external_edges.append(edge)

            # 收集循环依赖组
            if analysis.dependency_graph.circular_paths:
                all_circular_groups.extend(analysis.dependency_graph.circular_paths)

        # 去重节点（基于name和role）
        def deduplicate_nodes(nodes):
            seen = set()
            unique_nodes = []
            for node in nodes:
                node_key = (node.name, node.role)
                if node_key not in seen:
                    seen.add(node_key)
                    unique_nodes.append(node)
            return unique_nodes

        internal_nodes = deduplicate_nodes(internal_nodes)
        external_nodes = deduplicate_nodes(external_nodes)

        # 构建内部和外部依赖图
        internal_graph = DependencyGraph(
            nodes=internal_nodes,
            edges=internal_edges,
            circular_paths=all_circular_groups
        )

        external_graph = DependencyGraph(
            nodes=external_nodes,
            edges=external_edges,
            circular_paths=[]
        )

        # 去重循环依赖组
        unique_circular_groups = []
        seen_groups = set()
        for group in all_circular_groups:
            group_key = tuple(sorted(group))
            if group_key not in seen_groups:
                seen_groups.add(group_key)
                unique_circular_groups.append(group)

        matrix = DependencyMatrix(
            internal_dependencies=internal_graph,
            external_dependencies=external_graph,
            circular_dependency_groups=unique_circular_groups
        )

        logger.info(f"分析依赖矩阵：内部节点{len(internal_nodes)}个，内部依赖{len(internal_edges)}个，外部节点{len(external_nodes)}个，外部依赖{len(external_edges)}个")
        return matrix

    def _analyze_code_relationships(self, 
                                  module_clusters: List[ModuleCluster],
                                  dependency_matrix: DependencyMatrix,
                                  module_analyses: Dict[str, ModuleAnalysis]) -> CodeRelationshipAnalysis:
        """
        分析代码关系

        Args:
            module_clusters: 模块集群
            dependency_matrix: 依赖矩阵
            module_analyses: 模块分析结果

        Returns:
            CodeRelationshipAnalysis: 代码关系分析
        """
        # 分析导入关系
        import_analysis = self._extract_import_analysis(module_analyses)

        # 分析继承关系
        inheritance_hierarchy = self._extract_inheritance_relationships(module_analyses)

        # 分析接口契约
        interface_contracts = self._extract_interface_contracts(module_analyses)

        # 分析数据流路径
        data_flow_paths = self._extract_data_flow_paths(module_analyses)

        relationships = CodeRelationshipAnalysis(
            module_clusters=module_clusters,
            dependency_matrix=dependency_matrix,
            import_analysis=import_analysis,
            inheritance_hierarchy=inheritance_hierarchy,
            interface_contracts=interface_contracts,
            data_flow_paths=data_flow_paths
        )

        return relationships

    def _extract_import_analysis(self, module_analyses: Dict[str, ModuleAnalysis]) -> Dict[str, List[str]]:
        """
        提取导入关系分析

        Args:
            module_analyses: 模块分析结果

        Returns:
            Dict[str, List[str]]: 导入关系字典
        """
        import_map = defaultdict(list)

        for module_name, analysis in module_analyses.items():
            for edge in analysis.dependency_graph.edges:
                if edge.relation_type in ["import", "include", "require"]:
                    import_map[edge.source].append(edge.target)

        return dict(import_map)

    def _extract_inheritance_relationships(self, module_analyses: Dict[str, ModuleAnalysis]) -> Dict[str, List[str]]:
        """
        提取继承关系

        Args:
            module_analyses: 模块分析结果

        Returns:
            Dict[str, List[str]]: 继承关系字典
        """
        inheritance_map = defaultdict(list)
        
        for module_name, analysis in module_analyses.items():
            for edge in analysis.dependency_graph.edges:
                if edge.relation_type == "inheritance":
                    inheritance_map[edge.source].append(edge.target)

        return dict(inheritance_map)

    def _extract_interface_contracts(self, module_analyses: Dict[str, ModuleAnalysis]) -> Dict[str, List[str]]:
        """
        提取接口契约关系

        Args:
            module_analyses: 模块分析结果

        Returns:
            Dict[str, List[str]]: 接口契约字典
        """
        interface_map = defaultdict(list)
        
        for module_name, analysis in module_analyses.items():
            for edge in analysis.dependency_graph.edges:
                if edge.relation_type in ["interface", "protocol", "abstract"]:
                    interface_map[edge.source].append(edge.target)

        return dict(interface_map)

    def _extract_data_flow_paths(self, module_analyses: Dict[str, ModuleAnalysis]) -> List[List[str]]:
        """
        提取数据流路径

        Args:
            module_analyses: 模块分析结果

        Returns:
            List[List[str]]: 数据流路径列表
        """
        data_flow_paths = []
        
        # 基于依赖关系构建数据流路径
        for module_name, analysis in module_analyses.items():
            # 寻找数据流相关的依赖
            for edge in analysis.dependency_graph.edges:
                if edge.relation_type in ["data_flow", "function_call", "method_call"]:
                    # 简单的二元路径
                    path = [edge.source, edge.target]
                    if path not in data_flow_paths:
                        data_flow_paths.append(path)

        logger.info(f"提取了{len(data_flow_paths)}条数据流路径")
        return data_flow_paths

    def _analyze_api_design(self, module_analyses: Dict[str, ModuleAnalysis], project_name: str) -> APIAnalysis:
        """
        分析API设计

        Args:
            module_analyses: 模块分析结果
            project_name: 项目名称

        Returns:
            APIAnalysis: API设计分析结果
        """
        logger.info("开始分析API设计")
        
        # 收集所有API端点
        all_endpoints = []
        api_modules = []
        api_types = set()
        endpoints_by_type = {}
        endpoints_by_module = {}
        quality_by_module = {}
        
        for module_path, analysis in module_analyses.items():
            if analysis.api_design and analysis.api_design.is_api_module:
                api_modules.append(module_path)
                
                # 收集端点
                module_endpoints = analysis.api_design.endpoints
                all_endpoints.extend(module_endpoints)
                endpoints_by_module[module_path] = module_endpoints
                
                # 收集API类型
                if analysis.api_design.api_type != "none":
                    api_types.add(analysis.api_design.api_type)
                    
                    # 按类型分组
                    if analysis.api_design.api_type not in endpoints_by_type:
                        endpoints_by_type[analysis.api_design.api_type] = []
                    endpoints_by_type[analysis.api_design.api_type].extend(module_endpoints)
                
                # 收集质量评估
                quality_by_module[module_path] = analysis.api_design.quality
        
        # 检测重复路径
        duplicate_paths = self._detect_duplicate_api_paths(all_endpoints)
        
        # 计算统计信息
        deprecated_count = sum(1 for ep in all_endpoints if ep.deprecated)
        authenticated_count = sum(1 for ep in all_endpoints if ep.authentication_required)
        
        # 计算整体质量
        overall_quality = self._calculate_overall_api_quality(quality_by_module, duplicate_paths)
        
        api_analysis = APIAnalysis(
            project_name=project_name,
            total_api_modules=len(api_modules),
            total_endpoints=len(all_endpoints),
            api_types=list(api_types),
            all_endpoints=all_endpoints,
            endpoints_by_type=endpoints_by_type,
            endpoints_by_module=endpoints_by_module,
            overall_quality=overall_quality,
            quality_by_module=quality_by_module,
            deprecated_endpoints_count=deprecated_count,
            authenticated_endpoints_count=authenticated_count,
            duplicate_paths=duplicate_paths
        )
        
        logger.info(f"API设计分析完成：{len(api_modules)}个API模块，{len(all_endpoints)}个端点")
        return api_analysis
    
    def _detect_duplicate_api_paths(self, endpoints: List["APIEndpoint"]) -> List[str]:
        """
        检测重复的API路径

        Args:
            endpoints: API端点列表

        Returns:
            List[str]: 重复的API路径列表
        """
        path_method_combinations = [(ep.path, ep.method) for ep in endpoints]
        path_counts = Counter(path_method_combinations)
        
        # 找出重复的路径
        duplicate_paths = []
        for (path, method), count in path_counts.items():
            if count > 1:
                duplicate_paths.append(f"{method} {path}")
        
        return duplicate_paths
    
    def _calculate_overall_api_quality(self, quality_by_module: Dict[str, "APIQuality"], 
                                     duplicate_paths: List[str]) -> "APIQuality":
        """
        计算整体API质量

        Args:
            quality_by_module: 各模块API质量评估
            duplicate_paths: 重复的API路径

        Returns:
            APIQuality: 整体API质量评估
        """
        if not quality_by_module:
            return APIQuality()
        
        # 计算平均评分
        scores = [quality.overall_score for quality in quality_by_module.values()]
        avg_score = sum(scores) / len(scores) if scores else 0
        
        # 减分因素：重复路径
        if duplicate_paths:
            avg_score = max(0, avg_score - len(duplicate_paths) * 0.5)
        
        # 收集所有问题
        all_issues = []
        for quality in quality_by_module.values():
            all_issues.extend(quality.issues)
        
        # 添加重复路径问题
        if duplicate_paths:
            all_issues.append(f"发现 {len(duplicate_paths)} 个重复的API路径")
        
        # 去重问题列表
        unique_issues = list(set(all_issues))
        
        return APIQuality(
            overall_score=round(avg_score, 1),
            issues=unique_issues[:10]  # 限制问题数量
        )
