#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖项章节生成器
"""
import logging
import os
import re
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone

from langchain.chains import <PERSON><PERSON><PERSON>n
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser

from . import Generator
from ..section_models import DependenciesSection
from .....ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser
from ......utils.retry import retry_async

logger = logging.getLogger(__name__)


class DependenciesGenerator(Generator):
    """
    依赖项章节生成器
    负责生成README文档的依赖项章节
    """

    def __init__(self, llm: ChatOpenAI):
        """初始化依赖项章节生成器"""
        super().__init__(llm)

    async def create_chain(self) -> None:
        """初始化处理链"""
        try:
            # 依赖项章节生成提示模板
            dependencies_template = """
            作为专业的技术栈分析专家和依赖管理顾问，请基于项目分析数据生成准确、完整且结构清晰的项目依赖章节文档。

            ## 项目基本信息
            项目名称: {{ project_name }}
            项目路径: {{ project_path }}

            {%- if structure_analysis and structure_analysis.dependencies %}

            ## 依赖管理文件
            {%- for dep_file in structure_analysis.dependencies[:5] %}
            - **{{ dep_file.name }}** ({{ dep_file.package_manager or "未指定" }}): `{{ dep_file.path }}`
            {%- endfor %}
            {%- endif %}

            {%- if dependency_analysis %}

            ## 技术栈信息
            {%- if dependency_analysis.tech_stack %}
            {%- if dependency_analysis.tech_stack.primary_language %}
            主要语言: {{ dependency_analysis.tech_stack.primary_language }}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.frameworks %}
            核心框架: {%- for framework in dependency_analysis.tech_stack.frameworks[:4] %}{{ framework }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.build_tools %}
            构建工具: {%- for tool in dependency_analysis.tech_stack.build_tools[:4] %}{{ tool }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.databases %}
            数据存储: {%- for db in dependency_analysis.tech_stack.databases[:3] %}{{ db }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.deployment %}
            部署方式: {%- for deploy in dependency_analysis.tech_stack.deployment[:3] %}{{ deploy }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if production_dependencies %}

            ## 生产依赖
            {%- for dep in production_dependencies %}
            - **{{ dep.name }}**{%- if dep.version %} (v{{ dep.version }}){%- endif %}{%- if dep.purpose %}: {{ dep.purpose }}{%- endif %}
            {%- if dep.license %}  许可证: {{ dep.license }}{%- endif %}
            {%- if dep.is_vulnerable %}  ⚠️ 存在安全漏洞{%- endif %}
            {%- endfor %}
            {%- endif %}

            {%- if dev_dependencies %}

            ## 开发依赖
            {%- for dep in dev_dependencies %}
            - **{{ dep.name }}**{%- if dep.version %} (v{{ dep.version }}){%- endif %}{%- if dep.purpose %}: {{ dep.purpose }}{%- endif %}
            {%- endfor %}
            {%- endif %}

            {%- if dependency_matrices %}

            ## 依赖关系分析

            {%- for matrix in dependency_matrices %}
            {%- if matrix.internal_dependencies.nodes %}

            ### 内部依赖关系
            **核心模块依赖图**：
            {%- for node in matrix.internal_dependencies.nodes[:8] %}
            - **{{ node.name }}** ({{ node.role }}) - 重要性: {{ node.importance }}/5.0
            {%- endfor %}

            **关键依赖关系**：
            {%- for edge in matrix.internal_dependencies.edges[:6] %}
            - `{{ edge.source }}` → `{{ edge.target }}` ({{ edge.relation_type }})
              {%- if edge.description %} - {{ edge.description }}{%- endif %}
            {%- endfor %}
            {%- endif %}

            {%- if matrix.external_dependencies.nodes %}

            ### 外部依赖关系
            **外部依赖模块**：
            {%- for node in matrix.external_dependencies.nodes[:6] %}
            - **{{ node.name }}** - {{ node.role }}
            {%- endfor %}

            **外部依赖链接**：
            {%- for edge in matrix.external_dependencies.edges[:4] %}
            - `{{ edge.source }}` → `{{ edge.target }}` (强度: {{ edge.strength }}/5.0)
            {%- endfor %}
            {%- endif %}

            {%- if matrix.circular_dependency_groups %}

            ### 循环依赖检测
            **发现的循环依赖组**：
            {%- for group in matrix.circular_dependency_groups[:3] %}
            - {{ group | join(' → ') }} → {{ group[0] }}
            {%- endfor %}
            {%- endif %}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            ## 依赖章节生成要求与原则

            ### 核心生成原则
            1. **依赖准确性**：确保所有依赖信息基于实际项目数据，版本号和许可证信息准确无误
            2. **技术栈清晰**：明确展示项目的核心技术栈，帮助用户理解技术选择和架构
            3. **安装指导性**：提供清晰的安装命令和环境要求，确保用户能够成功搭建环境
            4. **安全关注**：突出显示安全漏洞和许可证风险，提供必要的安全提醒
            5. **实用管理**：提供实用的依赖管理建议和最佳实践
            6. **版本兼容性**：说明关键依赖的版本要求和兼容性信息

            ### 内容生成任务
            请生成一个准确、完整且结构清晰的项目依赖章节，包含以下6个核心要素：

            1. **技术栈概览**：项目主要编程语言、核心框架列表（最重要的3-5个）、构建工具和包管理器、数据存储技术（如适用）
            2. **关键依赖项**：选择最重要的8-12个核心依赖，每个包含依赖名称、版本信息、用途描述、许可证类型（如可获取）
            3. **开发依赖项**：重要的开发和测试依赖（4-6个），包含名称、版本、用途说明
            4. **安装说明**：基于技术栈提供准确的安装命令、环境要求说明、依赖文件下载方法
            5. **依赖管理**：更新命令说明、依赖冲突解决建议、版本锁定策略（如适用）
            6. **安全信息**：已知安全漏洞警告（如存在）、许可证合规性提醒、安全更新建议

            ### 输出语言要求
            所有生成的依赖章节文档内容必须使用中文(简体中文)输出。包括技术栈描述、依赖说明、安装指导、安全提醒等内容都必须是中文。依赖库名称、版本号、许可证类型、命令行指令等技术名词保持原样。

            ### 输出质量要求
            1. **基于实际数据**：所有依赖信息必须基于提供的实际项目分析结果，不得臆想或添加不存在的依赖
            2. **版本信息准确**：确保版本号和依赖关系描述准确，避免误导用户
            3. **安装可执行性**：提供的安装命令必须可执行且符合项目实际情况
            4. **安全意识强化**：主动识别和提醒安全风险，提供相应的解决建议
            5. **实用性最大化**：生成的依赖信息应对项目使用者有实际指导价值
            6. **结构清晰易读**：采用清晰的层次结构，便于用户快速查找和理解依赖信息

            {{ format_instructions }}
            """

            # 创建Parser
            dependencies_parser = AnalysisOutputParser(pydantic_object=DependenciesSection)
            parser = OutputFixingParser.from_llm(parser=dependencies_parser, llm=self.llm)

            # 获取格式指令
            dependencies_format_instructions = dependencies_parser.get_format_instructions()

            # 创建提示模板
            dependencies_prompt = PromptTemplate.from_template(
                dependencies_template,
                template_format="jinja2"
            ).partial(format_instructions=dependencies_format_instructions)

            # 创建处理链
            self.chain = (
                dependencies_prompt
                | self.llm
                | parser
            )

            logger.info("依赖项章节生成器处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: AgentInput) -> None:
        """
        准备输入数据并设置self.input_parameters

        Args:
            input_data: 原始输入数据
        """
        # 初始化self.input_parameters，直接使用输入参数
        self.input_parameters = input_data.parameters.copy()

        # 如果存在依赖分析数据，将其转换为Markdown格式
        if 'dependency_analysis' in self.input_parameters and self.input_parameters['dependency_analysis']:
            dependency_analysis = self.input_parameters['dependency_analysis']
            try:
                # 分离开发依赖和生产依赖
                if dependency_analysis.external_dependencies:
                    dev_dependencies = []
                    production_dependencies = []
                    
                    for dep in dependency_analysis.external_dependencies:
                        try:
                            if dep.is_dev_dependency:
                                dev_dependencies.append(dep)
                            else:
                                production_dependencies.append(dep)
                        except AttributeError:
                            # 如果没有is_dev_dependency属性，默认为生产依赖
                            production_dependencies.append(dep)
                    
                    self.input_parameters['dev_dependencies'] = dev_dependencies
                    self.input_parameters['production_dependencies'] = production_dependencies
                    
                    logger.info(f"分离依赖完成 - 开发依赖: {len(dev_dependencies)}, 生产依赖: {len(production_dependencies)}")
                else:
                    self.input_parameters['dev_dependencies'] = []
                    self.input_parameters['production_dependencies'] = []
                    
            except Exception as e:
                logger.error(f"转换依赖分析数据为Markdown格式失败: {str(e)}")
                # 如果转换失败，设置空的markdown数据以避免模板错误
                self.input_parameters['dev_dependencies'] = []
                self.input_parameters['production_dependencies'] = []

        # 提取依赖关系矩阵数据
        dependency_matrices = []
        if 'module_merger_analyses' in self.input_parameters and self.input_parameters['module_merger_analyses']:
            module_merger_analyses = self.input_parameters['module_merger_analyses']
            for group_key, merger_result in module_merger_analyses.items():
                if (merger_result.code_analysis and 
                    merger_result.code_analysis.code_relationships and 
                    merger_result.code_analysis.code_relationships.dependency_matrix):
                    dependency_matrices.append(merger_result.code_analysis.code_relationships.dependency_matrix)
                    logger.info(f"找到依赖关系矩阵数据: {group_key}")
        
        self.input_parameters['dependency_matrices'] = dependency_matrices

        logger.info("依赖项章节生成器输入参数准备完成")

    @retry_async(max_retries=3)
    async def process(self) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询

        Returns:
            Tuple[Any, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 调用LLM处理链
            # result = await self.chain.first.ainvoke(self.input_parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(self.input_parameters)

            # 设置章节顺序
            if result:
                result.order = 6  # 依赖项章节通常是第六个

            return result, {"generator": "dependencies_generator"}
        except Exception as e:
            logger.error(f"依赖项章节生成失败: {str(e)}")
            raise
