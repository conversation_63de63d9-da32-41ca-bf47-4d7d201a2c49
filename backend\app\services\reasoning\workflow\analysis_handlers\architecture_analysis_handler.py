#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
架构分析处理器
负责分析项目的架构设计和组件关系
"""
import structlog
from typing import Optional

from ..models import AnalysisConfig, AnalysisState
from .base_handler import BaseAnalysisHandler
from ....ai_agent_core import agent_manager
from ...analyzers.project_architecture_analyzer import ProjectArchitectureAnalyzer

logger = structlog.get_logger(__name__)

class ArchitectureAnalysisHandler(BaseAnalysisHandler):
    """
    架构分析处理器
    负责分析项目的架构设计和组件关系
    """

    def __init__(self, config: AnalysisConfig, cache_dir: str = '.cache'):
        """
        初始化架构分析处理器

        Args:
            config: 分析配置
            cache_dir: 缓存目录
        """
        self.architecture_analyzer: Optional[ProjectArchitectureAnalyzer] = None
        super().__init__(config, cache_dir)

    def initialize(self) -> None:
        """
        初始化架构分析器
        """
        try:
            self.architecture_analyzer = agent_manager.load("ProjectArchitectureAnalyzerAgent")
            logger.info("架构分析器初始化完成")
        except Exception as e:
            logger.error(f"架构分析器初始化失败: {str(e)}")
            raise

    async def process(self, state: AnalysisState) -> AnalysisState:
        """
        处理架构分析

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        # 1. 从缓存获取状态
        cached_state = self.get_cached_state()

        # 2. 执行架构分析
        try:
            # 检查是否需要执行架构分析
            if not self.config.analyze_architecture:
                logger.info("配置指定不分析架构，跳过架构分析")
                return state

            # 检查是否可以使用缓存
            if cached_state and cached_state.architecture_analysis and state.is_incremental_analysis:
                # 检查是否有新增或删除的模块
                has_structural_changes = len(state.new_modules) > 0 or len(state.deleted_modules) > 0

                # 如果没有新增或删除的模块，可以使用缓存的架构分析结果
                if not has_structural_changes:
                    logger.info("没有新增或删除的模块，使用缓存的架构分析结果")
                    state.architecture_analysis = cached_state.architecture_analysis
                    state.analysis_progress = 0.9  # 架构分析完成，进度90%
                    return state

            # 执行架构分析
            state = await self._analyze_architecture(state)

            logger.info("项目架构分析完成")
        except Exception as e:
            logger.error(f"架构分析失败: {str(e)}")
            state.errors["architecture_analysis"] = str(e)
            raise

        # 3. 设置缓存
        self.set_cached_state(state)

        # 4. 返回分析结果
        return state

    async def _analyze_architecture(self, state: AnalysisState) -> AnalysisState:
        """
        分析项目架构
        从high_level_orchestrator.py迁移的核心架构分析逻辑

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        logger.info("开始项目架构分析")

        try:
            # 检查架构分析器是否已初始化
            if self.architecture_analyzer is None:
                raise ValueError("架构分析器未初始化")

            # 准备输入数据
            input_data = {
                "parameters": {
                    "project_name": state.project_name,
                    "project_path": state.project_path,
                    "project_structure_analysis": state.structure_analysis,
                    "project_dependency_analysis": state.dependency_analysis,
                    "project_structure": state.project_structure
                }
            }

            # 调用架构分析器
            agent_input = await self.architecture_analyzer.prepare_input(input_data)
            output = await self.architecture_analyzer.process(agent_input)
            architecture_analysis = output.response.result

            # 保存分析结果
            state.architecture_analysis = architecture_analysis

            # 更新进度
            state.analysis_progress = 0.9
            logger.info("项目架构分析完成")
        except Exception as e:
            logger.error(f"项目架构分析失败: {str(e)}")
            state.errors["architecture_analysis"] = str(e)
            raise

        return state
