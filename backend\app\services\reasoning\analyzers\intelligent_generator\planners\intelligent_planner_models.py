#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能规划器数据模型

定义章节规划相关的数据结构
"""
from typing import List, Dict, Any, Optional, Literal
from pydantic import BaseModel, Field


class SectionPlan(BaseModel):
    """章节规划模型"""
    title: str = Field(..., description="章节标题")
    description: str = Field(default="", description="章节描述")
    content_points: List[str] = Field(default_factory=list, description="内容要点")
    priority: int = Field(default=5, description="优先级(1-10，10为最高)")
    estimated_length: str = Field(default="medium", description="预估长度(short/medium/long)")
    required_data: List[str] = Field(default_factory=list, description="所需数据源")
    section_type: Literal[
        "introduction", "features", "installation", "usage", "api_reference",
        "architecture", "dependencies", "contributing", "license", "changelog",
        "examples", "troubleshooting", "deployment", "configuration", "testing"
    ] = Field(default="introduction", description="章节类型")
    is_essential: bool = Field(default=True, description="是否为必要章节")
    depends_on: List[str] = Field(default_factory=list, description="依赖的其他章节")


class ProjectTypeStrategy(BaseModel):
    """项目类型策略模型"""
    project_type: Literal[
        "web_application", "library", "cli_tool", "framework",
        "microservice", "desktop_app", "mobile_app", "data_pipeline", "other"
    ] = Field(..., description="项目类型")
    essential_sections: List[str] = Field(default_factory=list, description="必要章节")
    recommended_sections: List[str] = Field(default_factory=list, description="推荐章节")
    optional_sections: List[str] = Field(default_factory=list, description="可选章节")
    section_priorities: Dict[str, int] = Field(default_factory=dict, description="章节优先级映射")
    special_considerations: List[str] = Field(default_factory=list, description="特殊考虑事项")


class PlanningContext(BaseModel):
    """规划上下文模型"""
    project_name: str = Field(..., description="项目名称")
    project_type: str = Field(..., description="项目类型")
    primary_language: str = Field(default="", description="主要编程语言")
    has_api: bool = Field(default=False, description="是否有API")
    has_cli: bool = Field(default=False, description="是否有CLI")
    has_web_interface: bool = Field(default=False, description="是否有Web界面")
    has_tests: bool = Field(default=False, description="是否有测试")
    has_docs: bool = Field(default=False, description="是否有文档")
    has_docker: bool = Field(default=False, description="是否有Docker")
    has_ci_cd: bool = Field(default=False, description="是否有CI/CD")
    complexity_level: Literal["simple", "moderate", "complex"] = Field(default="moderate", description="复杂度级别")
    target_audience: Literal["developers", "end_users", "both"] = Field(default="developers", description="目标受众")
    frameworks: List[str] = Field(default_factory=list, description="使用的框架")
    databases: List[str] = Field(default_factory=list, description="使用的数据库")
    deployment_platforms: List[str] = Field(default_factory=list, description="部署平台")


class ReadmePlan(BaseModel):
    """README规划结果模型"""
    project_context: PlanningContext = Field(..., description="项目上下文")
    sections: List[SectionPlan] = Field(default_factory=list, description="章节列表")
    total_estimated_length: str = Field(default="medium", description="总体预估长度")
    planning_rationale: str = Field(default="", description="规划理由")
    recommendations: List[str] = Field(default_factory=list, description="建议")
    warnings: List[str] = Field(default_factory=list, description="警告")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")

    def get_sections_by_priority(self) -> List[SectionPlan]:
        """按优先级排序获取章节"""
        return sorted(self.sections, key=lambda x: x.priority, reverse=True)

    def get_essential_sections(self) -> List[SectionPlan]:
        """获取必要章节"""
        return [section for section in self.sections if section.is_essential]

    def get_optional_sections(self) -> List[SectionPlan]:
        """获取可选章节"""
        return [section for section in self.sections if not section.is_essential]

    def get_sections_by_type(self, section_type: str) -> List[SectionPlan]:
        """按类型获取章节"""
        return [section for section in self.sections if section.section_type == section_type]


class PlanningRequest(BaseModel):
    """规划请求模型"""
    analysis_data: Dict[str, Any] = Field(..., description="分析数据")
    preferences: Dict[str, Any] = Field(default_factory=dict, description="用户偏好")
    constraints: Dict[str, Any] = Field(default_factory=dict, description="约束条件")
    target_length: Optional[str] = Field(None, description="目标长度")
    include_advanced_sections: bool = Field(default=True, description="是否包含高级章节")
    focus_areas: List[str] = Field(default_factory=list, description="重点关注领域")


class PlanningResponse(BaseModel):
    """规划响应模型"""
    success: bool = Field(default=True, description="是否成功")
    plan: Optional[ReadmePlan] = Field(None, description="规划结果")
    error_message: str = Field(default="", description="错误信息")
    processing_time: float = Field(default=0.0, description="处理时间(秒)")
    ai_reasoning: str = Field(default="", description="AI推理过程")
    confidence_score: float = Field(default=0.0, description="置信度评分(0-1)")
