#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
# @Author: tongbang.cui
# @Contact: <EMAIL>
# @File: multi_toolkit.py
# @Date: 2025/1/3 10:29
# @Version: 1.0.0
# @Description: 
"""

import asyncio
import logging
import random
import string
import time
from dataclasses import dataclass
from typing import Dict, Optional, List, Any
import itertools
import pydantic
from langchain_core.tools.base import BaseTool, BaseToolkit, ToolException
from langchain_core.utils.pydantic import get_fields
from mcp import ClientSession, ListToolsResult, StdioServerParameters
from mcp.client.stdio import stdio_client
import uuid
from pydantic import BaseModel, Field, SkipValidation


class IDGenerator:
    """线程安全的ID生成器"""

    def __init__(self, prefix: str = "T"):
        """初始化ID生成器

        Args:
            prefix: ID前缀，默认为'T'
        """
        self.prefix = prefix
        self._lock = asyncio.Lock()

    async def generate(self) -> str:
        """生成唯一ID

        生成格式为 "前缀+uuid前7位" 的8位ID，如 "T1a2b3c4"
        - 前缀：1位字母T
        - uuid：使用uuid4的前7位十六进制字符

        Returns:
            生成的唯一ID
        """
        async with self._lock:
            return f"{self.prefix}{uuid.uuid4().hex[:7]}"


# 创建全局ID生成器实例
id_generator = IDGenerator()


@dataclass
class ServerInstance:
    """服务器实例"""
    server_id: str
    params: StdioServerParameters
    session: Optional[ClientSession] = None
    tools: Optional[ListToolsResult] = None
    _stdio_context: Any = None
    _read: Any = None
    _write: Any = None


class MultiToolkit(BaseToolkit):
    """支持多个服务器的工具包"""

    servers: Dict[str, ServerInstance] = Field(default_factory=dict)
    tools_list: Dict[str, ListToolsResult] = Field(default_factory=dict)
    lock: SkipValidation[asyncio.Lock] = Field(default_factory=asyncio.Lock)

    model_config = pydantic.ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.lock = asyncio.Lock()

    async def add_server(self, params: StdioServerParameters) -> str:
        """添加新的服务器

        Args:
            params: 服务器参数

        Returns:
            server_id: 服务器ID

        Raises:
            RuntimeError: 添加服务器失败
        """
        server_id = await id_generator.generate()

        async with self.lock:
            try:
                # 1. 创建服务器实例
                server = ServerInstance(
                    server_id=server_id,
                    params=params
                )

                # 2. 创建stdio上下文
                server._stdio_context = stdio_client(params)
                # 进入上下文并保存流
                server._read, server._write = await server._stdio_context.__aenter__()

                # 3. 创建会话并初始化
                server.session = await ClientSession(server._read, server._write).__aenter__()
                await server.session.initialize()

                # 4. 获取工具列表
                server.tools = await server.session.list_tools()

                # 5. 保存服务器和工具列表
                self.servers[server_id] = server
                self.tools_list[server_id] = server.tools

                logging.info(f"添加服务器成功: {server_id}")
                return server_id

            except Exception as e:
                # 如果添加失败，确保清理资源
                if server_id in self.servers:
                    await self.cleanup_server(server_id)
                raise RuntimeError(f"添加服务器失败: {str(e)}")

    async def cleanup_server(self, server_id: str) -> None:
        """清理指定服务器的资源

        Args:
            server_id: 服务器ID
        """
        async with self.lock:
            server = self.servers.get(server_id)
            if not server:
                return

            errors = []

            # 1. 关闭ClientSession
            if server.session:
                try:
                    await server.session.__aexit__(None, None, None)
                except Exception as e:
                    errors.append(f"关闭session失败: {str(e)}")
                    logging.error("关闭session失败", exc_info=True)
                finally:
                    server.session = None

            # 2. 关闭stdio上下文
            if server._stdio_context:
                try:
                    await server._stdio_context.__aexit__(None, None, None)
                except Exception as e:
                    errors.append(f"关闭stdio上下文失败: {str(e)}")
                    logging.error("关闭stdio上下文失败", exc_info=True)
                finally:
                    server._stdio_context = None

            # 3. 清理内部状态
            server._read = None
            server._write = None
            server.tools = None

            # 4. 从服务器和工具列表中移除
            self.servers.pop(server_id, None)
            self.tools_list.pop(server_id, None)

            # 如果有错误，记录汇总信息
            if errors:
                logging.error(f"清理资源时发生以下错误:\n" + "\n".join(errors))

    async def cleanup_all(self) -> None:
        """清理所有服务器资源"""
        server_ids = list(self.servers.keys())
        for server_id in server_ids:
            await self.cleanup_server(server_id)

    def get_tools(self) -> List[BaseTool]:
        """获取所有服务器的工具列表"""
        tools = []

        # 遍历所有服务器的工具
        for server_id, tool_list in self.tools_list.items():
            server = self.servers.get(server_id)
            if not server or not server.session:
                continue

            # 为每个工具创建包装器
            for tool in tool_list.tools:
                try:
                    # 创建参数模式
                    schema_model = create_schema_model(tool.inputSchema)

                    # 创建工具实例
                    tool_instance = MCPRemoteTool(
                        toolkit=self,
                        server_id=server_id,
                        name=f"{server_id}_{tool.name}",  # 添加服务器ID前缀
                        description=tool.description or "",
                        args_schema=schema_model,
                    )
                    tools.append(tool_instance)
                except Exception as e:
                    logging.error(f"创建工具 {tool.name} 失败: {str(e)}")
                    continue

        return tools


class MCPRemoteTool(BaseTool):
    """远程MCP工具

    用于调用远程MCP服务器上的工具
    """

    toolkit: MultiToolkit
    server_id: str
    handle_tool_error: bool | str | None = True

    def _run(self, *args: Any, **kwargs: Any) -> Any:
        raise NotImplementedError("请使用异步方法")

    async def _arun(self, *args: Any, **kwargs: Any) -> Any:
        """执行工具

        Args:
            args: 位置参数
            kwargs: 关键字参数

        Returns:
            工具执行结果

        Raises:
            ToolException: 当工具执行失败时
        """
        try:
            # 1. 获取服务器实例
            server = self.toolkit.servers.get(self.server_id)
            if not server or not server.session:
                raise RuntimeError(f"服务器不存在或未连接: {self.server_id}")

            # 2. 准备参数
            if args:
                # 如果有位置参数，使用第一个参数作为输入
                if not self.args_schema:
                    arguments = {"input": args[0]}
                else:
                    key = next(iter(get_fields(self.args_schema).keys()))
                    arguments = {key: args[0]}
            else:
                arguments = kwargs

            # 3. 调用工具
            # 从工具名中移除服务器ID前缀
            tool_name = self.name.split("_", 1)[1]
            result = await server.session.call_tool(tool_name, arguments=arguments)

            # 4. 检查错误
            if result.isError:
                raise ToolException(str(result.content))

            # 5. 处理返回值
            if not result.content:
                return ""

            return "\n".join(
                content.text
                for content in result.content
                if content.type == "text"
            )

        except Exception as e:
            raise ToolException(f"工具执行失败: {str(e)}")


def create_schema_model(schema: dict[str, Any]) -> type[pydantic.BaseModel]:
    """从JSON schema创建Pydantic模型

    Args:
        schema: JSON schema

    Returns:
        Pydantic模型类
    """
    if not schema or not isinstance(schema, dict):
        return pydantic.create_model("EmptySchema", __base__=pydantic.BaseModel)

    try:
        # 确保schema包含必要的字段
        if "type" not in schema:
            schema["type"] = "object"
        if "properties" not in schema:
            schema["properties"] = {}

        # 从properties创建字段定义
        fields = {}
        required = schema.get("required", [])

        for field_name, field_schema in schema["properties"].items():
            field_type = field_schema.get("type", "string")
            field_desc = field_schema.get("description", "")

            # 转换JSON schema类型到Python类型
            type_map = {
                "string": str,
                "integer": int,
                "number": float,
                "boolean": bool,
                "array": list,
                "object": dict
            }

            python_type = type_map.get(field_type, str)

            # 如果字段是必需的，直接使用类型
            # 如果是可选的，使用Optional
            if field_name in required:
                field_type = python_type
            else:
                field_type = Optional[python_type]

            # 创建字段定义
            field_info = pydantic.Field(
                default=None if field_name not in required else ...,
                description=field_desc
            )

            fields[field_name] = (field_type, field_info)

        # 创建模型类
        model = pydantic.create_model(
            "DynamicSchema",
            **fields,
            __base__=pydantic.BaseModel
        )

        # 添加模型配置
        model.model_config = pydantic.ConfigDict(
            title=schema.get("title", "Tool Input Schema"),
            description=schema.get("description", ""),
            extra="forbid"
        )

        return model

    except Exception as e:
        logging.error(f"创建schema模型失败: {str(e)}")
        return pydantic.create_model("ErrorSchema", __base__=pydantic.BaseModel)
