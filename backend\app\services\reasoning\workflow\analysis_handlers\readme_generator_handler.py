#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
README生成处理器
负责生成项目的README文档
"""
import structlog
from typing import Optional
from datetime import datetime, timezone

from ..models import AnalysisConfig, AnalysisState
from .base_handler import BaseAnalysisHandler
from ....ai_agent_core import agent_manager
from ...analyzers.readme_generator.readme_generator import ReadmeGenerator
from ...analyzers.readme_generator.section_models import ReadmeDocument

logger = structlog.get_logger(__name__)

class ReadmeGeneratorHandler(BaseAnalysisHandler):
    """
    README生成处理器
    负责生成项目的README文档
    """

    def __init__(self, config: AnalysisConfig, cache_dir: str = '.cache'):
        """
        初始化README生成处理器

        Args:
            config: 分析配置
            cache_dir: 缓存目录
        """
        self.readme_generator: Optional[ReadmeGenerator] = None
        super().__init__(config, cache_dir)

    def initialize(self) -> None:
        """
        初始化README生成器
        """
        try:
            self.readme_generator = agent_manager.load("ReadmeGeneratorAgent")
            logger.info("README生成器初始化完成")
        except Exception as e:
            logger.error(f"README生成器初始化失败: {str(e)}")
            raise

    async def process(self, state: AnalysisState) -> AnalysisState:
        """
        处理README生成

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        # 1. 从缓存获取状态
        cached_state = self.get_cached_state()

        # 2. 执行README生成
        try:
            # 检查是否需要生成README
            if not self.config.generate_readme:
                logger.info("配置指定不生成README，跳过README生成")
                return state

            # 检查是否可以使用缓存
            if cached_state and cached_state.readme and state.is_incremental_analysis:
                # 如果没有模块变化，使用缓存的README
                if not (state.changed_modules or state.new_modules or state.deleted_modules):
                    logger.info("没有模块变化，使用缓存的README")
                    state.readme = cached_state.readme
                    state.analysis_progress = 1.0  # README生成完成，进度100%
                    return state

            # 执行README生成
            state = await self._generate_readme(state)

            logger.info("README文档生成完成")
        except Exception as e:
            logger.error(f"README生成失败: {str(e)}")
            state.errors["readme_generation"] = str(e)
            raise

        # 3. 设置缓存
        self.set_cached_state(state)

        # 4. 返回分析结果
        return state

    async def _generate_readme(self, state: AnalysisState) -> AnalysisState:
        """
        生成项目README
        从high_level_orchestrator.py迁移的核心README生成逻辑

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        logger.info("开始生成README")

        try:
            # 检查README生成器是否已初始化
            if self.readme_generator is None:
                raise ValueError("README生成器未初始化")

            # 准备输入数据
            input_data = {
                "parameters": {
                    "project_name": state.project_name,
                    "project_path": state.project_path,
                    "project_structure": state.project_structure.structure_string,
                    "structure_analysis": state.structure_analysis,
                    "architecture_analysis": state.architecture_analysis,
                    "dependency_analysis": state.dependency_analysis,
                    "module_groups": state.module_groups,
                    "module_merger_analyses": state.module_merger_analyses,
                }
            }

            # 使用分块式README生成器
            agent_input = await self.readme_generator.prepare_input(input_data)
            output = await self.readme_generator.process(agent_input)
            readme = output.response.result
            state.readme = readme
            state.end_time = datetime.now(timezone.utc)

            # 更新进度
            state.analysis_progress = 1.0
            logger.info("README生成完成")
        except Exception as e:
            logger.error(f"README生成失败: {str(e)}")
            state.errors["readme_generation"] = str(e)
            raise

        return state
