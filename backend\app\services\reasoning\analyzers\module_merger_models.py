#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块合并分析结果数据模型
用于承载模块合并分析后的结构化分析结果
包含AI智能整合和编程关系分析两个维度的数据模型
"""
from typing import List, Dict, Optional

from pydantic import BaseModel, Field

from .common_models import AnalysisFinding
from .module_models import ModuleAnalysis, DependencyGraph, ModuleFeature, APIEndpoint, APIQuality


class ArchitecturalComponent(BaseModel):
    """架构组件信息 - AI可分析属性类"""
    name: str = Field(default="", description="组件名称")
    type: str = Field(default="", description="组件类型(service/controller/model/util等)")
    layer: str = Field(default="", description="架构层级")
    related_modules: List[str] = Field(default_factory=list, description="相关模块路径")


class FunctionalModule(BaseModel):
    """功能模块信息 - AI可分析属性类"""
    name: str = Field(default="", description="功能模块名称")
    purpose: str = Field(default="", description="功能模块主要目的")
    features: List[ModuleFeature] = Field(default_factory=list, description="核心功能特性")
    related_modules: List[str] = Field(default_factory=list, description="相关模块路径")


class ModuleCluster(BaseModel):
    """模块集群信息 - 程序化获取属性类"""
    cluster_name: str = Field(default="", description="集群名称")
    directory_path: str = Field(default="", description="集群目录路径")
    module_paths: List[str] = Field(default_factory=list, description="包含的模块路径列表")
    primary_language: str = Field(default="", description="主要编程语言")


class DependencyMatrix(BaseModel):
    """依赖关系矩阵 - 程序化获取属性类"""
    internal_dependencies: DependencyGraph = Field(default_factory=DependencyGraph, description="内部依赖图")
    external_dependencies: DependencyGraph = Field(default_factory=DependencyGraph, description="外部依赖图")
    circular_dependency_groups: List[List[str]] = Field(default_factory=list, description="循环依赖组")


class AIAnalysisInsights(BaseModel):
    """AI分析洞察 - AI智能整合结果"""
    architectural_patterns: List[str] = Field(default_factory=list, description="识别的架构模式")
    design_principles: List[str] = Field(default_factory=list, description="遵循的设计原则")
    code_smells: List[str] = Field(default_factory=list, description="发现的代码异味")
    refactoring_suggestions: List[str] = Field(default_factory=list, description="重构建议")


class AIAnalysisResult(BaseModel):
    """AI智能分析结果 - 独立的AI分析模型"""
    # 基础标识信息
    project_name: str = Field(default="", description="项目名称")
    analysis_scope: str = Field(default="", description="分析范围描述")

    # AI分析结果
    architectural_components: List[ArchitecturalComponent] = Field(default_factory=list, description="架构组件列表")
    functional_modules: List[FunctionalModule] = Field(default_factory=list, description="功能模块列表")
    ai_insights: AIAnalysisInsights = Field(default_factory=AIAnalysisInsights, description="AI分析洞察")

    # AI分析发现
    ai_findings: List[AnalysisFinding] = Field(default_factory=list, description="AI分析发现")
    ai_recommendations: List[str] = Field(default_factory=list, description="AI改进建议")

    def to_markdown(self) -> str:
        """转换为Markdown格式的AI分析报告"""
        md_lines = [
            f"# AI智能分析报告",
            f"",
            f"**项目:** {self.project_name}",
            f"**分析范围:** {self.analysis_scope}",
            f"",
            f"## 架构组件概览",
            f""
        ]
        
        for component in self.architectural_components:
            md_lines.extend([
                f"### {component.name} ({component.type})",
                f"- **层级:** {component.layer}",
                f"- **相关模块:** {len(component.related_modules)}个",
                f""
            ])
        
        md_lines.extend([
            f"## 功能模块分析",
            f""
        ])
        
        for module in self.functional_modules:
            md_lines.extend([
                f"### {module.name}",
                f"- **目的:** {module.purpose}",
                f"- **核心功能:** {len(module.features)}个",
                f"- **相关模块:** {len(module.related_modules)}个",
                f""
            ])
        
        if self.ai_insights.architectural_patterns:
            md_lines.extend([
                f"## AI分析洞察",
                f"",
                f"**架构模式:** {', '.join(self.ai_insights.architectural_patterns)}",
                f"**设计原则:** {', '.join(self.ai_insights.design_principles)}",
                f"**优化机会:** {', '.join(self.ai_insights.code_smells)}",
                f""
            ])
        
        if self.ai_findings:
            md_lines.extend([
                f"## AI分析发现",
                f""
            ])
            for finding in self.ai_findings:
                md_lines.extend([
                    f"### {finding.title} ({finding.severity})",
                    f"{finding.description}",
                    f""
                ])
        
        if self.ai_recommendations:
            md_lines.extend([
                f"## AI改进建议",
                f""
            ])
            for i, rec in enumerate(self.ai_recommendations, 1):
                md_lines.append(f"{i}. {rec}")
        
        return "\n".join(md_lines)


class APIAnalysis(BaseModel):
    """API设计分析结果"""
    # 基础标识信息
    project_name: str = Field(default="", description="项目名称")
    
    # API统计信息
    total_api_modules: int = Field(default=0, description="API模块总数")
    total_endpoints: int = Field(default=0, description="API端点总数")
    api_types: List[str] = Field(default_factory=list, description="项目中使用的API类型列表")
    
    # 端点信息
    all_endpoints: List[APIEndpoint] = Field(default_factory=list, description="所有API端点列表")
    endpoints_by_type: Dict[str, List[APIEndpoint]] = Field(default_factory=dict, description="按API类型分组的端点")
    endpoints_by_module: Dict[str, List[APIEndpoint]] = Field(default_factory=dict, description="按模块分组的端点")
    
    # 质量评估
    overall_quality: APIQuality = Field(default_factory=APIQuality, description="整体API质量评估")
    quality_by_module: Dict[str, APIQuality] = Field(default_factory=dict, description="各模块API质量评估")
    
    # API分析统计
    deprecated_endpoints_count: int = Field(default=0, description="已废弃端点数量")
    authenticated_endpoints_count: int = Field(default=0, description="需要认证的端点数量")
    duplicate_paths: List[str] = Field(default_factory=list, description="重复的API路径")
    
    def to_markdown(self) -> str:
        """转换为Markdown格式的API设计报告"""
        md_lines = [
            "# API设计分析报告\n",
            f"**项目名称**: {self.project_name}\n",
            "",
            "## API统计信息",
            f"- **API模块总数**: {self.total_api_modules}",
            f"- **API端点总数**: {self.total_endpoints}",
            f"- **API类型**: {', '.join(self.api_types) if self.api_types else '无'}",
            f"- **已废弃端点**: {self.deprecated_endpoints_count}",
            f"- **需要认证端点**: {self.authenticated_endpoints_count}",
            "",
        ]
        
        # 重复路径警告
        if self.duplicate_paths:
            md_lines.extend([
                "## ⚠️ 重复API路径",
                "以下路径在多个模块中重复定义：",
                ""
            ])
            for path in self.duplicate_paths:
                md_lines.append(f"- `{path}`")
            md_lines.append("")
        
        # 按API类型分组的端点
        if self.endpoints_by_type:
            md_lines.extend([
                "## 按API类型分组的端点",
                ""
            ])
            for api_type, endpoints in self.endpoints_by_type.items():
                if endpoints:
                    md_lines.extend([
                        f"### {api_type.upper()} API",
                        f"共 {len(endpoints)} 个端点：",
                        ""
                    ])
                    for endpoint in endpoints[:10]:  # 限制显示前10个
                        status = " ⚠️ (已废弃)" if endpoint.deprecated else ""
                        auth = " 🔒" if endpoint.authentication_required else ""
                        md_lines.append(f"- `{endpoint.method} {endpoint.path}`{auth}{status}")
                        if endpoint.description:
                            md_lines.append(f"  - {endpoint.description}")
                    if len(endpoints) > 10:
                        md_lines.append(f"  - ... 还有 {len(endpoints) - 10} 个端点")
                    md_lines.append("")
        
        # 整体质量评估
        md_lines.extend([
            "## 整体API质量评估",
            f"**总体评分**: {self.overall_quality.overall_score}/10",
            ""
        ])
        
        if self.overall_quality.issues:
            md_lines.extend([
                "### 发现的问题",
                ""
            ])
            for issue in self.overall_quality.issues:
                md_lines.append(f"- {issue}")
            md_lines.append("")
        
        # 按模块的质量评估
        if self.quality_by_module:
            md_lines.extend([
                "## 各模块API质量",
                ""
            ])
            for module_path, quality in self.quality_by_module.items():
                md_lines.extend([
                    f"### {module_path}",
                    f"评分: {quality.overall_score}/10",
                    ""
                ])
                if quality.issues:
                    for issue in quality.issues[:3]:  # 限制显示前3个问题
                        md_lines.append(f"- {issue}")
                    md_lines.append("")
        
        return "\n".join(md_lines)


class CodeRelationshipAnalysis(BaseModel):
    """代码关系分析 - 编程关系分析结果"""
    module_clusters: List[ModuleCluster] = Field(default_factory=list, description="模块集群列表")
    dependency_matrix: DependencyMatrix = Field(default_factory=DependencyMatrix, description="依赖关系矩阵")
    import_analysis: Dict[str, List[str]] = Field(default_factory=dict, description="导入关系分析")
    inheritance_hierarchy: Dict[str, List[str]] = Field(default_factory=dict, description="继承层次结构")
    interface_contracts: Dict[str, List[str]] = Field(default_factory=dict, description="接口契约关系")
    data_flow_paths: List[List[str]] = Field(default_factory=list, description="数据流路径")


class CodeAnalysisResult(BaseModel):
    """编程关系分析结果 - 独立的代码分析模型"""
    # 基础标识信息
    project_name: str = Field(default="", description="项目名称")
    analysis_scope: str = Field(default="", description="分析范围描述")
    
    # 原始数据输入
    source_module_analyses: Dict[str, ModuleAnalysis] = Field(default_factory=dict, description="源模块分析结果")
    
    # 编程关系分析结果
    code_relationships: CodeRelationshipAnalysis = Field(default_factory=CodeRelationshipAnalysis, description="代码关系分析")
    
    # API设计合并分析结果
    api_analysis: Optional[APIAnalysis] = Field(default=None, description="API设计分析结果")

    def to_markdown(self) -> str:
        """转换为Markdown格式的代码分析报告"""
        md_lines = [
            f"# 编程关系分析报告",
            f"",
            f"**项目:** {self.project_name}",
            f"**分析范围:** {self.analysis_scope}",
            f"",
            f"## 代码关系分析",
            f"",
            f"**模块集群数量:** {len(self.code_relationships.module_clusters)}",
            f"**循环依赖组数:** {len(self.code_relationships.dependency_matrix.circular_dependency_groups)}",
            f"**数据流路径数:** {len(self.code_relationships.data_flow_paths)}",
            f""
        ]
        
        if self.code_relationships.module_clusters:
            md_lines.extend([
                f"## 模块集群详情",
                f""
            ])
            for cluster in self.code_relationships.module_clusters:
                md_lines.extend([
                    f"### {cluster.cluster_name}",
                    f"- **目录路径:** {cluster.directory_path}",
                    f"- **模块数量:** {len(cluster.module_paths)}",
                    f"- **主要语言:** {cluster.primary_language}",
                    f""
                ])
        
        if self.code_relationships.dependency_matrix.circular_dependency_groups:
            md_lines.extend([
                f"## 循环依赖分析",
                f""
            ])
            for i, group in enumerate(self.code_relationships.dependency_matrix.circular_dependency_groups, 1):
                md_lines.extend([
                    f"### 循环依赖组 {i}",
                    f"涉及模块: {' -> '.join(group)}",
                    f""
                ])
        
        # API设计合并分析
        if self.api_analysis:
            md_lines.extend([
                "",
                "---",
                "",
                self.api_analysis.to_markdown(),
                ""
            ])
        
        return "\n".join(md_lines)


class ModuleMergerAnalysisResult(BaseModel):
    """模块合并分析结果容器 - 包含AI和编程两个独立分析结果"""
    # 基础标识信息
    project_name: str = Field(default="", description="项目名称")
    
    # 独立的分析结果
    ai_analysis: Optional[AIAnalysisResult] = Field(default=None, description="AI智能分析结果")
    code_analysis: Optional[CodeAnalysisResult] = Field(default=None, description="编程关系分析结果")

    def to_markdown(self) -> str:
        """转换为完整的Markdown格式报告"""
        md_lines = [
            f"# 模块合并分析完整报告",
            f"",
            f"**项目:** {self.project_name}",
            f"",
        ]
        
        if self.ai_analysis:
            md_lines.extend([
                f"---",
                f"",
                self.ai_analysis.to_markdown(),
                f"",
            ])
        
        if self.code_analysis:
            md_lines.extend([
                f"---",
                f"",
                self.code_analysis.to_markdown(),
                f"",
            ])
        
        return "\n".join(md_lines)


class ModuleMergerConfig(BaseModel):
    """模块合并分析配置"""
    # AI分析配置
    enable_ai_analysis: bool = Field(default=True, description="是否启用AI分析")
    max_modules_per_ai_call: int = Field(default=10, description="单次AI调用最大模块数")
    
    # 代码关系分析配置
    enable_dependency_analysis: bool = Field(default=True, description="是否启用依赖关系分析")
    include_external_dependencies: bool = Field(default=True, description="是否包含外部依赖")
    
    # 输出配置
    generate_markdown_report: bool = Field(default=True, description="是否生成Markdown报告")
