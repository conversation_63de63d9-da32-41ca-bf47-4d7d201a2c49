"""数据库引擎模块"""
from typing import Optional
from sqlalchemy import create_engine, MetaData, Engine, select
from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine, AsyncSession
from sqlalchemy.orm import sessionmaker, scoped_session, Session
from sqlalchemy.pool import QueuePool
import structlog

from ...models.model_base import Base

logger = structlog.get_logger(__name__)

class DatabaseEngine:
    """数据库引擎管理器"""
    
    def __init__(
        self,
        url: str,
        *,
        echo: bool = True,
        pool_size: int = 20,
        max_overflow: int = 10,
        pool_timeout: int = 30,
        pool_recycle: int = 3600,
        pool_pre_ping: bool = True
    ):
        """初始化数据库引擎
        
        Args:
            url: 数据库连接URL
            echo: 是否打印SQL语句
            pool_size: 连接池大小
            max_overflow: 最大溢出连接数
            pool_timeout: 连接池超时时间（秒）
            pool_recycle: 连接回收时间（秒）
            pool_pre_ping: 是否启用连接预检测
        """
        try:
            # 同步引擎配置
            sync_pool_config = {
                "poolclass": QueuePool,
                "pool_size": pool_size,
                "max_overflow": max_overflow,
                "pool_timeout": pool_timeout,
                "pool_recycle": pool_recycle,
                "pool_pre_ping": pool_pre_ping
            }
            
            # 同步引擎
            self.sync_engine = create_engine(
                url,
                echo=echo,
                future=True,
                **sync_pool_config
            )
            self._setup_sync_session_factory()
            
            # 异步引擎配置（不使用 QueuePool）
            async_pool_config = {
                "pool_size": pool_size,
                "max_overflow": max_overflow,
                "pool_timeout": pool_timeout,
                "pool_recycle": pool_recycle,
                "pool_pre_ping": pool_pre_ping
            }
            
            # 异步引擎
            async_url = url.replace('postgresql://', 'postgresql+asyncpg://')
            self.async_engine = create_async_engine(
                async_url,
                echo=echo,
                **async_pool_config
            )
            self._setup_async_session_factory()
            
            logger.info("数据库引擎初始化成功",
                       sync_url=url.split("@")[-1],  # 不记录敏感信息
                       async_url=async_url.split("@")[-1],
                       pool_size=pool_size,
                       max_overflow=max_overflow)
                       
        except Exception as e:
            logger.error("数据库引擎初始化失败",
                        error=str(e),
                        error_type=type(e).__name__)
            raise
            
    def _setup_sync_session_factory(self):
        """设置同步会话工厂"""
        session_factory = sessionmaker(
            bind=self.sync_engine,
            class_=Session,
            expire_on_commit=False,
            autoflush=True
        )
        self.SyncSessionFactory = scoped_session(session_factory)
        
    def _setup_async_session_factory(self):
        """设置异步会话工厂"""
        self.AsyncSessionFactory = sessionmaker(
            bind=self.async_engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=True
        )
        
    def get_sync_engine(self) -> Engine:
        """获取同步SQLAlchemy引擎
        
        Returns:
            SQLAlchemy引擎
        """
        return self.sync_engine
        
    def get_async_engine(self) -> AsyncEngine:
        """获取异步SQLAlchemy引擎
        
        Returns:
            AsyncEngine
        """
        return self.async_engine
        
    def get_sync_session_factory(self) -> scoped_session:
        """获取同步会话工厂
        
        Returns:
            会话工厂
        """
        return self.SyncSessionFactory
        
    def get_async_session_factory(self) -> sessionmaker:
        """获取异步会话工厂
        
        Returns:
            会话工厂
        """
        return self.AsyncSessionFactory
        
    def create_all(self):
        """创建所有数据库表"""
        metadata = Base.metadata
        metadata.create_all(self.sync_engine)
        logger.info("创建所有数据库表")
        
    def drop_all(self):
        """删除所有数据库表"""
        metadata = Base.metadata
        metadata.drop_all(self.sync_engine)
        logger.info("删除所有数据库表")
