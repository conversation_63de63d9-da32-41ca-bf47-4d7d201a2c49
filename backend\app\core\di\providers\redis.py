"""
Redis提供者模块
"""
from typing import Any, Optional, Union
from datetime import timedelta
import structlog
from redis import Redis
from dependency_injector import providers

logger = structlog.get_logger(__name__)

class RedisProvider(providers.Provider):
    """Redis客户端提供者"""
    
    def __init__(self, redis_client: Redis):
        """初始化Redis客户端提供者
        
        Args:
            redis_client: Redis客户端
        """
        self._redis = redis_client
        super().__init__()
    
    def __call__(self) -> Redis:
        """获取Redis客户端实例
        
        Returns:
            Redis: Redis客户端实例
        """
        return self._redis
    
    def get(self, key: str) -> Any:
        """获取键值
        
        Args:
            key: 键名
            
        Returns:
            Any: 键值
        """
        try:
            return self._redis.get(key)
        except Exception as e:
            logger.error("获取Redis键值失败", key=key, error=str(e))
            return None
    
    def set(self, key: str, value: Any, **kwargs) -> bool:
        """设置键值
        
        Args:
            key: 键名
            value: 键值
            **kwargs: 其他参数，例如 ex（过期时间，单位秒）
            
        Returns:
            bool: 是否成功
        """
        try:
            return self._redis.set(key, value, **kwargs)
        except Exception as e:
            logger.error("设置Redis键值失败", key=key, error=str(e))
            return False
    
    def delete(self, *keys: str) -> int:
        """删除键
        
        Args:
            *keys: 键名列表
            
        Returns:
            int: 删除的键数量
        """
        try:
            return self._redis.delete(*keys)
        except Exception as e:
            logger.error("删除Redis键失败", keys=keys, error=str(e))
            return 0
    
    def exists(self, *keys: str) -> int:
        """检查键是否存在
        
        Args:
            *keys: 键名列表
            
        Returns:
            int: 存在的键数量
        """
        try:
            return self._redis.exists(*keys)
        except Exception as e:
            logger.error("检查Redis键是否存在失败", keys=keys, error=str(e))
            return 0
    
    def setex(self, key: str, time: Union[timedelta, int], value: Any) -> bool:
        """设置键值并设置过期时间
        
        Args:
            key: 键名
            time: 过期时间（timedelta对象或秒数）
            value: 键值
            
        Returns:
            bool: 是否成功
        """
        try:
            if isinstance(time, timedelta):
                seconds = int(time.total_seconds())
            else:
                seconds = time
            return self._redis.setex(key, seconds, value)
        except Exception as e:
            logger.error("设置Redis键值和过期时间失败", key=key, error=str(e))
            return False
    
    def sadd(self, key: str, *values: Any) -> int:
        """向集合添加元素
        
        Args:
            key: 集合键名
            *values: 元素列表
            
        Returns:
            int: 添加的元素数量
        """
        try:
            return self._redis.sadd(key, *values)
        except Exception as e:
            logger.error("向Redis集合添加元素失败", key=key, error=str(e))
            return 0
    
    def srem(self, key: str, *values: Any) -> int:
        """从集合移除元素
        
        Args:
            key: 集合键名
            *values: 元素列表
            
        Returns:
            int: 移除的元素数量
        """
        try:
            return self._redis.srem(key, *values)
        except Exception as e:
            logger.error("从Redis集合移除元素失败", key=key, error=str(e))
            return 0
    
    def smembers(self, key: str) -> set:
        """获取集合所有元素
        
        Args:
            key: 集合键名
            
        Returns:
            set: 元素集合
        """
        try:
            return self._redis.smembers(key)
        except Exception as e:
            logger.error("获取Redis集合元素失败", key=key, error=str(e))
            return set()
    
    def incr(self, key: str) -> int:
        """递增键值
        
        Args:
            key: 键名
            
        Returns:
            int: 递增后的值
        """
        try:
            return self._redis.incr(key)
        except Exception as e:
            logger.error("递增Redis键值失败", key=key, error=str(e))
            return 0
    
    def decr(self, key: str) -> int:
        """递减键值
        
        Args:
            key: 键名
            
        Returns:
            int: 递减后的值
        """
        try:
            return self._redis.decr(key)
        except Exception as e:
            logger.error("递减Redis键值失败", key=key, error=str(e))
            return 0
    
    def expire(self, key: str, time: Union[timedelta, int]) -> bool:
        """设置键的过期时间
        
        Args:
            key: 键名
            time: 过期时间（timedelta对象或秒数）
            
        Returns:
            bool: 是否成功
        """
        try:
            if isinstance(time, timedelta):
                seconds = int(time.total_seconds())
            else:
                seconds = time
            return self._redis.expire(key, seconds)
        except Exception as e:
            logger.error("设置Redis键过期时间失败", key=key, error=str(e))
            return False
    
    def ttl(self, key: str) -> int:
        """获取键的剩余过期时间
        
        Args:
            key: 键名
            
        Returns:
            int: 剩余过期时间（秒），-1表示永不过期，-2表示键不存在
        """
        try:
            return self._redis.ttl(key)
        except Exception as e:
            logger.error("获取Redis键过期时间失败", key=key, error=str(e))
            return -2
    
    def reset(self) -> None:
        """重置提供者状态"""
        pass
