"""
密码重置相关的模式类
"""
from pydantic import BaseModel, EmailStr, Field, validator
import re

class PasswordResetRequest(BaseModel):
    """密码重置请求"""
    email: EmailStr = Field(..., description="用户邮箱")
    reset_url: str = Field(..., description="重置url")

class PasswordResetTokenVerify(BaseModel):
    """密码重置令牌验证"""
    token: str = Field(..., description="重置令牌")

class PasswordResetComplete(BaseModel):
    """完成密码重置"""
    token: str = Field(..., description="重置验证码")
    password: str = Field(..., description="新密码")
    # confirm_password: str = Field(..., description="确认新密码")
    
    @validator('password')
    def password_strength(cls, v):
        """验证密码强度"""
        if len(v) < 8:
            raise ValueError("密码长度至少为8个字符")
        if not any(c.isupper() for c in v):
            raise ValueError("密码必须包含至少一个大写字母")
        if not any(c.islower() for c in v):
            raise ValueError("密码必须包含至少一个小写字母")
        if not any(c.isdigit() for c in v):
            raise ValueError("密码必须包含至少一个数字")
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError("密码必须包含至少一个特殊字符")
        return v
    
    # @validator('confirm_password')
    # def passwords_match(cls, v, values, **kwargs):
    #     """验证两次密码输入是否一致"""
    #     if 'password' in values and v != values['password']:
    #         raise ValueError("两次输入的密码不一致")
    #     return v