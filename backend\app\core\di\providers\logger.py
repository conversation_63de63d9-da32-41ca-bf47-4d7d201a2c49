"""
自定义提供者模块
提供特定于应用的依赖注入提供者
"""
from typing import Optional, Any, AsyncGenerator, Generator
from contextlib import asynccontextmanager, contextmanager
from dependency_injector import providers
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from app.core.database import Database
from redis import Redis

logger = structlog.get_logger(__name__)

class LoggerProvider(providers.Provider):
    """日志提供者
    
    提供结构化日志记录器，支持：
    1. 自定义日志名称
    2. 上下文信息注入
    3. 结构化日志格式
    """
    
    def __init__(self, name: Optional[str] = None, **context: Any):
        """初始化提供者
        
        Args:
            name: 日志记录器名称
            **context: 要注入到日志上下文的键值对
        """
        self.name = name
        self.context = context
        super().__init__()
        
    def __call__(self, *args, **kwargs) -> Any:
        """获取日志记录器
        
        Returns:
            Logger: 结构化日志记录器
            
        Example:
            logger = logger_provider(module="auth")
            logger.info("用户登录", user_id=123)
        """
        logger = structlog.get_logger(self.name)
        if self.context:
            logger = logger.bind(**self.context)
        return logger
        
    def reset(self) -> None:
        """重置提供者状态"""
        pass
