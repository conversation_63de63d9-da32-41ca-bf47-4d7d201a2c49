#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/6/17 15:37
# @File    : github_project_scan_workflow.py
# @Description: 
"""


from typing import Dict, Any, Optional, List
import structlog
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.schemas.github.github_project import GitHubProjectCreate, GitHubProject
from app.services.github.github_downloader import GitHubDownloader
from app.services.github.github_project import GitHubProjectService
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide
from app.core.middleware import require_auth
from app.utils.project_scanner_utils import ProjectScannerUtils
from app.utils.status_enum import ProjectStatusEnum

logger = structlog.get_logger(__name__)

# 人工筛选并投放项目的具体过程
# 1.扫描 get
# 2.筛选下载 post
# 3.获取项目下载进度 get
# 4.处理下载状态 post
# 5.获取项目分析进度 get
# 6.处理项目分析状态 post


class ProjectScanHandler(BaseHandler):
    """GitHub/Gitee项目扫描处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    # @require_auth(required=True, permissions=["github:project:list"])
    async def get(self) -> None:
        """获取项目列表"""
        try:
            # 获取请求来源参数
            source = self.get_argument("source", "github")  # github 或 gitee

            # 获取基础搜索参数
            page = int(self.get_argument("page", "1"))
            language = self.get_argument("language", None)
            keyword = self.get_argument("keyword", None)
            sort = self.get_argument("sort", "stars")
            order = self.get_argument("order", "desc")
            page_size = self.get_argument("page_size", "30")

            # 获取范围筛选参数
            stars_min = self.get_argument("stars_min", None)
            stars_max = self.get_argument("stars_max", None)
            size_min = self.get_argument("size_min", None)
            size_max = self.get_argument("size_max", None)

            # 转换数字参数
            if stars_min:
                stars_min = int(stars_min)
            if stars_max:
                stars_max = int(stars_max)
            if size_min:
                size_min = int(size_min)
            if size_max:
                size_max = int(size_max)

            # 根据来源调用不同的搜索方法
            if source.lower() == "github":
                result = await ProjectScannerUtils.search_github_projects(
                    page=page,
                    language=language,
                    stars_min=stars_min,
                    stars_max=stars_max,
                    size_min=size_min,
                    size_max=size_max,
                    keyword=keyword,
                    sort=sort,
                    order=order,
                    page_size=page_size
                )
            elif source.lower() == "gitee":
                result = await ProjectScannerUtils.search_gitee_projects(
                    page=page,
                    language=language,
                    stars_min=stars_min,
                    stars_max=stars_max,
                    size_min=size_min,
                    size_max=size_max,
                    keyword=keyword,
                    sort=sort,
                    order=order,
                    page_size=page_size

                )
            else:
                self.write_error(400, error_message=f"不支持的搜索来源: {source}")
                return

            self.success_response(result)

        except ValueError as e:
            logger.error("请求参数无效", error=str(e))
            self.write_error(400, error_message="请求参数无效: " + str(e))
        except Exception as e:
            logger.error("获取项目列表时发生错误", error=str(e))
            self.write_error(500, error_message="获取项目扫描列表时发生错误: " + str(e))



class ProjectDownloadProgressHandler(BaseHandler):
    """项目下载进度处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    # @require_auth(required=True, permissions=["github:project:progress"])
    async def get(self) -> None:
        """获取项目下载进度

        返回下载队列状态和各个状态的项目统计信息
        """
        try:
            # 获取下载队列状态
            downloader = GitHubDownloader()
            queue_status = await downloader.get_download_queue_status()

            # 获取各状态的项目统计
            status_stats = await self.github_project_service.get_project_status_statistics()

            # 获取正在下载的项目列表
            downloading_projects = await self.github_project_service.get_downloading_projects()

            result = {
                "queue_status": queue_status,
                "status_statistics": status_stats,
                "downloading_projects": downloading_projects
            }

            self.success_response(result)

        except Exception as e:
            logger.error("获取项目下载进度时发生错误", error=str(e))
            self.write_error(500, error_message="获取项目下载进度时发生错误: " + str(e))


class ProjectDownloadStatusHandler(BaseHandler):
    """项目下载状态处理处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    # @require_auth(required=True, permissions=["github:project:status"])
    async def put(self) -> None:
        """处理项目下载状态

        请求体:
        {
                putputputputputput
            "project_ids": ["id1", "id2", ...]
        }
        """
        try:
            project_ids = self.json_body.get("project_ids", [])
            # 调用服务层处理项目状态
            result = await self.github_project_service.put_download_status(project_ids)

            self.success_response(result)

        except Exception as e:
            logger.error("处理项目下载状态时发生错误", error=str(e))
            self.write_error(500, error_message="处理项目下载状态时发生错误: " + str(e))