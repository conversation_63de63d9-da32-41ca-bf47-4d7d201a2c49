#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能体模型配置管理器
从YAML文件中加载智能体模型配置
"""
import os
import logging
from typing import Dict, Any, Optional, List
import yaml
from datetime import datetime, timezone

from pydantic import BaseModel, Field, field_validator

import structlog

logger = structlog.get_logger(__name__)

class ModelConfig(BaseModel):
    """智能体模型配置"""
    name: str = Field(description="模型名称")
    url: Optional[str] = Field(None, description="模型API URL")
    api_key: Optional[str] = Field(None, description="API密钥")
    api_version: Optional[str] = Field(None, description="API版本")
    temperature: float = Field(0.0, description="温度参数")
    max_tokens: Optional[int] = Field(None, description="最大生成token数")
    request_timeout: int = Field(300, description="请求超时时间（秒）")
    max_retries: int = Field(3, description="最大重试次数")
    streaming: bool = Field(True, description="是否使用流式响应")
    additional_params: Dict[str, Any] = Field(default_factory=dict, description="额外参数")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="创建时间")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="更新时间")

    @field_validator("temperature")
    def validate_temperature(cls, v):
        if v < 0.0 or v > 1.0:
            raise ValueError("温度参数必须在0.0到1.0之间")
        return v


class AgentModelConfig(BaseModel):
    """智能体的模型配置"""
    agent_name: str = Field(description="智能体名称")
    model: ModelConfig = Field(description="模型配置")
    capabilities: List[str] = Field(default_factory=list, description="支持的能力")
    description: Optional[str] = Field(None, description="配置描述")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="创建时间")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="更新时间")


class ModelConfigManager:
    """
    智能体模型配置管理器
    加载和管理智能体模型配置
    """
    
    def __init__(self, config_dir: Optional[str] = None, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，如果为None则使用默认路径
            config_file: 配置文件名，如果为None则使用默认文件名
        """
        # 默认配置目录
        self.config_dir = config_dir or os.path.join(
            os.path.dirname(os.path.abspath(__file__)), 
            "config"
        )
        
        # 配置文件名
        self.config_file = config_file or "agent_configs.yml"
        
        # 配置文件的完整路径
        self.config_path = os.path.join(self.config_dir, self.config_file)
        
        # 确保配置目录存在
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
            
        # 缓存所有已加载的配置
        self.configs: Dict[str, AgentModelConfig] = {}
        
        # 默认模型配置
        self.default_config = ModelConfig(
            name="google/gemma-3-27b-it:free",
            url="https://openrouter.ai/api/v1",
            api_key="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",
            temperature=0.0,
            streaming=True,
            request_timeout=200
        )
        
        logger.info(f"智能体模型配置管理器初始化，配置文件: {self.config_path}")
        
        # 加载配置
        self.load_configs()
    
    def load_configs(self) -> None:
        """加载所有可用的模型配置"""
        try:
            # 检查配置目录是否存在
            if not os.path.exists(self.config_dir):
                logger.warning(f"配置目录不存在，创建目录: {self.config_dir}")
                os.makedirs(self.config_dir, exist_ok=True)
            
            # 检查配置文件是否存在
            if not os.path.exists(self.config_path):
                logger.warning(f"配置文件不存在，创建默认配置文件: {self.config_path}")
                self._create_default_config_file()
                return
                
            # 加载YAML配置
            with open(self.config_path, "r", encoding="utf-8") as f:
                config_data = yaml.safe_load(f)
                
            if not config_data or not isinstance(config_data, dict):
                logger.error(f"无效的配置文件格式: {self.config_path}")
                self._create_default_config_file()
                return
                
            # 加载每个智能体的配置
            agents_config = config_data.get("agents", {})
            if not agents_config:
                logger.warning("配置文件中未找到智能体配置，将使用默认配置")
                self._create_default_config_file()
                return
                
            for agent_name, agent_config in agents_config.items():
                try:
                    # 特殊处理default配置
                    if agent_name.lower() == "default":
                        formatted_name = "DefaultAgent"
                    else:
                        # 格式化其他智能体名称
                        formatted_name = self._format_agent_name(agent_name)
                    
                    # 构造模型配置
                    model_config = ModelConfig(**agent_config.get("model", {}))
                    
                    # 构造智能体配置
                    self.configs[formatted_name] = AgentModelConfig(
                        agent_name=formatted_name,
                        model=model_config,
                        capabilities=agent_config.get("capabilities", []),
                        description=agent_config.get("description", f"{formatted_name}的模型配置")
                    )
                    
                    logger.info(f"成功加载智能体 {formatted_name} 的模型配置")
                except Exception as e:
                    logger.error(f"加载智能体 {agent_name} 的配置失败: {str(e)}")
            
            logger.info(f"成功加载 {len(self.configs)} 个智能体模型配置")
        except Exception as e:
            logger.error(f"加载模型配置失败: {str(e)}")
    
    def _format_agent_name(self, name: str) -> str:
        """
        格式化智能体名称
        
        Args:
            name: 原始名称
            
        Returns:
            str: 格式化后的名称
        """
        # 转换为驼峰式并确保以Agent结尾
        if name.lower().endswith("agent"):
            name = name[:-5]
            
        name_parts = name.split("_")
        formatted_name = "".join(part.capitalize() for part in name_parts)
        
        return f"{formatted_name}Agent"
    
    def _create_default_config_file(self) -> None:
        """创建默认配置文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            # 创建默认配置
            default_configs = {
                "agents": {
                    "default": {
                        "model": self.default_config.model_dump(),
                        "capabilities": [],
                        "description": "默认模型配置"
                    }
                },
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
            
            # 写入配置文件
            with open(self.config_path, "w", encoding="utf-8") as f:
                yaml.dump(default_configs, f, default_flow_style=False, allow_unicode=True)
                
            logger.info(f"已创建默认配置文件: {self.config_path}")
            
            # 加载默认配置
            self.load_configs()
        except Exception as e:
            logger.error(f"创建默认配置文件失败: {str(e)}")
    
    def get_model_config(self, agent_name: str) -> ModelConfig:
        """
        获取智能体模型配置
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            ModelConfig: 模型配置，如果不存在则返回默认配置
        """
        # 首先尝试获取智能体特定配置
        formatted_name = self._format_agent_name(agent_name)
        agent_config = self.configs.get(formatted_name)
        
        # 如果找不到特定配置，尝试获取默认配置
        if not agent_config:
            default_config = self.configs.get("DefaultAgent")
            
            # 如果找到默认配置，则使用它
            if default_config:
                logger.info(f"使用默认配置替代 {formatted_name} 的配置")
                return default_config.model
            
            # 如果找不到默认配置，则使用硬编码的默认配置
            logger.info(f"未找到智能体 {formatted_name} 的配置，使用硬编码默认配置")
            return self.default_config
            
        return agent_config.model
    
    def save_config(self, config: AgentModelConfig) -> bool:
        """
        保存配置到文件
        
        Args:
            config: 要保存的配置
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 更新时间戳
            config.updated_at = datetime.now(timezone.utc)
            
            # 加载当前配置文件
            current_configs = {}
            if os.path.exists(self.config_path):
                with open(self.config_path, "r", encoding="utf-8") as f:
                    current_configs = yaml.safe_load(f) or {}
            
            # 确保agents字典存在
            if "agents" not in current_configs:
                current_configs["agents"] = {}
                
            # 特殊处理DefaultAgent
            if config.agent_name == "DefaultAgent":
                agent_key = "default"
            else:
                # 提取智能体名称（移除Agent后缀并转为小写+下划线格式）
                agent_key = config.agent_name
                if agent_key.endswith("Agent"):
                    agent_key = agent_key[:-5]
                    
                # 转换为蛇形命名法
                agent_key = "".join(["_" + c.lower() if c.isupper() else c.lower() for c in agent_key]).lstrip("_")
            
            # 更新配置
            current_configs["agents"][agent_key] = {
                "model": config.model.model_dump(),
                "capabilities": config.capabilities,
                "description": config.description
            }
            
            # 更新时间戳
            current_configs["updated_at"] = datetime.now(timezone.utc).isoformat()
            
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            # 保存到YAML文件
            with open(self.config_path, "w", encoding="utf-8") as f:
                yaml.dump(current_configs, f, default_flow_style=False, allow_unicode=True)
                
            # 更新缓存
            self.configs[config.agent_name] = config
            
            logger.info(f"成功保存智能体 {config.agent_name} 的模型配置")
            return True
        except Exception as e:
            logger.error(f"保存智能体 {config.agent_name} 的模型配置失败: {str(e)}")
            return False


# 创建全局模型配置管理器实例
model_config_manager = ModelConfigManager()
