#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目分析配置模型和状态模型
"""
import multiprocessing
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from pydantic import Field, BaseModel

from ..analyzers.module_models import ModuleAnalysis
from ..analyzers.project_dependency_models import DependencyAnalysis
from ..analyzers.project_architecture_models import ArchitectureAnalysis
from ..analyzers.project_structure_models import ProjectStructureAnalysis
from ..analyzers.readme_generator.section_models import ReadmeDocument
from ..analyzers.module_merger_models import ModuleMergerAnalysisResult
from ..analyzers.intelligent_generator.planners.intelligent_planner_models import ReadmePlan
from ..project_scanning.project_info_models import ProjectStructure, FileInfo

# 获取CPU核心数，用于计算默认并行工作线程数
def get_default_workers():
    """计算默认并行工作线程数，基于CPU核心数"""
    # 获取CPU核心数
    cpu_count = multiprocessing.cpu_count()
    # 使用总核心数的75%，至少为16，避免CPU过载
    if cpu_count > 32:
        return 32
    return max(16, int(cpu_count * 0.75))
    # return 10

class AnalysisConfig(BaseModel):
    """项目分析配置"""
    project_path: str = Field(default="",description="项目路径")
    project_name: str = Field(default="",description="项目名称")
    analyze_architecture: bool = Field(default=True, description="是否分析项目架构")
    analyze_structure: bool = Field(default=True, description="是否分析项目结构")
    analyze_dependencies: bool = Field(default=True, description="是否分析项目依赖")
    analyze_modules: bool = Field(default=True, description="是否分析项目模块")
    integrate_modules: bool = Field(default=True, description="是否整合项目模块")
    analyze_changes: bool = Field(default=True, description="是否分析项目变更")
    generate_readme: bool = Field(default=True, description="是否生成项目README")
    enable_intelligent_planning: bool = Field(default=True, description="是否启用智能规划")
    validate_analysis: bool = Field(default=True, description="是否验证分析结果")
    parallel_workers: int = Field(default_factory=get_default_workers, description="并行工作线程数，默认为CPU核心数的75%")
    max_memory_usage: float = Field(default=0.8, description="最大内存使用率，默认0.8(80%)")
    adaptive_batch_size: bool = Field(default=True, description="是否启用自适应批处理大小")
    force_full_analysis: bool = Field(default=False, description="是否强制进行完整分析，忽略缓存")

class AnalysisState(BaseModel):
    """分析状态与结果"""
    project_path: str = Field(..., description="项目路径")
    project_name: str = Field(..., description="项目名称")
    stage: str = Field(default="initialized", description="当前分析阶段")
    files: Dict[str, FileInfo] = Field(default_factory=dict, description="文件列表")
    module_analyses: Dict[str, ModuleAnalysis] = Field(default_factory=dict, description="模块分析结果")
    dependency_analysis: Optional[DependencyAnalysis] = Field(default=None, description="项目依赖分析结果")
    architecture_analysis: Optional[ArchitectureAnalysis] = Field(default=None, description="架构分析结果")
    structure_analysis: Optional[ProjectStructureAnalysis] = Field(default=None, description="结构分析结果")
    project_structure: Optional[ProjectStructure] = Field(default=None, description="项目结构扫描结果")
    readme: Optional[ReadmeDocument] = Field(default=None, description="README文档内容")
    readme_plan: Optional[ReadmePlan] = Field(default=None, description="智能规划的README章节结构")
    module_groups: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="模块分组信息，键为目录路径，值为分组详情")
    module_merger_analyses: Dict[str, ModuleMergerAnalysisResult] = Field(default_factory=dict, description="模块合并分析结果，键为分组key，值为合并分析结果")

    # 通用处理队列
    current_module: Optional[str] = Field(default=None, description="当前正在分析的模块")
    completed_modules: List[str] = Field(default_factory=list, description="已完成模块分析的文件")
    pending_modules: List[str] = Field(default_factory=list, description="待分析的模块文件")
    priority_modules: List[str] = Field(default_factory=list, description="优先分析的模块文件")
    failed_modules: List[str] = Field(default_factory=list, description="分析失败的模块文件")
    module_priorities: Dict[str, int] = Field(default_factory=dict, description="模块优先级字典")

    # 错误和统计
    errors: Dict[str, str] = Field(default_factory=dict, description="错误信息")
    start_time: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="分析开始时间")
    end_time: Optional[datetime] = Field(default=None, description="分析结束时间")
    parallel_analysis_in_progress: bool = Field(default=False, description="是否正在进行并行分析")
    analysis_progress: float = Field(default=0.0, description="分析进度，0.0-1.0之间")

    # 验证结果
    validation_results: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="分析结果验证信息")
    is_incremental_analysis: bool = Field(default=True, description="是否是增量分析")
    changed_modules: List[str] = Field(default_factory=list, description="变化的模块文件")
    new_modules: List[str] = Field(default_factory=list, description="新增的模块文件")
    deleted_modules: List[str] = Field(default_factory=list, description="删除的模块文件")

    def get_modules_count(self) -> int:
        """获取模块总数"""
        return len(self.completed_modules) + len(self.pending_modules) + len(self.failed_modules)

    def get_completed_percentage(self) -> float:
        """获取已完成百分比"""
        total = self.get_modules_count()
        if total == 0:
            return 0.0
        return len(self.completed_modules) / total * 100
