#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装说明章节生成器
"""
import logging
import os
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone

from langchain.chains import <PERSON><PERSON>hain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser

from . import Generator
from ..section_models import InstallationSection, InstallationStep
from .....ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser
from ......utils.retry import retry_async

logger = logging.getLogger(__name__)


class InstallationGenerator(Generator):
    """
    安装说明章节生成器
    负责生成README文档的安装说明章节
    """

    def __init__(self, llm: ChatOpenAI):
        """初始化安装说明章节生成器"""
        super().__init__(llm)

    async def create_chain(self) -> None:
        """初始化处理链"""
        try:
            # 安装说明章节生成提示模板
            installation_template = """
            作为专业的技术文档撰写专家，请基于项目分析数据生成全面、详细且易于理解的安装说明章节。

            ## 项目基本信息
            项目名称: {{ project_name }}
            项目路径: {{ project_path }}
            {%- if structure_analysis %}

            ## 安装相关结构分析
            {%- if structure_analysis.dependencies %}

            ### 依赖管理:
            {%- for dep in structure_analysis.dependencies[:3] %}
            - **{{ dep.name }}** ({{ dep.package_manager }}): {{ dep.dependency_category }}
            {%- if dep.has_lockfile %}  版本锁定: 支持{%- endif %}
            {%- endfor %}
            {%- endif %}

            {%- if structure_analysis.builds %}

            ### 构建部署:
            {%- for build in structure_analysis.builds[:3] %}
            - **{{ build.build_tool }}**: {{ build.deployment_stage }}
            {%- if build.target_platforms %}  平台: {{ build.target_platforms|join(", ") }}{%- endif %}
            {%- endfor %}
            {%- endif %}

            {%- if structure_analysis.configs %}

            ### 配置文件:
            {%- for config in structure_analysis.configs[:3] %}
            - **{{ config.name }}** ({{ config.config_format }}): {{ config.config_scope }}
            {%- if config.is_required %}  必需配置{%- endif %}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if dependency_analysis %}

            ## 技术栈和依赖
            {%- if dependency_analysis.tech_stack %}
            {%- if dependency_analysis.tech_stack.primary_language %}
            主要语言: {{ dependency_analysis.tech_stack.primary_language }}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.frameworks %}
            核心框架: {%- for framework in dependency_analysis.tech_stack.frameworks[:3] %}{{ framework }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.build_tools %}
            构建工具: {%- for tool in dependency_analysis.tech_stack.build_tools[:3] %}{{ tool }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.databases %}
            数据存储: {%- for db in dependency_analysis.tech_stack.databases[:3] %}{{ db }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if dependency_analysis.external_dependencies %}

            ### 关键依赖:
            {%- for dep in dependency_analysis.external_dependencies[:8] %}
            - **{{ dep.name }}** {%- if dep.version %}{{ dep.version }}{%- endif %}: {{ dep.purpose }}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if module_merger_analyses %}

            ## 安装相关模块分析
            {%- for group_key, merger_result in module_merger_analyses.items() %}
            {%- if merger_result.ai_analysis and merger_result.ai_analysis.ai_findings %}
            {%- for finding in merger_result.ai_analysis.ai_findings[:3] %}
            {%- if "install" in finding.title.lower() or "安装" in finding.title.lower() or "setup" in finding.title.lower() or "配置" in finding.title.lower() or "requirement" in finding.title.lower() or "依赖" in finding.title.lower() %}
            {%- if loop.first %}

            ### {{ group_key }} 安装洞察:
            {%- endif %}
            - **{{ finding.title }}**: {{ finding.description }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}
            {%- endfor %}
            {%- endif %}

            ## 安装文档生成要求与原则

            ### 核心生成原则
            1. **实用性优先**：生成实际可执行的安装步骤，确保用户能够成功安装项目
            2. **基于实际数据**：所有安装说明必须基于提供的项目依赖和构建工具信息
            3. **清晰简洁**：使用明确的命令和步骤，避免技术术语过载
            4. **全面覆盖**：考虑不同操作系统、环境和用户技能水平
            5. **验证完整**：提供安装验证方法，确保用户知道安装是否成功
            6. **问题解决**：预见常见问题并提供解决方案

            ### 内容生成任务
            请生成一个全面、详细且易于理解的安装说明章节，包含以下9个核心要素：

            1. **系统要求**（100-150字）：详细列出必要的系统要求（操作系统、硬件、内存等）、推荐和最低系统配置、所有必需的前置软件和依赖项
            2. **安装方式概述**：简要说明所有可用的安装方法（包管理器、源码安装、容器等）、为不同用户类型推荐最适合的安装方式
            3. **快速安装指南**（150-200字）：提供最简单、最快速的安装方法、使用清晰的步骤和实际可执行的命令、包含验证安装成功的方法
            4. **详细安装步骤**：使用编号列表提供详细的分步说明、每个步骤包含明确的命令和预期输出、解释每个步骤的目的和重要性、包含可能的错误和解决方案
            5. **不同环境的安装说明**：为不同环境（开发、测试、生产）提供专门的安装说明、说明环境特定的配置和优化
            6. **配置说明**：详细说明所有必要的配置步骤、解释关键配置文件和选项、提供配置示例和最佳实践
            7. **验证安装**：提供验证安装是否成功的具体步骤和命令、包含基本的测试或健康检查方法
            8. **故障排除**：列出3-5个常见的安装问题、为每个问题提供详细的解决方案
            9. **升级说明**（如适用）：提供从旧版本升级的步骤、说明版本之间的重要变化

            ### 输出语言要求
            所有生成的安装说明文档内容必须使用中文(简体中文)输出。包括所有安装前提、安装步骤、环境要求、配置说明等内容都必须是中文。只有命令行指令、代码示例、路径和文件名等技术内容保持原样。

            ### 输出质量要求
            1. **内容真实准确**：内容必须基于提供的实际项目数据，不要臆想不存在的安装步骤
            2. **命令准确有效**：所有命令必须是准确的，并且符合项目使用的实际包管理器或构建工具
            3. **语言清晰简洁**：使用清晰、简洁的语言，避免技术术语过载
            4. **适应不同用户**：针对不同技能水平的用户提供适当的说明
            5. **指令完整无遗**：确保指令的完整性，不要遗漏关键步骤
            6. **工具使用正确**：如果项目使用特定的包管理器（如npm、pip、poetry等），请使用相应的命令格式
            7. **普遍适用性**：生成的安装说明章节应该使任何技能水平的用户都能成功安装和配置项目

            {{ format_instructions }}
            """

            # 创建Parser
            installation_parser = AnalysisOutputParser(pydantic_object=InstallationSection)
            parser = OutputFixingParser.from_llm(parser=installation_parser, llm=self.llm)

            # 获取格式指令
            installation_format_instructions = installation_parser.get_format_instructions()

            # 创建提示模板
            installation_prompt = PromptTemplate.from_template(
                installation_template,
                template_format="jinja2"
            ).partial(format_instructions=installation_format_instructions)

            # 创建处理链
            self.chain = (
                installation_prompt
                | self.llm
                | parser
            )

            logger.info("安装说明章节生成器处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: AgentInput) -> None:
        """
        准备输入数据并设置self.input_parameters

        Args:
            input_data: 原始输入数据
        """
        # 初始化self.input_parameters，直接使用输入参数
        self.input_parameters = input_data.parameters.copy()

        logger.info("安装说明章节生成器输入参数准备完成")

    @retry_async(max_retries=3)
    async def process(self) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询

        Returns:
            Tuple[Any, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 调用LLM处理链
            # result = await self.chain.first.ainvoke(self.input_parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(self.input_parameters)

            # 设置章节顺序
            if result:
                result.order = 3  # 安装说明章节通常是第三个

            return result, {"generator": "installation_generator"}
        except Exception as e:
            logger.error(f"安装说明章节生成失败: {str(e)}")
            raise
