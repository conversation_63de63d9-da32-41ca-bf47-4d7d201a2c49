#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块级分析模型定义
包含模块级分析使用的Pydantic模型
"""
from typing import List, Literal
from pydantic import BaseModel, Field
from .common_models import AnalysisFinding

# 代码质量分析相关模型
class ComplexityMetrics(BaseModel):
    """复杂度指标"""
    cyclomatic_complexity: int = Field(default=0, description="圈复杂度")
    cognitive_complexity: int = Field(default=0, description="认知复杂度")
    nesting_depth: int = Field(default=0, description="最大嵌套深度")


class CodeSmell(BaseModel):
    """代码异味"""
    smell_type: str = Field(description="异味类型")
    location: str = Field(description="位置信息（行号或函数名）")
    severity: Literal["low", "medium", "high", "critical"] = Field(description="严重程度")
    description: str = Field(description="详细描述")


class TechnicalDebt(BaseModel):
    """技术债务项"""
    debt_type: str = Field(description="债务类型")
    title: str = Field(description="债务标题")
    description: str = Field(description="详细描述")
    impact: Literal["low", "medium", "high", "critical"] = Field(description="影响程度")
    priority: int = Field(default=3, description="优先级(1-5，5为最高)")


class CodeQualityMetrics(BaseModel):
    """代码质量指标"""
    complexity_metrics: ComplexityMetrics = Field(default_factory=ComplexityMetrics, description="复杂度指标")
    code_smells: List[CodeSmell] = Field(default_factory=list, description="代码异味列表")
    technical_debts: List[TechnicalDebt] = Field(default_factory=list, description="技术债务列表")
    overall_score: float = Field(default=5.0, description="总体质量评分(1.0-10.0)")


# API相关分析模型
class APIEndpoint(BaseModel):
    """API端点信息"""
    path: str = Field(description="API路径")
    method: Literal["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"] = Field(description="HTTP方法")
    function_name: str = Field(description="对应的函数名")
    description: str = Field(default="", description="端点功能描述")
    parameters: List[str] = Field(default_factory=list, description="参数列表")
    response_type: str = Field(default="", description="响应类型")
    authentication_required: bool = Field(default=False, description="是否需要认证")
    deprecated: bool = Field(default=False, description="是否已废弃")


class APIQuality(BaseModel):
    """API质量评估"""
    overall_score: float = Field(default=5.0, description="总体质量评分(1.0-10.0)")
    issues: List[str] = Field(default_factory=list, description="发现的问题")


class APIDesignInfo(BaseModel):
    """API设计信息"""
    is_api_module: bool = Field(default=False, description="是否为API模块")
    api_type: Literal["rest", "graphql", "grpc", "websocket", "other", "none"] = Field(
        default="none", description="API类型"
    )
    endpoints: List[APIEndpoint] = Field(default_factory=list, description="API端点列表")
    quality: APIQuality = Field(default_factory=APIQuality, description="质量评估")


class ModuleFeature(BaseModel):
    """模块功能特性"""
    name: str = Field(default="", description="功能名称")
    description: str = Field(default="", description="功能详细说明")
    use_cases: List[str] = Field(default_factory=list, description="使用场景")
    importance: float = Field(default=3.0, description="重要性评分(1.0-5.0)")


class DependencyNode(BaseModel):
    """依赖图节点"""
    name: str = Field(default="", description="模块名称")
    module_path: str = Field(default="", description="模块文件路径")
    importance: float = Field(default=1.0, description="节点重要性(1.0-5.0)")
    role: str = Field(default="", description="模块角色")
    dependency_scope: Literal["internal", "external"] = Field(default="internal", description="依赖范围：internal(内部依赖)或external(外部依赖)")


class DependencyEdge(BaseModel):
    """依赖图边"""
    source: str = Field(default="", description="源模块")
    target: str = Field(default="", description="目标模块")
    relation_type: str = Field(default="", description="关系类型")
    dependency_scope: Literal["internal", "external"] = Field(default="internal", description="依赖范围：internal(内部依赖)或external(外部依赖)")
    strength: float = Field(default=1.0, description="关系强度(0.0-5.0)")
    description: str = Field(default="", description="关系描述")
    usage_locations: List[str] = Field(default_factory=list, description="使用位置")
    is_circular: bool = Field(default=False, description="是否形成循环依赖")
    is_direct: bool = Field(default=True, description="是否直接依赖")


class DependencyGraph(BaseModel):
    """依赖关系图"""
    nodes: List[DependencyNode] = Field(default_factory=list, description="图节点")
    edges: List[DependencyEdge] = Field(default_factory=list, description="图边")
    circular_paths: List[List[str]] = Field(default_factory=list, description="循环依赖路径")


class ModuleAnalysis(BaseModel):
    """模块功能分析结果"""
    # 基本标识信息
    module_name: str = Field(default="", description="模块名称")
    module_path: str = Field(default="", description="模块路径")
    purpose: str = Field(default="", description="模块主要功能和目的")
    language: str = Field(default="", description="模块使用的编程语言")
    importance: float = Field(default=1.0, description="模块重要性(1.0-5.0)")

    # 架构上下文信息
    role: str = Field(default="", description="模块在项目中的角色")
    layer: str = Field(default="", description="架构层级")
    responsibilities: List[str] = Field(default_factory=list, description="功能职责")

    # 功能特性
    features: List[ModuleFeature] = Field(default_factory=list, description="核心功能特性")

    # 依赖关系
    dependency_graph: DependencyGraph = Field(default_factory=DependencyGraph, description="依赖关系图")

    # 代码质量分析
    code_quality: CodeQualityMetrics = Field(default_factory=CodeQualityMetrics, description="代码质量指标和评估")

    # API相关分析
    api_design: APIDesignInfo = Field(default_factory=APIDesignInfo, description="API设计信息和质量评估")

    # 分析发现
    findings: List[AnalysisFinding] = Field(default_factory=list, description="模块分析发现")

    @property
    def dependency_count(self) -> int:
        """依赖总数"""
        return len(self.dependency_graph.edges)

    @property
    def has_circular_dependencies(self) -> bool:
        """是否存在循环依赖"""
        return len(self.dependency_graph.circular_paths) > 0

    @property
    def direct_dependencies(self) -> List[DependencyEdge]:
        """直接依赖关系"""
        return [edge for edge in self.dependency_graph.edges if edge.is_direct]

    @property
    def interactions(self) -> List[str]:
        """交互模块列表（从依赖关系中提取）"""
        interactions = set()
        for edge in self.dependency_graph.edges:
            if edge.target != self.module_name:
                interactions.add(edge.target)
        return list(interactions)

    @property
    def code_quality_summary(self) -> dict:
        """代码质量摘要"""
        return {
            "overall_score": self.code_quality.overall_score,
            "complexity": self.code_quality.complexity_metrics.cyclomatic_complexity,
            "smells_count": len(self.code_quality.code_smells),
            "technical_debts_count": len(self.code_quality.technical_debts)
        }

    @property
    def api_summary(self) -> dict:
        """API摘要信息"""
        return {
            "is_api_module": self.api_design.is_api_module,
            "api_type": self.api_design.api_type,
            "endpoints_count": len(self.api_design.endpoints),
            "overall_score": self.api_design.quality.overall_score,
            "issues_count": len(self.api_design.quality.issues)
        }

    @property
    def high_priority_issues(self) -> List[str]:
        """高优先级问题列表"""
        issues = []

        # 添加严重的代码异味
        critical_smells = [smell.description for smell in self.code_quality.code_smells
                          if smell.severity in ["high", "critical"]]
        issues.extend(critical_smells)

        # 添加高优先级技术债务
        high_priority_debts = [debt.title for debt in self.code_quality.technical_debts
                              if debt.priority >= 4]
        issues.extend(high_priority_debts)

        # 添加API设计问题
        if self.api_design.is_api_module:
            issues.extend(self.api_design.quality.issues)

        return issues

    @property
    def risk_level(self) -> str:
        """模块风险等级评估"""
        risk_factors = 0

        # 复杂度风险
        if self.code_quality.complexity_metrics.cyclomatic_complexity > 10:
            risk_factors += 1

        # 质量评分风险
        if self.code_quality.overall_score < 5.0:
            risk_factors += 1

        # 技术债务风险
        critical_debts = [debt for debt in self.code_quality.technical_debts
                         if debt.impact in ["high", "critical"]]
        if len(critical_debts) > 0:
            risk_factors += 1



        # API质量风险
        if self.api_design.is_api_module and self.api_design.quality.overall_score < 5.0:
            risk_factors += 1

        if risk_factors >= 4:
            return "critical"
        elif risk_factors >= 3:
            return "high"
        elif risk_factors >= 2:
            return "medium"
        else:
            return "low"

    def to_markdown(self) -> str:
        """
        将ModuleAnalysis的内容转换为markdown格式

        Returns:
            str: markdown格式的内容
        """
        markdown = []

        # 标题和基本信息
        markdown.append(f"# {self.module_name} 模块分析报告\n")

        markdown.append("## 基本信息\n")
        markdown.append(f"- **模块路径**: {self.module_path}")
        markdown.append(f"- **编程语言**: {self.language}")
        markdown.append(f"- **主要功能和目的**: {self.purpose}")
        markdown.append(f"- **重要性**: {self.importance}/5.0")
        markdown.append(f"- **风险等级**: {self.risk_level}\n")

        # 代码质量概览
        quality_summary = self.code_quality_summary
        markdown.append("## 代码质量概览\n")
        markdown.append(f"- **总体质量评分**: {quality_summary['overall_score']:.1f}/10.0")
        markdown.append(f"- **圈复杂度**: {quality_summary['complexity']}")
        markdown.append(f"- **代码异味数量**: {quality_summary['smells_count']}")
        markdown.append(f"- **技术债务数量**: {quality_summary['technical_debts_count']}\n")

        # API概览（如果是API模块）
        if self.api_design.is_api_module:
            api_summary = self.api_summary
            markdown.append("## API概览\n")
            markdown.append(f"- **API类型**: {api_summary['api_type']}")
            markdown.append(f"- **端点数量**: {api_summary['endpoints_count']}")
            markdown.append(f"- **总体质量评分**: {api_summary['overall_score']:.1f}/10.0")
            markdown.append(f"- **问题数量**: {api_summary['issues_count']}\n")

        # 依赖关系
        if self.dependency_graph and (self.dependency_graph.nodes or self.dependency_graph.edges):
            markdown.append("## 依赖关系\n")
            markdown.append(f"**依赖总数**: {self.dependency_count}")
            markdown.append(f"**存在循环依赖**: {'是' if self.has_circular_dependencies else '否'}\n")

            # 直接依赖
            direct_deps = self.direct_dependencies
            if direct_deps:
                markdown.append("### 直接依赖")
                for edge in direct_deps:
                    markdown.append(f"#### {edge.target}")
                    markdown.append(f"- **依赖类型**: {edge.relation_type}")
                    markdown.append(f"- **依赖范围**: {edge.dependency_scope}")
                    markdown.append(f"- **依赖强度**: {edge.strength:.1f}/5.0")
                    markdown.append(f"- **描述**: {edge.description}")

                    if edge.usage_locations:
                        markdown.append("\n**使用位置**:")
                        for location in edge.usage_locations:
                            markdown.append(f"- {location}")

                    if edge.is_circular:
                        markdown.append("\n**警告**: 此依赖形成循环依赖")

                    markdown.append("")

            # 依赖图结构
            markdown.append("### 依赖图结构")

            if self.dependency_graph.nodes:
                markdown.append("\n**节点**:")
                for node in self.dependency_graph.nodes:
                    node_info = f"- {node.name}"
                    if node.module_path:
                        node_info += f" ({node.module_path})"
                    node_info += f" - 重要性: {node.importance:.1f}"
                    if node.role:
                        node_info += f", 角色: {node.role}"
                    markdown.append(node_info)

            if self.dependency_graph.edges:
                markdown.append("\n**边**:")
                for edge in self.dependency_graph.edges:
                    markdown.append(f"- {edge.source} → {edge.target} ({edge.relation_type}, 强度: {edge.strength:.1f})")

            if self.dependency_graph.circular_paths:
                markdown.append("\n**循环依赖路径**:")
                for i, path in enumerate(self.dependency_graph.circular_paths):
                    markdown.append(f"- 路径 {i+1}: {' → '.join(path)}")

            markdown.append("")

        # 模块上下文信息
        if self.role or self.layer or self.responsibilities or self.interactions:
            markdown.append("## 模块上下文\n")
            if self.role:
                markdown.append(f"- **项目角色**: {self.role}")
            if self.layer:
                markdown.append(f"- **架构层级**: {self.layer}")

            if self.responsibilities:
                markdown.append("\n### 功能职责")
                for resp in self.responsibilities:
                    markdown.append(f"- {resp}")

            if self.interactions:
                markdown.append("\n### 交互模块")
                for interact in self.interactions:
                    markdown.append(f"- {interact}")
            markdown.append("")

        # 核心功能分析
        if self.features:
            markdown.append("## 核心功能分析\n")
            for feature in self.features:
                markdown.append(f"### {feature.name}")
                markdown.append(f"{feature.description}")

                if feature.use_cases:
                    markdown.append("\n**使用场景:**")
                    for use_case in feature.use_cases:
                        markdown.append(f"- {use_case}")

                markdown.append(f"\n**重要性**: {feature.importance}/5.0\n")

        # 代码质量详细分析
        if (self.code_quality.code_smells or self.code_quality.technical_debts or
            self.code_quality.complexity_metrics.cyclomatic_complexity > 0):
            markdown.append("## 代码质量详细分析\n")

            # 复杂度指标
            complexity = self.code_quality.complexity_metrics
            if complexity.cyclomatic_complexity > 0:
                markdown.append("### 复杂度指标")
                markdown.append(f"- **圈复杂度**: {complexity.cyclomatic_complexity}")
                markdown.append(f"- **认知复杂度**: {complexity.cognitive_complexity}")
                markdown.append(f"- **最大嵌套深度**: {complexity.nesting_depth}\n")

            # 代码异味
            if self.code_quality.code_smells:
                markdown.append("### 代码异味")
                for smell in self.code_quality.code_smells:
                    markdown.append(f"#### {smell.smell_type} ({smell.severity})")
                    markdown.append(f"- **位置**: {smell.location}")
                    markdown.append(f"- **描述**: {smell.description}\n")

            # 技术债务
            if self.code_quality.technical_debts:
                markdown.append("### 技术债务")
                for debt in self.code_quality.technical_debts:
                    markdown.append(f"#### {debt.title} ({debt.impact})")
                    markdown.append(f"- **类型**: {debt.debt_type}")
                    markdown.append(f"- **描述**: {debt.description}")
                    markdown.append(f"- **优先级**: {debt.priority}/5")
                    markdown.append("")

        # API设计分析（如果是API模块）
        if self.api_design.is_api_module:
            markdown.append("## API设计分析\n")

            # API端点
            if self.api_design.endpoints:
                markdown.append("### API端点")
                for endpoint in self.api_design.endpoints:
                    markdown.append(f"#### {endpoint.method} {endpoint.path}")
                    markdown.append(f"- **函数名**: {endpoint.function_name}")
                    if endpoint.description:
                        markdown.append(f"- **描述**: {endpoint.description}")
                    if endpoint.parameters:
                        markdown.append(f"- **参数**: {', '.join(endpoint.parameters)}")
                    if endpoint.response_type:
                        markdown.append(f"- **响应类型**: {endpoint.response_type}")
                    markdown.append(f"- **需要认证**: {'是' if endpoint.authentication_required else '否'}")
                    if endpoint.deprecated:
                        markdown.append("- **状态**: 已废弃")
                    markdown.append("")

            # 质量评估
            quality = self.api_design.quality
            markdown.append("### 质量评估")
            markdown.append(f"- **总体质量评分**: {quality.overall_score:.1f}/10.0")

            if quality.issues:
                markdown.append("\n**发现的问题**:")
                for issue in quality.issues:
                    markdown.append(f"- {issue}")
            markdown.append("")

        # 高优先级问题
        high_priority = self.high_priority_issues
        if high_priority:
            markdown.append("## 高优先级问题\n")
            for issue in high_priority:
                markdown.append(f"- {issue}")
            markdown.append("")

        # 分析发现
        if self.findings:
            markdown.append("## 分析发现\n")

            for finding in self.findings:
                markdown.append(f"### {finding.title}")
                markdown.append(f"{finding.description}")
                markdown.append(f"\n**严重程度**: {finding.severity}")
                markdown.append("")

        return "\n".join(markdown)
