#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析验证处理器
负责验证分析结果的质量和完整性
"""
import structlog

from ..models import AnalysisConfig, AnalysisState
from .base_handler import BaseAnalysisHandler

logger = structlog.get_logger(__name__)

class AnalysisValidatorHandler(BaseAnalysisHandler):
    """
    分析验证处理器
    负责验证分析结果的质量和完整性
    """

    def initialize(self) -> None:
        """
        初始化分析验证处理器
        该处理器不需要特殊的初始化操作
        """
        pass

    async def process(self, state: AnalysisState) -> AnalysisState:
        """
        处理分析验证
        
        Args:
            state: 当前分析状态
            
        Returns:
            更新后的分析状态
        """
        try:
            logger.info("开始验证分析结果")
            
            # 初始化验证结果
            validation_results = {
                "module_analysis": {"status": "passed", "issues": []},
                "dependency_analysis": {"status": "passed", "issues": []},
                "architecture_analysis": {"status": "passed", "issues": []},
                "structure_analysis": {"status": "passed", "issues": []},
                "readme_generation": {"status": "passed", "issues": []}
            }
            
            # 验证模块分析
            if state.files:
                code_files = [f for f in state.files.keys() if state.files[f].is_code_file]
                module_coverage = len(state.module_analyses) / len(code_files) if code_files else 0
                if module_coverage < 0.8:
                    validation_results["module_analysis"]["status"] = "warning"
                    validation_results["module_analysis"]["issues"].append(
                        f"模块分析覆盖率低: {module_coverage:.2%}"
                    )
            
            # 验证依赖分析
            if state.dependency_analysis:
                # 直接访问属性，并使用条件检查
                if not state.dependency_analysis.tech_stack or not state.dependency_analysis.tech_stack.frameworks:
                    validation_results["dependency_analysis"]["status"] = "warning"
                    validation_results["dependency_analysis"]["issues"].append(
                        "依赖分析缺少技术栈信息"
                    )
            else:
                validation_results["dependency_analysis"]["status"] = "failed"
                validation_results["dependency_analysis"]["issues"].append(
                    "依赖分析结果缺失"
                )
            
            # 验证架构分析
            if state.architecture_analysis:
                # 直接访问属性，并使用条件检查
                if not state.architecture_analysis.architecture_layers:
                    validation_results["architecture_analysis"]["status"] = "warning"
                    validation_results["architecture_analysis"]["issues"].append(
                        "架构分析缺少层次信息"
                    )
                
                if not state.architecture_analysis.architecture_components:
                    validation_results["architecture_analysis"]["status"] = "warning"
                    validation_results["architecture_analysis"]["issues"].append(
                        "架构分析缺少组件信息"
                    )
            else:
                validation_results["architecture_analysis"]["status"] = "failed"
                validation_results["architecture_analysis"]["issues"].append(
                    "架构分析结果缺失"
                )
            
            # 验证结构分析
            if state.structure_analysis:
                # 检查核心组件（替代原来的key_directories）
                if not state.structure_analysis.core:
                    validation_results["structure_analysis"]["status"] = "warning"
                    validation_results["structure_analysis"]["issues"].append(
                        "结构分析缺少核心组件信息"
                    )
            else:
                validation_results["structure_analysis"]["status"] = "failed"
                validation_results["structure_analysis"]["issues"].append(
                    "结构分析结果缺失"
                )
            
            # 验证README生成
            if state.readme:
                # 获取README内容
                readme_content = state.readme.to_markdown() if state.readme else ""
                if not readme_content or len(readme_content) < 500:
                    validation_results["readme_generation"]["status"] = "warning"
                    validation_results["readme_generation"]["issues"].append(
                        "README内容过短或不完整"
                    )
            else:
                validation_results["readme_generation"]["status"] = "failed"
                validation_results["readme_generation"]["issues"].append(
                    "README生成失败"
                )
            
            # 记录验证结果
            state.validation_results = validation_results
            
            # 生成验证报告
            validation_report = ["# 分析结果验证报告\n"]
            
            for section, result in validation_results.items():
                status_icon = "✅" if result["status"] == "passed" else "⚠️" if result["status"] == "warning" else "❌"
                validation_report.append(f"## {section.replace('_', ' ').title()} {status_icon}\n")
                
                if result["issues"]:
                    validation_report.append("发现以下问题：\n")
                    for issue in result["issues"]:
                        validation_report.append(f"- {issue}\n")
                else:
                    validation_report.append("未发现问题。\n")
            
            logger.info("\n".join(validation_report))
            
            # 更新进度
            state.analysis_progress = 1.0  # 验证完成，进度100%
            
            logger.info("分析结果验证完成")
        except Exception as e:
            logger.error(f"分析验证失败: {str(e)}")
            state.errors["validate_analysis"] = str(e)
        return state
