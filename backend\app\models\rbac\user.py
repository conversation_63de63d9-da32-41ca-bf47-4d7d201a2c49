"""
用户相关模型
包含用户(UserModel)及其与角色的关联
"""
from datetime import datetime, timezone
from sqlalchemy import String,DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.model_base import ModelBase
from app.models.rbac.associations import user_role
from app.utils.security import get_password_hash

class UserModel(ModelBase):
    """
    用户模型
    用户可以拥有多个角色，通过角色获取相应的权限
    """
    __tablename__ = 'users'
    
    username: Mapped[str] = mapped_column(String(150), unique=True, index=True, comment='用户名')
    nickname: Mapped[str] = mapped_column(String(150), comment='昵称')
    email: Mapped[str] = mapped_column(String(254),  nullable=True , unique=True, index=True, comment='邮箱')
    phone: Mapped[str] = mapped_column(String(20), nullable=True, index=True, unique=True, comment='手机号')
    hashed_password: Mapped[str] = mapped_column(String(128), comment='密码')
    is_active: Mapped[bool] = mapped_column(default=True, comment='是否启用')
    is_superuser: Mapped[bool] = mapped_column(default=False, comment='是否超级管理员')
    needs_email_binding: Mapped[bool] = mapped_column(default=False, comment='是否需要邮箱绑定')
    last_login: Mapped[datetime | None] = mapped_column(DateTime(timezone=True), default=None, comment='最后登录时间')
    failed_login_attempts: Mapped[int] = mapped_column(default=0, comment='失败登录次数')
    locked_until: Mapped[datetime | None] = mapped_column(DateTime(timezone=True), default=None, comment='锁定时间')

    # 关系定义
    roles: Mapped[list['RoleModel']] = relationship(
        'RoleModel',
        secondary=user_role,
        order_by='RoleModel.sort_order',
    )

    tokens: Mapped[list['TokenModel']] = relationship(
        'TokenModel',
        back_populates='user',
        lazy='select',
        cascade='all, delete-orphan',
        order_by='TokenModel.created_at.desc()'
    )
    
    # OAuth账号关系
    oauth_accounts: Mapped[list['OAuthAccountModel']] = relationship(
        'OAuthAccountModel',
        back_populates='user',
        lazy='select',
        cascade='all, delete-orphan',
        order_by='OAuthAccountModel.created_at.desc()'
    )

    def set_password(self, password: str) -> None:
        """设置用户密码
        
        Args:
            password: 密码字符串
        """
        self.hashed_password = get_password_hash(password)
        # self.hashed_password = password