#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能规划处理器
负责协调智能规划器和数据源管理器之间的交互
"""
import structlog
from typing import Optional

from ..models import AnalysisConfig, AnalysisState
from .base_handler import BaseAnalysisHandler
from ....ai_agent_core import agent_manager
from ...analyzers.intelligent_generator.planners.intelligent_planner import IntelligentPlanner
from ...analyzers.intelligent_generator.planners.intelligent_planner_models import ReadmePlan
from ...analyzers.intelligent_generator.data_sources.data_source_manager import DataSourceManager

logger = structlog.get_logger(__name__)


class IntelligentPlanningHandler(BaseAnalysisHandler):
    """
    智能规划处理器
    负责协调智能规划器和数据源管理器之间的交互
    """

    def __init__(self, config: AnalysisConfig, cache_dir: str = '.cache'):
        """
        初始化智能规划处理器

        Args:
            config: 分析配置
            cache_dir: 缓存目录
        """
        self.intelligent_planner: Optional[IntelligentPlanner] = None
        self.data_source_manager: Optional[DataSourceManager] = None
        super().__init__(config, cache_dir)

    def initialize(self) -> None:
        """
        初始化智能规划器和数据源管理器
        """
        try:
            # 初始化智能规划器
            self.intelligent_planner = agent_manager.load("IntelligentPlannerAgent")
            logger.info("智能规划器初始化完成")
        except Exception as e:
            logger.error(f"智能规划器初始化失败: {str(e)}")
            raise

    async def process(self, state: AnalysisState) -> AnalysisState:
        """
        处理智能规划

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        # 1. 从缓存获取状态
        cached_state = self.get_cached_state()

        # 2. 执行智能规划
        try:
            # 检查是否需要进行智能规划
            if not self.config.enable_intelligent_planning:
                logger.info("配置指定不进行智能规划，跳过智能规划")
                return state

            # 检查是否可以使用缓存
            if cached_state and cached_state.readme_plan and state.is_incremental_analysis:
                # 如果没有重大变化，使用缓存的规划结果
                if not self._has_significant_changes(state, cached_state):
                    logger.info("没有重大变化，使用缓存的智能规划结果")
                    state.readme_plan = cached_state.readme_plan
                    return state

            # 执行智能规划
            state = await self._execute_intelligent_planning(state)

            logger.info("智能规划完成")
        except Exception as e:
            logger.error(f"智能规划失败: {str(e)}")
            state.errors["intelligent_planning"] = str(e)
            raise

        # 3. 设置缓存
        self.set_cached_state(state)

        # 4. 返回分析结果
        return state

    async def _execute_intelligent_planning(self, state: AnalysisState) -> AnalysisState:
        """
        执行智能规划
        使用DataSourceManager聚合数据，然后传递给IntelligentPlanner

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        logger.info("开始执行智能规划")

        try:
            # 检查智能规划器是否已初始化
            if self.intelligent_planner is None:
                raise ValueError("智能规划器未初始化")

            # 1. 初始化数据源管理器
            self.data_source_manager = DataSourceManager(
                analysis_state=state,
                # 可以根据需要添加其他API密钥
            )

            # 2. 聚合所有数据源的数据
            logger.info("开始聚合项目数据")
            aggregated_data = await self.data_source_manager.aggregate_all_data()
            
            # 3. 从聚合数据中提取内部数据
            internal_data = aggregated_data.get("internal", {})
            
            # 4. 准备智能规划器的输入数据
            planning_input_data = {
                "parameters": {
                    "project_name": internal_data.get("project_metadata", {}).get("project_name", state.project_name),
                    "project_type": internal_data.get("structure", {}).get("project_type", "other"),
                    "primary_language": internal_data.get("tech_stack", {}).get("primary_language", ""),
                    "project_metadata": str(internal_data.get("project_metadata", {})),
                    "tech_stack": str(internal_data.get("tech_stack", {})),
                    "structure": str(internal_data.get("structure", {})),
                    "api_design": str(internal_data.get("api_design", {})),
                    "modules": str(internal_data.get("modules", {})),
                    "dependencies": str(internal_data.get("dependencies", {})),
                    "architecture": str(internal_data.get("architecture", {}))
                }
            }

            # 5. 调用智能规划器
            logger.info("调用智能规划器进行章节规划")
            agent_input = await self.intelligent_planner.prepare_input(planning_input_data)
            output = await self.intelligent_planner.process(agent_input)
            
            # 6. 处理规划结果
            readme_plan: ReadmePlan = output.response.result
            state.readme_plan = readme_plan
            
            logger.info(f"智能规划完成，生成了 {len(readme_plan.sections)} 个章节")

        except Exception as e:
            logger.error(f"智能规划执行失败: {str(e)}")
            state.errors["intelligent_planning"] = str(e)
            raise

        return state

    def _has_significant_changes(self, current_state: AnalysisState, cached_state: AnalysisState) -> bool:
        """
        检查是否有重大变化需要重新规划

        Args:
            current_state: 当前分析状态
            cached_state: 缓存的分析状态

        Returns:
            是否有重大变化
        """
        # 检查模块变化
        if (current_state.changed_modules or 
            current_state.new_modules or 
            current_state.deleted_modules):
            return True

        # 检查结构分析变化
        if (current_state.structure_analysis and cached_state.structure_analysis and
            current_state.structure_analysis != cached_state.structure_analysis):
            return True

        # 检查架构分析变化
        if (current_state.architecture_analysis and cached_state.architecture_analysis and
            current_state.architecture_analysis != cached_state.architecture_analysis):
            return True

        # 检查依赖分析变化
        if (current_state.dependency_analysis and cached_state.dependency_analysis and
            current_state.dependency_analysis != cached_state.dependency_analysis):
            return True

        return False


