from pydantic import BaseModel

from app.core.config.base import BaseAppSettings
from app.core.config.phone import PhoneSettings
from app.core.config.security import SecuritySettings
from app.core.config.database import PostgreSQLSettings, RedisSettings, MongoSettings
from app.core.config.oauth import OAuthSettings
from app.core.config.github import GitHubSettings
from app.core.config.email import EmailSettings

class Settings(BaseModel):
    """应用总配置"""
    app: BaseAppSettings = BaseAppSettings()
    security: SecuritySettings = SecuritySettings()
    postgresql: PostgreSQLSettings = PostgreSQLSettings()
    redis: RedisSettings = RedisSettings()
    mongodb: MongoSettings = MongoSettings()
    oauth: OAuthSettings = OAuthSettings()
    github: GitHubSettings = GitHubSettings()
    email: EmailSettings = EmailSettings()
    phone: PhoneSettings = PhoneSettings()

settings = Settings()
