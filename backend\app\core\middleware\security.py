"""
安全响应头中间件
"""
from typing import Optional, Dict
from tornado.web import RequestHandler

from . import MiddlewareHandler, middleware
from app.core.config import settings
import structlog

logger = structlog.get_logger(__name__)


class SecurityHeadersMiddleware(MiddlewareHandler):
    """安全响应头中间件实现"""
    
    def __init__(self, handler: RequestHandler,
                 hsts_enabled: Optional[bool] = None,
                 hsts_max_age: Optional[int] = None) -> None:
        """
        初始化安全响应头中间件
        
        Args:
            handler: 请求处理器实例
            hsts_enabled: 是否启用 HSTS
            hsts_max_age: HSTS 过期时间（秒）
        """
        self.hsts_enabled = hsts_enabled if hsts_enabled is not None else settings.security.HSTS_ENABLED
        self.hsts_max_age = hsts_max_age or settings.security.HSTS_MAX_AGE
        logger.debug(
            "安全响应头中间件初始化",
            hsts_enabled=self.hsts_enabled,
            hsts_max_age=self.hsts_max_age
        )
        super().__init__(handler)
    
    def set_headers(self, handler: RequestHandler) -> None:
        """设置安全响应头"""
        # 防止点击劫持
        handler.set_header("X-Frame-Options", "DENY")
        logger.debug(
            "设置点击劫持防护",
            header="X-Frame-Options",
            value="DENY"
        )
        
        # XSS 保护
        handler.set_header("X-XSS-Protection", "1; mode=block")
        logger.debug(
            "设置XSS防护",
            header="X-XSS-Protection",
            value="1; mode=block"
        )
        
        # 内容类型选项
        handler.set_header("X-Content-Type-Options", "nosniff")
        logger.debug(
            "设置内容类型选项",
            header="X-Content-Type-Options",
            value="nosniff"
        )
        
        # 引用策略
        handler.set_header("Referrer-Policy", "strict-origin-when-cross-origin")
        logger.debug(
            "设置引用策略",
            header="Referrer-Policy",
            value="strict-origin-when-cross-origin"
        )
        
        # 内容安全策略
        csp_value = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
        handler.set_header("Content-Security-Policy", csp_value)
        logger.debug(
            "设置内容安全策略",
            header="Content-Security-Policy",
            value=csp_value
        )
        
        # HTTP 严格传输安全
        if self.hsts_enabled:
            if handler.request.protocol == "https":
                hsts_value = f"max-age={self.hsts_max_age}; includeSubDomains; preload"
                handler.set_header("Strict-Transport-Security", hsts_value)
                logger.debug(
                    "设置HSTS策略",
                    header="Strict-Transport-Security",
                    value=hsts_value,
                    max_age=self.hsts_max_age
                )
            else:
                logger.warning(
                    "HSTS已启用但当前连接不是HTTPS",
                    protocol=handler.request.protocol,
                    uri=handler.request.uri
                )
        else:
            logger.debug("HSTS已禁用")

def security_headers_middleware(hsts_enabled: Optional[bool] = None,
                              hsts_max_age: Optional[int] = None):
    """
    安全响应头中间件装饰器
    
    Args:
        hsts_enabled: 是否启用 HSTS
        hsts_max_age: HSTS 过期时间（秒）
    
    Returns:
        装饰器函数
    """
    return middleware(lambda handler: SecurityHeadersMiddleware(
        handler,
        hsts_enabled=hsts_enabled,
        hsts_max_age=hsts_max_age,
    ))
