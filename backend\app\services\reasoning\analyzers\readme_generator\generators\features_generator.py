#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能特性章节生成器
"""
import logging
import os
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone

from langchain.chains import <PERSON><PERSON>hain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser

from . import Generator
from ..section_models import FeaturesSection, Feature
from .....ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser
from ......utils.retry import retry_async

logger = logging.getLogger(__name__)


class FeaturesGenerator(Generator):
    """
    功能特性章节生成器
    负责生成README文档的功能特性章节
    """

    def __init__(self, llm: ChatOpenAI):
        """初始化功能特性章节生成器"""
        super().__init__(llm)

    async def create_chain(self) -> None:
        """初始化处理链"""
        try:
            # 功能特性章节生成提示模板
            features_template = """
            作为专业的技术文档撰写专家，请基于项目分析数据生成全面、详细且结构清晰的功能特性章节。

            ## 项目基本信息
            项目名称: {{ project_name }}
            项目路径: {{ project_path }}

            {%- if structure_analysis %}

            ## 项目结构分析
            {%- if structure_analysis.core %}

            ### 核心功能组件:
            {%- for component in structure_analysis.core[:5] %}
            {%- if component.component_kind == "directory" or "功能" in component.description or "feature" in component.description.lower() or "service" in component.description.lower() %}
            - **{{ component.name }}**: {{ component.description }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}

            {%- if structure_analysis.entries %}

            ### 系统入口点:
            {%- for entry in structure_analysis.entries[:3] %}
            - **{{ entry.name }}** ({{ entry.entry_category }}): {{ entry.description }}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if module_merger_analyses %}

            ## 模块功能列表
            {%- for group_key, merger_result in module_merger_analyses.items() %}
            {%- if merger_result.ai_analysis and merger_result.ai_analysis.functional_modules %}

            ### {{ group_key }} 模块功能:
            {%- for module in merger_result.ai_analysis.functional_modules[:20] %}
            - **{{ module.name }}**: {{ module.purpose }}
            {%- if module.features %}
            {%- for feature in module.features[:16] %}
              · {{ feature.name }}: {{ feature.description }}
            {%- endfor %}
            {%- endif %}
            {%- endfor %}

            {%- endif %}
            {%- endfor %}
            {%- endif %}

            {%- if dependency_analysis and dependency_analysis.tech_stack %}

            ## 功能支撑技术
            {%- if dependency_analysis.tech_stack.frameworks %}
            核心框架: {%- for framework in dependency_analysis.tech_stack.frameworks[:3] %}{{ framework }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.primary_language %}
            主要语言: {{ dependency_analysis.tech_stack.primary_language }}
            {%- endif %}
            {%- endif %}

            ## 文档生成要求与原则

            ### 核心生成原则
            1. **功能导向生成**：重点突出项目的具体功能和用户价值
            2. **实际数据驱动**：所有功能特性必须基于提供的项目分析数据
            3. **用户价值突出**：强调功能对不同用户角色的实际价值
            4. **结构清晰完整**：确保功能分类逻辑清晰，描述完整
            5. **技术准确专业**：使用准确的技术描述，避免过度营销表述
            6. **实用性导向**：生成对用户理解和使用项目有实际帮助的内容

            ### 内容生成任务
            请生成一个全面、详细且结构清晰的功能特性章节，包含以下要素：

            1. **功能分类**：将功能按照逻辑类别分组，每个类别下列出相关功能
            2. **功能详述**：每个功能包含标题、详细描述（100-150字）、核心特点列表、使用场景
            3. **技术实现**：简要说明实现该功能的技术方法（如适用）
            4. **使用示例**：如可能，提供简单的使用示例或代码片段
            5. **功能截图占位符**：为重要功能添加截图占位符说明
            6. **功能对比**：如适用，简要对比本项目功能的优势

            ### 输出语言要求
            所有生成的功能特性文档内容必须使用中文(简体中文)输出。包括所有功能标题、功能描述、核心特点、使用场景等内容都必须是中文。只有必要的代码示例、函数名或API名称等技术名词可保持原样。

            ### 输出质量要求
            1. **内容真实准确**：内容必须基于提供的实际项目数据，不要臆想不存在的功能
            2. **描述专业全面**：描述应专业、准确、全面，突出功能的价值和优势
            3. **语言积极吸引**：使用积极、吸引人的语言，但避免过度营销式表述
            4. **术语准确清晰**：确保技术术语使用准确，并在必要时提供简短解释
            5. **用户角色针对**：针对不同用户角色（开发者、管理员、普通用户）分别说明相关功能
            6. **详尽完整展示**：生成的功能特性章节应该是README中最详尽的部分，让读者能够全面了解项目的所有主要功能和使用方式

            {{ format_instructions }}
            """

            # 创建Parser
            features_parser = AnalysisOutputParser(pydantic_object=FeaturesSection)
            parser = OutputFixingParser.from_llm(parser=features_parser, llm=self.llm)

            # 获取格式指令
            features_format_instructions = features_parser.get_format_instructions()

            # 创建提示模板
            features_prompt = PromptTemplate.from_template(
                features_template,
                template_format="jinja2"
            ).partial(format_instructions=features_format_instructions)

            # 创建处理链
            self.chain = (
                features_prompt
                | self.llm
                | parser
            )

            logger.info("功能特性章节生成器处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: AgentInput) -> None:
        """
        准备输入数据并设置self.input_parameters

        Args:
            input_data: 原始输入数据
        """
        # 初始化self.input_parameters，直接使用输入参数
        self.input_parameters = input_data.parameters.copy()

        logger.info("功能特性章节生成器输入参数准备完成")

    @retry_async(max_retries=3)
    async def process(self) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询

        Returns:
            Tuple[Any, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 调用LLM处理链
            # result = await self.chain.first.ainvoke(self.input_parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(self.input_parameters)

            # 设置章节顺序
            if result:
                result.order = 2  # 功能特性章节通常是第二个

            return result, {"generator": "features_generator"}
        except Exception as e:
            logger.error(f"功能特性章节生成失败: {str(e)}")
            raise
