"""
请求速率限制中间件
"""
import time
from typing import Optional, Dict
from tornado.web import Request<PERSON>and<PERSON>, HTTPError

from . import MiddlewareHandler, middleware
from app.core.config import settings
import structlog

logger = structlog.get_logger(__name__)

class RateLimiter:
    """速率限制器"""
    def __init__(self, max_requests: int, time_window: int) -> None:
        """
        初始化速率限制器
        
        Args:
            max_requests: 时间窗口内允许的最大请求数
            time_window: 时间窗口（秒）
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests: Dict[str, list] = {}
        logger.debug(
            "速率限制器初始化",
            max_requests=max_requests,
            time_window=time_window
        )
    
    def is_allowed(self, client_id: str) -> bool:
        """
        检查请求是否允许
        
        Args:
            client_id: 客户端标识（如 IP 地址）
        
        Returns:
            bool: 是否允许请求
        """
        current_time = time.time()
        
        # 获取客户端的请求历史
        if client_id not in self.requests:
            self.requests[client_id] = []
            logger.debug(
                "新客户端首次请求",
                client_id=client_id
            )
        
        # 清理过期的请求记录
        original_count = len(self.requests[client_id])
        self.requests[client_id] = [
            req_time for req_time in self.requests[client_id]
            if current_time - req_time <= self.time_window
        ]
        cleaned_count = len(self.requests[client_id])
        if original_count != cleaned_count:
            logger.debug(
                "清理过期请求记录",
                client_id=client_id,
                original_count=original_count,
                current_count=cleaned_count
            )
        
        # 检查是否超过限制
        if len(self.requests[client_id]) >= self.max_requests:
            logger.warning(
                "请求超过速率限制",
                client_id=client_id,
                request_count=len(self.requests[client_id]),
                max_requests=self.max_requests,
                time_window=self.time_window
            )
            return False
        
        # 记录新请求
        self.requests[client_id].append(current_time)
        logger.debug(
            "记录新请求",
            client_id=client_id,
            current_count=len(self.requests[client_id]),
            max_requests=self.max_requests
        )
        return True

class RateLimitMiddleware(MiddlewareHandler):
    """速率限制中间件实现"""
    
    def __init__(self, handler: RequestHandler,
                 max_requests: Optional[int] = None,
                 time_window: Optional[int] = None) -> None:
        """
        初始化速率限制中间件
        
        Args:
            handler: 请求处理器实例
            max_requests: 时间窗口内允许的最大请求数
            time_window: 时间窗口（秒）
        """
        self.max_requests = max_requests or settings.security.RATE_LIMIT_REQUESTS
        self.time_window = time_window or settings.security.RATE_LIMIT_PERIOD
        logger.debug(
            "速率限制中间件初始化",
            max_requests=self.max_requests,
            time_window=self.time_window,
            enabled=settings.security.RATE_LIMIT_ENABLED
        )
        self.limiter = RateLimiter(self.max_requests, self.time_window)
        super().__init__(handler)
    
    async def before_request(self, handler: RequestHandler) -> None:
        """检查请求是否超过速率限制"""
        if not settings.security.RATE_LIMIT_ENABLED:
            logger.debug("速率限制已禁用")
            return
        
        # 获取客户端 IP
        client_ip = handler.request.remote_ip
        logger.debug(
            "检查请求速率",
            client_ip=client_ip,
            uri=handler.request.uri,
            method=handler.request.method
        )
        
        # 检查是否允许请求
        if not self.limiter.is_allowed(client_ip):
            logger.warning(
                "请求被限流",
                client_ip=client_ip,
                uri=handler.request.uri,
                method=handler.request.method
            )
            raise HTTPError(
                429,
                "请求过于频繁，请稍后再试"
            )
    
    def set_headers(self, handler: RequestHandler) -> None:
        """设置速率限制相关的响应头"""
        if settings.security.RATE_LIMIT_ENABLED:
            handler.set_header("X-RateLimit-Limit", str(self.max_requests))
            handler.set_header("X-RateLimit-Window", f"{self.time_window}s")
            logger.debug(
                "设置速率限制响应头",
                rate_limit=self.max_requests,
                time_window=self.time_window
            )

def rate_limit_middleware(max_requests: Optional[int] = None,
                         time_window: Optional[int] = None):
    """
    速率限制中间件装饰器
    
    Args:
        max_requests: 时间窗口内允许的最大请求数
        time_window: 时间窗口（秒）
    
    Returns:
        装饰器函数
    """
    return middleware(lambda handler: RateLimitMiddleware(
        handler,
        max_requests=max_requests,
        time_window=time_window,
    ))
