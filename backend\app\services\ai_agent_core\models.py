#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上下文感知推理系统配置
"""
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union, Literal
from uuid import uuid4

from pydantic import BaseModel, Field, field_validator


class ContextItem(BaseModel):
    """上下文项"""
    id: str = Field(default_factory=lambda: str(uuid4()), description="上下文项ID")
    type: str = Field(..., description="上下文类型")
    key: str = Field(..., description="上下文项键名")
    value: Any = Field(..., description="上下文项值")
    weight: float = Field(default=0.0, description="权重")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="创建时间")


class ReasoningContext(BaseModel):
    """推理上下文"""
    id: str = Field(default_factory=lambda: str(uuid4()), description="上下文ID")
    items: List[ContextItem] = Field(default_factory=list, description="上下文项列表")
    max_items: int = Field(default=20, description="最大上下文项数量")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="创建时间")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="更新时间")

    def add_item(self, item: ContextItem) -> None:
        """
        添加上下文项
        
        Args:
            item: 要添加的上下文项
        """
        # 检查是否已存在相同ID的项，如果存在则更新
        for i, existing_item in enumerate(self.items):
            if existing_item.id == item.id:
                self.items[i] = item
                self.updated_at = datetime.now(timezone.utc)
                return
                
        # 添加新项
        self.items.append(item)
        self.updated_at = datetime.now(timezone.utc)
        
        # 如果超出最大数量，删除权重最低的项
        if len(self.items) > self.max_items:
            self.items.sort(key=lambda x: x.weight)
            self.items = self.items[-self.max_items:]
    
    def get_item(self, item_id: str) -> Optional[ContextItem]:
        """
        获取特定ID的上下文项
        
        Args:
            item_id: 上下文项ID
            
        Returns:
            Optional[ContextItem]: 上下文项，不存在则返回None
        """
        for item in self.items:
            if item.id == item_id:
                return item
        return None
    
    def get_items_by_type(self, item_type: str) -> List[ContextItem]:
        """
        获取特定类型的所有上下文项
        
        Args:
            item_type: 上下文项类型
            
        Returns:
            List[ContextItem]: 符合类型的上下文项列表
        """
        return [item for item in self.items if item.type == item_type]
    
    def get_items_by_key(self, key: str) -> List[ContextItem]:
        """
        获取特定键名的所有上下文项
        
        Args:
            key: 上下文项键名
            
        Returns:
            List[ContextItem]: 符合键名的上下文项列表
        """
        return [item for item in self.items if item.key == key]
    
    def remove_item(self, item_id: str) -> bool:
        """
        移除特定ID的上下文项
        
        Args:
            item_id: 上下文项ID
            
        Returns:
            bool: 是否成功移除
        """
        for i, item in enumerate(self.items):
            if item.id == item_id:
                self.items.pop(i)
                self.updated_at = datetime.now(timezone.utc)
                return True
        return False
    
    def update_item_weight(self, item_id: str, weight: float) -> bool:
        """
        更新上下文项的权重
        
        Args:
            item_id: 上下文项ID
            weight: 新权重值
            
        Returns:
            bool: 是否成功更新
        """
        for item in self.items:
            if item.id == item_id:
                item.weight = weight
                self.updated_at = datetime.now(timezone.utc)
                return True
        return False
    
    def sort_by_weight(self, reverse: bool = True) -> None:
        """
        按权重排序上下文项
        
        Args:
            reverse: 是否降序排序，默认为True（权重从高到低）
        """
        self.items.sort(key=lambda x: x.weight, reverse=reverse)
        self.updated_at = datetime.now(timezone.utc)
    
    def clear(self) -> None:
        """清空上下文"""
        self.items = []
        self.updated_at = datetime.now(timezone.utc)
    
    def merge(self, other_context: 'ReasoningContext') -> None:
        """
        合并另一个上下文
        
        Args:
            other_context: 要合并的上下文
        """
        for item in other_context.items:
            self.add_item(item)
    
    def add(self, key: str, value: Any, type: str = "general", weight: float = 0.0, 
            metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        通过键值对直接添加上下文项
        
        Args:
            key: 键名
            value: 值
            type: 上下文项类型，默认为general
            weight: 权重，默认为0.0
            metadata: 元数据，默认为None
            
        Returns:
            str: 新创建的上下文项ID
        """
        item = ContextItem(
            type=type,
            key=key,
            value=value,
            weight=weight,
            metadata=metadata or {}
        )
        self.add_item(item)
        return item.id
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        通过键名获取上下文项的值
        
        Args:
            key: 键名
            default: 如果键名不存在，返回的默认值
            
        Returns:
            Any: 对应键名的值，如果不存在则返回默认值
        """
        for item in self.items:
            if item.key == key:
                return item.value
        return default
    
    def contains(self, key: str) -> bool:
        """
        检查是否包含指定键名的上下文项
        
        Args:
            key: 键名
            
        Returns:
            bool: 是否包含该键名
        """
        return any(item.key == key for item in self.items)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将上下文转换为字典
        
        Returns:
            Dict[str, Any]: 上下文字典
        """
        result = {}
        for item in self.items:
            value = item.value
            
            # 如果值是BaseModel实例，将其转换为字典
            if isinstance(value, BaseModel):
                value = value.model_dump()
                
            result[item.key] = value
            
        return result


class AgentInput(BaseModel):
    """智能体输入"""
    request_id: str = Field(default_factory=lambda: str(uuid4()), description="请求ID")
    context: Optional[ReasoningContext] = Field(None, description="上下文")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="参数")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="时间戳")

class Response(BaseModel):
    has_error: bool = Field(default=False, description="是否发生错误")
    error_message: str = Field(default="", description="错误信息")
    result: Any = Field(default=None, description="响应内容")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")

class AgentOutput(BaseModel):
    """智能体输出"""
    response: Response = Field(default_factory=Response, description="响应内容")
    context: Optional[ReasoningContext] = Field(None, description="上下文")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
