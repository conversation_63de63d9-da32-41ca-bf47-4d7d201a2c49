"""
搜索功能和查询构建模块

提供复杂的搜索条件组合、高亮功能处理、搜索结果处理和格式化。
支持项目和卡片的分离搜索策略。
"""
import logging
from typing import Dict, Any, List
from dataclasses import dataclass

from app.services.elasticsearch.client import ElasticsearchClient
from .utils import SearchValidationUtils

# 索引名称常量（由 Logstash 管理）
PROJECT_INDEX_NAME = "modular_projects"

# 搜索结果评分过滤阈值
MIN_SCORE_THRESHOLD = 35

logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """搜索结果数据类"""
    projects: List[Dict[str, Any]]
    total: int
    page: int
    page_size: int
    total_pages: int
    search_time: int
    aggregations: Dict[str, Any]
    query: str
    filters: Dict[str, Any]

class SearchQueryBuilder:
    """搜索查询构建器"""

    def __init__(self):
        """初始化查询构建器"""
        self.validation_utils = SearchValidationUtils()

    def build_project_query(self,
                          query: str = None,
                          filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        构建项目搜索查询

        Args:
            query: 搜索关键词
            filters: 过滤条件

        Returns:
            Dict[str, Any]: Elasticsearch查询
        """
        must_conditions = []
        should_conditions = []
        filter_conditions = []

        # 处理关键词搜索
        if query:
            query = query.strip()
            if query:
                # 检测是否包含中文字符
                has_chinese = any('\u4e00' <= char <= '\u9fff' for char in query)

                # 对于短查询词，使用更精确的匹配策略
                is_short_query = len(query) <= 3

                project_query = {
                    "bool": {
                        "should": [
                            # 精确匹配，权重最高
                            {"match_phrase": {"name": {"query": query, "boost": 10.0}}},
                            {"match_phrase": {"description_project": {"query": query, "boost": 8.0}}},
                            {"match_phrase": {"description_recommend": {"query": query, "boost": 8.0}}},

                            # 智能匹配（根据语言和查询长度调整，集成模糊匹配和N-gram匹配）
                            {"match": {
                                "name.ik" if has_chinese else "name.english": {
                                    "query": query,
                                    "boost": 6.0,
                                    "minimum_should_match": "90%" if is_short_query else "80%",
                                    "operator": "and" if is_short_query else "or",
                                    **({
                                        "fuzziness": "1" if len(query) <= 6 else "AUTO"
                                    } if not has_chinese and len(query) >= 2 else {})
                                }
                            }},
                            {"match": {
                                "description_project.ik" if has_chinese else "description_project.english": {
                                    "query": query,
                                    "boost": 4.0,
                                    "minimum_should_match": "90%" if is_short_query else "80%",
                                    "operator": "and" if is_short_query else "or",
                                    **({
                                        "fuzziness": "1" if len(query) <= 6 else "AUTO"
                                    } if not has_chinese and len(query) >= 2 else {})
                                }
                            }},
                            {"match": {
                                "description_recommend.ik" if has_chinese else "description_recommend.english": {
                                    "query": query,
                                    "boost": 4.0,
                                    "minimum_should_match": "90%" if is_short_query else "80%",
                                    "operator": "and" if is_short_query else "or",
                                    **({
                                        "fuzziness": "1" if len(query) <= 6 else "AUTO"
                                    } if not has_chinese and len(query) >= 2 else {})
                                }
                            }},

                            # 标签精确匹配（支持中英文）
                            {"terms": {"tags": query.split(), "boost": 5.0}},
                            {"match": {"tags.ik" if has_chinese else "tags.english": {"query": query, "boost": 4.0}}},

                            # 自动补全匹配
                            {"match_phrase_prefix": {"name.ik" if has_chinese else "name.english": {"query": query, "boost": 3.0}}},

                            # N-gram匹配（仅对较长查询词启用，避免短词过度匹配）
                            *([
                                {"match": {"name.ngram": {"query": query, "boost": 0.5 if not has_chinese else 1.0}}},
                                {"match": {"description_project.ngram": {"query": query, "boost": (0.5 if not has_chinese else 1.0) * 0.6}}},
                                {"match": {"description_recommend.ngram": {"query": query, "boost": (0.5 if not has_chinese else 1.0) * 0.6}}}
                            ] if (not has_chinese and len(query) >= 4) or (has_chinese and len(query) >= 3) else [])
                        ],
                        "minimum_should_match": 1
                    }
                }



                should_conditions.append(project_query)
                
                # 添加嵌套卡片的查询
                card_query = {
                    "nested": {
                        "path": "cards",
                        "query": {
                            "bool": {
                                "should": [
                                    # 精确匹配，权重最高
                                    {"match_phrase": {"cards.title": {"query": query, "boost": 8.0}}},
                                    {"match_phrase": {"cards.content": {"query": query, "boost": 5.0}}},

                                    # 智能匹配（根据语言和查询长度调整，集成模糊匹配和N-gram匹配）
                                    {"match": {
                                        "cards.title.ik" if has_chinese else "cards.title.english": {
                                            "query": query,
                                            "boost": 5.0,
                                            "minimum_should_match": "90%" if is_short_query else "80%",
                                            "operator": "and" if is_short_query else "or",
                                            **({
                                                "fuzziness": "1" if len(query) <= 6 else "AUTO"
                                            } if not has_chinese and len(query) >= 2 else {})
                                        }
                                    }},
                                    {"match": {
                                        "cards.content.ik" if has_chinese else "cards.content.english": {
                                            "query": query,
                                            "boost": 3.0,
                                            "minimum_should_match": "90%" if is_short_query else "80%",
                                            "operator": "and" if is_short_query else "or",
                                            **({
                                                "fuzziness": "1" if len(query) <= 6 else "AUTO"
                                            } if not has_chinese and len(query) >= 2 else {})
                                        }
                                    }},

                                    # 前缀匹配
                                    {"match_phrase_prefix": {"cards.title.ik" if has_chinese else "cards.title.english": {"query": query, "boost": 4.0}}},

                                    # N-gram匹配（仅对较长查询词启用，避免短词过度匹配）
                                    *([
                                        {"match": {"cards.title.ngram": {"query": query, "boost": 1.0 if has_chinese else 0.5}}},
                                        {"match": {"cards.content.ngram": {"query": query, "boost": (1.0 if has_chinese else 0.5) * 0.4}}}
                                    ] if (not has_chinese and len(query) >= 4) or (has_chinese and len(query) >= 3) else [])
                                ],
                                "minimum_should_match": 1
                            }
                        },
                        "inner_hits": {
                            "highlight": self.build_highlight_config("nested_card", query)
                        }
                    }
                }



                should_conditions.append(card_query)

        # 处理过滤条件
        if filters:
            # 项目的过滤条件
            project_filters = {k: v for k, v in filters.items() if not k.startswith('card_')}
            if project_filters:
                filter_conditions.extend(self._build_filter_conditions(project_filters))
            
            # 卡片的过滤条件
            card_filters = {}
            for key, value in filters.items():
                if key.startswith('card_'):
                    # 对于嵌套卡片的字段，需要加上"cards."前缀
                    card_filters[f"cards.{key[5:]}"] = value  # 移除 'card_' 前缀并添加 'cards.' 前缀

            if card_filters:
                # 对卡片的过滤条件添加嵌套查询
                nested_filters = []
                for field, value in card_filters.items():
                    if field == "cards.tags" and isinstance(value, list):
                        nested_filters.append({"terms": {field: value}})
                    elif isinstance(value, list):
                        nested_filters.append({"terms": {field: value}})
                    elif isinstance(value, dict):
                        nested_filters.append({"range": {field: value}})
                    else:
                        nested_filters.append({"term": {field: value}})
                
                if nested_filters:
                    filter_conditions.append({
                        "nested": {
                            "path": "cards",
                            "query": {
                                "bool": {
                                    "filter": nested_filters
                                }
                            }
                        }
                    })

        # 组合查询条件
        return self._combine_query_conditions(must_conditions, should_conditions, filter_conditions)

    def _build_filter_conditions(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """构建过滤条件"""
        filter_conditions = []

        for field, value in filters.items():
            if field == "tags" and isinstance(value, list):
                filter_conditions.append({"terms": {field: value}})
            elif field == "status" and value:
                filter_conditions.append({"term": {field: value}})
            elif field == "date_range" and isinstance(value, dict):
                filter_conditions.append({"range": {"created_at": value}})
            elif field == "cards_count_range" and isinstance(value, dict):
                filter_conditions.append({"range": {"cards_count": value}})
            elif field == "likes_count_range" and isinstance(value, dict):
                filter_conditions.append({"range": {"likes_count": value}})
            elif field == "views_count_range" and isinstance(value, dict):
                filter_conditions.append({"range": {"views_count": value}})
            elif isinstance(value, list):
                filter_conditions.append({"terms": {field: value}})
            elif isinstance(value, dict):
                filter_conditions.append({"range": {field: value}})
            else:
                filter_conditions.append({"term": {field: value}})

        return filter_conditions

    def _combine_query_conditions(self,
                                must_conditions: List[Dict[str, Any]],
                                should_conditions: List[Dict[str, Any]],
                                filter_conditions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """组合查询条件"""
        if not must_conditions and not should_conditions and not filter_conditions:
            return {"match_all": {}}

        query_dict = {"bool": {}}

        if must_conditions:
            query_dict["bool"]["must"] = must_conditions

        if should_conditions:
            query_dict["bool"]["should"] = should_conditions
            if not must_conditions:
                query_dict["bool"]["minimum_should_match"] = 1

        if filter_conditions:
            query_dict["bool"]["filter"] = filter_conditions

        return query_dict

    def build_sort_conditions(self, sort_by: str = None, sort_order: str = "desc") -> List[Dict[str, Any]]:
        """
        构建排序条件

        Args:
            sort_by: 排序字段
            sort_order: 排序方式

        Returns:
            List[Dict[str, Any]]: 排序条件
        """
        sort_conditions = []

        # 当未指定排序字段时，默认使用分数排序
        if not sort_by:
            sort_conditions.append({"_score": {"order": sort_order}})
        else:
            if sort_by == "cards_count":
                sort_conditions.append({"cards_count": {"order": sort_order}})
            elif sort_by == "likes":
                sort_conditions.append({"likes_count": {"order": sort_order}})
            elif sort_by == "views":
                sort_conditions.append({"views_count": {"order": sort_order}})
            elif sort_by in ["name", "title"]:
                sort_conditions.append({f"{sort_by}.keyword": {"order": sort_order}})
            elif sort_by == "created_at":
                sort_conditions.append({"created_at": {"order": sort_order}})
            elif sort_by == "updated_at":
                sort_conditions.append({"updated_at": {"order": sort_order}})
            else:
                sort_conditions.append({sort_by: {"order": sort_order}})

        # 添加默认的二级排序
        if sort_by != "updated_at":
            sort_conditions.append({"updated_at": {"order": "desc"}})

        return sort_conditions

    def build_highlight_config(self, index_type: str = "project", query: str = None) -> Dict[str, Any]:
        """
        构建高亮配置，根据查询语言动态选择合适的字段避免重复高亮

        Args:
            index_type: 索引类型 ("project" 或 "nested_card")
            query: 搜索查询词，用于判断语言类型

        Returns:
            Dict[str, Any]: 高亮配置
        """
        # 检测查询语言类型
        has_chinese = False
        if query:
            has_chinese = any('\u4e00' <= char <= '\u9fff' for char in query)
        if index_type == "project":
            # 根据查询语言选择合适的字段，避免重复高亮
            name_field = "name.ik" if has_chinese else "name.english"
            desc_project_field = "description_project.ik" if has_chinese else "description_project.english"
            desc_recommend_field = "description_recommend.ik" if has_chinese else "description_recommend.english"
            tags_field = "tags.ik" if has_chinese else "tags"

            return {
                "fields": {
                    # 项目名称 - 根据语言选择单一字段
                    name_field: {
                        "number_of_fragments": 2,
                        "fragment_size": 100,
                        "boundary_scanner": "sentence",
                        "boundary_max_scan": 100,
                        "fragment_offset": 0,
                        "require_field_match": True,
                        "no_match_size": 0,
                        "order": "score",
                    },
                    # 项目描述 - 根据语言选择单一字段
                    desc_project_field: {
                        "number_of_fragments": 4,
                        "fragment_size": 100,
                        "boundary_scanner": "sentence",
                        "boundary_max_scan": 100,
                        "fragment_offset": 0,
                        "require_field_match": True,
                        "no_match_size": 0,
                        "order": "score",
                    },
                    # 推荐描述 - 根据语言选择单一字段
                    desc_recommend_field: {
                        "number_of_fragments": 4,
                        "fragment_size": 100,
                        "boundary_scanner": "sentence",
                        "boundary_max_scan": 100,
                        "fragment_offset": 0,
                        "require_field_match": True,
                        "no_match_size": 0,
                        "order": "score",
                    },
                    # 标签 - 根据语言选择单一字段
                    tags_field: {
                        "number_of_fragments": 5,
                        "fragment_size": 100,
                        "boundary_scanner": "sentence",
                        "boundary_max_scan": 60,
                        "require_field_match": True,
                        "no_match_size": 0,
                        "order": "score",
                    },
                    # 嵌套卡片字段高亮 - 根据语言选择单一字段
                    f"cards.title.{'ik' if has_chinese else 'english'}": {
                        "number_of_fragments": 2,
                        "fragment_size": 100,
                        "boundary_scanner": "sentence",
                        "boundary_max_scan": 100,
                        "fragment_offset": 0,
                        "require_field_match": True,
                        "no_match_size": 0,
                        "order": "score",
                    },
                    f"cards.content.{'ik' if has_chinese else 'english'}": {
                        "number_of_fragments": 4,
                        "fragment_size": 100,
                        "boundary_scanner": "sentence",
                        "boundary_max_scan": 100,
                        "fragment_offset": 0,
                        "require_field_match": True,
                        "no_match_size": 0,
                        "order": "score",
                    }
                },
                "pre_tags": ["<mark>"],
                "post_tags": ["</mark>"],
                "require_field_match": True,
                "order": "score",
                "number_of_fragments": 3,
                "fragment_size": 120,
                "boundary_scanner": "sentence",
                "boundary_max_scan": 80,
                "no_match_size": 0,
                "phrase_limit": 256,
                "max_analyzed_offset": 1000000
            }
        else:  # nested_card
            # 根据查询语言选择合适的字段，避免重复高亮
            cards_title_field = f"cards.title.{'ik' if has_chinese else 'english'}"
            cards_content_field = f"cards.content.{'ik' if has_chinese else 'english'}"

            return {
                "fields": {
                    # 卡片标题 - 根据语言选择单一字段
                    cards_title_field: {
                        "number_of_fragments": 2,
                        "fragment_size": 100,
                        "boundary_scanner": "sentence",
                        "boundary_max_scan": 100,
                        "fragment_offset": 0,
                        "require_field_match": True,
                        "no_match_size": 0,
                        "order": "score",
                    },
                    # 卡片内容 - 根据语言选择单一字段
                    cards_content_field: {
                        "number_of_fragments": 4,
                        "fragment_size": 100,
                        "boundary_scanner": "sentence",
                        "boundary_max_scan": 100,
                        "fragment_offset": 0,
                        "require_field_match": True,
                        "no_match_size": 0,
                        "order": "score",
                    }
                },
                "pre_tags": ["<mark>"],
                "post_tags": ["</mark>"],
                "require_field_match": True,
                "order": "score",
                "number_of_fragments": 3,
                "fragment_size": 120,
                "boundary_scanner": "sentence",
                "boundary_max_scan": 80,
                "no_match_size": 0,
                "phrase_limit": 256,
                "max_analyzed_offset": 1000000
            }

    def build_aggregations(self, index_type: str = "project") -> Dict[str, Any]:
        """
        构建聚合查询

        Args:
            index_type: 索引类型 ("project" 或 "nested_card")

        Returns:
            Dict[str, Any]: 聚合配置
        """
        if index_type == "project":
            return {
                "popular_tags": {"terms": {"field": "tags", "size": 20}},
                "status_distribution": {"terms": {"field": "status", "size": 10}},
                "cards_count_stats": {"stats": {"field": "cards_count"}},
                "likes_count_stats": {"stats": {"field": "likes_count"}},
                "views_count_stats": {"stats": {"field": "views_count"}},
                # 添加嵌套卡片的聚合
                "nested_cards_stats": {
                    "nested": {"path": "cards"},
                    "aggs": {
                        "likes": {"sum": {"field": "cards.like"}},
                        "collects": {"sum": {"field": "cards.collect"}},
                        "dislikes": {"sum": {"field": "cards.dislike"}}
                    }
                }
            }
        else:  # nested_card
            return {
                "cards_interaction_stats": {
                    "nested": {"path": "cards"},
                    "aggs": {
                        "likes": {"sum": {"field": "cards.like"}},
                        "collects": {"sum": {"field": "cards.collect"}},
                        "dislikes": {"sum": {"field": "cards.dislike"}},
                        "types_distribution": {"terms": {"field": "cards.card_type", "size": 10}}
                    }
                }
            }

class SearchResultProcessor:
    """搜索结果处理器"""

    def __init__(self):
        """初始化结果处理器"""
        pass

    def process_project_results(self, es_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理项目搜索结果

        Args:
            es_result: Elasticsearch搜索结果

        Returns:
            Dict[str, Any]: 处理后的项目搜索结果
        """
        hits = es_result.get("hits", {})
        total_hits = hits.get("total", {}).get("value", 0)

        projects = []

        for hit in hits.get("hits", []):
            project_data = hit["_source"]
            project_data["_score"] = hit.get("_score", 0)

            # 处理高亮
            if "highlight" in hit:
                project_data["highlights"] = self._process_project_highlights(hit["highlight"])

            # 处理嵌套卡片
            nested_cards = self._process_nested_card_hits(hit)
            if nested_cards:
                project_data["matching_cards"] = nested_cards

            projects.append(project_data)

        return {
            "projects": projects,
            "total": total_hits,
            "search_time": es_result.get("took", 0),
            "aggregations": es_result.get("aggregations", {})
        }
        
    def _process_nested_card_hits(self, hit: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        处理嵌套卡片的匹配结果

        Args:
            hit: 包含inner_hits的Elasticsearch搜索结果

        Returns:
            List[Dict[str, Any]]: 处理后的匹配卡片列表
        """
        matching_cards = []

        # 检查是否存在嵌套卡片的inner_hits
        inner_hits = hit.get("inner_hits", {})
        if not inner_hits or "cards" not in inner_hits:
            return matching_cards

        # 处理卡片inner_hits
        card_hits = inner_hits.get("cards", {}).get("hits", {}).get("hits", [])

        for card_hit in card_hits:
            card_source = card_hit.get("_source", {})
            card_data = {
                "id": card_source.get("id", ""),
                "title": card_source.get("title", ""),
                "content": card_source.get("content", ""),
                "sort_order": card_source.get("sort_order", 0),
                "like": card_source.get("like", 0),
                "collect": card_source.get("collect", 0),
                "dislike": card_source.get("dislike", 0),
                "card_type": card_source.get("card_type", ""),
                "created_at": card_source.get("created_at", ""),
                "updated_at": card_source.get("updated_at", ""),
                "_score": card_hit.get("_score", 0)
            }

            # 处理卡片高亮
            if "highlight" in card_hit:
                card_data["highlights"] = self._process_card_highlights(card_hit["highlight"])

            matching_cards.append(card_data)

        return matching_cards

    def format_search_result(self,
                          project_results: Dict[str, Any],
                          page: int = 1,
                          page_size: int = 10) -> SearchResult:
        """
        格式化项目搜索结果

        Args:
            project_results: 项目搜索结果(已包含嵌套卡片数据)
            page: 页码
            page_size: 每页数量

        Returns:
            SearchResult: 格式化后的搜索结果
        """
        # 获取项目列表
        projects = project_results.get("projects", [])

        # 统计匹配卡片的项目数量
        for project in projects:
            if "matching_cards" in project:
                project["matched_cards_count"] = len(project["matching_cards"])
            else:
                project["matching_cards"] = []
                project["matched_cards_count"] = 0

        # 计算总数和分页信息
        total_projects = project_results.get("total", 0)
        total_pages = (total_projects + page_size - 1) // page_size if total_projects > 0 else 0

        # 聚合统计信息
        projects_with_matched_cards = len([p for p in projects if p.get("matched_cards_count", 0) > 0])

        # 添加汇总信息到聚合结果
        aggregations = project_results.get("aggregations", {})
        aggregations["summary"] = {
            "total_projects": total_projects,
            "projects_with_matched_cards": projects_with_matched_cards
        }

        return SearchResult(
            projects=projects,
            total=total_projects,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            search_time=project_results.get("search_time", 0),
            aggregations=aggregations,
            query="",  # 将在调用时设置
            filters={}  # 将在调用时设置
        )

    def _process_project_highlights(self, highlight_data: Dict[str, List[str]]) -> Dict[str, Any]:
        """处理项目高亮信息"""
        processed_highlights = []

        # 字段类型映射
        field_type_mapping = {
            "name": "项目名称",
            "name.ik": "项目名称",
            "name.english": "项目名称",
            "name.ngram": "项目名称",
            "description_project": "项目描述",
            "description_project.ik": "项目描述",
            "description_project.english": "项目描述",
            "description_project.ngram": "项目描述",
            "description_recommend": "推荐描述",
            "description_recommend.ik": "推荐描述",
            "description_recommend.english": "推荐描述",
            "description_recommend.ngram": "推荐描述",
            "tags": "标签"
        }

        for field, fragments in highlight_data.items():
            # 过滤掉 N-gram 字段的高亮，避免过度匹配显示
            if field.endswith(".ngram"):
                continue

            field_type = field_type_mapping.get(field, field)

            highlight_item = {
                "field_type": field_type,
                "field_name": field,
                "source_type": "project",
                "fragments": fragments,
                "fragment_count": len(fragments),
                "match_metadata": {
                    "is_exact_match": not (field.endswith(".ik") or field.endswith(".english")),
                    "is_partial_match": field.endswith(".ik") or field.endswith(".english")
                }
            }

            processed_highlights.append(highlight_item)

        return {
            "highlights": processed_highlights,
            "total_matches": len(processed_highlights),
            "total_fragments": sum(h["fragment_count"] for h in processed_highlights)
        }

    def _process_card_highlights(self, highlight_data: Dict[str, List[str]]) -> Dict[str, Any]:
        """处理卡片高亮信息"""
        processed_highlights = []

        # 字段类型映射
        field_type_mapping = {
            "cards.title": "卡片标题",
            "cards.title.ik": "卡片标题",
            "cards.title.english": "卡片标题",
            "cards.title.ngram": "卡片标题",
            "cards.content": "卡片内容",
            "cards.content.ik": "卡片内容",
            "cards.content.english": "卡片内容",
            "cards.content.ngram": "卡片内容"
        }

        for field, fragments in highlight_data.items():
            # 过滤掉 N-gram 字段的高亮，避免过度匹配显示
            if field.endswith(".ngram"):
                continue

            field_type = field_type_mapping.get(field, field)

            highlight_item = {
                "field_type": field_type,
                "field_name": field,
                "source_type": "card",
                "fragments": fragments,
                "fragment_count": len(fragments),
                "match_metadata": {
                    "is_exact_match": not (field.endswith(".ik") or field.endswith(".english")),
                    "is_partial_match": field.endswith(".ik") or field.endswith(".english")
                }
            }

            processed_highlights.append(highlight_item)

        return {
            "highlights": processed_highlights,
            "total_matches": len(processed_highlights),
            "total_fragments": sum(h["fragment_count"] for h in processed_highlights)
        }

class SearchEngine:
    """搜索引擎主类"""

    def __init__(self, es_client: ElasticsearchClient):
        """
        初始化搜索引擎

        Args:
            es_client: Elasticsearch客户端
        """
        self.es_client = es_client
        self.query_builder = SearchQueryBuilder()
        self.result_processor = SearchResultProcessor()
        self.validation_utils = SearchValidationUtils()

    async def search_projects(self,
                            query: str = None,
                            filters: Dict[str, Any] = None,
                            sort_by: str = None,
                            sort_order: str = "desc",
                            page: int = 1,
                            page_size: int = 10,
                            highlight: bool = True) -> SearchResult:
        """
        搜索项目（使用嵌套查询搜索项目和嵌套的卡片）

        Args:
            query: 搜索关键词
            filters: 过滤条件
            sort_by: 排序字段
            sort_order: 排序方式
            page: 页码
            page_size: 每页数量
            highlight: 是否高亮匹配文本

        Returns:
            SearchResult: 搜索结果
        """
        try:
            # 验证搜索参数
            query, page, page_size = self.validation_utils.validate_search_params(query, page, page_size)

            # 执行项目搜索（包含嵌套卡片搜索）
            project_results = await self._search_projects_index(
                query, filters, sort_by, sort_order, page, page_size, highlight
            )
            
            # 创建搜索结果
            search_result = self.result_processor.format_search_result(
                project_results,
                page=page,
                page_size=page_size
            )
            
            # 设置查询信息
            search_result.query = query or ""
            search_result.filters = filters or {}

            # 返回搜索结果
            return search_result
        except Exception as e:
            logger.error(f"搜索项目时发生错误: {str(e)}")
            raise

    async def _search_projects_index(self,
                                   query: str = None,
                                   filters: Dict[str, Any] = None,
                                   sort_by: str = None,
                                   sort_order: str = "desc",
                                   page: int = 1,
                                   page_size: int = 10,
                                   highlight: bool = True) -> Dict[str, Any]:
        """搜索项目索引"""
        try:
            from_ = (page - 1) * page_size

            # 构建查询
            es_query = self.query_builder.build_project_query(query, filters)

            # 构建排序
            sort_conditions = self.query_builder.build_sort_conditions(sort_by, sort_order)

            # 构建搜索体
            es_body = {
                "query": es_query,
                "from": from_,
                "size": page_size,
                "track_total_hits": True,
                "track_scores": True,  # 确保追踪分数
                "min_score": MIN_SCORE_THRESHOLD  # 在查询阶段过滤低评分结果
            }

            if sort_conditions:
                es_body["sort"] = sort_conditions

            # 添加高亮配置
            if highlight and query:
                es_body["highlight"] = self.query_builder.build_highlight_config("project", query)

            # 添加聚合
            es_body["aggs"] = self.query_builder.build_aggregations("project")

            # 执行搜索
            result = self.es_client.client.search(
                index=PROJECT_INDEX_NAME,
                body=es_body
            )

            # 处理结果
            return self.result_processor.process_project_results(result)

        except Exception as e:
            logger.error(f"搜索项目索引异常: {str(e)}")
            return {"projects": [], "total": 0, "search_time": 0, "aggregations": {}}
