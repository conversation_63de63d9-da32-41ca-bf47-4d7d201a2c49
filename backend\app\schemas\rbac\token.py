"""
Token 相关的请求和响应模型
"""
from typing import Optional
from pydantic import BaseModel, Field

class Token(BaseModel):
    """刷新令牌请求"""
    id: str = Field(..., description='用户ID')
    token_type: str = Field("bearer", description="令牌类型")
    refresh_token: str = Field(..., description="刷新令牌")
    access_token: Optional[str] = Field(None, description="访问令牌")
    version: str = Field('1.0.0', description='版本号')
    user_id: Optional[str] = Field(None, description='用户ID')

    class Config:
        """配置"""
        from_attributes = True

class RefreshTokenRequest(BaseModel):
    """刷新令牌请求"""
    refresh_token: str = Field(..., description="刷新令牌")
    version: str = Field('1.0.0', description='版本号')
    user_id: Optional[str] = Field(None, description='用户ID')