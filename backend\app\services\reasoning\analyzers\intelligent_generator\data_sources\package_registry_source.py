#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
包注册表数据源

从各种包注册表（PyPI、npm、crates.io等）获取包信息
"""

import logging
from typing import Dict, Any, Optional, List

from .external_data_source import ExternalDataSource

logger = logging.getLogger(__name__)


class PackageRegistrySource(ExternalDataSource):
    """包注册表数据源类"""
    
    def __init__(self, timeout: int = 30):
        """初始化包注册表数据源"""
        super().__init__(timeout)
        
        # 各种包注册表的API端点
        self.registries = {
            "pypi": "https://pypi.org/pypi",
            "npm": "https://registry.npmjs.org",
            "crates": "https://crates.io/api/v1/crates",
            "packagist": "https://packagist.org/packages",
            "rubygems": "https://rubygems.org/api/v1/gems",
            "nuget": "https://api.nuget.org/v3-flatcontainer"
        }
    
    def get_data_source_name(self) -> str:
        """获取数据源名称"""
        return "PackageRegistry"
    
    async def fetch_data(self, package_name: str, registry_type: str = "auto") -> Dict[str, Any]:
        """
        获取包注册表数据
        
        Args:
            package_name: 包名称
            registry_type: 注册表类型（auto表示自动检测）
            
        Returns:
            Dict[str, Any]: 包数据
        """
        try:
            logger.info(f"开始获取包注册表数据: {package_name}")
            
            if registry_type == "auto":
                # 尝试从多个注册表获取数据
                data = {}
                for reg_type in self.registries.keys():
                    reg_data = await self._fetch_from_registry(package_name, reg_type)
                    if reg_data:
                        data[reg_type] = reg_data
                return data
            else:
                # 从指定注册表获取数据
                data = await self._fetch_from_registry(package_name, registry_type)
                return {registry_type: data} if data else {}
                
        except Exception as e:
            logger.error(f"获取包注册表数据失败: {str(e)}")
            return {}
    
    async def _fetch_from_registry(self, package_name: str, registry_type: str) -> Optional[Dict[str, Any]]:
        """从指定注册表获取数据"""
        try:
            if registry_type == "pypi":
                return await self._fetch_pypi_data(package_name)
            elif registry_type == "npm":
                return await self._fetch_npm_data(package_name)
            elif registry_type == "crates":
                return await self._fetch_crates_data(package_name)
            elif registry_type == "packagist":
                return await self._fetch_packagist_data(package_name)
            elif registry_type == "rubygems":
                return await self._fetch_rubygems_data(package_name)
            elif registry_type == "nuget":
                return await self._fetch_nuget_data(package_name)
            else:
                logger.warning(f"不支持的注册表类型: {registry_type}")
                return None
                
        except Exception as e:
            logger.error(f"从{registry_type}获取数据失败: {str(e)}")
            return None
    
    async def _fetch_pypi_data(self, package_name: str) -> Optional[Dict[str, Any]]:
        """获取PyPI数据"""
        url = f"{self.registries['pypi']}/{package_name}/json"
        
        data = await self._make_request(url)
        if data:
            info = data.get("info", {})
            return {
                "name": info.get("name"),
                "version": info.get("version"),
                "summary": info.get("summary"),
                "description": info.get("description"),
                "author": info.get("author"),
                "author_email": info.get("author_email"),
                "maintainer": info.get("maintainer"),
                "home_page": info.get("home_page"),
                "project_url": info.get("project_url"),
                "download_url": info.get("download_url"),
                "license": info.get("license"),
                "keywords": info.get("keywords"),
                "classifiers": info.get("classifiers", []),
                "requires_dist": info.get("requires_dist", []),
                "requires_python": info.get("requires_python"),
                "platform": info.get("platform"),
                "downloads": data.get("downloads", {}),
                "upload_time": data.get("releases", {}).get(info.get("version", ""), [{}])[0].get("upload_time") if data.get("releases") else None
            }
        return None
    
    async def _fetch_npm_data(self, package_name: str) -> Optional[Dict[str, Any]]:
        """获取npm数据"""
        url = f"{self.registries['npm']}/{package_name}"
        
        data = await self._make_request(url)
        if data:
            latest_version = data.get("dist-tags", {}).get("latest", "")
            latest_info = data.get("versions", {}).get(latest_version, {})
            
            return {
                "name": data.get("name"),
                "version": latest_version,
                "description": latest_info.get("description"),
                "keywords": latest_info.get("keywords", []),
                "author": latest_info.get("author"),
                "license": latest_info.get("license"),
                "homepage": latest_info.get("homepage"),
                "repository": latest_info.get("repository"),
                "bugs": latest_info.get("bugs"),
                "dependencies": latest_info.get("dependencies", {}),
                "dev_dependencies": latest_info.get("devDependencies", {}),
                "scripts": latest_info.get("scripts", {}),
                "engines": latest_info.get("engines", {}),
                "created": data.get("time", {}).get("created"),
                "modified": data.get("time", {}).get("modified"),
                "versions_count": len(data.get("versions", {}))
            }
        return None
    
    async def _fetch_crates_data(self, package_name: str) -> Optional[Dict[str, Any]]:
        """获取crates.io数据"""
        url = f"{self.registries['crates']}/{package_name}"
        
        data = await self._make_request(url)
        if data:
            crate = data.get("crate", {})
            versions = data.get("versions", [])
            latest_version = versions[0] if versions else {}
            
            return {
                "name": crate.get("name"),
                "version": latest_version.get("num"),
                "description": crate.get("description"),
                "documentation": crate.get("documentation"),
                "homepage": crate.get("homepage"),
                "repository": crate.get("repository"),
                "license": latest_version.get("license"),
                "keywords": data.get("keywords", []),
                "categories": data.get("categories", []),
                "downloads": crate.get("downloads"),
                "recent_downloads": crate.get("recent_downloads"),
                "created_at": crate.get("created_at"),
                "updated_at": crate.get("updated_at"),
                "versions_count": len(versions)
            }
        return None
    
    async def _fetch_packagist_data(self, package_name: str) -> Optional[Dict[str, Any]]:
        """获取Packagist数据"""
        url = f"{self.registries['packagist']}/{package_name}.json"
        
        data = await self._make_request(url)
        if data:
            package = data.get("package", {})
            versions = package.get("versions", {})
            
            # 获取最新版本信息
            latest_version_key = None
            for version_key in versions.keys():
                if not any(pre in version_key for pre in ['dev', 'alpha', 'beta', 'rc']):
                    latest_version_key = version_key
                    break
            
            if not latest_version_key and versions:
                latest_version_key = list(versions.keys())[0]
            
            latest_info = versions.get(latest_version_key, {}) if latest_version_key else {}
            
            return {
                "name": package.get("name"),
                "version": latest_version_key,
                "description": latest_info.get("description"),
                "keywords": latest_info.get("keywords", []),
                "license": latest_info.get("license", []),
                "authors": latest_info.get("authors", []),
                "homepage": latest_info.get("homepage"),
                "support": latest_info.get("support", {}),
                "require": latest_info.get("require", {}),
                "require_dev": latest_info.get("require-dev", {}),
                "type": latest_info.get("type"),
                "downloads": package.get("downloads", {}),
                "favers": package.get("favers"),
                "repository": package.get("repository")
            }
        return None
    
    async def _fetch_rubygems_data(self, package_name: str) -> Optional[Dict[str, Any]]:
        """获取RubyGems数据"""
        url = f"{self.registries['rubygems']}/{package_name}.json"
        
        data = await self._make_request(url)
        if data:
            return {
                "name": data.get("name"),
                "version": data.get("version"),
                "description": data.get("info"),
                "summary": data.get("summary"),
                "authors": data.get("authors"),
                "email": data.get("email"),
                "homepage": data.get("homepage_uri"),
                "source_code": data.get("source_code_uri"),
                "documentation": data.get("documentation_uri"),
                "bug_tracker": data.get("bug_tracker_uri"),
                "changelog": data.get("changelog_uri"),
                "licenses": data.get("licenses", []),
                "dependencies": data.get("dependencies", {}),
                "downloads": data.get("downloads"),
                "version_downloads": data.get("version_downloads"),
                "platform": data.get("platform"),
                "created_at": data.get("created_at"),
                "updated_at": data.get("updated_at")
            }
        return None
    
    async def _fetch_nuget_data(self, package_name: str) -> Optional[Dict[str, Any]]:
        """获取NuGet数据"""
        # NuGet API比较复杂，这里简化处理
        search_url = f"https://azuresearch-usnc.nuget.org/query?q={package_name}&take=1"
        
        data = await self._make_request(search_url)
        if data and data.get("data"):
            package = data["data"][0]
            return {
                "name": package.get("id"),
                "version": package.get("version"),
                "description": package.get("description"),
                "summary": package.get("summary"),
                "authors": package.get("authors", []),
                "owners": package.get("owners", []),
                "license_url": package.get("licenseUrl"),
                "project_url": package.get("projectUrl"),
                "icon_url": package.get("iconUrl"),
                "tags": package.get("tags", []),
                "total_downloads": package.get("totalDownloads"),
                "verified": package.get("verified"),
                "versions": package.get("versions", [])
            }
        return None
    
    async def get_package_suggestions(self, query: str, registry_type: str = "pypi", limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取包建议
        
        Args:
            query: 搜索查询
            registry_type: 注册表类型
            limit: 结果限制
            
        Returns:
            List[Dict[str, Any]]: 包建议列表
        """
        try:
            if registry_type == "pypi":
                return await self._search_pypi(query, limit)
            elif registry_type == "npm":
                return await self._search_npm(query, limit)
            # 其他注册表的搜索可以后续添加
            else:
                logger.warning(f"不支持的搜索注册表: {registry_type}")
                return []
                
        except Exception as e:
            logger.error(f"搜索包失败: {str(e)}")
            return []
    
    async def _search_pypi(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """搜索PyPI包"""
        url = f"https://pypi.org/search/?q={query}&o=-created"
        # PyPI搜索需要解析HTML，这里简化处理
        return []
    
    async def _search_npm(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """搜索npm包"""
        url = f"https://registry.npmjs.org/-/v1/search?text={query}&size={limit}"
        
        data = await self._make_request(url)
        if data and data.get("objects"):
            results = []
            for obj in data["objects"]:
                package = obj.get("package", {})
                results.append({
                    "name": package.get("name"),
                    "version": package.get("version"),
                    "description": package.get("description"),
                    "keywords": package.get("keywords", []),
                    "author": package.get("author", {}).get("name"),
                    "date": package.get("date"),
                    "score": obj.get("score", {})
                })
            return results
        return []
