# pyright: reportPrivateUsage=false

"""主oxml模块的临时替代。

此模块随PackageReader移植而来。可能大部分内容将被pptx.oxml.core中的对象替代，
然后此模块将被删除或仅保留包相关的自定义元素类。
"""

from __future__ import annotations

from typing import cast

from lxml import etree

from app.utils.slx_parser.opc.constants import NAMESPACE as NS
from app.utils.slx_parser.opc.constants import RELATIONSHIP_TARGET_MODE as RTM

# 配置XML解析器
element_class_lookup = etree.ElementNamespaceClassLookup()
oxml_parser = etree.XMLParser(remove_blank_text=True, resolve_entities=False)
oxml_parser.set_element_class_lookup(element_class_lookup)

nsmap = {
    "ct": NS.OPC_CONTENT_TYPES,
    "pr": NS.OPC_RELATIONSHIPS,
    "r": NS.OFC_RELATIONSHIPS,
}


# ===========================================================================
# 函数
# ===========================================================================


def parse_xml(text: str) -> etree._Element:
    """使用oxml解析器替代`etree.fromstring()`。"""
    return etree.fromstring(text, oxml_parser)


def qn(tag):
    """代表"限定名称"，一个实用函数，用于将带命名空间前缀的标签名
    转换为lxml使用的Clark表示法限定标签名。

    例如，``qn('p:cSld')`` 返回 ``'{http://schemas.../main}cSld'``。
    """
    prefix, tagroot = tag.split(":")
    uri = nsmap[prefix]
    return "{%s}%s" % (uri, tagroot)


def serialize_part_xml(part_elm: etree._Element):
    """将`part_elm` etree元素序列化为适合存储为XML部件的XML格式。

    也就是说，不添加用于可读性的无关空白，并添加带有UTF-8编码
    指定的适当XML声明。
    """
    return etree.tostring(part_elm, encoding="UTF-8", standalone=True)


def serialize_for_reading(element):
    """将`element`序列化为适合测试的人类可读XML格式。

    不包含XML声明。
    """
    return etree.tostring(element, encoding="unicode", pretty_print=True)


# ===========================================================================
# 自定义元素类
# ===========================================================================


class BaseOxmlElement(etree.ElementBase):
    """所有自定义元素类的基类，用于在一个地方为所有类添加标准化行为。"""

    @property
    def xml(self):
        """返回此元素的XML字符串，适用于测试目的。

        为了可读性进行美化打印，且顶部没有XML声明。
        """
        return serialize_for_reading(self)


class CT_Default(BaseOxmlElement):
    """``<Default>``元素，指定要应用于具有指定扩展名的部件的默认内容类型。"""

    @property
    def content_type(self):
        """此``<Default>``元素的``ContentType``属性中保存的字符串。"""
        return self.get("ContentType")

    @property
    def extension(self):
        """此``<Default>``元素的``Extension``属性中保存的字符串。"""
        return self.get("Extension")

    @staticmethod
    def new(ext, content_type):
        """返回一个新的``<Default>``元素，属性设置为参数值。"""
        xml = '<Default xmlns="%s"/>' % nsmap["ct"]
        default = parse_xml(xml)
        default.set("Extension", ext)
        default.set("ContentType", content_type)
        return default


class CT_Override(BaseOxmlElement):
    """``<Override>``元素，指定要应用于具有指定partname的部件的内容类型。"""

    @property
    def content_type(self):
        """此``<Override>``元素的``ContentType``属性中保存的字符串。"""
        return self.get("ContentType")

    @staticmethod
    def new(partname, content_type):
        """返回一个新的``<Override>``元素，属性设置为参数值。"""
        xml = '<Override xmlns="%s"/>' % nsmap["ct"]
        override = parse_xml(xml)
        override.set("PartName", partname)
        override.set("ContentType", content_type)
        return override

    @property
    def partname(self):
        """此``<Override>``元素的``PartName``属性中保存的字符串。"""
        return self.get("PartName")


class CT_Relationship(BaseOxmlElement):
    """``<Relationship>``元素，表示从源到目标部件的单个关系。"""

    @staticmethod
    def new(rId: str, reltype: str, target: str, target_mode: str = RTM.INTERNAL):
        """返回一个新的``<Relationship>``元素。"""
        xml = '<Relationship xmlns="%s"/>' % nsmap["pr"]
        relationship = parse_xml(xml)
        relationship.set("Id", rId)
        relationship.set("Type", reltype)
        relationship.set("Target", target)
        if target_mode == RTM.EXTERNAL:
            relationship.set("TargetMode", RTM.EXTERNAL)
        return relationship

    @property
    def rId(self):
        """此``<Relationship>``元素的``Id``属性中保存的字符串。"""
        return self.get("Id")

    @property
    def reltype(self):
        """此``<Relationship>``元素的``Type``属性中保存的字符串。"""
        return self.get("Type")

    @property
    def target_ref(self):
        """此``<Relationship>``元素的``Target``属性中保存的字符串。"""
        return self.get("Target")

    @property
    def target_mode(self):
        """此``<Relationship>``元素的``TargetMode``属性中保存的字符串，
        可以是``Internal``或``External``。

        默认为``Internal``。
        """
        return self.get("TargetMode", RTM.INTERNAL)


class CT_Relationships(BaseOxmlElement):
    """``<Relationships>``元素，.rels文件中的根元素。"""

    def add_rel(self, rId: str, reltype: str, target: str, is_external: bool = False):
        """添加一个子``<Relationship>``元素，属性根据参数值设置。"""
        target_mode = RTM.EXTERNAL if is_external else RTM.INTERNAL
        relationship = CT_Relationship.new(rId, reltype, target, target_mode)
        self.append(relationship)

    @staticmethod
    def new() -> CT_Relationships:
        """返回一个新的``<Relationships>``元素。"""
        xml = '<Relationships xmlns="%s"/>' % nsmap["pr"]
        return cast(CT_Relationships, parse_xml(xml))

    @property
    def Relationship_lst(self):
        """返回包含所有``<Relationship>``子元素的列表。"""
        return self.findall(qn("pr:Relationship"))

    @property
    def xml(self):
        """返回此元素的XML字符串，适合保存在.rels流中，
        不进行美化打印，且顶部有XML声明。"""
        return serialize_part_xml(self)


class CT_Types(BaseOxmlElement):
    """``<Types>``元素，[Content_Types].xml中Default和Override元素的容器元素。"""

    def add_default(self, ext, content_type):
        """添加一个子``<Default>``元素，属性设置为参数值。"""
        default = CT_Default.new(ext, content_type)
        self.append(default)

    def add_override(self, partname, content_type):
        """添加一个子``<Override>``元素，属性设置为参数值。"""
        override = CT_Override.new(partname, content_type)
        self.append(override)

    @property
    def defaults(self):
        return self.findall(qn("ct:Default"))

    @staticmethod
    def new():
        """返回一个新的``<Types>``元素。"""
        xml = '<Types xmlns="%s"/>' % nsmap["ct"]
        types = parse_xml(xml)
        return types

    @property
    def overrides(self):
        return self.findall(qn("ct:Override"))


ct_namespace = element_class_lookup.get_namespace(nsmap["ct"])
ct_namespace["Default"] = CT_Default
ct_namespace["Override"] = CT_Override
ct_namespace["Types"] = CT_Types

pr_namespace = element_class_lookup.get_namespace(nsmap["pr"])
pr_namespace["Relationship"] = CT_Relationship
pr_namespace["Relationships"] = CT_Relationships
