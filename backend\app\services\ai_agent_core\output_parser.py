import json
import structlog
from typing import Optional, Annotated
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.outputs import Generation
from langchain_core.utils.pydantic import (
    TBaseModel,
)
from pydantic import BaseModel, ValidationError, SkipValidation

logger = structlog.get_logger(__name__)

_PYDANTIC_FORMAT_INSTRUCTIONS = """
Output a JSON instance that strictly conforms to the JSON schema below. The output must adhere to these rules:
1. Must be 100% valid, parseable JSON
2. Do not include any comments (like // or /**/)
3. Do not add trailing commas after the last element in JSON objects or arrays
4. Ellipses are not allowed in arrays or objects
5. All strings must use double quotes (") not single quotes
6. All key names must be enclosed in double quotes
7. Ensure all brackets, braces, and quotation marks are properly paired
8. Do not add any explanatory text outside the JSON
9. Avoid using data types not supported by JSON (like undefined)
10. Numbers should not be quoted (unless intentionally represented as strings)
11. Use standard JSON boolean values: true and false (not True or False)

IMPORTANT: You must output a valid JSON instance object, NOT a JSON Schema definition.

For example, given the schema:
{{"properties": {{"foo": {{"title": "Foo", "description": "a list of strings", "type": "array", "items": {{"type": "string"}}}}}}, "required": ["foo"]}}

A CORRECT JSON instance output would be:
{{"foo": ["bar", "baz"]}}

An INCORRECT output would be:
{{"properties": {{"foo": {{"title": "Foo", "description": "a list of strings"...}}}}}}

Below is the output schema you need to follow. Please create a valid JSON instance based on this schema:
```
{schema}
```

Your output should be a JSON object that conforms to the above schema, not a repetition or modification of the schema itself.
"""

_CHINESE_PYDANTIC_FORMAT_INSTRUCTIONS = """
请输出一个严格符合以下JSON架构的JSON实例。输出必须遵循以下规则:
1. 必须是100%有效的、可解析的JSON格式
2. 不包含任何注释（如//或/**/)
3. 不在JSON对象或数组的最后一个元素后添加逗号
4. 不允许在数组或对象中添加省略号
5. 所有字符串必须使用双引号（"）而非单引号
6. 所有键名必须使用双引号包围
7. 确保所有的括号、大括号和引号都正确配对
8. 不在JSON外添加任何解释性文本
9. 避免使用JSON不支持的数据类型(如undefined)
10. 数值不要加引号（除非是有意表示为字符串）
11. 使用标准的JSON布尔值:true和false(不要使用True或False)

重要：请务必输出有效的JSON实例对象，而不是JSON Schema定义。

例如：对于下面的架构:
{{"properties": {{"foo": {{"title": "Foo", "description": "a list of strings", "type": "array", "items": {{"type": "string"}}}}}}, "required": ["foo"]}}

正确的JSON实例输出应该是:
{{"foo": ["bar", "baz"]}}

错误的输出是:
{{"properties": {{"foo": {{"title": "Foo", "description": "a list of strings"...}}}}}}

下面是您需要遵循的输出架构，请根据这个架构创建一个有效的JSON实例：
```
{schema}
```

您的输出应该是一个符合上述架构的JSON对象，而不是重复或修改架构本身。
"""

class AnalysisOutputParser(PydanticOutputParser):
    """用于解析分析结果的输出解析器。

    支持将LLM生成的内容解析为各种分析模型，如模块分析、函数分析、依赖分析和项目结构分析等。
    提供了强大的错误处理能力和格式化指令。
    """

    pydantic_object: Annotated[Optional[type[TBaseModel]], SkipValidation()] = None  # type: ignore
    use_chinese_instructions: bool = True

    def parse(self, text: str) -> TBaseModel:
        """解析文本内容为Pydantic模型。

        Args:
            text: 要解析的文本内容

        Returns:
            解析后的Pydantic模型实例

        Raises:
            OutputParserException: 解析失败时抛出异常
        """
        try:
            # 处理可能的Markdown代码块标记
            text = text.strip()
            if text.startswith("```json"):
                text = text[7:]
            if text.endswith("```"):
                text = text[:-3]
            text = text.strip()

            # 处理文件路径中的反斜杠
            # 将所有的\\替换为/，避免转义问题
            text = self._fix_backslashes_in_json(text)

            return super().parse(text)
        except Exception as e:
            logger.error(f"解析失败: {e}")
            raise

    def _fix_backslashes_in_json(self, text: str) -> str:
        """修复 JSON 中的反斜杠问题

        Args:
            text: JSON 字符串

        Returns:
            修复后的 JSON 字符串
        """
        try:
            # 尝试直接解析
            json.loads(text)  # 如果可以解析，说明没有问题
            return text
        except json.JSONDecodeError as e:
            error_msg = str(e)
            # 如果错误与反斜杠有关，尝试修复
            if "Invalid \\escape" in error_msg or "Expecting ',' delimiter" in error_msg:
                # 尝试两种修复方法
                # 1. 将所有字符串中的反斜杠替换为正斜杠
                fixed_text = self._replace_backslashes_in_strings(text)
                try:
                    # 尝试解析修复后的文本
                    json.loads(fixed_text)
                    return fixed_text
                except json.JSONDecodeError:
                    # 如果还是失败，尝试更激进的方法
                    # 2. 将所有反斜杠加倍，确保正确转义
                    fixed_text = self._escape_backslashes(text)
                    try:
                        json.loads(fixed_text)
                        return fixed_text
                    except json.JSONDecodeError:
                        # 如果两种方法都失败，返回原始文本
                        logger.warning("两种反斜杠修复方法都失败，返回原始文本")
                        return text
            else:
                # 如果错误与反斜杠无关，返回原始文本
                return text

    def _replace_backslashes_in_strings(self, text: str) -> str:
        """将JSON字符串中的字符串字段中的反斜杠替换为正斜杠

        Args:
            text: JSON字符串

        Returns:
            处理后的JSON字符串
        """
        import re

        # 匹配引号内的内容，包括转义字符
        pattern = r'"((?:[^"\\]|\\.)*)"'

        def replace_path_backslashes(match):
            content = match.group(1)
            # 判断是否可能是文件路径
            if '\\' in content and self._looks_like_path(content):
                # 将反斜杠替换为正斜杠
                content = content.replace('\\', '/')
            return '"' + content + '"'

        # 替换所有匹配到的字符串
        return re.sub(pattern, replace_path_backslashes, text)

    def _escape_backslashes(self, text: str) -> str:
        """将JSON字符串中的所有反斜杠加倍，确保正确转义

        Args:
            text: JSON字符串

        Returns:
            处理后的JSON字符串
        """
        import re

        # 匹配引号内的内容，包括转义字符
        pattern = r'"((?:[^"\\]|\\.)*)"'

        def escape_backslashes(match):
            content = match.group(1)
            # 将所有单个反斜杠替换为双反斜杠
            # 首先处理已经转义的字符，如\n, \t等
            # 将\n替换为临时标记
            content = re.sub(r'\\(["\\bfnrt])', r'__ESCAPED__\1', content)
            # 将剩下的单个反斜杠加倍
            content = content.replace('\\', '\\\\')
            # 将临时标记恢复为原始转义字符
            content = content.replace('__ESCAPED__', '\\')
            return '"' + content + '"'

        # 替换所有匹配到的字符串
        return re.sub(pattern, escape_backslashes, text)

    def _looks_like_path(self, s: str) -> bool:
        """判断字符串是否看起来像文件路径

        Args:
            s: 要判断的字符串

        Returns:
            是否看起来像文件路径
        """
        # 检查是否包含常见的目录结构模式
        common_patterns = [
            # 常见文件扩展名
            '.py', '.js', '.ts', '.html', '.css', '.json', '.md', '.txt', '.xml', '.yaml', '.yml',
            # 常见目录名
            '\\src\\', '\\app\\', '\\utils\\', '\\components\\', '\\models\\', '\\views\\',
            '\\tests\\', '\\config\\', '\\public\\', '\\assets\\', '\\backend\\', '\\frontend\\',
            '/src/', '/app/', '/utils/', '/components/', '/models/', '/views/',
            '/tests/', '/config/', '/public/', '/assets/', '/backend/', '/frontend/'
        ]

        # 检查是否包含多个目录分隔符
        has_multiple_separators = s.count('\\') > 1 or s.count('/') > 1

        # 检查是否包含常见模式
        for pattern in common_patterns:
            if pattern in s:
                return True

        # 如果有多个目录分隔符，也可能是路径
        return has_multiple_separators

    def parse_result(self, result: list[Generation], *, partial: bool = False) -> TBaseModel:
        """解析生成结果列表。

        Args:
            result: 生成结果列表
            partial: 是否允许部分解析

        Returns:
            解析后的Pydantic模型实例

        Raises:
            OutputParserException: 解析失败时抛出异常
        """
        try:
            return super().parse_result(result, partial=partial)
        except Exception as e:
            logger.error(f"解析失败: {e}")
            raise
    def get_format_instructions(self) -> str:
        """返回JSON输出的格式指令。

        Returns:
            JSON输出的格式指令
        """
        # 复制schema以避免修改原始Pydantic schema
        schema = dict(self.pydantic_object.model_json_schema().items())

        # 移除多余字段
        reduced_schema = schema
        if "title" in reduced_schema:
            del reduced_schema["title"]
        if "type" in reduced_schema:
            del reduced_schema["type"]
        # 确保json上下文中使用双引号格式良好
        schema_str = json.dumps(reduced_schema, ensure_ascii=False)

        template = _CHINESE_PYDANTIC_FORMAT_INSTRUCTIONS if self.use_chinese_instructions else _PYDANTIC_FORMAT_INSTRUCTIONS
        return template.format(schema=schema_str)