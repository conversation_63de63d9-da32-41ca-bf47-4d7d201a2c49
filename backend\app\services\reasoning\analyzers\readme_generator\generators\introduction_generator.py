#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目介绍章节生成器
"""
import logging
import os
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone

from langchain.chains import <PERSON><PERSON><PERSON>n
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser

from . import Generator
from ..section_models import IntroductionSection, Badge
from .....ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser
from ......utils.retry import retry_async
from ......utils.file_utils import read_file_content

logger = logging.getLogger(__name__)


class IntroductionGenerator(Generator):
    """
    项目介绍章节生成器
    负责生成README文档的项目介绍章节
    """

    def __init__(self, llm: ChatOpenAI):
        """初始化项目介绍章节生成器"""
        super().__init__(llm)

    async def create_chain(self) -> None:
        """初始化处理链"""
        try:
            # 项目介绍章节生成提示模板
            introduction_template = """
            作为专业的技术文档撰写专家，请为以下项目生成一个引人入胜、内容丰富的项目介绍章节。

            ## 项目基本信息
            项目名称: {{ project_name }}
            项目路径: {{ project_path }}

            {%- if project_files %}

            ## 现有README内容参考
            {%- for file_path, content in project_files.items() %}
            ### {{ file_path }}:
            ```
            {{ content }}
            ```
            {%- endfor %}
            {%- endif %}

            {%- if structure_analysis %}

            ## 项目概览
            {%- if structure_analysis.primary_language %}
            主要编程语言: {{ structure_analysis.primary_language }}
            {%- endif %}
            {%- if structure_analysis.project_type %}
            项目类型: {{ structure_analysis.project_type }}
            {%- endif %}

            {%- if structure_analysis.entries %}

            ### 核心入口点:
            {%- for entry in structure_analysis.entries[:3] %}
            - {{ entry.name }}: {{ entry.description }}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if dependency_analysis and dependency_analysis.tech_stack %}

            ## 核心技术栈
            {%- if dependency_analysis.tech_stack.primary_language %}
            主要语言: {{ dependency_analysis.tech_stack.primary_language }}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.frameworks %}
            核心框架: {%- for framework in dependency_analysis.tech_stack.frameworks[:4] %}{{ framework }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.databases %}
            数据存储: {%- for db in dependency_analysis.tech_stack.databases[:3] %}{{ db }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if architecture_analysis %}

            ## 架构特点
            {%- if architecture_analysis.architecture_style %}
            架构风格: {{ architecture_analysis.architecture_style }}
            {%- endif %}
            {%- if architecture_analysis.architecture_patterns %}
            关键架构模式: {%- for pattern in architecture_analysis.architecture_patterns[:3] %}{{ pattern.name }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if module_merger_analyses %}

            ## 核心功能模块
            {%- for group_key, merger_result in module_merger_analyses.items() %}
            {%- if merger_result.ai_analysis and merger_result.ai_analysis.functional_modules %}

            ### {{ group_key }} 功能组:
            {%- for module in merger_result.ai_analysis.functional_modules[:4] %}
            - **{{ module.name }}**: {{ module.purpose }}
            {%- endfor %}
            {%- endif %}
            {%- endfor %}
            {%- endif %}

            ## 生成要求与原则

            ### 核心生成原则
            1. **基于实际数据**：所有内容生成必须基于提供的项目分析数据
            2. **功能重点突出**：核心功能部分是最重要的，必须详细描述
            3. **现有内容参考**：如果提供了现有README内容，必须参考其信息保持一致性
            4. **内容改进扩展**：在现有内容基础上进行改进和扩展，不要简单重复
            5. **优先级明确**：优先使用现有README中明确提到的项目特点和功能描述
            6. **功能模块优先**：优先使用module_merger_analyses的功能模块信息进行功能分析
            7. **合理推断扩展**：基于提供的实际分析数据，进行合理的推断和扩展

            ### 内容生成任务
            请生成一个引人入胜、内容丰富的项目介绍章节，包括以下9个部分：
            1. **项目标题与标语**（10-20字）：创建一个吸引人的标题和简洁有力的标语，突出项目的核心价值主张和独特卖点
            2. **项目概述**（150-200字）：全面概述项目的功能、目的、价值和应用场景，使读者能够快速理解项目的本质
            3. **项目核心功能**（这是最重要的部分）：
               - 详细描述4-6个主要功能，每个功能3-4句话
               - 突出每个功能的价值、创新点和使用场景
               - 使用具体的例子说明功能如何解决实际问题
            4. **技术亮点**：描述3-5个技术亮点或创新点，展示项目的技术实力和优势
            5. **技术栈概述**：以分类方式列出核心技术，包括语言、框架、数据库等，并简要说明选择这些技术的原因
            6. **项目愿景与未来规划**（2-3句话）：描述项目的长期目标和发展方向
            7. **适用场景**：列出2-3个项目的主要应用场景或目标用户群体
            8. **功能标签**：根据项目的主要功能和特点，提供5-8个准确的功能领域标签（如「AI驱动」、「数据分析」、「Web应用」、「DevOps工具」、「安全工具」等），这些标签将被用作项目的关键词和分类依据
            9. **徽章展示**：根据项目特点，推荐4-6个适合的徽章（如构建状态、版本、许可证等）

            ### 输出语言要求
            所有生成的项目介绍文档内容必须使用中文(简体中文)输出。包括项目概述、背景、目标、价值主张、解决方案、核心特点等内容都必须是中文。只有技术名词、项目名称和专有名词可保持原样。

            ### 输出质量要求
            1. **内容驱动生成**：所有内容必须基于提供的项目分析数据生成
            2. **功能描述详细**：核心功能部分必须详细描述，这是最重要的部分
            3. **标签准确相关**：功能标签必须准确反映项目的实际功能和技术领域
            4. **语言专业生动**：使用专业而生动的语言，避免过多的技术栈相关术语，除非必要时才使用
            5. **术语使用克制**：技术术语应当适度使用，优先用通俗易懂的方式描述功能和价值
            6. **内容全面精炼**：总字数控制在800-1000字，确保内容既全面又不冗长
            7. **格式清晰易读**：添加适当的强调和列表，提高可读性
            8. **吸引力与专业性**：确保内容既专业又有吸引力，能够激发读者进一步探索项目的兴趣

            {{ format_instructions }}
            """

            # 创建Parser
            introduction_parser = AnalysisOutputParser(pydantic_object=IntroductionSection)
            parser = OutputFixingParser.from_llm(parser=introduction_parser, llm=self.llm)

            # 获取格式指令
            introduction_format_instructions = introduction_parser.get_format_instructions()

            # 创建提示模板
            introduction_prompt = PromptTemplate.from_template(
                introduction_template,
                template_format="jinja2"
            ).partial(format_instructions=introduction_format_instructions)

            # 创建处理链
            self.chain = (
                introduction_prompt
                | self.llm
                | parser
            )

            logger.info("项目介绍章节生成器处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: AgentInput) -> None:
        """
        准备输入数据并设置self.input_parameters

        Args:
            input_data: 原始输入数据
        """
        # 初始化self.input_parameters
        self.input_parameters = input_data.parameters.copy()

        # 确保项目路径可用
        if "project_path" in self.input_parameters:
            project_path = self.input_parameters["project_path"]
        else:
            project_path = ""
            logger.warning("项目路径不可用")

        # 准备项目文件内容
        project_files = {}

        # 处理结构分析数据
        if "structure_analysis" in self.input_parameters:
            structure_analysis = self.input_parameters["structure_analysis"]

            # 从文档组件中查找README等自述文件
            if structure_analysis.docs:
                for doc in structure_analysis.docs:
                    # 查找README类型的文档
                    if doc.doc_category in ['readme', 'other']:
                        full_path = os.path.join(project_path, doc.path)
                        content = read_file_content(full_path)
                        if content:
                            project_files[doc.path] = content

        # 将项目文件内容添加到输入参数中
        self.input_parameters["project_files"] = project_files

        logger.info(f"项目介绍章节生成器输入参数准备完成，包含 {len(project_files)} 个项目文件")

    @retry_async(max_retries=3)
    async def process(self) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询

        Returns:
            Tuple[Any, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 调用LLM处理链
            # result = await self.chain.first.ainvoke(self.input_parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(self.input_parameters)

            # 设置章节顺序
            if result:
                result.order = 1  # 介绍章节通常是第一个

            return result, {"generator": "introduction_generator"}
        except Exception as e:
            logger.error(f"项目介绍章节生成失败: {str(e)}")
            raise
