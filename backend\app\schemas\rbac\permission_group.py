"""
权限组相关的Pydantic模型
包含权限组的创建、更新和查询模型
"""
from datetime import datetime
from typing import Optional, List
from typing_extensions import Self
from pydantic import BaseModel, Field, field_validator, model_validator
from .permission import Permission

class PermissionGroup(BaseModel):
    """权限组数据模型"""
    id: str = Field(..., description='权限组ID')
    name: str = Field(..., min_length=2, max_length=100, description='权限组名称')
    code: str = Field(..., min_length=2, max_length=100, pattern=r'^[a-z_]+$', description='权限组标识符，格式为resource:action')
    description: Optional[str] = Field(None, max_length=200, description='描述信息')
    sort_order: int = Field(0, ge=0, description='排序号')
    is_active: bool = Field(True, description='是否启用')
    permissions: List[Permission] = Field(default_factory=list, exclude=True, description='包含的权限列表')
    created_at: datetime = Field(..., description='创建时间')
    updated_at: datetime = Field(..., description='更新时间')
    version: str = Field('1.0.0', description='版本号')

    @field_validator('code')
    def validate_code(cls, v: str) -> str:
        if not v.islower():
            raise ValueError('权限组标识符必须是小写字母、下划线组成')
        return v

    @model_validator(mode='after')
    def validate_permissions(self) -> Self:
        """验证权限列表中的权限是否属于同一个资源"""
        if self.permissions:
            for permission in self.permissions:
                if not permission.code.startswith(self.code):
                    raise ValueError(f'权限 {permission.code} 不属于资源 {self.code}')
        return self

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "1",
                "name": "用户列表管理",
                "code": "user",
                "description": "用户列表管理相关的权限组",
                "sort_order": 0,
                "is_active": True,
                "permissions": [
                    {
                        "id": "1",
                        "name": "查看用户列表",
                        "code": "user:list",
                        "type": "api"
                    },
                    {
                        "id": "2",
                        "name": "导出用户列表",
                        "code": "user:export",
                        "type": "api"
                    }
                ],
                "created_at": "2024-01-01T12:00:00Z",
                "updated_at": "2024-01-01T12:00:00Z",
                "version": "1.0.0"
            }
        }

class PermissionGroupCreateRequest(BaseModel):
    """创建权限组的请求数据模型"""
    name: str = Field(..., min_length=2, max_length=100, description='权限组名称')
    code: str = Field(..., min_length=2, max_length=100, pattern=r'^[a-z_]+$', description='权限组标识符，格式为resource:action')
    description: Optional[str] = Field(None, max_length=200, description='描述信息')
    sort_order: int = Field(0, ge=0, description='排序号')
    is_active: bool = Field(True, description='是否启用')
    permission_ids: List[str] = Field(default_factory=list, description='包含的权限列表')
    role_ids: Optional[List[str]] = Field(default_factory=list, description='关联的角色标识符')

    @field_validator('code')
    def validate_code(cls, v: str) -> str:
        if not v.islower():
            raise ValueError('权限组标识符必须是小写字母、下划线组成')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "name": "用户列表管理",
                "code": "user",
                "description": "用户列表管理相关的权限组",
                "sort_order": 0,
                "is_active": True,
                "permission_ids": ["1", "2"],
                "role_ids": ["1", "2"]
            }
        }

class PermissionGroupUpdateRequest(BaseModel):
    """更新权限组的请求数据模型"""
    permission_group_id: str = Field(..., description='权限组ID')
    name: Optional[str] = Field(None, min_length=2, max_length=100, description='权限组名称')
    code: Optional[str] = Field(None, min_length=2, max_length=100, pattern=r'^[a-z_]+$', description='权限组标识符，格式为resource:action')
    description: Optional[str] = Field(None, max_length=200, description='描述信息')
    sort_order: Optional[int] = Field(None, ge=0, description='排序号')
    is_active: Optional[bool] = Field(None, description='是否启用')
    permission_ids: Optional[List[str]] = Field(default_factory=list, description='包含的权限列表')
    role_ids: Optional[List[str]] = Field(default_factory=list, description='关联的角色标识符')

    @field_validator('code')
    def validate_code(cls, v: str) -> str:
        if v is None:
            return v
        if not v.islower():
            raise ValueError('权限组标识符必须是小写字母、下划线组成')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "permission_group_id": "1",
                "name": "用户列表管理",
                "code": "user",
                "description": "用户列表管理相关的权限组",
                "sort_order": 1,
                "is_active": True,
                "permission_ids": ["1", "2"],
                "role_ids": ["1", "2"]
            }
        }

class PermissionGroupListResponse(BaseModel):
    """权限组列表分页响应"""
    total: int = Field(..., description="总记录数")
    permission_groups: List[PermissionGroup] = Field(..., description="权限组列表")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(0, description="总页数")

    @model_validator(mode="after")
    def compute_total_pages(self) -> Self:
        """计算总页数"""    
        if self.total and self.page_size and self.total > 0 and self.page_size > 0:
            self.total_pages = (self.total + self.page_size - 1) // self.page_size
        else:
            self.total_pages = 0
        return self

    class Config:
        """配置"""
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class PermissionGroupPermissionRequest(BaseModel):
    """权限组权限管理请求模型"""
    permission_group_id: str = Field(..., description="权限组ID")
    permission_ids: List[str] = Field(..., description="权限ID列表")

    class Config:
        json_schema_extra = {
            "example": {
                "permission_group_id": "1234567890",
                "permission_ids": ["1234567890", "0987654321"]
            }
        }

PermissionGroup.model_rebuild()
PermissionGroupListResponse.model_rebuild()