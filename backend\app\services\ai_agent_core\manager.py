#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能体管理器
管理和协调不同智能体的工作
"""
import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple, Union, TypeVar, Type
from uuid import uuid4

from .base_agent import BaseAgent
from .models import ReasoningContext, AgentInput, AgentOutput

logger = logging.getLogger(__name__)


class AgentManager:
    """智能体管理器"""
    
    _agent_classes: Dict[str, Type[BaseAgent]] = {}  # 注册的智能体类
    
    def __init__(self):
        """初始化管理器"""
        self.agents: Dict[str, BaseAgent] = {}  # 已加载的智能体实例
        self.contexts: Dict[str, Dict[str, Any]] = {}  # 智能体上下文
    
    # ============= 智能体注册功能 =============
    @classmethod
    def register(cls, name: Optional[str] = None):
        """
        注册智能体装饰器
        
        Args:
            name: 注册名称，默认为类名
        """
        def decorator(agent_cls: Type[BaseAgent]) -> Type[BaseAgent]:
            agent_name = name or agent_cls.__name__
            cls._agent_classes[agent_name] = agent_cls
            return agent_cls
        return decorator
    
    @classmethod
    def get(cls, name: str) -> Optional[Type[BaseAgent]]:
        """
        获取智能体类
        
        Args:
            name: 智能体名称
            
        Returns:
            Optional[Type[BaseAgent]]: 智能体类
        """
        return cls._agent_classes.get(name)
    
    @classmethod
    def get_all(cls) -> Dict[str, Type[BaseAgent]]:
        """
        获取所有注册的智能体类
        
        Returns:
            Dict[str, Type[BaseAgent]]: 智能体类字典
        """
        return cls._agent_classes.copy()
    
    @classmethod
    def create(cls, name: str) -> Optional[BaseAgent]:
        """
        创建智能体实例
        
        Args:
            name: 智能体名称
            
        Returns:
            Optional[BaseAgent]: 智能体实例
        """
        agent_cls = cls.get(name)
        if agent_cls:
            return agent_cls()
        return None
    
    # ============= 智能体管理功能 =============
    
    def load(self, name: str) -> Optional[BaseAgent]:
        """
        加载智能体实例
        
        Args:
            name: 智能体名称
            
        Returns:
            Optional[BaseAgent]: 智能体实例
        """
        if name not in self.agents:
            agent = self.create(name)
            if agent:
                self.agents[name] = agent
                return agent
            return None
        return self.agents.get(name)

    def load_all(self) -> None:
        """
        加载所有已注册的智能体
        """
        for name in self._agent_classes:
            self.load(name)

    async def initialize(self) -> bool:
        """
        初始化所有已加载的智能体
        
        Returns:
            bool: 是否全部初始化成功
        """
        all_success = True
        for name, agent in self.agents.items():
            logger.info(f"初始化智能体: {name}")
            success = await agent.initialize()
            if not success:
                logger.error(f"智能体 {name} 初始化失败: {agent.last_error}")
                all_success = False
        
        return all_success
    
    async def process(
        self, 
        agent_name: str, 
        parameters: Optional[Dict[str, Any]] = None
    ) -> Optional[AgentOutput]:
        """
        处理请求
        
        Args:
            agent_name: 智能体名称
            parameters: 参数
            
        Returns:
            Optional[AgentOutput]: 处理结果
        """
        if agent_name not in self.agents:
            logger.error(f"智能体 {agent_name} 未加载")
            return None
        
        agent = self.agents[agent_name]
        reasoning_context = parameters.get("context", ReasoningContext())
        self.contexts[agent_name] = reasoning_context
        
        # 准备输入
        input_data = AgentInput(
            context=reasoning_context,
            parameters=parameters or {}
        )
        
        try:
            # 处理请求
            output = await agent.process(input_data)
            
            # 更新上下文
            self.contexts[agent_name] = output.context
            
            return output
        except Exception as e:
            logger.error(f"处理请求失败: {str(e)}")
            return None
    
    async def shutdown(self) -> None:
        """关闭所有智能体"""
        for name, agent in self.agents.items():
            try:
                await agent.shutdown()
                logger.info(f"智能体 {name} 已关闭")
            except Exception as e:
                logger.error(f"关闭智能体 {name} 失败: {str(e)}")


# 创建全局管理器实例
agent_manager = AgentManager()
