#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
README章节模型

定义README文档的各种章节模型，用于解析和生成项目README文档
"""

import uuid
from typing import List, Optional, Union, Dict, Any
from datetime import datetime, timezone
from pydantic import BaseModel, Field, field_validator

# 基础数据模型
class Badge(BaseModel):
    """项目徽章模型"""
    name: str = Field(default="", description="徽章名称")
    url: str = Field(default="", description="徽章URL")

class Feature(BaseModel):
    """功能特性模型"""
    title: str = Field(default="", description="功能标题")
    description: str = Field(default="", description="功能描述")

class InstallationStep(BaseModel):
    """安装步骤模型"""
    title: str = Field(default="", description="步骤标题")
    command: str = Field(default="", description="执行命令")

class CodeExample(BaseModel):
    """代码示例模型"""
    description: str = Field(default="", description="示例描述")
    code: str = Field(default="", description="示例代码")
    language: str = Field(default="python", description="代码语言")

class ParamInfo(BaseModel):
    """API参数信息"""
    name: str = Field(default="", description="参数名称")
    type: str = Field(default="", description="参数类型")
    required: str = Field(default="否", description="是否必填")
    description: str = Field(default="", description="参数描述")

class Endpoint(BaseModel):
    """API端点模型"""
    name: str = Field(default="", description="端点名称")
    method: str = Field(default="", description="HTTP方法")
    url: str = Field(default="", description="端点URL")
    description: str = Field(default="", description="端点描述")
    params: List[ParamInfo] = Field(default_factory=list, description="参数列表")
    response: str = Field(default="", description="响应示例")



class DependencyLicense(BaseModel):
    """依赖项许可证模型"""
    name: str = Field(default="", description="依赖项名称")
    license_type: str = Field(default="", description="许可证类型")
    version: str = Field(default="", description="依赖项版本")
    description: str = Field(default="", description="许可证说明")
    compatibility: str = Field(default="", description="与项目主许可证的兼容性")
    url: str = Field(default="", description="许可证URL")

class Component(BaseModel):
    """架构组件模型"""
    name: str = Field(default="", description="组件名称")
    description: str = Field(default="", description="组件描述")
    responsibility: str = Field(default="", description="组件职责")

class Relationship(BaseModel):
    """组件关系模型"""
    source: str = Field(default="", description="源组件")
    target: str = Field(default="", description="目标组件")
    type: str = Field(default="", description="关系类型")
    description: str = Field(default="", description="关系描述")

class ReadmeSection(BaseModel):
    """README文档章节的基类"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str = Field(default="", description="章节标题")
    content: str = Field(default="", description="章节内容")
    order: int = Field(default=0, description="章节排序位置")

    @field_validator("title")
    def title_not_empty(cls, v):
        if not v or v.strip() == "":
            raise ValueError("章节标题不能为空")
        return v

    def to_markdown(self) -> str:
        """将章节转换为Markdown格式"""
        return f"{self.content}\n"

    def to_html(self) -> str:
        """
        将章节转换为HTML格式

        Returns:
            str: 格式化的HTML字符串
        """
        # 将换行符转换为HTML段落
        content_html = ""
        if self.content:
            paragraphs = self.content.split("\n\n")
            for p in paragraphs:
                if p.strip():
                    content_html += f"<p>{p}</p>\n"

        return content_html

class IntroductionSection(ReadmeSection):
    """项目介绍章节"""
    title: str = Field(default="简介", description="章节标题")
    project_name: str = Field(default="", description="项目名称")
    slogan: str = Field(default="", description="项目标语，简洁有力的短语，突出项目的核心价值")
    project_description: str = Field(default="", description="项目描述，全面概述项目功能、目的和价值")
    project_vision: str = Field(default="", description="项目愿景与未来规划")
    core_features: List[Feature] = Field(default_factory=list, description="项目核心功能列表")
    technical_highlights: List[str] = Field(default_factory=list, description="技术亮点或创新点列表")
    tech_stack: Dict[str, List[str]] = Field(default_factory=dict, description="技术栈概述，分类列出核心技术")
    use_cases: List[str] = Field(default_factory=list, description="项目适用场景或目标用户群体")
    feature_tags: List[str] = Field(default_factory=list, description="项目功能领域标签列表，如'AI驱动'、'数据分析'、'Web应用'等")
    badges: List[Badge] = Field(default_factory=list, description="项目徽章列表")

    def to_markdown(self) -> str:
        """将介绍章节转换为Markdown格式"""
        markdown = ""

        # 添加项目描述
        if self.project_description:
            markdown += f"{self.project_description}\n\n"

        # 添加核心功能
        if self.core_features:
            markdown += f"### 核心功能\n\n"
            for feature in self.core_features:
                markdown += f"#### {feature.title}\n\n{feature.description}\n\n"

        # 添加技术亮点
        if self.technical_highlights:
            markdown += f"### 技术亮点\n\n"
            for highlight in self.technical_highlights:
                markdown += f"- {highlight}\n"
            markdown += "\n"

        # 添加技术栈
        if self.tech_stack:
            markdown += f"### 技术栈\n\n"
            for category, technologies in self.tech_stack.items():
                markdown += f"#### {category}\n\n"
                for tech in technologies:
                    markdown += f"- {tech}\n"
                markdown += "\n"

        # 添加适用场景
        if self.use_cases:
            markdown += f"### 适用场景\n\n"
            for use_case in self.use_cases:
                markdown += f"- {use_case}\n"
            markdown += "\n"

        # 添加项目愿景
        if self.project_vision:
            markdown += f"### 项目愿景\n\n{self.project_vision}\n\n"

        # 添加其他内容
        if self.content and self.content.strip():
            markdown += f"{self.content}\n"

        return markdown

    def to_html(self) -> str:
        """
        将介绍章节转换为HTML格式

        Returns:
            str: 格式化的HTML字符串
        """
        html = ""

        # 添加项目描述
        if self.project_description:
            html += f"<p class='description'>{self.project_description}</p>\n"

        # 添加核心功能
        if self.core_features:
            html += "<section class='core-features'>\n"
            html += "<h3>核心功能</h3>\n"
            for feature in self.core_features:
                html += f"<div class='feature'>\n"
                html += f"<h4>{feature.title}</h4>\n"
                html += f"<p>{feature.description}</p>\n"
                html += "</div>\n"
            html += "</section>\n"

        # 添加技术亮点
        if self.technical_highlights:
            html += "<section class='technical-highlights'>\n"
            html += "<h3>技术亮点</h3>\n"
            html += "<ul>\n"
            for highlight in self.technical_highlights:
                html += f"<li>{highlight}</li>\n"
            html += "</ul>\n"
            html += "</section>\n"

        # 添加技术栈
        if self.tech_stack:
            html += "<section class='tech-stack'>\n"
            html += "<h3>技术栈</h3>\n"
            for category, technologies in self.tech_stack.items():
                html += f"<div class='tech-category'>\n"
                html += f"<h4>{category}</h4>\n"
                html += "<ul>\n"
                for tech in technologies:
                    html += f"<li>{tech}</li>\n"
                html += "</ul>\n"
                html += "</div>\n"
            html += "</section>\n"

        # 添加适用场景
        if self.use_cases:
            html += "<section class='use-cases'>\n"
            html += "<h3>适用场景</h3>\n"
            html += "<ul>\n"
            for use_case in self.use_cases:
                html += f"<li>{use_case}</li>\n"
            html += "</ul>\n"
            html += "</section>\n"

        # 添加项目愿景
        if self.project_vision:
            html += "<section class='vision'>\n"
            html += "<h3>项目愿景</h3>\n"
            html += f"<p>{self.project_vision}</p>\n"
            html += "</section>\n"

        # 添加其他内容
        if self.content and self.content.strip():
            html += "<section class='additional-content'>\n"
            paragraphs = self.content.split("\n\n")
            for p in paragraphs:
                if p.strip():
                    html += f"<p>{p}</p>\n"
            html += "</section>\n"

        return html

class FeaturesSection(ReadmeSection):
    """功能特性章节"""
    title: str = Field(default="功能特性", description="章节标题")
    features: List[Feature] = Field(default_factory=list, description="功能特性列表")

    def to_markdown(self) -> str:
        """将功能特性章节转换为Markdown格式"""
        markdown = ""

        if self.content:
            markdown += f"{self.content}\n\n"

        for feature in self.features:
            if feature.title:
                markdown += f"#### {feature.title}\n\n"
            if feature.description:
                markdown += f"{feature.description}\n\n"

        return markdown

    def to_html(self) -> str:
        """
        将功能特性章节转换为HTML格式

        Returns:
            str: 格式化的HTML字符串
        """
        html = ""

        # 添加章节内容
        if self.content:
            paragraphs = self.content.split("\n\n")
            for p in paragraphs:
                if p.strip():
                    html += f"<p>{p}</p>\n"

        # 添加功能特性列表
        if self.features:
            html += "<div class='features'>\n"
            for feature in self.features:
                html += "<div class='feature'>\n"
                if feature.title:
                    html += f"<h3>{feature.title}</h3>\n"
                if feature.description:
                    html += f"<p>{feature.description}</p>\n"
                html += "</div>\n"
            html += "</div>\n"

        return html

class InstallationSection(ReadmeSection):
    """安装说明章节"""
    title: str = Field(default="安装", description="章节标题")
    prerequisites: List[str] = Field(default_factory=list, description="前置条件")
    installation_steps: List[InstallationStep] = Field(default_factory=list, description="安装步骤列表")

    def to_markdown(self) -> str:
        """将安装说明章节转换为Markdown格式"""
        markdown = ""

        if self.prerequisites:
            markdown += "#### 前置条件\n\n"
            for prerequisite in self.prerequisites:
                markdown += f"- {prerequisite}\n"
            markdown += "\n"

        if self.content:
            markdown += f"{self.content}\n\n"

        for step in self.installation_steps:
            if step.title:
                markdown += f"#### {step.title}\n\n"
            if step.command:
                markdown += f"```bash\n{step.command}\n```\n\n"

        return markdown

    def to_html(self) -> str:
        """
        将安装说明章节转换为HTML格式

        Returns:
            str: 格式化的HTML字符串
        """
        html = ""

        # 添加前置条件
        if self.prerequisites:
            html += "<div class='prerequisites'>\n"
            html += "<h3>前置条件</h3>\n"
            html += "<ul>\n"
            for prerequisite in self.prerequisites:
                html += f"<li>{prerequisite}</li>\n"
            html += "</ul>\n"
            html += "</div>\n"

        # 添加章节内容
        if self.content:
            paragraphs = self.content.split("\n\n")
            for p in paragraphs:
                if p.strip():
                    html += f"<p>{p}</p>\n"

        # 添加安装步骤
        if self.installation_steps:
            html += "<div class='installation-steps'>\n"
            for step in self.installation_steps:
                if step.title:
                    html += f"<h3>{step.title}</h3>\n"
                if step.command:
                    html += "<pre><code class='language-bash'>\n"
                    html += f"{step.command}\n"
                    html += "</code></pre>\n"
            html += "</div>\n"

        return html

class UsageSection(ReadmeSection):
    """使用说明章节"""
    title: str = Field(default="使用方法", description="章节标题")
    
    # 安装与设置相关属性
    installation_guide: str = Field(default="", description="详细的安装步骤和设置说明")
    environment_setup: Dict[str, str] = Field(default_factory=dict, description="不同环境下的安装方法，键为环境名称，值为安装说明")
    configuration_guide: str = Field(default="", description="配置说明，包括必要的环境变量或配置文件")
    
    # 基本使用方法相关属性
    startup_steps: List[str] = Field(default_factory=list, description="启动和运行项目的详细步骤")
    command_options: Dict[str, str] = Field(default_factory=dict, description="命令行参数或配置选项的说明，键为选项名，值为说明")
    basic_workflow: str = Field(default="", description="基本操作流程的概述")
    
    # 核心功能使用指南
    core_features_guide: List[Dict[str, Any]] = Field(default_factory=list, description="核心功能的使用方法指南")
    examples: List[CodeExample] = Field(default_factory=list, description="使用示例列表")
    
    # 常见问题与解决方案
    common_issues: List[Dict[str, Any]] = Field(default_factory=list, description="常见问题与解决方案列表，包含问题描述和解决步骤")
    
    # 使用技巧与最佳实践
    tips_and_best_practices: List[str] = Field(default_factory=list, description="提高效率或避免常见陷阱的技巧列表")

    def to_markdown(self) -> str:
        """将使用说明章节转换为Markdown格式"""
        markdown = ""

        if self.content:
            markdown += f"{self.content}\n\n"

        # 添加安装与设置部分
        if any([self.installation_guide, self.environment_setup, self.configuration_guide]):
            markdown += "#### 安装与设置\n\n"

            if self.installation_guide:
                markdown += f"{self.installation_guide}\n\n"

            if self.environment_setup:
                markdown += "##### 不同环境的安装方法\n\n"
                for env, guide in self.environment_setup.items():
                    markdown += f"###### {env}\n\n{guide}\n\n"

            if self.configuration_guide:
                markdown += "##### 配置说明\n\n"
                markdown += f"{self.configuration_guide}\n\n"

        # 添加基本使用方法部分
        if any([self.startup_steps, self.command_options, self.basic_workflow]):
            markdown += "#### 基本使用方法\n\n"

            if self.basic_workflow:
                markdown += f"{self.basic_workflow}\n\n"

            if self.startup_steps:
                markdown += "##### 启动和运行步骤\n\n"
                for i, step in enumerate(self.startup_steps, 1):
                    markdown += f"{i}. {step}\n"
                markdown += "\n"

            if self.command_options:
                markdown += "##### 命令行选项\n\n"
                for option, description in self.command_options.items():
                    markdown += f"- **{option}**: {description}\n"
                markdown += "\n"

        # 添加核心功能使用指南
        if self.core_features_guide:
            markdown += "#### 核心功能使用指南\n\n"

            for feature in self.core_features_guide:
                if 'name' in feature:
                    markdown += f"##### {feature.get('name')}\n\n"
                if 'description' in feature:
                    markdown += f"{feature.get('description')}\n\n"
                if 'steps' in feature and feature['steps']:
                    markdown += "###### 使用步骤\n\n"
                    for i, step in enumerate(feature['steps'], 1):
                        markdown += f"{i}. {step}\n"
                    markdown += "\n"
                if 'parameters' in feature and feature['parameters']:
                    markdown += "###### 参数选项\n\n"
                    for param, desc in feature['parameters'].items():
                        markdown += f"- **{param}**: {desc}\n"
                    markdown += "\n"
                if 'example' in feature:
                    markdown += "###### 示例\n\n"
                    markdown += f"{feature.get('example')}\n\n"
                if 'output' in feature:
                    markdown += "###### 预期输出\n\n"
                    markdown += f"{feature.get('output')}\n\n"

        # 添加使用示例
        if self.examples:
            markdown += "#### 使用示例\n\n"
            for example in self.examples:
                if example.description:
                    markdown += f"{example.description}\n\n"
                if example.code:
                    markdown += f"```{example.language}\n{example.code}\n```\n\n"

        # 添加常见问题与解决方案
        if self.common_issues:
            markdown += "#### 常见问题与解决方案\n\n"
            for i, issue in enumerate(self.common_issues, 1):
                if 'problem' in issue:
                    markdown += f"##### 问题 {i}: {issue.get('problem')}\n\n"
                if 'solution' in issue or '解决方案' in issue:
                    solution = issue.get('solution') or issue.get('解决方案')
                    markdown += f"**解决方案**:\n\n"
                    if isinstance(solution, list):
                        for step in solution:
                            markdown += f"- {step}\n"
                        markdown += "\n"
                    else:
                        markdown += f"{solution}\n\n"

        # 添加使用技巧与最佳实践
        if self.tips_and_best_practices:
            markdown += "#### 使用技巧与最佳实践\n\n"
            for tip in self.tips_and_best_practices:
                markdown += f"- {tip}\n"
            markdown += "\n"

        return markdown

    def to_html(self) -> str:
        """
        将使用说明章节转换为HTML格式

        Returns:
            str: 格式化的HTML字符串
        """
        html = ""

        # 添加章节内容
        if self.content:
            paragraphs = self.content.split("\n\n")
            for p in paragraphs:
                if p.strip():
                    html += f"<p>{p}</p>\n"

        # 添加安装与设置部分
        if any([self.installation_guide, self.environment_setup, self.configuration_guide]):
            html += "<section class='installation-setup'>\n"
            html += "<h2>安装与设置</h2>\n"

            if self.installation_guide:
                html += f"<div class='installation-guide'>\n"
                html += f"<p>{self.installation_guide}</p>\n"
                html += "</div>\n"

            if self.environment_setup:
                html += "<div class='environment-setup'>\n"
                html += "<h3>不同环境的安装方法</h3>\n"
                for env, guide in self.environment_setup.items():
                    html += f"<div class='env-type'>\n"
                    html += f"<h4>{env}</h4>\n"
                    html += f"<p>{guide}</p>\n"
                    html += "</div>\n"
                html += "</div>\n"

            if self.configuration_guide:
                html += "<div class='configuration-guide'>\n"
                html += "<h3>配置说明</h3>\n"
                html += f"<p>{self.configuration_guide}</p>\n"
                html += "</div>\n"

            html += "</section>\n"

        # 添加基本使用方法部分
        if any([self.startup_steps, self.command_options, self.basic_workflow]):
            html += "<section class='basic-usage'>\n"
            html += "<h2>基本使用方法</h2>\n"

            if self.basic_workflow:
                html += f"<p>{self.basic_workflow}</p>\n"

            if self.startup_steps:
                html += "<div class='startup-steps'>\n"
                html += "<h3>启动和运行步骤</h3>\n"
                html += "<ol>\n"
                for step in self.startup_steps:
                    html += f"<li>{step}</li>\n"
                html += "</ol>\n"
                html += "</div>\n"

            if self.command_options:
                html += "<div class='command-options'>\n"
                html += "<h3>命令行选项</h3>\n"
                html += "<ul>\n"
                for option, description in self.command_options.items():
                    html += f"<li><strong>{option}</strong>: {description}</li>\n"
                html += "</ul>\n"
                html += "</div>\n"

            html += "</section>\n"

        # 添加核心功能使用指南
        if self.core_features_guide:
            html += "<section class='core-features-guide'>\n"
            html += "<h2>核心功能使用指南</h2>\n"

            for feature in self.core_features_guide:
                html += "<div class='feature-guide'>\n"

                if 'name' in feature:
                    html += f"<h3>{feature.get('name')}</h3>\n"

                if 'description' in feature:
                    html += f"<p>{feature.get('description')}</p>\n"

                if 'steps' in feature and feature['steps']:
                    html += "<div class='feature-steps'>\n"
                    html += "<h4>使用步骤</h4>\n"
                    html += "<ol>\n"
                    for step in feature['steps']:
                        html += f"<li>{step}</li>\n"
                    html += "</ol>\n"
                    html += "</div>\n"

                if 'parameters' in feature and feature['parameters']:
                    html += "<div class='feature-parameters'>\n"
                    html += "<h4>参数选项</h4>\n"
                    html += "<ul>\n"
                    for param, desc in feature['parameters'].items():
                        html += f"<li><strong>{param}</strong>: {desc}</li>\n"
                    html += "</ul>\n"
                    html += "</div>\n"

                if 'example' in feature:
                    html += "<div class='feature-example'>\n"
                    html += "<h4>示例</h4>\n"
                    html += f"<p>{feature.get('example')}</p>\n"
                    html += "</div>\n"

                if 'output' in feature:
                    html += "<div class='feature-output'>\n"
                    html += "<h4>预期输出</h4>\n"
                    html += f"<p>{feature.get('output')}</p>\n"
                    html += "</div>\n"

                html += "</div>\n"

            html += "</section>\n"

        # 添加使用示例
        if self.examples:
            html += "<section class='code-examples'>\n"
            html += "<h2>使用示例</h2>\n"
            for example in self.examples:
                html += "<div class='code-example'>\n"
                if example.description:
                    html += f"<p>{example.description}</p>\n"
                if example.code:
                    html += f"<pre><code class='language-{example.language}'>\n"
                    html += f"{example.code}\n"
                    html += "</code></pre>\n"
                html += "</div>\n"
            html += "</section>\n"

        # 添加常见问题与解决方案
        if self.common_issues:
            html += "<section class='common-issues'>\n"
            html += "<h2>常见问题与解决方案</h2>\n"
            for i, issue in enumerate(self.common_issues, 1):
                html += "<div class='issue'>\n"
                if 'problem' in issue:
                    html += f"<h3>问题 {i}: {issue.get('problem')}</h3>\n"
                if 'solution' in issue or '解决方案' in issue:
                    solution = issue.get('solution') or issue.get('解决方案')
                    html += "<div class='solution'>\n"
                    html += f"<h4>解决方案</h4>\n"
                    if isinstance(solution, list):
                        html += "<ul>\n"
                        for step in solution:
                            html += f"<li>{step}</li>\n"
                        html += "</ul>\n"
                    else:
                        html += f"<p>{solution}</p>\n"
                    html += "</div>\n"
                html += "</div>\n"
            html += "</section>\n"

        # 添加使用技巧与最佳实践
        if self.tips_and_best_practices:
            html += "<section class='tips-and-practices'>\n"
            html += "<h2>使用技巧与最佳实践</h2>\n"
            html += "<ul>\n"
            for tip in self.tips_and_best_practices:
                html += f"<li>{tip}</li>\n"
            html += "</ul>\n"
            html += "</section>\n"

        return html

class ApiReferenceSection(ReadmeSection):
    """API参考章节"""
    title: str = Field(default="API参考", description="章节标题")
    endpoints: List[Endpoint] = Field(default_factory=list, description="API端点列表")

    def to_markdown(self) -> str:
        """将API参考章节转换为Markdown格式"""
        markdown = ""

        if self.content:
            markdown += f"{self.content}\n\n"

        for endpoint in self.endpoints:
            if endpoint.name:
                markdown += f"#### {endpoint.name}\n\n"
            if endpoint.method and endpoint.url:
                markdown += f"`{endpoint.method} {endpoint.url}`\n\n"
            if endpoint.description:
                markdown += f"{endpoint.description}\n\n"

            if endpoint.params:
                markdown += "**参数:**\n\n"
                markdown += "| 名称 | 类型 | 必填 | 描述 |\n"
                markdown += "| ---- | ---- | ---- | ---- |\n"
                for param in endpoint.params:
                    markdown += f"| {param.name} | {param.type} | {param.required} | {param.description} |\n"
                markdown += "\n"

            if endpoint.response:
                markdown += f"**响应示例:**\n\n```json\n{endpoint.response}\n```\n\n"

        return markdown

    def to_html(self) -> str:
        """
        将API参考章节转换为HTML格式

        Returns:
            str: 格式化的HTML字符串
        """
        html = ""

        # 添加章节内容
        if self.content:
            paragraphs = self.content.split("\n\n")
            for p in paragraphs:
                if p.strip():
                    html += f"<p>{p}</p>\n"

        # 添加API端点
        if self.endpoints:
            html += "<div class='api-endpoints'>\n"
            for endpoint in self.endpoints:
                html += "<div class='api-endpoint'>\n"

                # 端点名称
                if endpoint.name:
                    html += f"<h3>{endpoint.name}</h3>\n"

                # 方法和URL
                if endpoint.method and endpoint.url:
                    html += f"<code class='api-method'>{endpoint.method} {endpoint.url}</code>\n"

                # 描述
                if endpoint.description:
                    html += f"<p>{endpoint.description}</p>\n"

                # 参数表格
                if endpoint.params:
                    html += "<div class='api-params'>\n"
                    html += "<h4>参数:</h4>\n"
                    html += "<table>\n"
                    html += "<thead>\n"
                    html += "<tr>\n"
                    html += "<th>名称</th>\n"
                    html += "<th>类型</th>\n"
                    html += "<th>必填</th>\n"
                    html += "<th>描述</th>\n"
                    html += "</tr>\n"
                    html += "</thead>\n"
                    html += "<tbody>\n"

                    for param in endpoint.params:
                        html += "<tr>\n"
                        html += f"<td>{param.name}</td>\n"
                        html += f"<td>{param.type}</td>\n"
                        html += f"<td>{param.required}</td>\n"
                        html += f"<td>{param.description}</td>\n"
                        html += "</tr>\n"

                    html += "</tbody>\n"
                    html += "</table>\n"
                    html += "</div>\n"

                # 响应示例
                if endpoint.response:
                    html += "<div class='api-response'>\n"
                    html += "<h4>响应示例:</h4>\n"
                    html += "<pre><code class='language-json'>\n"
                    html += f"{endpoint.response}\n"
                    html += "</code></pre>\n"
                    html += "</div>\n"

                html += "</div>\n"
            html += "</div>\n"

        return html

class DependenciesSection(ReadmeSection):
    """依赖项章节"""
    title: str = Field(default="依赖项", description="章节标题")

    # 技术栈概览
    primary_language: str = Field(default="", description="主要编程语言")
    main_frameworks: List[str] = Field(default_factory=list, description="主要框架列表")
    build_tools: List[str] = Field(default_factory=list, description="构建工具列表")

    # 重点依赖项（核心框架和库）
    key_dependencies: Dict[str, str] = Field(default_factory=dict, description="关键依赖项字典，键为依赖名称，值为用途描述")

    # 安全和许可证概览
    vulnerable_dependencies: int = Field(default=0, description="存在安全漏洞的依赖数量")
    license_types: List[str] = Field(default_factory=list, description="主要许可证类型列表")

    # 依赖管理文件
    dependency_files: List[str] = Field(default_factory=list, description="依赖管理文件列表")

    # 安装和管理说明
    installation_commands: List[str] = Field(default_factory=list, description="安装命令列表")
    update_commands: List[str] = Field(default_factory=list, description="更新命令列表")

    def to_markdown(self) -> str:
        """将依赖项章节转换为Markdown格式"""
        markdown = ""

        if self.content:
            markdown += f"{self.content}\n\n"

        # 技术栈概览
        if self.primary_language or self.main_frameworks or self.build_tools:
            markdown += "#### 技术栈概览\n\n"

            if self.primary_language:
                markdown += f"- **主要语言**: {self.primary_language}\n"

            if self.main_frameworks:
                markdown += f"- **主要框架**: {', '.join(self.main_frameworks)}\n"

            if self.build_tools:
                markdown += f"- **构建工具**: {', '.join(self.build_tools)}\n"

            markdown += "\n"

        # 关键依赖
        if self.key_dependencies:
            markdown += "#### 关键依赖\n\n"
            markdown += "| 依赖名称 | 用途 |\n"
            markdown += "| -------- | ---- |\n"
            for dep_name, purpose in self.key_dependencies.items():
                markdown += f"| {dep_name} | {purpose} |\n"
            markdown += "\n"

        # 安全和许可证信息
        if self.vulnerable_dependencies or self.license_types:
            markdown += "#### 安全和许可证\n\n"

            if self.vulnerable_dependencies:
                markdown += f"- **安全漏洞**: {self.vulnerable_dependencies} 个依赖存在已知漏洞\n"

            if self.license_types:
                markdown += f"- **主要许可证**: {', '.join(self.license_types)}\n"

            markdown += "\n"

        # 依赖管理
        if self.dependency_files or self.installation_commands or self.update_commands:
            markdown += "#### 依赖管理\n\n"

            if self.dependency_files:
                markdown += "**依赖文件**:\n"
                for file in self.dependency_files:
                    markdown += f"- `{file}`\n"
                markdown += "\n"

            if self.installation_commands:
                markdown += "**安装命令**:\n"
                for cmd in self.installation_commands:
                    markdown += f"```bash\n{cmd}\n```\n"
                markdown += "\n"

            if self.update_commands:
                markdown += "**更新命令**:\n"
                for cmd in self.update_commands:
                    markdown += f"```bash\n{cmd}\n```\n"
                markdown += "\n"

        return markdown

    def to_html(self) -> str:
        """
        将依赖项章节转换为HTML格式

        Returns:
            str: 格式化的HTML字符串
        """
        html = ""

        # 添加章节内容
        if self.content:
            paragraphs = self.content.split("\n\n")
            for p in paragraphs:
                if p.strip():
                    html += f"<p>{p}</p>\n"

        # 技术栈概览
        if self.primary_language or self.main_frameworks or self.build_tools:
            html += "<section class='tech-stack-overview'>\n"
            html += "<h3>技术栈概览</h3>\n"
            html += "<ul>\n"

            if self.primary_language:
                html += f"<li><strong>主要语言</strong>: {self.primary_language}</li>\n"

            if self.main_frameworks:
                html += f"<li><strong>主要框架</strong>: {', '.join(self.main_frameworks)}</li>\n"

            if self.build_tools:
                html += f"<li><strong>构建工具</strong>: {', '.join(self.build_tools)}</li>\n"

            html += "</ul>\n"
            html += "</section>\n"

        # 关键依赖
        if self.key_dependencies:
            html += "<section class='key-dependencies'>\n"
            html += "<h3>关键依赖</h3>\n"
            html += "<table>\n"
            html += "<thead>\n"
            html += "<tr>\n"
            html += "<th>依赖名称</th>\n"
            html += "<th>用途</th>\n"
            html += "</tr>\n"
            html += "</thead>\n"
            html += "<tbody>\n"
            for dep_name, purpose in self.key_dependencies.items():
                html += "<tr>\n"
                html += f"<td>{dep_name}</td>\n"
                html += f"<td>{purpose}</td>\n"
                html += "</tr>\n"
            html += "</tbody>\n"
            html += "</table>\n"
            html += "</section>\n"

        # 安全和许可证信息
        if self.vulnerable_dependencies or self.license_types:
            html += "<section class='security-license'>\n"
            html += "<h3>安全和许可证</h3>\n"
            html += "<ul>\n"

            if self.vulnerable_dependencies:
                html += f"<li><strong>安全漏洞</strong>: {self.vulnerable_dependencies} 个依赖存在已知漏洞</li>\n"

            if self.license_types:
                html += f"<li><strong>主要许可证</strong>: {', '.join(self.license_types)}</li>\n"

            html += "</ul>\n"
            html += "</section>\n"

        # 依赖管理
        if self.dependency_files or self.installation_commands or self.update_commands:
            html += "<section class='dependency-management'>\n"
            html += "<h3>依赖管理</h3>\n"

            if self.dependency_files:
                html += "<div class='dependency-files'>\n"
                html += "<h4>依赖文件</h4>\n"
                html += "<ul>\n"
                for file in self.dependency_files:
                    html += f"<li><code>{file}</code></li>\n"
                html += "</ul>\n"
                html += "</div>\n"

            if self.installation_commands:
                html += "<div class='installation-commands'>\n"
                html += "<h4>安装命令</h4>\n"
                for cmd in self.installation_commands:
                    html += f"<pre><code class='language-bash'>{cmd}</code></pre>\n"
                html += "</div>\n"

            if self.update_commands:
                html += "<div class='update-commands'>\n"
                html += "<h4>更新命令</h4>\n"
                for cmd in self.update_commands:
                    html += f"<pre><code class='language-bash'>{cmd}</code></pre>\n"
                html += "</div>\n"

            html += "</section>\n"

        return html

class ContributionSection(ReadmeSection):
    """贡献指南章节"""
    title: str = Field(default="贡献指南", description="章节标题")
    guidelines: List[str] = Field(default_factory=list, description="贡献指南列表")
    code_of_conduct_url: str = Field(default="", description="行为准则URL")

    def to_markdown(self) -> str:
        """将贡献指南章节转换为Markdown格式"""
        markdown = ""

        if self.content:
            markdown += f"{self.content}\n\n"

        for guideline in self.guidelines:
            markdown += f"- {guideline}\n"
        markdown += "\n"

        if self.code_of_conduct_url:
            markdown += f"请查看我们的[行为准则]({self.code_of_conduct_url})了解更多信息。\n\n"

        return markdown

    def to_html(self) -> str:
        """
        将贡献指南章节转换为HTML格式

        Returns:
            str: 格式化的HTML字符串
        """
        html = ""

        # 添加章节内容
        if self.content:
            paragraphs = self.content.split("\n\n")
            for p in paragraphs:
                if p.strip():
                    html += f"<p>{p}</p>\n"

        # 添加贡献指南列表
        if self.guidelines:
            html += "<div class='contribution-guidelines'>\n"
            html += "<ul>\n"
            for guideline in self.guidelines:
                html += f"<li>{guideline}</li>\n"
            html += "</ul>\n"
            html += "</div>\n"

        # 添加行为准则链接
        if self.code_of_conduct_url:
            html += f"<p>请查看我们的<a href='{self.code_of_conduct_url}'>行为准则</a>了解更多信息。</p>\n"

        return html

class ArchitectureSection(ReadmeSection):
    """架构章节"""
    title: str = Field(default="架构", description="章节标题")
    overview: str = Field(default="", description="架构概述")
    components: List[Component] = Field(default_factory=list, description="组件列表")
    relationships: List[Relationship] = Field(default_factory=list, description="关系列表")
    diagram_url: str = Field(default="", description="架构图URL")

    def to_markdown(self) -> str:
        """将架构章节转换为Markdown格式"""
        markdown = ""

        if self.overview:
            markdown += f"{self.overview}\n\n"

        if self.content:
            markdown += f"{self.content}\n\n"

        if self.diagram_url:
            markdown += f"![架构图]({self.diagram_url})\n\n"

        if self.components:
            markdown += "#### 主要组件\n\n"
            for component in self.components:
                markdown += f"##### {component.name}\n\n"
                if component.description:
                    markdown += f"{component.description}\n\n"
                if component.responsibility:
                    markdown += f"**职责**: {component.responsibility}\n\n"

        if self.relationships:
            markdown += "#### 组件关系\n\n"
            markdown += "| 源组件 | 关系 | 目标组件 | 描述 |\n"
            markdown += "| ------ | ---- | ------ | ---- |\n"
            for rel in self.relationships:
                markdown += f"| {rel.source} | {rel.type} | {rel.target} | {rel.description} |\n"
            markdown += "\n"

        return markdown

    def to_html(self) -> str:
        """
        将架构章节转换为HTML格式

        Returns:
            str: 格式化的HTML字符串
        """
        html = ""

        # 添加架构概述
        if self.overview:
            html += f"<div class='architecture-overview'>\n"
            html += f"<p>{self.overview}</p>\n"
            html += "</div>\n"

        # 添加章节内容
        if self.content:
            paragraphs = self.content.split("\n\n")
            for p in paragraphs:
                if p.strip():
                    html += f"<p>{p}</p>\n"

        # 添加架构图
        if self.diagram_url:
            html += "<div class='architecture-diagram'>\n"
            html += f"<img src='{self.diagram_url}' alt='架构图' class='img-fluid'>\n"
            html += "</div>\n"

        # 添加组件信息
        if self.components:
            html += "<div class='architecture-components'>\n"
            html += "<h3>主要组件</h3>\n"

            for component in self.components:
                html += "<div class='component'>\n"
                html += f"<h4>{component.name}</h4>\n"

                if component.description:
                    html += f"<p>{component.description}</p>\n"

                if component.responsibility:
                    html += f"<p><strong>职责</strong>: {component.responsibility}</p>\n"

                html += "</div>\n"

            html += "</div>\n"

        # 添加组件关系
        if self.relationships:
            html += "<div class='component-relationships'>\n"
            html += "<h3>组件关系</h3>\n"
            html += "<table>\n"
            html += "<thead>\n"
            html += "<tr>\n"
            html += "<th>源组件</th>\n"
            html += "<th>关系</th>\n"
            html += "<th>目标组件</th>\n"
            html += "<th>描述</th>\n"
            html += "</tr>\n"
            html += "</thead>\n"
            html += "<tbody>\n"

            for rel in self.relationships:
                html += "<tr>\n"
                html += f"<td>{rel.source}</td>\n"
                html += f"<td>{rel.type}</td>\n"
                html += f"<td>{rel.target}</td>\n"
                html += f"<td>{rel.description}</td>\n"
                html += "</tr>\n"

            html += "</tbody>\n"
            html += "</table>\n"
            html += "</div>\n"

        return html

class LicenseSection(ReadmeSection):
    """许可证章节"""
    title: str = Field(default="许可证", description="章节标题")
    
    # 许可证概述
    license_type: str = Field(default="", description="许可证类型")
    license_url: str = Field(default="", description="许可证URL")
    license_summary: str = Field(default="", description="许可证性质的简要说明，如开源、商业、专有等")
    license_version: str = Field(default="", description="许可证版本")
    
    # 许可证详情
    license_terms: List[str] = Field(default_factory=list, description="许可证的主要条款和条件")
    license_rights: List[str] = Field(default_factory=list, description="用户可以做什么（权利）")
    license_restrictions: List[str] = Field(default_factory=list, description="用户不能做什么（限制）")
    license_obligations: List[str] = Field(default_factory=list, description="用户必须做什么（义务）")
    
    # 许可证徽章
    license_badge_code: str = Field(default="", description="适合在README中使用的许可证徽章代码")
    license_full_text_url: str = Field(default="", description="指向许可证全文的链接")
    
    # 依赖许可证
    dependency_licenses: List[DependencyLicense] = Field(default_factory=list, description="主要依赖项的许可证信息")
    license_compatibility: str = Field(default="", description="依赖许可证与项目主许可证的兼容性说明")
    license_issues: List[str] = Field(default_factory=list, description="需要特别注意的许可证问题")
    
    # 商业使用说明
    commercial_use_terms: str = Field(default="", description="商业使用的条件")
    commercial_restrictions: List[str] = Field(default_factory=list, description="需要特别注意的商业限制")
    
    # 贡献者许可协议
    contributor_agreement: str = Field(default="", description="贡献者需要了解的许可证信息")
    copyright_handling: str = Field(default="", description="如何处理贡献者的版权")
    
    # 第三方内容
    third_party_content: List[Dict[str, str]] = Field(default_factory=list, description="项目中使用的第三方内容的许可证情况")
    attribution_info: List[str] = Field(default_factory=list, description="必要的归属信息")

    def to_markdown(self) -> str:
        """将许可证章节转换为Markdown格式"""
        markdown = ""

        # 添加自定义内容
        if self.content:
            markdown += f"{self.content}\n\n"

        # 添加许可证徽章
        if self.license_badge_code:
            markdown += f"{self.license_badge_code}\n\n"

        # 添加许可证概述
        if self.license_type:
            markdown += "#### 许可证概述\n\n"

            license_text = f"本项目采用 **{self.license_type}**"
            if self.license_version:
                license_text += f" {self.license_version}"
            if self.license_url:
                license_text += f" [{'许可证全文'}]({self.license_url})"
            if self.license_summary:
                license_text += f"\u3002{self.license_summary}"
            else:
                license_text += "\u3002"

            markdown += f"{license_text}\n\n"

            if self.license_full_text_url and self.license_full_text_url != self.license_url:
                markdown += f"[查看许可证全文]({self.license_full_text_url})\n\n"

        # 添加许可证详情
        if any([self.license_terms, self.license_rights, self.license_restrictions, self.license_obligations]):
            markdown += "#### 许可证详情\n\n"

            if self.license_terms:
                markdown += "##### 主要条款\n\n"
                for term in self.license_terms:
                    markdown += f"- {term}\n"
                markdown += "\n"

            if self.license_rights:
                markdown += "##### 用户权利\n\n"
                for right in self.license_rights:
                    markdown += f"- {right}\n"
                markdown += "\n"

            if self.license_restrictions:
                markdown += "##### 使用限制\n\n"
                for restriction in self.license_restrictions:
                    markdown += f"- {restriction}\n"
                markdown += "\n"

            if self.license_obligations:
                markdown += "##### 用户义务\n\n"
                for obligation in self.license_obligations:
                    markdown += f"- {obligation}\n"
                markdown += "\n"

        # 添加依赖许可证信息
        if any([self.dependency_licenses, self.license_compatibility, self.license_issues]):
            markdown += "#### 依赖许可证\n\n"

            if self.dependency_licenses:
                markdown += "##### 主要依赖项许可证\n\n"
                markdown += "| 依赖项 | 许可证类型 | 版本 | 说明 | 兼容性 |\n"
                markdown += "| --- | --- | --- | --- | --- |\n"
                for dep in self.dependency_licenses:
                    name_text = dep.name
                    license_text = dep.license_type
                    # 如果有URL，添加链接
                    if dep.url:
                        license_text = f"[{dep.license_type}]({dep.url})"
                    markdown += f"| {name_text} | {license_text} | {dep.version} | {dep.description} | {dep.compatibility} |\n"
                markdown += "\n"

            if self.license_compatibility:
                markdown += "##### 许可证兼容性\n\n"
                markdown += f"{self.license_compatibility}\n\n"

            if self.license_issues:
                markdown += "##### 需要注意的许可证问题\n\n"
                for issue in self.license_issues:
                    markdown += f"- {issue}\n"
                markdown += "\n"

        # 添加商业使用说明
        if self.commercial_use_terms or self.commercial_restrictions:
            markdown += "#### 商业使用说明\n\n"

            if self.commercial_use_terms:
                markdown += f"{self.commercial_use_terms}\n\n"

            if self.commercial_restrictions:
                markdown += "##### 商业限制\n\n"
                for restriction in self.commercial_restrictions:
                    markdown += f"- {restriction}\n"
                markdown += "\n"

        # 添加贡献者许可协议
        if self.contributor_agreement or self.copyright_handling:
            markdown += "#### 贡献者许可协议\n\n"

            if self.contributor_agreement:
                markdown += f"{self.contributor_agreement}\n\n"

            if self.copyright_handling:
                markdown += f"**版权处理:** {self.copyright_handling}\n\n"

        # 添加第三方内容
        if self.third_party_content or self.attribution_info:
            markdown += "#### 第三方内容\n\n"

            if self.third_party_content:
                markdown += "##### 第三方许可证\n\n"
                markdown += "| 内容 | 许可证 | 说明 |\n"
                markdown += "| --- | --- | --- |\n"
                for content in self.third_party_content:
                    name = content.get("name", "")
                    license_type = content.get("license", "")
                    description = content.get("description", "")
                    markdown += f"| {name} | {license_type} | {description} |\n"
                markdown += "\n"

            if self.attribution_info:
                markdown += "##### 归属信息\n\n"
                for info in self.attribution_info:
                    markdown += f"{info}\n\n"

        return markdown

    def to_html(self) -> str:
        """
        将许可证章节转换为HTML格式

        Returns:
            str: 格式化的HTML字符串
        """
        html = ""

        # 添加自定义内容
        if self.content:
            paragraphs = self.content.split("\n\n")
            for p in paragraphs:
                if p.strip():
                    html += f"<p>{p}</p>\n"

        # 添加许可证徽章
        if self.license_badge_code:
            html += "<div class='license-badge'>\n"
            html += f"{self.license_badge_code}\n"
            html += "</div>\n"

        # 添加许可证概述
        if self.license_type:
            html += "<section class='license-overview'>\n"
            html += "<h3>许可证概述</h3>\n"

            license_text = f"本项目采用 <strong>{self.license_type}</strong>"
            if self.license_version:
                license_text += f" {self.license_version}"
            if self.license_url:
                license_text += f" <a href='{self.license_url}'>许可证全文</a>"
            if self.license_summary:
                license_text += f"\u3002{self.license_summary}"
            else:
                license_text += "\u3002"

            html += f"<p>{license_text}</p>\n"

            if self.license_full_text_url and self.license_full_text_url != self.license_url:
                html += f"<p><a href='{self.license_full_text_url}'>查看许可证全文</a></p>\n"

            html += "</section>\n"

        # 添加许可证详情
        if any([self.license_terms, self.license_rights, self.license_restrictions, self.license_obligations]):
            html += "<section class='license-details'>\n"
            html += "<h3>许可证详情</h3>\n"

            if self.license_terms:
                html += "<div class='license-terms'>\n"
                html += "<h4>主要条款</h4>\n"
                html += "<ul>\n"
                for term in self.license_terms:
                    html += f"<li>{term}</li>\n"
                html += "</ul>\n"
                html += "</div>\n"

            if self.license_rights:
                html += "<div class='license-rights'>\n"
                html += "<h4>用户权利</h4>\n"
                html += "<ul>\n"
                for right in self.license_rights:
                    html += f"<li>{right}</li>\n"
                html += "</ul>\n"
                html += "</div>\n"

            if self.license_restrictions:
                html += "<div class='license-restrictions'>\n"
                html += "<h4>使用限制</h4>\n"
                html += "<ul>\n"
                for restriction in self.license_restrictions:
                    html += f"<li>{restriction}</li>\n"
                html += "</ul>\n"
                html += "</div>\n"

            if self.license_obligations:
                html += "<div class='license-obligations'>\n"
                html += "<h4>用户义务</h4>\n"
                html += "<ul>\n"
                for obligation in self.license_obligations:
                    html += f"<li>{obligation}</li>\n"
                html += "</ul>\n"
                html += "</div>\n"

            html += "</section>\n"

        # 添加依赖许可证信息
        if any([self.dependency_licenses, self.license_compatibility, self.license_issues]):
            html += "<section class='dependency-licenses'>\n"
            html += "<h3>依赖许可证</h3>\n"

            if self.dependency_licenses:
                html += "<div class='dependency-license-list'>\n"
                html += "<h4>主要依赖项许可证</h4>\n"
                html += "<table>\n"
                html += "<thead>\n"
                html += "<tr>\n"
                html += "<th>依赖项</th>\n"
                html += "<th>许可证类型</th>\n"
                html += "<th>版本</th>\n"
                html += "<th>说明</th>\n"
                html += "<th>兼容性</th>\n"
                html += "</tr>\n"
                html += "</thead>\n"
                html += "<tbody>\n"
                for dep in self.dependency_licenses:
                    name_text = dep.name
                    license_text = dep.license_type
                    # 如果有URL，添加链接
                    if dep.url:
                        license_text = f"<a href='{dep.url}'>{dep.license_type}</a>"
                    html += "<tr>\n"
                    html += f"<td>{name_text}</td>\n"
                    html += f"<td>{license_text}</td>\n"
                    html += f"<td>{dep.version}</td>\n"
                    html += f"<td>{dep.description}</td>\n"
                    html += f"<td>{dep.compatibility}</td>\n"
                    html += "</tr>\n"
                html += "</tbody>\n"
                html += "</table>\n"
                html += "</div>\n"

            if self.license_compatibility:
                html += "<div class='license-compatibility'>\n"
                html += "<h4>许可证兼容性</h4>\n"
                html += f"<p>{self.license_compatibility}</p>\n"
                html += "</div>\n"

            if self.license_issues:
                html += "<div class='license-issues'>\n"
                html += "<h4>需要注意的许可证问题</h4>\n"
                html += "<ul>\n"
                for issue in self.license_issues:
                    html += f"<li>{issue}</li>\n"
                html += "</ul>\n"
                html += "</div>\n"

            html += "</section>\n"

        # 添加商业使用说明
        if self.commercial_use_terms or self.commercial_restrictions:
            html += "<section class='commercial-use'>\n"
            html += "<h3>商业使用说明</h3>\n"

            if self.commercial_use_terms:
                html += f"<p>{self.commercial_use_terms}</p>\n"

            if self.commercial_restrictions:
                html += "<div class='commercial-restrictions'>\n"
                html += "<h4>商业限制</h4>\n"
                html += "<ul>\n"
                for restriction in self.commercial_restrictions:
                    html += f"<li>{restriction}</li>\n"
                html += "</ul>\n"
                html += "</div>\n"

            html += "</section>\n"

        # 添加贡献者许可协议
        if self.contributor_agreement or self.copyright_handling:
            html += "<section class='contributor-agreement'>\n"
            html += "<h3>贡献者许可协议</h3>\n"

            if self.contributor_agreement:
                html += f"<p>{self.contributor_agreement}</p>\n"

            if self.copyright_handling:
                html += f"<p><strong>版权处理:</strong> {self.copyright_handling}</p>\n"

            html += "</section>\n"

        # 添加第三方内容
        if self.third_party_content or self.attribution_info:
            html += "<section class='third-party-content'>\n"
            html += "<h3>第三方内容</h3>\n"

            if self.third_party_content:
                html += "<div class='third-party-licenses'>\n"
                html += "<h4>第三方许可证</h4>\n"
                html += "<table>\n"
                html += "<thead>\n"
                html += "<tr>\n"
                html += "<th>内容</th>\n"
                html += "<th>许可证</th>\n"
                html += "<th>说明</th>\n"
                html += "</tr>\n"
                html += "</thead>\n"
                html += "<tbody>\n"
                for content in self.third_party_content:
                    name = content.get("name", "")
                    license_type = content.get("license", "")
                    description = content.get("description", "")
                    html += "<tr>\n"
                    html += f"<td>{name}</td>\n"
                    html += f"<td>{license_type}</td>\n"
                    html += f"<td>{description}</td>\n"
                    html += "</tr>\n"
                html += "</tbody>\n"
                html += "</table>\n"
                html += "</div>\n"

            if self.attribution_info:
                html += "<div class='attribution-info'>\n"
                html += "<h4>归属信息</h4>\n"
                for info in self.attribution_info:
                    html += f"<p>{info}</p>\n"
                html += "</div>\n"

            html += "</section>\n"

        return html

class ReadmeDocument(BaseModel):
    """完整的README文档模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    project_id: str = Field(default="", description="关联的项目ID")
    sections: Dict[str, ReadmeSection] = Field(default_factory=dict, description="文档章节，键为章节类型")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    def add_section(self, section_type: str, section: ReadmeSection) -> None:
        """添加章节"""
        self.sections[section_type] = section
        self.updated_at = datetime.now(timezone.utc)

    def remove_section(self, section_type: str) -> None:
        """移除章节"""
        if section_type in self.sections:
            del self.sections[section_type]
            self.updated_at = datetime.now(timezone.utc)

    def to_markdown(self) -> str:
        """将整个文档转换为Markdown格式"""
        # 按照章节顺序排序
        ordered_sections = sorted(
            self.sections.values(),
            key=lambda s: s.order
        )

        markdown = ""
        for section in ordered_sections:
            markdown += section.to_markdown() + "\n"

        return markdown

    def to_html(self) -> str:
        """
        将整个文档转换为HTML格式

        Returns:
            str: 格式化的HTML字符串，包含完整的HTML文档结构
        """
        # 按照章节顺序排序
        ordered_sections = sorted(
            self.sections.values(),
            key=lambda s: s.order
        )

        # 创建HTML文档头部
        html = "<!DOCTYPE html>\n"
        html += "<html lang='zh-CN'>\n"
        html += "<head>\n"
        html += "    <meta charset='UTF-8'>\n"
        html += "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n"

        # 如果有介绍章节，使用项目名称作为标题
        title = "项目文档"
        for section in ordered_sections:
            if isinstance(section, IntroductionSection) and section.project_name:
                title = section.project_name
                break

        html += f"    <title>{title}</title>\n"
        html += "    <style>\n"
        html += "        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size: 14px; line-height: 1.6; color: #333; max-width: 1200px; margin: 0 auto; padding: 20px; }\n"
        html += "        h1, h2, h3, h4 { margin-top: 1.5em; margin-bottom: 0.5em; }\n"
        html += "        h1 { color: #2c3e50; font-size: 24px; }\n"
        html += "        h2 { color: #3498db; font-size: 20px; border-bottom: 1px solid #eee; padding-bottom: 0.3em; }\n"
        html += "        h3 { color: #2980b9; font-size: 18px; }\n"
        html += "        h4 { color: #34495e; font-size: 16px; }\n"
        html += "        p { margin-bottom: 1em; font-size: 14px; }\n"
        html += "        code { background-color: #f8f8f8; padding: 0.2em 0.4em; border-radius: 3px; font-family: Consolas, Monaco, 'Andale Mono', monospace; font-size: 13px; }\n"
        html += "        pre { background-color: #f8f8f8; padding: 1em; border-radius: 5px; overflow-x: auto; font-size: 13px; }\n"
        html += "        pre code { background-color: transparent; padding: 0; font-size: 13px; }\n"
        html += "        table { border-collapse: collapse; width: 100%; margin-bottom: 1em; font-size: 14px; }\n"
        html += "        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 14px; }\n"
        html += "        th { background-color: #f2f2f2; font-weight: 600; }\n"
        html += "        img { max-width: 100%; height: auto; }\n"
        html += "        .badges { margin-bottom: 1em; }\n"
        html += "        .badge { margin-right: 0.5em; }\n"
        html += "        .feature, .component { margin-bottom: 1.5em; }\n"
        html += "        .api-endpoint { margin-bottom: 2em; padding-bottom: 1em; border-bottom: 1px dashed #eee; }\n"
        html += "        .api-method { display: inline-block; padding: 0.3em 0.6em; margin-bottom: 0.5em; background-color: #e7f4ff; border-radius: 3px; font-size: 13px; font-family: monospace; }\n"
        html += "        .slogan { font-size: 16px; font-style: italic; color: #666; }\n"
        html += "        .description { font-size: 14px; }\n"
        html += "        .tag { display: inline-block; background-color: #f1f1f1; padding: 0.2em 0.5em; margin: 0.1em; border-radius: 3px; font-size: 12px; }\n"
        html += "        ul, ol { font-size: 14px; }\n"
        html += "        li { margin-bottom: 0.3em; }\n"
        html += "        footer { margin-top: 2em; padding-top: 1em; border-top: 1px solid #eee; font-size: 12px; color: #666; }\n"
        html += "        /* 响应式字体设置 */\n"
        html += "        @media (max-width: 768px) {\n"
        html += "            body { font-size: 13px; padding: 15px; }\n"
        html += "            h1 { font-size: 22px; }\n"
        html += "            h2 { font-size: 18px; }\n"
        html += "            h3 { font-size: 16px; }\n"
        html += "            h4 { font-size: 15px; }\n"
        html += "            p, table, ul, ol { font-size: 13px; }\n"
        html += "            code, pre, pre code { font-size: 12px; }\n"
        html += "        }\n"
        html += "        @media (max-width: 480px) {\n"
        html += "            body { font-size: 12px; padding: 10px; }\n"
        html += "            h1 { font-size: 20px; }\n"
        html += "            h2 { font-size: 17px; }\n"
        html += "            h3 { font-size: 15px; }\n"
        html += "            h4 { font-size: 14px; }\n"
        html += "            p, table, ul, ol { font-size: 12px; }\n"
        html += "            code, pre, pre code { font-size: 11px; }\n"
        html += "        }\n"
        html += "    </style>\n"
        html += "</head>\n"
        html += "<body>\n"

        # 添加各个章节的HTML内容
        for section in ordered_sections:
            html += "<section class='readme-section'>\n"
            html += section.to_html()
            html += "</section>\n\n"

        # 添加文档底部信息
        html += "    <footer>\n"
        html += f"        <p><small>文档生成时间: {self.updated_at.strftime('%Y-%m-%d %H:%M:%S')}</small></p>\n"
        html += "    </footer>\n"
        html += "</body>\n"
        html += "</html>"

        return html
