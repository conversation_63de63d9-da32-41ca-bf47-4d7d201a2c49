"""
统一智能体模块
提供智能体的基本接口、数据模型及LangChain集成实现
"""
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple, Union, TypeVar, Generic
from unittest import result
import uuid
import logging
from abc import ABC, abstractmethod

from pydantic import BaseModel, Field

# LangChain导入
from langchain.chains.base import Chain
from langchain_core.runnables import Runnable, RunnableConfig
from langchain_core.prompts import ChatPromptTemplate, PromptTemplate
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_core.output_parsers import StrOutputParser, JsonOutputParser
from langchain_openai import ChatOpenAI

from .models import AgentInput, AgentOutput, Response
from ..reasoning.model_config_manager import model_config_manager

logger = logging.getLogger(__name__)


class BaseAgent(ABC):
    """统一智能体接口"""
    
    def __init__(self):
        """
        初始化智能体
        
        Args:
            config: 配置参数
        """
        self.name = self.__class__.__name__
        self.initialized = False
        self.last_error = None
        
        # LangChain相关属性
        self.chain = None
        self.llm = None
    
    async def initialize(self) -> bool:
        """
        初始化智能体
        
        Returns:
            bool: 是否初始化成功
        """
        try:            
            
            # 创建LLM客户端
            await self._create_llm()
            
            # 执行其他初始化逻辑
            await self._initialize()
            # 创建处理链
            await self._create_chain()
            self.initialized = True
            return True
        except Exception as e:
            logger.error(f"初始化智能体失败: {str(e)}")
            self.last_error = str(e)
            return False
    
    async def _initialize(self) -> None:
        """初始化具体实现，子类可覆盖"""
        pass
    
    async def _create_llm(self) -> None:
        """
        创建语言模型
        默认实现，子类可以覆盖以使用特定的LLM
        """
        try:
            # 从模型配置管理器加载配置
            model_config = model_config_manager.get_model_config(self.name)
            
            self.llm = ChatOpenAI(
                model_name=model_config.name,
                api_key=model_config.api_key,
                base_url=model_config.url,
                max_tokens=model_config.max_tokens,
                temperature=model_config.temperature,
                streaming=model_config.streaming,
                timeout=model_config.request_timeout,
                max_retries=model_config.max_retries,
                **model_config.additional_params
            )
            logger.info(f"使用自定义模型配置为 {self.name} 创建LLM实例: {model_config.name}")
        except Exception as e:
            logger.error(f"创建LLM失败: {str(e)}")
            raise
    
    @abstractmethod
    async def _create_chain(self) -> None:
        """
        创建处理链
        子类可实现此方法创建自定义处理链
        """
        pass
    
    async def prepare_input(self, input_data: Dict[str, Any]) -> AgentInput:
        """
        将原始输入数据转换为标准化的AgentInput对象
        
        Args:
            input_data: 原始输入数据，通常是一个字典
            
        Returns:
            AgentInput: 格式化后的输入数据对象
        
        示例:
            input_data = {
                "parameters": {
                    "module_name": "example_module",
                    "module_path": "/path/to/module",
                    "analyze_functions": True
                }
            }
            agent_input = await agent.prepare_input(input_data)
        """
        try:
            # 提取参数，如果不存在则使用空字典
            parameters = input_data.get("parameters", {})
            
            # 提取上下文，如果不存在则使用空字典
            context = input_data.get("context", {})
            
            # 生成请求ID
            request_id = input_data.get("request_id", str(uuid.uuid4()))
            
            # 创建AgentInput对象
            agent_input = AgentInput(
                request_id=request_id,
                parameters=parameters,
                context=context,
                timestamp=datetime.now(timezone.utc)
            )
            
            logger.debug(f"已准备智能体输入: {agent_input.request_id}")
            return agent_input
            
        except Exception as e:
            logger.error(f"准备输入数据失败: {str(e)}")
            raise RuntimeError(f"准备输入数据失败: {str(e)}")
    
    async def process(self, input_data: AgentInput) -> AgentOutput:
        """
        处理输入
        
        Args:
            input_data: 输入数据
            
        Returns:
            AgentOutput: 输出结果
        """
        if not self.initialized:
            try:
                await self.initialize()
            except Exception as e:
                raise RuntimeError(f"初始化失败: {str(e)}")
        
        # 更新上下文
        context = input_data.context
        
        # 处理输入
        try:
            # 处理逻辑
            result, metadata = await self._process(input_data)
            
            has_error = "error" in metadata
            error_message = metadata.get("error", "")
            # 构造输出
            return AgentOutput(
                response=Response(
                    has_error=has_error,
                    error_message=error_message,
                    result=result,
                    metadata=metadata
                ),
                context=context,
                metadata=metadata
            )
        except Exception as e:
            logger.error(f"处理请求失败: {str(e)}")
            self.last_error = str(e)
            raise
    
    async def _process(
        self, 
        input_data: AgentInput
    ) -> Tuple[Any, Dict[str, Any]]:
        """
        处理具体实现
        
        Args:
            input_data: 输入数据
            
        Returns:
            Tuple[Any, Dict[str, Any]]: (响应内容, 元数据)
        """
        # 默认实现，子类可覆盖
        return f"收到参数: {input_data.parameters}", {"processed_by": self.name}
    
    async def shutdown(self) -> None:
        """关闭智能体"""
        try:
            # 释放LangChain资源
            if self.chain:
                self.chain = None
                self.llm = None
            
            # 执行其他关闭逻辑
            await self._shutdown()
            
            logger.info(f"{self.name} 智能体已关闭")
        except Exception as e:
            logger.error(f"关闭智能体失败: {str(e)}")
            self.last_error = str(e)
    
    async def _shutdown(self) -> None:
        """关闭具体实现，子类可覆盖"""
        pass
