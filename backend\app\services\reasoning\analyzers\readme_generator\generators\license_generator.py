#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
许可证章节生成器
"""
import logging
import os
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone
from pathlib import Path

from langchain.chains import <PERSON><PERSON>hain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser

from . import Generator
from ..section_models import LicenseSection
from .....ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser
from ......utils.retry import retry_async
from ......utils.file_utils import read_file_content

logger = logging.getLogger(__name__)


class LicenseGenerator(Generator):
    """
    许可证章节生成器
    负责生成README文档的许可证章节
    """

    def __init__(self, llm: ChatOpenAI):
        """初始化许可证章节生成器"""
        super().__init__(llm)

    async def create_chain(self) -> None:
        """初始化处理链"""
        try:
            # 许可证章节生成提示模板
            license_template = """
            作为专业的开源许可证专家和法律合规顾问，请基于项目分析数据生成准确、清晰且法律合规的许可证章节文档。

            ## 项目基本信息
            项目名称: {{ project_name }}
            项目路径: {{ project_path }}

            {%- if license_files %}

            ## 项目许可证文档
            {%- for file_path, content in license_files.items() %}
            ### {{ file_path }}:
            ```
            {{ content }}
            ```
            {%- endfor %}
            {%- endif %}

            {%- if dependency_analysis and dependency_analysis.external_dependencies %}

            ## 依赖许可证信息

            ### 外部依赖许可证:
            {%- for dep in dependency_analysis.external_dependencies[:10] %}
            {%- if dep.license and dep.is_direct %}
            - **{{ dep.name }}** {%- if dep.version %} {{ dep.version }}{%- endif %}: {{ dep.license }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}

            {%- if structure_analysis and structure_analysis.docs %}

            ## 文档结构分析

            ### 许可证相关文档:
            {%- for doc in structure_analysis.docs[:5] %}
            {%- if doc.doc_category == 'license' or 'license' in doc.name.lower() or 'licence' in doc.name.lower() or 'copyright' in doc.name.lower() %}
            - **{{ doc.name }}** ({{ doc.doc_format }}): {{ doc.path }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}

            {%- if module_merger_analyses %}

            ## 许可证合规洞察
            {%- for group_key, merger_result in module_merger_analyses.items() %}
            {%- if merger_result.ai_analysis and merger_result.ai_analysis.ai_findings %}

            ### {{ group_key }} 许可证分析:
            {%- for finding in merger_result.ai_analysis.ai_findings[:2] %}
            {%- if "license" in finding.title.lower() or "许可" in finding.title.lower() or "legal" in finding.title.lower() or "法律" in finding.title.lower() or "copyright" in finding.title.lower() or "版权" in finding.title.lower() %}
            - **{{ finding.title }}**: {{ finding.description }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}
            {%- endfor %}
            {%- endif %}

            ## 许可证章节生成要求与原则

            ### 核心生成原则
            1. **法律准确性**：确保所有许可证信息准确无误，避免可能的法律风险和误导
            2. **基于实际文档**：内容必须基于项目中实际存在的许可证文件，忠实反映其内容
            3. **专业通俗并重**：使用专业而准确的术语，同时确保非法律专业人士也能理解
            4. **客观中立**：保持客观中立，不对特定许可证表达偏好或建议
            5. **合规指导**：提供清晰的合规指导，帮助用户理解权利、义务和限制
            6. **文档忠实性**：如果发现许可证文档，请严格按照其内容生成，不得随意修改或添加

            ### 内容生成任务
            请生成一个专业、准确且用户友好的许可证章节，包含以下7个核心要素：

            1. **许可证概述**（80-100字）：明确说明项目使用的许可证类型、简要解释该许可证的性质（如开源、商业、专有等）、提供许可证的官方名称和版本
            2. **许可证详情**：许可证的主要条款和条件、用户可以做什么（权利）、用户不能做什么（限制）、用户必须做什么（义务）
            3. **许可证徽章**：提供适合在README中使用的许可证徽章代码、包含指向许可证全文的链接
            4. **依赖许可证**（如适用）：列出主要依赖项的许可证类型、说明这些许可证与项目主许可证的兼容性、提供任何需要特别注意的许可证问题
            5. **商业使用说明**（如适用）：明确说明商业使用的条件、解释任何需要特别注意的商业限制
            6. **贡献者许可协议**（如适用）：说明贡献者需要了解的许可证信息、解释如何处理贡献者的版权
            7. **第三方内容**（如适用）：说明项目中使用的任何第三方内容的许可证情况、提供必要的归属信息

            ### 输出语言要求
            所有生成的许可证章节文档内容必须使用中文(简体中文)输出。包括许可证概述、许可证详情、商业使用说明等内容都必须是中文。只有技术名词、许可证名称和专有名词可保持原样。

            ### 输出质量要求
            1. **法律准确性**：确保许可证信息的准确性，避免误导用户或产生法律风险
            2. **实际数据驱动**：内容必须基于提供的实际项目数据，特别是许可证文档内容
            3. **文档忠实性**：如果项目中有现有的许可证文件，请优先使用其中的信息，严格按照内容生成
            4. **专业性与可读性**：使用专业而准确的术语，但同时确保非法律专业人士也能理解
            5. **客观中立性**：保持客观中立，不要对特定许可证表达偏好或推荐建议
            6. **合规指导性**：生成的许可证章节应该帮助用户清楚地了解他们在使用、修改和分发项目时的权利和义务

            {{ format_instructions }}
            """

            # 创建Parser
            license_parser = AnalysisOutputParser(pydantic_object=LicenseSection)
            parser = OutputFixingParser.from_llm(parser=license_parser, llm=self.llm)

            # 获取格式指令
            license_format_instructions = license_parser.get_format_instructions()

            # 创建提示模板
            license_prompt = PromptTemplate.from_template(
                license_template,
                template_format="jinja2"
            ).partial(format_instructions=license_format_instructions)

            # 创建处理链
            self.chain = (
                license_prompt
                | self.llm
                | parser
            )

            logger.info("许可证章节生成器处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: AgentInput) -> None:
        """
        准备输入数据并设置self.input_parameters

        Args:
            input_data: 原始输入数据
        """
        # 初始化self.input_parameters，直接使用输入参数
        self.input_parameters = input_data.parameters.copy()
        
        # 读取许可证相关文档内容
        license_files = {}
        project_path = Path(self.input_parameters.get("project_path", ""))
        
        # 获取项目结构分析数据
        structure_analysis = self.input_parameters.get("structure_analysis")
        
        if structure_analysis and hasattr(structure_analysis, 'docs') and structure_analysis.docs:
            for doc in structure_analysis.docs:
                # 重点关注许可证相关的文档
                if (doc.doc_category == 'license' or 
                    'license' in doc.name.lower() or 
                    'licence' in doc.name.lower() or
                    'copying' in doc.name.lower() or
                    'copyright' in doc.name.lower()):
                    
                    full_path = project_path / doc.path
                    content = read_file_content(str(full_path.resolve()), 10000)  # 限制为10000字符
                    if content:
                        license_files[doc.path] = content
                        logger.info(f"读取许可证文档: {doc.path}")
        
        # 如果没有找到明确的许可证文档，尝试查找常见的许可证文件名
        if not license_files:
            common_license_files = [
                'LICENSE', 'LICENSE.txt', 'LICENSE.md', 'LICENSE.rst',
                'LICENCE', 'LICENCE.txt', 'LICENCE.md', 'LICENCE.rst',
                'COPYING', 'COPYING.txt', 'COPYRIGHT', 'COPYRIGHT.txt'
            ]
            
            for filename in common_license_files:
                license_path = project_path / filename
                if license_path.exists() and license_path.is_file():
                    content = read_file_content(str(license_path.resolve()), 10000)
                    if content:
                        license_files[filename] = content
                        logger.info(f"找到并读取许可证文件: {filename}")
                        break  # 找到第一个就停止
        
        # 将许可证文件内容添加到参数中
        self.input_parameters["license_files"] = license_files
        
        if license_files:
            logger.info(f"成功读取 {len(license_files)} 个许可证相关文档")
        else:
            logger.warning("未找到任何许可证相关文档")

    @retry_async(max_retries=3)
    async def process(self) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询

        Returns:
            Tuple[Any, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 调用LLM处理链
            # result = await self.chain.first.ainvoke(self.input_parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(self.input_parameters)

            # 设置章节顺序
            if result:
                result.order = 8  # 许可证章节通常是最后一个

            return result, {"generator": "license_generator"}
        except Exception as e:
            logger.error(f"许可证章节生成失败: {str(e)}")
            raise
