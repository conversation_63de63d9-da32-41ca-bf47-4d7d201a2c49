"""
关联表定义
包含所有多对多关系的关联表
"""
from sqlalchemy import Table, Column, ForeignKey
from app.models.model_base import ModelBase

# 角色-权限关联表
role_permission = Table(
    'role_permission',
    ModelBase.metadata,
    Column('role_id', ForeignKey('roles.id', ondelete='CASCADE'), primary_key=True, comment='角色ID'),
    Column('permission_id', ForeignKey('permissions.id', ondelete='CASCADE'), primary_key=True, comment='权限ID'),
    comment='角色-权限多对多关联表'
)

# 角色-权限组关联表
role_permission_group = Table(
    'role_permission_group',
    ModelBase.metadata,
    Column('role_id', ForeignKey('roles.id', ondelete='CASCADE'), primary_key=True, comment='角色ID'),
    Column('group_id', ForeignKey('permission_groups.id', ondelete='CASCADE'), primary_key=True, comment='权限组ID'),
    comment='角色-权限组多对多关联表'
)

# 用户-角色关联表
user_role = Table(
    'user_role',
    ModelBase.metadata,
    Column('user_id', ForeignKey('users.id', ondelete='CASCADE'), primary_key=True, comment='用户ID'),
    Column('role_id', ForeignKey('roles.id', ondelete='CASCADE'), primary_key=True, comment='角色ID'),
    comment='用户-角色多对多关联表'
)
