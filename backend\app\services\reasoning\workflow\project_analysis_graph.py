#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于LangGraph实现的项目分析工作流
使用专门的处理器处理每个分析步骤
"""
import os
from typing import Dict, Any
from datetime import datetime

import structlog
from langgraph.graph import StateGraph, END
from langgraph.graph.state import CompiledStateGraph

from .project_analysis_orchestrator import ProjectAnalysisOrchestrator
from .models import AnalysisConfig, AnalysisState

logger = structlog.get_logger(__name__)


class ProjectAnalysisGraph:
    """项目分析工作流图"""

    def __init__(self, config: AnalysisConfig):
        """
        初始化项目分析工作流图

        Args:
            config: 项目分析配置
        """
        self.config = config
        self.orchestrator = ProjectAnalysisOrchestrator(config)
        self.graph = self._build_graph()

    async def initialize(self):
        """初始化分析处理器"""
        await self.orchestrator.initialize()

    def _build_graph(self) -> CompiledStateGraph:
        """
        构建项目分析工作流图

        Returns:
            构建好的StateGraph
        """
        # 创建工作流状态图
        graph = StateGraph(AnalysisState)

        # 添加节点
        graph.add_node("scan_project", self.orchestrator.scan_project)
        graph.add_node("analyze_project_changes", self.orchestrator.analyze_project_changes)
        # 按分析流程顺序添加节点
        # 结构分析 --> 依赖分析 --> 架构分析 --> 模块分析 --> 模块合并 --> 模块合并分析 --> 生成readme
        graph.add_node("analyze_structure", self.orchestrator.analyze_structure)
        graph.add_node("analyze_dependencies", self.orchestrator.analyze_dependencies)
        graph.add_node("analyze_architecture", self.orchestrator.analyze_architecture)
        graph.add_node("analyze_modules", self.orchestrator.analyze_modules)
        graph.add_node("merge_modules", self.orchestrator.merge_modules)
        graph.add_node("analyze_module_mergers", self.orchestrator.analyze_module_mergers)
        graph.add_node("generate_readme", self.orchestrator.generate_readme)
        graph.add_node("validate_analysis", self.orchestrator.validate_analysis)

        # 定义工作流连接
        # 起点：扫描项目
        graph.set_entry_point("scan_project")

        # 扫描项目后进行项目变化分析
        graph.add_edge("scan_project", "analyze_project_changes")

        # 项目变化分析后进行结构分析
        graph.add_edge("analyze_project_changes", "analyze_structure")

        # 结构分析后进行依赖分析
        graph.add_edge("analyze_structure", "analyze_dependencies")

        # 依赖分析后进行架构分析
        graph.add_conditional_edges(
            "analyze_dependencies",
            self.orchestrator.should_analyze_architecture,
            {
                True: "analyze_architecture",
                False: "analyze_modules"
            }
        )

        # 架构分析后进行模块分析
        graph.add_edge("analyze_architecture", "analyze_modules")

        # 模块分析后进行模块合并
        graph.add_edge("analyze_modules", "merge_modules")

        # 模块合并后进行模块合并分析
        graph.add_edge("merge_modules", "analyze_module_mergers")

        # 模块合并分析后生成README
        graph.add_conditional_edges(
            "analyze_module_mergers",
            self.orchestrator.should_generate_readme,
            {
                True: "generate_readme",
                False: "validate_analysis"
            }
        )

        # README生成后进行分析验证
        graph.add_edge("generate_readme", "validate_analysis")

        # 分析验证是最后一步
        graph.add_edge("validate_analysis", END)

        return graph.compile()

    async def run(self, file_name: str , generated_path:str, config: AnalysisConfig) -> AnalysisState:
        """
        运行项目分析流程

        Args:
            config: 项目分析配置

        Returns:
            分析完成后的状态
        """
        # 确保处理器已初始化
        await self.initialize()

        # 准备初始状态
        initial_state = AnalysisState(
            project_path=config.project_path,
            project_name=config.project_name,
            start_time=datetime.now()
        )

        logger.info(f"开始分析项目: {config.project_name}")
        logger.info(f"项目路径: {config.project_path}")
        logger.info("分析流程: 结构分析 --> 依赖分析 --> 架构分析 --> 模块分析 --> 模块合并 --> 模块合并分析 --> 生成README")

        # 运行工作流
        result = await self.graph.ainvoke(initial_state)

        # 如果结果是字典，将其转换为AnalysisState对象
        if isinstance(result, dict):
            # 验证并转换为AnalysisState对象
            result = AnalysisState.model_validate(result)

        # 设置结束时间
        result.end_time = datetime.now()

        # for k,mod in result.module_analyses.items():
        #     path=os.path.join("modules",f"{mod.module_name}.md")
        #     if not os.path.exists("modules"):
        #         os.makedirs("modules", exist_ok=True)
        #     with open(path,"w",encoding='utf-8') as ana:
        #         ana.write(mod.to_markdown())

        # 如果README生成成功，保存到文件
        if result.readme:
            if generated_path and os.path.exists(generated_path):
                with open(os.path.join(generated_path, f"{config.project_name}_README.md"), "w", encoding='utf-8') as readme_file:
                    readme_file.write(result.readme.to_markdown())
                with open(os.path.join(generated_path, f"{config.project_name}_README.html"), "w", encoding='utf-8') as readme_file:
                    readme_file.write(result.readme.to_html())
        else:
            logger.warning("README生成失败，无法保存到文件")

        # 计算分析耗时
        duration = (result.end_time - result.start_time).total_seconds()
        logger.info(f"项目分析完成，总耗时: {duration:.2f}秒")
        logger.info(f"分析了 {len(result.completed_modules)} 个模块")

        # 检查是否有错误
        if result.errors:
            logger.warning(f"分析过程中出现 {len(result.errors)} 个错误")
            for error_key, error_msg in result.errors.items():
                logger.warning(f"错误 {error_key}: {error_msg}")

        return result


# 使用示例
async def analyze_project(file_name: str, generated_path: str,project_path: str, project_name: str = None) -> AnalysisState:
    """
    分析指定路径的项目

    Args:
        project_path: 项目路径
        project_name: 项目名称，如果为None则使用目录名称

    Returns:
        分析结果的字典表示
    """
    if not project_name:
        project_name = os.path.basename(os.path.abspath(project_path))

    # 创建分析配置
    config = AnalysisConfig(
        project_path=project_path,
        project_name=project_name,
        analyze_architecture=True,
        analyze_structure=True,
        analyze_dependencies=True
    )

    # 创建并运行分析图
    graph = ProjectAnalysisGraph(config)
    result = await graph.run(file_name=file_name,
                             generated_path=generated_path,
                             config=config)
    return result
