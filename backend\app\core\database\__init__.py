"""Database infrastructure package."""
from typing import Union, Generator, AsyncGenerator, Optional, cast
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from .exceptions import DatabaseError
from .engine import DatabaseEngine
from ...models.model_base import Base
from .session import SessionManager, SyncSessionManager, AsyncSessionManager

logger = structlog.get_logger(__name__)

class Database(DatabaseEngine):
    """数据库服务，提供会话管理功能"""
    
    def __init__(
        self,
        url: Optional[str] = None,
        *,
        echo: bool = True,
        pool_size: int = 20,
        max_overflow: int = 10,
        pool_timeout: int = 30,
        pool_recycle: int = 3600,
        pool_pre_ping: bool = True
    ):
        """初始化数据库服务
        
        Args:
            url: 数据库连接URL，如果为None则从配置中读取
            echo: 是否打印SQL语句
            pool_size: 连接池大小
            max_overflow: 最大溢出连接数
            pool_timeout: 连接池超时时间（秒）
            pool_recycle: 连接回收时间（秒）
            pool_pre_ping: 是否启用连接预检测
            
        Raises:
            DatabaseError: 当数据库配置无效时
        """
        try:
            url = url
            if not url:
                raise ValueError("Database URL is required")
                
            super().__init__(
                url=url,
                echo=echo,
                pool_size=pool_size,
                max_overflow=max_overflow,
                pool_timeout=pool_timeout,
                pool_recycle=pool_recycle,
                pool_pre_ping=pool_pre_ping
            )
            
            # 验证会话工厂
            sync_factory = self.get_sync_session_factory()
            async_factory = self.get_async_session_factory()
            
            self._session_manager = SessionManager(sync_factory, async_factory)
            logger.info("Database initialized",
                       url=url.split("@")[-1],  # 不记录敏感信息
                       pool_size=pool_size,
                       max_overflow=max_overflow)
                       
        except Exception as e:
            logger.error("Failed to initialize database",
                        error=str(e),
                        error_type=type(e).__name__)
            raise DatabaseError(f"Failed to initialize database: {str(e)}") from e
        
    @property
    def session_manager(self) -> SessionManager:
        """获取会话管理器"""
        return self._session_manager
        
    @property
    def sync_session_manager(self) -> SyncSessionManager:
        """获取同步会话管理器"""
        return self._session_manager.sync_session_manager
        
    @property
    def async_session_manager(self) -> AsyncSessionManager:
        """获取异步会话管理器"""
        return self._session_manager.async_session_manager
        
    def get_session(self) -> SessionManager:
        """获取会话管理器"""
        return self.session_manager
        
    def __call__(
        self,
        *,
        sync: bool = False
    ) -> Union[Generator[Session, None, None], AsyncGenerator[AsyncSession, None]]:
        """获取会话
        
        Args:
            sync: 是否使用同步会话
            
        Returns:
            Union[Generator[Session, None, None], AsyncGenerator[AsyncSession, None]]: 会话生成器
            
        Example:
            # 同步用法
            with database(sync=True) as session:
                result = session.execute(query)
                
            # 异步用法
            async with database() as session:
                result = await session.execute(query)
        """
        if sync:
            return cast(
                Generator[Session, None, None],
                self.sync_session_manager.session()
            )
        return cast(
            AsyncGenerator[AsyncSession, None],
            self.async_session_manager.session()
        )
        
    async def cleanup(self):
        """清理数据库资源"""
        try:
            # 清理会话管理器
            await self._session_manager.cleanup()
            
            # 清理引擎
            if hasattr(self, 'async_engine'):
                await self.async_engine.dispose()
            if hasattr(self, 'sync_engine'):
                self.sync_engine.dispose()
                
            logger.info("Database resources cleaned up")
        except Exception as e:
            logger.error("Failed to cleanup database resources",
                        error=str(e),
                        error_type=type(e).__name__)
            raise DatabaseError(f"Failed to cleanup database: {str(e)}") from e


__all__ = [
    'Base',
    'Database',
    'DatabaseEngine',
    'SessionManager',
    'SyncSessionManager',
    'AsyncSessionManager',
]
