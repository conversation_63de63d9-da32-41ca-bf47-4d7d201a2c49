"""开放包装约定（OPC）包的低级只读API。"""

from app.utils.slx_parser.opc.constants import RELATIONSHIP_TARGET_MODE as RTM
from app.utils.slx_parser.opc.oxml import parse_xml
from app.utils.slx_parser.opc.packuri import PACKAGE_URI, PackURI
from app.utils.slx_parser.opc.phys_pkg import PhysPkgReader
from app.utils.slx_parser.opc.shared import CaseInsensitiveDict


class PackageReader:
    """通过其:attr:`serialized_parts`和:attr:`pkg_srels`属性提供对zip格式OPC包内容的访问。"""

    def __init__(self, content_types, pkg_srels, sparts):
        super(PackageReader, self).__init__()
        self._pkg_srels = pkg_srels
        self._sparts = sparts

    @staticmethod
    def from_file(pkg_file):
        """返回一个加载了`pkg_file`内容的|PackageReader|实例。"""
        phys_reader = PhysPkgReader(pkg_file)
        content_types = _ContentTypeMap.from_xml(phys_reader.content_types_xml)
        pkg_srels = PackageReader._srels_for(phys_reader, PACKAGE_URI)
        sparts = PackageReader._load_serialized_parts(
            phys_reader, pkg_srels, content_types
        )
        phys_reader.close()
        return PackageReader(content_types, pkg_srels, sparts)

    def iter_sparts(self):
        """为包中的每个序列化部件生成一个4元组`(partname, content_type, reltype, blob)`。"""
        for s in self._sparts:
            yield (s.partname, s.content_type, s.reltype, s.blob)

    def iter_srels(self):
        """为包中的每个关系生成一个2元组`(source_uri, srel)`。"""
        for srel in self._pkg_srels:
            yield (PACKAGE_URI, srel)
        for spart in self._sparts:
            for srel in spart.srels:
                yield (spart.partname, srel)

    @staticmethod
    def _load_serialized_parts(phys_reader, pkg_srels, content_types):
        """返回一个|_SerializedPart|实例列表，对应于通过从`pkg_srels`开始遍历关系图可访问的
        `phys_reader`中的部件。"""
        sparts = []
        part_walker = PackageReader._walk_phys_parts(phys_reader, pkg_srels)
        for partname, blob, reltype, srels in part_walker:
            content_type = content_types[partname]
            spart = _SerializedPart(partname, content_type, reltype, blob, srels)
            sparts.append(spart)
        return tuple(sparts)

    @staticmethod
    def _srels_for(phys_reader, source_uri):
        """返回一个|_SerializedRelationships|实例，其中包含由`source_uri`标识的源的关系。"""
        rels_xml = phys_reader.rels_xml_for(source_uri)
        return _SerializedRelationships.load_from_xml(source_uri.baseURI, rels_xml)

    @staticmethod
    def _walk_phys_parts(phys_reader, srels, visited_partnames=None):
        """通过遍历以srels为根的关系图，为`phys_reader`中的每个部件生成一个4元组
        `(partname, blob, reltype, srels)`。"""
        if visited_partnames is None:
            visited_partnames = []
        for srel in srels:
            if srel.is_external:
                continue
            partname = srel.target_partname
            if partname in visited_partnames:
                continue
            visited_partnames.append(partname)
            reltype = srel.reltype
            part_srels = PackageReader._srels_for(phys_reader, partname)
            blob = phys_reader.blob_for(partname)
            yield (partname, blob, reltype, part_srels)
            next_walker = PackageReader._walk_phys_parts(
                phys_reader, part_srels, visited_partnames
            )
            for partname, blob, reltype, srels in next_walker:
                yield (partname, blob, reltype, srels)


class _ContentTypeMap:
    """提供按部件名查找内容类型的字典语义的值类型，
    例如``content_type = cti['/ppt/presentation.xml']``。"""

    def __init__(self):
        super(_ContentTypeMap, self).__init__()
        self._overrides = CaseInsensitiveDict()
        self._defaults = CaseInsensitiveDict()

    def __getitem__(self, partname):
        """返回由`partname`标识的部件的内容类型。"""
        if not isinstance(partname, PackURI):
            tmpl = "_ContentTypeMap键必须是<type 'PackURI'>类型，得到的是%s"
            raise KeyError(tmpl % type(partname))
        if partname in self._overrides:
            return self._overrides[partname]
        if partname.ext in self._defaults:
            return self._defaults[partname.ext]
        tmpl = "[Content_Types].xml中没有找到partname'%s'的内容类型"
        raise KeyError(tmpl % partname)

    @staticmethod
    def from_xml(content_types_xml):
        """返回一个新的|_ContentTypeMap|实例，其中填充了`content_types_xml`的内容。"""
        types_elm = parse_xml(content_types_xml)
        ct_map = _ContentTypeMap()
        for o in types_elm.overrides:
            ct_map._add_override(o.partname, o.content_type)
        for d in types_elm.defaults:
            ct_map._add_default(d.extension, d.content_type)
        return ct_map

    def _add_default(self, extension, content_type):
        """将`extension`到`content_type`的默认映射添加到此内容类型映射中。"""
        self._defaults[extension] = content_type

    def _add_override(self, partname, content_type):
        """将`partname`到`content_type`的默认映射添加到此内容类型映射中。"""
        self._overrides[partname] = content_type


class _SerializedPart:
    """OPC包部件的值对象。

    提供对部件的部件名、内容类型、二进制数据和序列化关系的访问。
    """

    def __init__(self, partname, content_type, reltype, blob, srels):
        super(_SerializedPart, self).__init__()
        self._partname = partname
        self._content_type = content_type
        self._reltype = reltype
        self._blob = blob
        self._srels = srels

    @property
    def partname(self):
        return self._partname

    @property
    def content_type(self):
        return self._content_type

    @property
    def blob(self):
        return self._blob

    @property
    def reltype(self):
        """此部件的引用关系类型。"""
        return self._reltype

    @property
    def srels(self):
        return self._srels


class _SerializedRelationship:
    """表示OPC包中序列化关系的值对象。

    在这种情况下，序列化意味着任何目标部件都是通过其部件名引用的，
    而不是通过到内存中|Part|对象的直接链接。
    """

    def __init__(self, baseURI, rel_elm):
        super(_SerializedRelationship, self).__init__()
        self._baseURI = baseURI
        self._rId = rel_elm.rId
        self._reltype = rel_elm.reltype
        self._target_mode = rel_elm.target_mode
        self._target_ref = rel_elm.target_ref

    @property
    def is_external(self):
        """如果target_mode是``RTM.EXTERNAL``则返回True"""
        return self._target_mode == RTM.EXTERNAL

    @property
    def reltype(self):
        """关系类型，如``RT.OFFICE_DOCUMENT``"""
        return self._reltype

    @property
    def rId(self):
        """关系ID，如'rId9'，对应于``CT_Relationship``元素上的``Id``属性。"""
        return self._rId

    @property
    def target_mode(self):
        """``CT_Relationship``元素的``TargetMode``属性中的字符串，
        可以是``RTM.INTERNAL``或``RTM.EXTERNAL``之一。"""
        return self._target_mode

    @property
    def target_ref(self):
        """``CT_Relationship``元素的``Target``属性中的字符串，
        对于内部目标模式是相对部件引用，对于外部目标模式是任意URI（如HTTP URL）。"""
        return self._target_ref

    @property
    def target_partname(self):
        """包含此关系目标部件名的|PackURI|实例。

        如果target_mode是``'External'``，在引用时会引发``ValueError``。
        使用:attr:`target_mode`在引用前进行检查。
        """
        if self.is_external:
            msg = (
                '当TargetMode为"External"时，'
                "Relationship的target_partname属性未定义"
            )
            raise ValueError(msg)
        # 延迟加载_target_partname属性
        if not hasattr(self, "_target_partname"):
            self._target_partname = PackURI.from_rel_ref(self._baseURI, self.target_ref)
        return self._target_partname


class _SerializedRelationships:
    """只读的|_SerializedRelationship|实例序列，对应于传递给构造函数的关系项XML。"""

    def __init__(self):
        super(_SerializedRelationships, self).__init__()
        self._srels = []

    def __iter__(self):
        """支持迭代，例如'for x in srels:'。"""
        return self._srels.__iter__()

    @staticmethod
    def load_from_xml(baseURI, rels_item_xml):
        """返回一个加载了`rels_item_xml`中包含的关系的|_SerializedRelationships|实例。

        如果`rels_item_xml`是|None|，则返回一个空集合。
        """
        srels = _SerializedRelationships()
        if rels_item_xml is not None:
            rels_elm = parse_xml(rels_item_xml)
            for rel_elm in rels_elm.Relationship_lst:
                srels._srels.append(_SerializedRelationship(baseURI, rel_elm))
        return srels
