"""
用户服务模块

提供用户管理相关功能
"""
from datetime import datetime, timezone, timedelta
from typing import Optional, List, Dict, Any
import structlog
from sqlalchemy import select, and_, or_, func, delete, update
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import DatabaseError

from app.core.database import DatabaseError
from app.core.config import settings
from app.models.rbac.permission import PermissionModel
from app.models.rbac.permission_group import PermissionGroupModel
from app.utils.security import get_password_hash, verify_password
from app.core.di.providers import SessionProvider, AsyncSessionProvider
from app.models.rbac.user import UserModel
from app.models.rbac.role import RoleModel
from app.schemas.rbac.user import (
    User, UserListResponse, UserCreateRequest,
    UserUpdateRequest, UserChangePasswordRequest,
    UserResetPasswordRequest, CurrentUserUpdateRequest, UpdateSelfPasswordRequest,
    UpdateSelfPasswordWithOutValidateRequest
)
from app.models.rbac.token import TokenModel
from app.schemas.rbac.token import Token
from app.schemas.rbac.role import Role
from app.model_converters.rbac.user import UserConverter

logger = structlog.get_logger(__name__)

class UserService:
    """用户服务类"""
    
    def __init__(
        self,
        session: SessionProvider,
        async_session: AsyncSessionProvider
    ):
        """初始化用户服务
        
        Args:
            session: 数据库会话提供者
            async_session: 异步数据库会话提供者
        """
        self.session = session
        self.async_session = async_session
        self.user_converter = UserConverter()
        logger.debug("用户服务初始化完成")

    async def fast_register(self, phone: Optional[str] = None, email: Optional[str] = None) -> User:
        """快速注册用户

        Args:
            phone: 手机号
            email: 邮箱

        Returns:
            创建的用户信息

        Raises:
            ValueError: 手机号和邮箱都为空
        """
        if not phone and not email:
            raise ValueError("手机号和邮箱不能同时为空")

        # 生成随机用户名和密码
        import uuid
        import random
        import string

        # 生成随机用户名
        username = f"user_{uuid.uuid4().hex[:8]}"
        # 生成随机密码
        password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))

        # 创建用户请求
        user_data = {
            "username": username,
            "nickname": username,  # 使用用户名作为昵称
            "email": email,
            "phone": phone,
            "password": password,
            "is_active": True,
            "needs_email_binding": False
        }

        # 验证并创建用户
        create_request = UserCreateRequest.model_validate(user_data)
        return await self.create(create_request)



    async def check_user_email_exists(self, user_id: str) -> str:
        """检查邮箱是否已存在

        Args:
            email: 要检查的邮箱地址

        Returns:
            bool: True表示邮箱已存在，False表示邮箱不存在
        """
        async with self.async_session() as session:
            try:
                stmt = select(UserModel).where(UserModel.id == user_id)
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                if user and user.email:
                    return user.email
                return None

            except Exception as e:
                logger.error(
                    "检查邮箱是否存在时发生错误",
                    user_id=user_id,
                    error=str(e)
                )
                raise ValueError(f"检查邮箱失败: {str(e)}")

    async def check_email_exists(self, email: str) -> bool:
        """检查邮箱是否已存在

        Args:
            email: 要检查的邮箱地址

        Returns:
            bool: True表示邮箱已存在，False表示邮箱不存在
        """
        async with self.async_session() as session:
            try:
                stmt = select(UserModel).where(UserModel.email == email)
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                return user is not None

            except Exception as e:
                logger.error(
                    "检查邮箱是否存在时发生错误",
                    email=email,
                    error=str(e)
                )
                raise ValueError(f"检查邮箱失败: {str(e)}")


    async def check_user_phone_exists(self, user_id: str) -> str:

        async with self.async_session() as session:
            try:
                stmt = select(UserModel).where(UserModel.id == user_id)
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                if user and user.phone:
                    return user.phone
                return None

            except Exception as e:
                logger.error(
                    "检查电话是否存在时发生错误",
                    user_id=user_id,
                    error=str(e)
                )
                raise ValueError(f"检查电话失败: {str(e)}")


    async def check_phone_exists(self, phone: str) -> bool:
        """检查电话是否已存在

        Args:
            phone: 要检查的电话地址

        Returns:
            bool: True表示电话已存在，False表示电话不存在
        """
        async with self.async_session() as session:
            try:
                stmt = select(UserModel).where(UserModel.phone == phone)
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                return user is not None

            except Exception as e:
                logger.error(
                    "检查电话是否存在时发生错误",
                    email=phone,
                    error=str(e)
                )
                raise ValueError(f"检查电话失败: {str(e)}")

    async def get_by_id(self, user_id: str) -> Optional[User]:
        """根据ID获取用户

        Args:
            user_id: 用户ID

        Returns:
            用户信息，不存在返回None
        """
        async with self.async_session() as session:
            stmt = (
                select(UserModel)
                .options(
                    selectinload(UserModel.roles)
                )
                .where(UserModel.id == user_id)
            )
            result = await session.execute(stmt)
            user = result.scalar_one_or_none()
            return self.user_converter.to_schema(user) if user else None

    def to_schema_with_roles_and_permissions(self, user_model: UserModel) -> User:
        """将用户模型转换为schema，并包含角色和权限信息

        Args:
            user_model: 用户模型实例

        Returns:
            包含角色和权限信息的用户schema实例
        """
        # 1. 基本属性转换
        user_dict = {
            "id": user_model.id,
            "username": user_model.username,
            "nickname": user_model.nickname,
            "password": user_model.hashed_password,
            "email": user_model.email,
            "phone": user_model.phone,
            "is_active": user_model.is_active,
            "is_superuser": user_model.is_superuser,
            "last_login": user_model.last_login,
            "created_at": user_model.created_at,
            "updated_at": user_model.updated_at
        }

        # 2. 转换角色信息
        roles = []
        all_permissions = set()

        for role_model in user_model.roles:
            # 将角色转换为字典
            # role_dict = {
            #     "id": role_model.id,
            #     "name": role_model.name,
            #     "code": role_model.code,
            #     "description": role_model.description,
            #     "sort_order": role_model.sort_order,
            #     "permission_groups": []
            # }

            roles.append(str(role_model.code))
            # 简写
            if user_model.is_superuser:
                all_permissions.add("*:*:*")
                break
            # 添加权限组信息
            if hasattr(role_model, 'permission_groups'):
                for pg in role_model.permission_groups:
                    pg_dict = {
                        "id": pg.id,
                        "name": pg.name,
                        "description": pg.description
                    }

                    # 添加权限信息
                    pg_permissions = []
                    if hasattr(pg, 'permissions'):
                        for perm in pg.permissions:
                            perm_dict = {
                                "id": perm.id,
                                "code": perm.code,
                                "name": perm.name,
                                "description": perm.description
                            }
                            pg_permissions.append(perm_dict)
                            all_permissions.add(perm.code)  # 收集所有权限代码

                    pg_dict["permissions"] = pg_permissions
                    # role_dict["permission_groups"].append(pg_dict)
            if hasattr(role_model, 'permissions'):
                for pg in role_model.permissions:
                    all_permissions.add(pg.code)  # 收集所有权限代码


        # 3. 组装完整的用户信息
        user_dict["roles"] = roles
        user_dict["permissions"] = list(all_permissions)

        # 4. 创建用户schema实例
        return user_dict

    async def get_user_with_roles(self, user_id: str, is_current_user_need_password:bool=False):
        """根据用户ID获取用户信息和绑定角色

        Args:
            user_id: 用户ID

        Returns:
            Dict: 包含用户信息和角色列表的字典，不存在返回None
        """
        async with self.async_session() as session:
            stmt = (
                select(UserModel)
                    .options(
                    selectinload(UserModel.roles)
                        .selectinload(RoleModel.permissions)
                        .selectinload(PermissionModel.roles),
                    selectinload(UserModel.roles)
                        .selectinload(RoleModel.permission_groups)
                        .selectinload(PermissionGroupModel.permissions)

                )
                    .where(UserModel.id == user_id)
            )
            result = await session.execute(stmt)
            user = result.scalar_one_or_none()

            if not user:
                return None

            user_data = self.to_schema_with_roles_and_permissions(user)
            if not is_current_user_need_password:
                del user_data["password"]

        async with self.async_session() as session:
            stmt = (
                select(RoleModel)
                    .options(
                    selectinload(RoleModel.parent),
                    selectinload(RoleModel.children),
                    selectinload(RoleModel.permissions),
                    selectinload(RoleModel.permission_groups),
                    selectinload(RoleModel.users),
                )
                    .where(RoleModel.code.in_(user_data['roles']))
            )
            result = await session.execute(stmt)
            role_models = result.scalars().all()

            permissions_solve = set(user_data.get('permissions', []))
            # 遍历所有角色模型，合并权限
            for role_model in role_models:
                role_data = self.to_schema_with_permissions(role_model)
                permissions_solve.update(role_data.get('permissions', []))
            # 更新用户权限（去重后的结果）
            user_data['permissions'] = list(permissions_solve)
        return user_data

    def to_schema_with_permissions(self, role_model: RoleModel) -> Role:
        """将角色模型转换为schema，并包含权限组和权限信息

        Args:
            role_model: 角色模型实例

        Returns:
            包含完整权限信息的角色schema实例
        """
        # 1. 基本属性转换
        role_dict = {
            "id": role_model.id,
            "name": role_model.name,
            "code": role_model.code,
            "description": role_model.description,
            #            "parent_id": role_model.parent_id,
            "is_active": role_model.is_active,
            "is_system": role_model.is_system,
            "is_inherit": role_model.is_inherit,
            "sort_order": role_model.sort_order,
            "created_at": role_model.created_at,
            "updated_at": role_model.updated_at,
            "permissions": []
        }

        # 2. 不处理父、子角色、权限组信息

        direct_permissions = []
        for perm in role_model.permissions:
            # perm_dict = {
            #     "id": perm.id,
            #     "code": perm.code,
            #     "name": perm.name,
            #     "description": perm.description,
            #     # 添加验证所需的字段
            #     "type": getattr(perm, "type", "system"),  # 默认为system类型
            #     "group_id": getattr(perm, "group_id", None),
            #     "created_at": getattr(perm, "created_at", role_model.created_at),
            #     "updated_at": getattr(perm, "updated_at", role_model.updated_at)
            #
            # }
            # direct_permissions.append(perm_dict)
            direct_permissions.append(str(perm.code))
        role_dict["permissions"] = direct_permissions
        # 8. 创建并返回角色schema实例
        return role_dict

    async def get_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户

        Args:
            username: 用户名

        Returns:
            用户信息，不存在返回None
        """
        async with self.async_session() as session:
            stmt = (
                select(UserModel)
                .options(
                    selectinload(UserModel.roles)
                )
                .where(UserModel.username == username)
            )
            result = await session.execute(stmt)
            user = result.scalar_one_or_none()
            return self.user_converter.to_schema(user) if user else None

    async def get_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户

        Args:
            email: 邮箱地址

        Returns:
            用户信息，不存在返回None
        """
        async with self.async_session() as session:
            stmt = (
                select(UserModel)
                .options(
                    selectinload(UserModel.roles)
                )
                .where(UserModel.email == email)
            )
            result = await session.execute(stmt)
            user = result.scalar_one_or_none()
            return self.user_converter.to_schema(user) if user else None

    async def get_by_phone(self, phone: str) -> Optional[User]:
        """根据手机号获取用户

        Args:
            phone: 手机号

        Returns:
            用户信息，不存在返回None
        """
        async with self.async_session() as session:
            stmt = (
                select(UserModel)
                .options(
                    selectinload(UserModel.roles)
                )
                .where(UserModel.phone == phone)
            )
            result = await session.execute(stmt)
            user = result.scalar_one_or_none()
            return self.user_converter.to_schema(user) if user else None

    async def get_list(
        self,
        *,
        page: int = 1,
        page_size: int = 100,
        is_superuser: Optional[bool] = None,
        role_id: Optional[str] = None,
        name: Optional[str] = None,
        phone: Optional[str] = None,
        status: Optional[bool] = None,
        created_at_begin: Optional[str] = None,
        created_at_end: Optional[str] = None
    ) -> UserListResponse:
        """获取用户列表

        Args:
            page: 页码
            page_size: 每页记录数
            is_active: 是否启用
            is_superuser: 是否超级管理员
            role_id: 角色ID
            name: 用户名(模糊匹配)
            phone: 手机号(模糊匹配)
            status: 用户状态(精确匹配)
            created_at_begin: 创建时间开始
            created_at_end: 创建时间结束

        Returns:
            用户列表分页响应
        """
        async with self.async_session() as session:
            stmt = (
                select(UserModel)
                .options(
                    selectinload(UserModel.roles)
                )
            )
            
            # 构建查询条件
            conditions = []

            if is_superuser is not None:
                conditions.append(UserModel.is_superuser == is_superuser)
                
            if role_id is not None:
                stmt = stmt.join(UserModel.roles)
                conditions.append(RoleModel.id == role_id)

            if name is not None:
                conditions.append(UserModel.username.ilike(f"%{name}%"))

            if phone is not None:
                conditions.append(UserModel.phone.ilike(f"%{phone}%"))

            # 创建时间范围查询
            if created_at_begin is not None:
                try:
                    begin_date = datetime.fromisoformat(created_at_begin.replace('Z', '+00:00'))
                    conditions.append(UserModel.created_at >= begin_date)
                except ValueError:
                    logger.warning(f"Invalid created_at_begin format: {created_at_begin}")

            if created_at_end is not None:
                try:
                    end_date = datetime.fromisoformat(created_at_end.replace('Z', '+00:00'))
                    conditions.append(UserModel.created_at <= end_date)
                except ValueError:
                    logger.warning(f"Invalid created_at_end format: {created_at_end}")

            if status is not None:
                if status.lower() == 'true':
                    conditions.append(UserModel.is_active == True)
                elif status.lower() == 'false':
                    conditions.append(UserModel.is_active == False)
                
            if conditions:
                stmt = stmt.where(and_(*conditions))
                
            # 获取总记录数
            count_stmt = select(func.count()).select_from(stmt.subquery())
            total = await session.scalar(count_stmt)
            
            # 计算分页
            skip = (page - 1) * page_size
            
            # 获取分页数据
            stmt = (
                stmt.order_by(UserModel.id.desc())
                .offset(skip)
                .limit(page_size)
            )
            result = await session.execute(stmt)
            users = result.scalars().all()
            
            ret =  UserListResponse(
                total=total,
                users=[self.user_converter.to_schema(u) for u in users],
                page=page,
                page_size=page_size
            )
            return ret

    async def create(
        self,
        request: UserCreateRequest
    ) -> User:
        """创建用户

        Args:
            request: 用户创建请求

        Returns:
            创建的用户信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 用户名、邮箱或手机号已存在
        """

        # 检查必填字段
        if not request.username:
            raise ValueError("用户名不能为空")
        # if not request.email:
        #     raise ValueError("邮箱不能为空")

        # 检查邮箱和手机号至少提供一个
        if (not request.email) and (not request.phone):
            raise ValueError("service-邮箱和手机号至少需要提供一个")

        if not request.password:
            raise ValueError("密码不能为空")
        if not request.nickname:
            raise ValueError("昵称不能为空")

        async with self.async_session() as session:
            try:
                # 邮箱重复A 手机重复B 用户名重复账号C 必须分次查询，scalar_one_or_none存在返回多个的情况
                if request.username:
                    username_stmt = select(UserModel).where(UserModel.username == request.username)
                    username_result = await session.execute(username_stmt)
                    if username_result.scalar_one_or_none():
                        raise ValueError("用户名已被使用")

                    # 查询邮箱
                if request.email:
                    email_stmt = select(UserModel).where(UserModel.email == request.email)
                    email_result = await session.execute(email_stmt)
                    if email_result.scalar_one_or_none():
                        raise ValueError("邮箱已被使用")

                    # 查询手机号
                if request.phone:
                    phone_stmt = select(UserModel).where(UserModel.phone == request.phone)
                    phone_result = await session.execute(phone_stmt)
                    if phone_result.scalar_one_or_none():
                        raise ValueError("手机号已被使用")

                if request.nickname:
                    phone_stmt = select(UserModel).where(UserModel.nickname == request.nickname)
                    phone_result = await session.execute(phone_stmt)
                    if phone_result.scalar_one_or_none():
                        raise ValueError("昵称已被使用")

                # 创建用户
                user = UserModel(
                    username=request.username,
                    email=request.email,
                    phone=request.phone,
                    hashed_password=get_password_hash(request.password),
                    #hashed_password=request.password,
                    needs_email_binding=request.needs_email_binding,
                    nickname=request.nickname,
                    is_active=request.is_active,
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc)
                )
                
                # 分配角色
                if request.role_ids:
                    result = await session.execute(
                        select(RoleModel).where(RoleModel.id.in_(request.role_ids))
                    )
                    roles = result.scalars().all()
                    if len(roles) != len(request.role_ids):
                        raise ValueError("部分角色不存在")
                    user.roles = roles
                else:
                    # 如果没有指定角色,分配普通用户角色
                    result = await session.execute(
                        select(RoleModel).where(RoleModel.code == "user")
                    )
                    default_role = result.scalar_one_or_none()
                    if not default_role:
                        raise ValueError("默认角色不存在")
                    user.roles = [default_role]
                session.add(user)
                await session.commit()
                # await session.refresh(user)

                # 手动获取用户信息而不是使用refresh
                result = await session.execute(
                    select(UserModel)
                        .options(selectinload(UserModel.roles))
                        .where(UserModel.id == user.id)
                )
                user = result.scalar_one()

                logger.info(
                    "用户创建成功",
                    username=request.username,
                    email=request.email,
                    user_id=user.id
                )
                
                return self.user_converter.to_schema(user)
                
            except Exception as e:
                logger.error(
                    "用户创建失败",
                    username=request.username,
                    email=request.email,
                    error=str(e)
                )
                await session.rollback()
                raise

    async def update(
        self,
        request: UserUpdateRequest,
        is_bild = None,
        solve_unauth_email=None
    ) -> User:
        """更新用户信息

        Args:
            request: 用户更新请求

        Returns:
            更新后的用户信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 用户名或邮箱已存在
        """
        async with self.async_session() as session:
            try:
                # 获取用户
                stmt = (
                    select(UserModel)
                    .options(
                        selectinload(UserModel.roles)
                    )
                    .where(UserModel.id == request.user_id)
                )
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                if not user:
                    raise ValueError("用户不存在")
                
                # 检查用户名和邮箱是否重复
                if request.username or request.email:
                    conditions = []
                    if request.username and request.username != user.username:
                        conditions.append(UserModel.username == request.username)
                    if request.email and request.email != user.email:
                        conditions.append(UserModel.email == request.email)
                        
                    if conditions:
                        stmt = (
                            select(UserModel)
                            .where(
                                and_(
                                    UserModel.id != request.user_id,
                                    or_(*conditions)
                                )
                            )
                        )
                        result = await session.execute(stmt)
                        existing_user = result.scalar_one_or_none()
                        
                        if existing_user:
                            if request.username and existing_user.username == request.username:
                                raise ValueError("用户名已重复，已存在")
                            else:
                                raise ValueError("该邮箱重复，已存在")
                
                # 更新字段
                if solve_unauth_email is not None:
                    # 确定未认证账号处理完毕
                    if request.email or request.phone:
                        user.email = None
                if request.username is not None:
                    user.username = request.username
                if request.email is not None:
                    user.email = request.email
                if request.password is not None:
                    user.hashed_password = get_password_hash(request.password)
                if request.nickname is not None:
                    user.nickname = request.nickname
                if request.phone is not None:
                    user.phone = request.phone
                if request.is_active is not None:
                    user.is_active = request.is_active
                if is_bild is not None:
                    user.needs_email_binding = is_bild


                # 更新角色
                if request.role_ids is not None:
                    result = await session.execute(
                        select(RoleModel).where(RoleModel.id.in_(request.role_ids))
                    )
                    roles = result.scalars().all()
                    if len(roles) != len(request.role_ids):
                        raise ValueError("部分角色不存在")
                    user.roles = roles
                    
                user.updated_at = datetime.now(timezone.utc)
                tmp = self.user_converter.to_schema(user)
                await session.commit()
                await session.refresh(user)
                
                logger.info(
                    "用户更新成功",
                    user_id=request.user_id,
                    username=request.username,
                    email=request.email
                )
                
                return tmp
                
            except Exception as e:
                logger.error(
                    "用户更新失败",
                    user_id=request.user_id,
                    username=request.username,
                    email=request.email,
                    error=str(e)
                )
                await session.rollback()
                raise e


    async def update_current_user(
            self,
            user_id: str,
            request: CurrentUserUpdateRequest
    ) -> User:
        async with self.async_session() as session:
            try:
                async with self.async_session() as session:
                    # 第一次查询：检查用户是否存在
                    stmt = select(UserModel).where(UserModel.id == user_id)
                    result = await session.execute(stmt)
                    user = result.scalar_one_or_none()

                    if not user:
                        raise ValueError("用户不存在")

                    # 第二次查询：检查用户名是否重复
                    if request.username and request.username != user.username:
                        stmt = select(UserModel).where(
                            and_(
                                UserModel.id != user_id,
                                UserModel.username == request.username
                            )
                        )
                        result = await session.execute(stmt)
                        if result.scalar_one_or_none():
                            raise ValueError("用户名已存在")

                    # 第三次查询：检查邮箱是否重复
                    if request.email and request.email != user.email:
                        stmt = select(UserModel).where(
                            and_(
                                UserModel.id != user_id,
                                UserModel.email == request.email
                            )
                        )
                        result = await session.execute(stmt)
                        if result.scalar_one_or_none():
                            raise ValueError("邮箱已存在")

                    # 更新用户信息
                    if request.username is not None:
                        user.username = request.username
                    if request.email is not None:
                        user.email = request.email
                    if request.password is not None:
                        user.hashed_password = get_password_hash(request.password)
                    if request.nickname is not None:
                        user.nickname = request.nickname

                    # 提交更改
                    await session.commit()
                    # return self.user_converter.to_schema(user)
                    return
            except Exception as e:
                logger.error(
                    "当前用户信息更新失败",
                    user_id=user_id,
                    username=request.username,
                    email=request.email,
                    error=str(e)
                )
                await session.rollback()
                raise

    async def batch_delete(self, user_ids: List[str]) -> Dict[str, Any]:
        """批量删除用户

        Args:
            user_ids: 用户ID列表

        Returns:
            Dict: 包含删除结果的字典
        """
        deleted_count = 0
        failed_count = 0
        errors = {}

        async with self.async_session() as session:
            for user_id in user_ids:
                try:
                    # 查询用户
                    user = await self.get_by_id(user_id)
                    if not user:
                        failed_count += 1
                        errors[user_id] = "用户不存在"
                        continue

                    # 执行删除
                    delete_stmt = delete(UserModel).where(UserModel.id == user_id)
                    await session.execute(delete_stmt)
                    deleted_count += 1
                    logger.info("用户删除成功" + str(user_id))

                except Exception as e:
                    failed_count += 1
                    errors[user_id] = str(e)
                    logger.error(
                        "用户删除失败" + str(user_id),
                        error=str(e)
                    )

            # 提交事务
            await session.commit()

        return {
            "deleted": deleted_count,
            "failed": failed_count,
            "errors": errors
        }

    async def delete(self, user_id: int) -> bool:
        """删除用户

        Args:
            user_id: 用户ID

        Returns:
            是否删除成功

        Raises:
            DatabaseError: 数据库错误
        """
        async with self.async_session() as session:
            try:
                # 绑定的Oauth会自动删除。
                result = await session.execute(
                    delete(UserModel).where(UserModel.id == user_id)
                )
                await session.commit()
                
                deleted = result.rowcount > 0
                if deleted:
                    logger.info("用户删除成功", user_id=user_id)
                else:
                    logger.warning("用户不存在", user_id=user_id)
                    
                return deleted
                
            except Exception as e:
                logger.error(
                    "用户删除失败",
                    user_id=user_id,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("用户删除失败") from e

    async def check_is_locked(
        self,
        user: User
    ) -> bool:
        """检查用户是否被锁定

        Args:
            user: 用户信息

        Returns:
            是否被锁定
        """
        if not user.locked_until:
            return False
            
        # 如果锁定时间已过，重置锁定状态
        if user.locked_until <= datetime.now(timezone.utc):
            async with self.async_session() as session:
                stmt = (
                    update(UserModel)
                    .where(UserModel.id == user.id)
                    .values(
                        failed_login_attempts=0,
                        locked_until=None
                    )
                )
                await session.execute(stmt)
                await session.commit()
            return False
            
        return True

    async def reset_failed_login_attempts(
        self,
        user_id: str
    ) -> None:
        """重置用户失败登录次数

        Args:
            user_id: 用户ID
        """
        async with self.async_session() as session:
            stmt = (
                update(UserModel)
                .where(UserModel.id == user_id)
                .values(
                    failed_login_attempts=0,
                    locked_until=None,
                    last_login=datetime.now(timezone.utc)
                )
            )
            await session.execute(stmt)
            await session.commit()

    async def update_failed_login_attempts(
        self,
        user_id: str
    ) -> None:
        """更新用户失败登录次数

        Args:
            user_id: 用户ID
        """
        async with self.async_session() as session:
            # 获取当前用户
            stmt = select(UserModel).where(UserModel.id == user_id)
            result = await session.execute(stmt)
            user = result.scalar_one_or_none()
            
            if user:
                # 增加失败次数
                user.failed_login_attempts += 1
                
                # 如果达到最大失败次数，锁定账户
                if user.failed_login_attempts >= settings.security.MAX_FAILED_LOGIN_ATTEMPTS:
                    user.locked_until = datetime.now(timezone.utc) + \
                        timedelta(minutes=settings.security.ACCOUNT_LOCKOUT_MINUTES)
                
                await session.commit()

    async def create_token(
        self,
        *,
        user_id: str,
        access_token: str,
        refresh_token: str,
        expires_at: datetime
    ) -> Token:
        """创建用户令牌

        Args:
            user_id: 用户ID
            access_token: 访问令牌
            refresh_token: 刷新令牌
            expires_at: 过期时间

        Returns:
            令牌信息

        Raises:
            DatabaseError: 数据库错误
        """
        async with self.async_session() as session:
            try:
                # 创建令牌
                token = TokenModel(
                    token_type="bearer",
                    access_token=access_token,
                    refresh_token=refresh_token,
                    user_id=user_id,
                    expires_at=expires_at
                )
                session.add(token)
                await session.commit()
                
                return Token(
                    id=token.id,
                    token_type=token.token_type,
                    access_token=token.access_token,
                    refresh_token=token.refresh_token,
                    user_id=token.user_id,
                    version="1.0.0"
                )
            except Exception as e:
                logger.error(
                    "创建令牌失败",
                    user_id=user_id,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("创建令牌失败") from e

    async def get_token_by_refresh_token(
        self,
        refresh_token: str
    ) -> Optional[Token]:
        """根据刷新令牌获取令牌信息

        Args:
            refresh_token: 刷新令牌

        Returns:
            令牌信息，不存在返回None
        """
        async with self.async_session() as session:
            stmt = (
                select(TokenModel)
                .where(TokenModel.refresh_token == refresh_token)
            )
            result = await session.execute(stmt)
            token = result.scalar_one_or_none()
            
            if token:
                return Token.model_validate(token)
            return None

    async def delete_expired_tokens(
        self,
        user_id: str
    ) -> None:
        """删除用户过期的令牌

        Args:
            user_id: 用户ID
        """
        async with self.async_session() as session:
            try:
                # 删除过期的令牌
                stmt = (
                    delete(TokenModel)
                    .where(
                        and_(
                            TokenModel.user_id == user_id,
                            TokenModel.expires_at <= datetime.now(timezone.utc)
                        )
                    )
                )
                await session.execute(stmt)
                await session.commit()
            except Exception as e:
                logger.error(
                    "删除过期令牌失败",
                    user_id=user_id,
                    error=str(e)
                )
                await session.rollback()

    async def delete_user_tokens(
        self,
        user_id: str
    ) -> None:
        """删除用户所有令牌

        Args:
            user_id: 用户ID
        """
        async with self.async_session() as session:
            try:
                # 删除用户所有令牌
                stmt = (
                    delete(TokenModel)
                    .where(TokenModel.user_id == user_id)
                )
                await session.execute(stmt)
                await session.commit()
            except Exception as e:
                logger.error(
                    "删除用户令牌失败",
                    user_id=user_id,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("删除用户令牌失败") from e

    async def change_password(
        self,
        # request: UpdateSelfPasswordRequest
        request: UpdateSelfPasswordWithOutValidateRequest
    ) -> User:

        async with self.async_session() as session:
            try:
                # 获取用户
                # if request.email:
                #     stmt = (
                #         select(UserModel)
                #         .where(UserModel.email == request.email)
                #     )
                # if request.phone:
                #     stmt = (
                #         select(UserModel)
                #         .where(UserModel.phone == request.phone)
                #     )
                # 修改用户不再需要验证码 所以入参也就变成了userid
                if request.user_id:
                    stmt = (
                        select(UserModel)
                        .where(UserModel.id == request.user_id)
                    )

                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                if not user:
                    logger.warning("修改密码失败：用户不存在", email=request.email, phone=request.phone)
                    return False

                # 验证当前密码
                # if not verify_password(request.current_password, user.hashed_password):
                #     # 记录失败次数
                #     user.failed_login_attempts += 1
                #     if user.failed_login_attempts >= 5:
                #         user.locked_until = datetime.now(timezone.utc) + timedelta(minutes=30)
                #     await session.commit()
                #     logger.warning(
                #         "修改密码失败：当前密码错误",
                #         user_id=user.id,
                #         username=user.username,
                #         failed_attempts=user.failed_login_attempts
                #     )
                #     return False

                # 更新密码
                user.hashed_password = get_password_hash(request.new_password)
                user.failed_login_attempts = 0
                user.locked_until = None
                user.updated_at = datetime.now(timezone.utc)
                await session.commit()

                # logger.info(
                #     "用户密码修改成功",
                #     user_id=user.id,
                #     username=user.username,
                #     user_mail=user.email
                # )
                # return self.user_converter.to_schema(user)

            except Exception as e:
                logger.error(
                    "修改密码时发生错误",
                    #user_id=user.id,
                    error=str(e),
                    exc_info=True
                )
                await session.rollback()
                raise DatabaseError(f"修改密码失败: 数据库错误")

    async def reset_password(
        self,
        request: UserResetPasswordRequest
    ) -> User:
        """重置用户密码

        Args:
            request: 重置密码请求

        Returns:
            更新后的用户信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 用户不存在
        """
        async with self.async_session() as session:
            try:
                # 获取用户
                stmt = (
                    select(UserModel)
                    .where(UserModel.id == request.user_id)
                )
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                if not user:
                    raise ValueError("用户不存在")

                # 重置密码
                user.hashed_password = get_password_hash(request.password)
                user.failed_login_attempts = 0
                user.locked_until = None
                user.updated_at = datetime.now(timezone.utc)
                await session.commit()

                logger.info(
                    "用户密码重置成功",
                    user_id=user.id,
                    username=user.username
                )

                return True

            except Exception as e:
                logger.error(
                    "用户密码重置失败",
                    user_id=request.id,
                    error=str(e)
                )
                await session.rollback()
                raise


    async def repick_password(
        self,
        email,
        new_password
    ) -> User:
        """重置用户密码

        Args:
            request: 重置密码请求

        Returns:
            更新后的用户信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 用户不存在
        """
        async with self.async_session() as session:
            try:
                # 获取用户
                stmt = (
                    select(UserModel)
                        .where(UserModel.email == email)
                )
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                if not user:
                    logger.warning("修改密码失败：用户不存在")
                    return False


                # 更新密码
                user.hashed_password = get_password_hash(new_password)
                user.failed_login_attempts = 0
                user.locked_until = None
                user.updated_at = datetime.now(timezone.utc)
                await session.commit()

                logger.info(
                    "用户密码修改成功",
                    user_id=user.id,
                    username=user.username
                )
                return self.user_converter.to_schema(user)

            except Exception as e:
                logger.error(
                    "修改密码时发生错误",
                    user_id=user.id,
                    error=str(e),
                    exc_info=True
                )
                await session.rollback()
                raise DatabaseError(f"修改密码失败: {str(e)}")

    async def activate(self, user_id: str) -> User:
        """激活用户

        Args:
            user_id: 用户ID

        Returns:
            激活后的用户信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 用户不存在
        """
        async with self.async_session() as session:
            try:
                # 获取用户
                stmt = (
                    select(UserModel)
                    .where(UserModel.id == user_id)
                )
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                if not user:
                    raise ValueError("用户不存在")
                
                user.is_active = True
                await session.commit()
                
                logger.info(
                    "用户激活成功",
                    user_id=user.id,
                    username=user.username
                )
                
                # return self.user_converter.to_schema(user)
                
            except Exception as e:
                logger.error(
                    "用户激活失败",
                    user_id=user_id,
                    error=str(e)
                )
                await session.rollback()
                raise


    async def deactivate(self, user_id: str) -> User:
        """禁用用户

        Args:
            user_id: 用户ID

        Returns:
            禁用后的用户信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 用户不存在
        """
        async with self.async_session() as session:
            try:
                # 获取用户
                stmt = (
                    select(UserModel)
                    .where(UserModel.id == user_id)
                )
                result = await session.execute(stmt)
                user = result.scalar_one_or_none()
                if not user:
                    raise ValueError("用户不存在")
                
                user.is_active = False
                await session.commit()
                
                logger.info(
                    "用户禁用成功",
                    user_id=user.id,
                    username=user.username
                )
                
                # return self.user_converter.to_schema(user)
                
            except Exception as e:
                logger.error(
                    "用户禁用失败",
                    user_id=user_id,
                    error=str(e)
                )
                await session.rollback()
                raise


