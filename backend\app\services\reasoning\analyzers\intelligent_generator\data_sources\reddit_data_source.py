#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Reddit数据源

通过Reddit API搜索项目相关的讨论和评价信息
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from urllib.parse import quote_plus
import time
import re

from .external_data_source import ExternalDataSource

logger = logging.getLogger(__name__)


class RedditDataSource(ExternalDataSource):
    """Reddit数据源类"""
    
    def __init__(
        self, 
        client_id: Optional[str] = None,
        client_secret: Optional[str] = None,
        user_agent: str = "IntelligentReadmeGenerator/1.0",
        timeout: int = 30
    ):
        """
        初始化Reddit数据源
        
        Args:
            client_id: Reddit应用客户端ID
            client_secret: Reddit应用客户端密钥
            user_agent: 用户代理字符串
            timeout: 请求超时时间
        """
        super().__init__(timeout)
        self.client_id = client_id
        self.client_secret = client_secret
        self.user_agent = user_agent
        self.base_url = "https://www.reddit.com"
        self.oauth_url = "https://oauth.reddit.com"
        self.access_token = None
        self.token_expires_at = 0
        self.request_delay = 1.0  # Reddit API请求间隔（秒）
        
        # 相关的subreddit列表
        self.relevant_subreddits = [
            "programming", "webdev", "javascript", "python", "reactjs",
            "node", "golang", "rust", "cpp", "java", "dotnet",
            "MachineLearning", "datascience", "artificial",
            "opensource", "github", "coding", "learnprogramming",
            "softwaredevelopment", "technology", "startups"
        ]
    
    def get_data_source_name(self) -> str:
        """获取数据源名称"""
        return "Reddit"
    
    async def fetch_data(
        self, 
        project_name: str = None, 
        keywords: List[str] = None,
        subreddits: List[str] = None
    ) -> Dict[str, Any]:
        """
        获取Reddit数据
        
        Args:
            project_name: 项目名称
            keywords: 搜索关键词
            subreddits: 指定的subreddit列表
            
        Returns:
            Dict[str, Any]: Reddit数据
        """
        try:
            if not project_name and not keywords:
                logger.warning("缺少搜索参数")
                return {}
            
            logger.info(f"开始获取Reddit数据: {project_name}")
            
            # 如果有认证信息，获取访问令牌
            if self.client_id and self.client_secret:
                await self._ensure_access_token()
            
            # 构建搜索查询
            search_queries = self._build_search_queries(project_name, keywords)
            target_subreddits = subreddits or self.relevant_subreddits[:10]  # 限制subreddit数量
            
            # 获取各种数据
            data = {
                "posts": await self._fetch_posts(search_queries, target_subreddits),
                "comments": await self._fetch_comments(search_queries, target_subreddits),
                "subreddit_stats": await self._fetch_subreddit_stats(target_subreddits),
                "trending_discussions": await self._fetch_trending_discussions(search_queries)
            }
            
            # 分析和汇总数据
            data["analysis"] = self._analyze_data(data)
            
            # 缓存数据
            cache_key = f"{project_name}_{','.join(keywords or [])}_{','.join(target_subreddits[:3])}"
            self.cache_data(cache_key, data)
            
            logger.info(f"Reddit数据获取完成: {project_name}")
            return data
            
        except Exception as e:
            logger.error(f"获取Reddit数据失败: {str(e)}")
            return {}
    
    async def _ensure_access_token(self):
        """确保有有效的访问令牌"""
        try:
            current_time = time.time()
            
            # 如果令牌未过期，直接返回
            if self.access_token and current_time < self.token_expires_at:
                return
            
            # 获取新的访问令牌
            auth_url = "https://www.reddit.com/api/v1/access_token"
            
            headers = {
                "User-Agent": self.user_agent
            }
            
            data = {
                "grant_type": "client_credentials"
            }
            
            auth = (self.client_id, self.client_secret)
            
            response = await self._make_request(
                auth_url, 
                method="POST", 
                headers=headers, 
                data=data, 
                auth=auth
            )
            
            if response and "access_token" in response:
                self.access_token = response["access_token"]
                expires_in = response.get("expires_in", 3600)
                self.token_expires_at = current_time + expires_in - 60  # 提前1分钟刷新
                logger.info("Reddit访问令牌获取成功")
            else:
                logger.warning("Reddit访问令牌获取失败")
                
        except Exception as e:
            logger.error(f"获取Reddit访问令牌失败: {str(e)}")
    
    def _build_search_queries(
        self, 
        project_name: Optional[str], 
        keywords: Optional[List[str]]
    ) -> List[str]:
        """构建搜索查询"""
        queries = []
        
        if project_name:
            # 项目名称查询
            queries.append(project_name)
            
            # 项目名称 + 常见讨论词汇
            discussion_terms = ["review", "experience", "tutorial", "guide", "vs", "alternative"]
            for term in discussion_terms[:3]:
                queries.append(f"{project_name} {term}")
        
        if keywords:
            queries.extend(keywords[:3])
        
        return queries[:6]  # 限制查询数量
    
    async def _fetch_posts(
        self, 
        search_queries: List[str], 
        subreddits: List[str]
    ) -> List[Dict[str, Any]]:
        """获取相关帖子"""
        try:
            all_posts = []
            
            for query in search_queries:
                await asyncio.sleep(self.request_delay)
                
                # 使用Reddit搜索API
                url = f"{self.base_url}/search.json"
                params = {
                    "q": query,
                    "sort": "relevance",
                    "limit": 10,
                    "type": "link",
                    "restrict_sr": "off"
                }
                
                headers = {"User-Agent": self.user_agent}
                if self.access_token:
                    headers["Authorization"] = f"Bearer {self.access_token}"
                
                data = await self._make_request(url, params=params, headers=headers)
                
                if data and "data" in data and "children" in data["data"]:
                    posts = []
                    for item in data["data"]["children"]:
                        post_data = item.get("data", {})
                        post = {
                            "id": post_data.get("id"),
                            "title": post_data.get("title"),
                            "selftext": post_data.get("selftext", "")[:500],  # 限制内容长度
                            "score": post_data.get("score", 0),
                            "upvote_ratio": post_data.get("upvote_ratio", 0),
                            "num_comments": post_data.get("num_comments", 0),
                            "subreddit": post_data.get("subreddit"),
                            "author": post_data.get("author"),
                            "created_utc": post_data.get("created_utc"),
                            "url": post_data.get("url"),
                            "permalink": f"https://reddit.com{post_data.get('permalink', '')}",
                            "is_self": post_data.get("is_self", False),
                            "domain": post_data.get("domain"),
                            "flair_text": post_data.get("link_flair_text")
                        }
                        posts.append(post)
                    
                    all_posts.extend(posts)
            
            # 去重和排序
            unique_posts = {}
            for post in all_posts:
                post_id = post["id"]
                if post_id not in unique_posts or post["score"] > unique_posts[post_id]["score"]:
                    unique_posts[post_id] = post
            
            # 按评分排序
            sorted_posts = sorted(
                unique_posts.values(),
                key=lambda x: (x["score"], x["num_comments"]),
                reverse=True
            )
            
            return sorted_posts[:20]
            
        except Exception as e:
            logger.error(f"获取Reddit帖子失败: {str(e)}")
            return []
    
    async def _fetch_comments(
        self, 
        search_queries: List[str], 
        subreddits: List[str]
    ) -> List[Dict[str, Any]]:
        """获取相关评论"""
        try:
            all_comments = []
            
            for query in search_queries[:3]:  # 限制查询数量
                await asyncio.sleep(self.request_delay)
                
                # 搜索评论
                url = f"{self.base_url}/search.json"
                params = {
                    "q": query,
                    "sort": "relevance",
                    "limit": 5,
                    "type": "comment"
                }
                
                headers = {"User-Agent": self.user_agent}
                if self.access_token:
                    headers["Authorization"] = f"Bearer {self.access_token}"
                
                data = await self._make_request(url, params=params, headers=headers)
                
                if data and "data" in data and "children" in data["data"]:
                    comments = []
                    for item in data["data"]["children"]:
                        comment_data = item.get("data", {})
                        comment = {
                            "id": comment_data.get("id"),
                            "body": comment_data.get("body", "")[:300],  # 限制内容长度
                            "score": comment_data.get("score", 0),
                            "subreddit": comment_data.get("subreddit"),
                            "author": comment_data.get("author"),
                            "created_utc": comment_data.get("created_utc"),
                            "permalink": f"https://reddit.com{comment_data.get('permalink', '')}",
                            "parent_id": comment_data.get("parent_id"),
                            "link_title": comment_data.get("link_title")
                        }
                        comments.append(comment)
                    
                    all_comments.extend(comments)
            
            # 按评分排序
            sorted_comments = sorted(
                all_comments,
                key=lambda x: x["score"],
                reverse=True
            )
            
            return sorted_comments[:15]
            
        except Exception as e:
            logger.error(f"获取Reddit评论失败: {str(e)}")
            return []
    
    async def _fetch_subreddit_stats(self, subreddits: List[str]) -> List[Dict[str, Any]]:
        """获取subreddit统计信息"""
        try:
            stats = []
            
            for subreddit in subreddits[:5]:  # 限制数量
                await asyncio.sleep(self.request_delay)
                
                url = f"{self.base_url}/r/{subreddit}/about.json"
                headers = {"User-Agent": self.user_agent}
                
                if self.access_token:
                    headers["Authorization"] = f"Bearer {self.access_token}"
                
                data = await self._make_request(url, headers=headers)
                
                if data and "data" in data:
                    subreddit_data = data["data"]
                    stat = {
                        "name": subreddit,
                        "display_name": subreddit_data.get("display_name"),
                        "subscribers": subreddit_data.get("subscribers", 0),
                        "active_users": subreddit_data.get("accounts_active", 0),
                        "description": subreddit_data.get("public_description", "")[:200],
                        "created_utc": subreddit_data.get("created_utc"),
                        "over18": subreddit_data.get("over18", False),
                        "lang": subreddit_data.get("lang")
                    }
                    stats.append(stat)
            
            return stats
            
        except Exception as e:
            logger.error(f"获取subreddit统计失败: {str(e)}")
            return []
    
    async def _fetch_trending_discussions(self, search_queries: List[str]) -> List[Dict[str, Any]]:
        """获取热门讨论"""
        try:
            trending = []
            
            for query in search_queries[:2]:  # 限制查询数量
                await asyncio.sleep(self.request_delay)
                
                url = f"{self.base_url}/search.json"
                params = {
                    "q": query,
                    "sort": "hot",
                    "limit": 5,
                    "t": "week"  # 本周热门
                }
                
                headers = {"User-Agent": self.user_agent}
                if self.access_token:
                    headers["Authorization"] = f"Bearer {self.access_token}"
                
                data = await self._make_request(url, params=params, headers=headers)
                
                if data and "data" in data and "children" in data["data"]:
                    for item in data["data"]["children"]:
                        post_data = item.get("data", {})
                        discussion = {
                            "title": post_data.get("title"),
                            "score": post_data.get("score", 0),
                            "num_comments": post_data.get("num_comments", 0),
                            "subreddit": post_data.get("subreddit"),
                            "created_utc": post_data.get("created_utc"),
                            "permalink": f"https://reddit.com{post_data.get('permalink', '')}"
                        }
                        trending.append(discussion)
            
            # 按热度排序
            trending.sort(key=lambda x: (x["score"] + x["num_comments"]), reverse=True)
            return trending[:10]
            
        except Exception as e:
            logger.error(f"获取热门讨论失败: {str(e)}")
            return []
    
    def _analyze_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分析Reddit数据"""
        try:
            posts = data.get("posts", [])
            comments = data.get("comments", [])
            subreddit_stats = data.get("subreddit_stats", [])
            
            analysis = {
                "community_sentiment": self._analyze_sentiment(posts, comments),
                "discussion_topics": self._analyze_discussion_topics(posts),
                "user_engagement": self._analyze_user_engagement(posts, comments),
                "community_reach": self._analyze_community_reach(subreddit_stats),
                "trending_status": self._analyze_trending_status(data.get("trending_discussions", []))
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析Reddit数据失败: {str(e)}")
            return {}
    
    def _analyze_sentiment(
        self, 
        posts: List[Dict[str, Any]], 
        comments: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """分析社区情感倾向"""
        if not posts and not comments:
            return {"sentiment": "neutral", "confidence": 0.0}
        
        # 简单的情感分析（基于评分和关键词）
        positive_keywords = ["great", "awesome", "excellent", "love", "amazing", "perfect", "best"]
        negative_keywords = ["bad", "terrible", "awful", "hate", "worst", "broken", "useless"]
        
        total_score = 0
        positive_signals = 0
        negative_signals = 0
        total_items = 0
        
        # 分析帖子
        for post in posts:
            total_score += post.get("score", 0)
            total_items += 1
            
            text = (post.get("title", "") + " " + post.get("selftext", "")).lower()
            positive_signals += sum(1 for word in positive_keywords if word in text)
            negative_signals += sum(1 for word in negative_keywords if word in text)
        
        # 分析评论
        for comment in comments:
            total_score += comment.get("score", 0)
            total_items += 1
            
            text = comment.get("body", "").lower()
            positive_signals += sum(1 for word in positive_keywords if word in text)
            negative_signals += sum(1 for word in negative_keywords if word in text)
        
        # 计算情感倾向
        avg_score = total_score / total_items if total_items > 0 else 0
        
        if avg_score > 5 and positive_signals > negative_signals:
            sentiment = "positive"
        elif avg_score < -2 or negative_signals > positive_signals * 1.5:
            sentiment = "negative"
        else:
            sentiment = "neutral"
        
        confidence = min(1.0, (positive_signals + negative_signals) / max(1, total_items))
        
        return {
            "sentiment": sentiment,
            "confidence": round(confidence, 2),
            "average_score": round(avg_score, 2),
            "positive_signals": positive_signals,
            "negative_signals": negative_signals,
            "total_analyzed": total_items
        }
    
    def _analyze_discussion_topics(self, posts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析讨论主题"""
        if not posts:
            return []
        
        # 提取主要讨论主题
        topics = {}
        
        for post in posts:
            title = post.get("title", "").lower()
            subreddit = post.get("subreddit", "")
            
            # 简单的主题分类
            if any(word in title for word in ["tutorial", "guide", "how to", "learn"]):
                topic = "教程和指南"
            elif any(word in title for word in ["review", "opinion", "thoughts"]):
                topic = "评价和观点"
            elif any(word in title for word in ["problem", "issue", "error", "bug"]):
                topic = "问题和错误"
            elif any(word in title for word in ["vs", "compare", "alternative"]):
                topic = "对比和选择"
            elif any(word in title for word in ["news", "release", "update"]):
                topic = "新闻和更新"
            else:
                topic = "一般讨论"
            
            if topic not in topics:
                topics[topic] = {"count": 0, "total_score": 0, "posts": []}
            
            topics[topic]["count"] += 1
            topics[topic]["total_score"] += post.get("score", 0)
            topics[topic]["posts"].append({
                "title": post.get("title"),
                "score": post.get("score", 0),
                "subreddit": subreddit,
                "permalink": post.get("permalink")
            })
        
        # 转换为列表并排序
        topic_list = []
        for topic, data in topics.items():
            topic_info = {
                "topic": topic,
                "count": data["count"],
                "average_score": round(data["total_score"] / data["count"], 2),
                "top_posts": sorted(data["posts"], key=lambda x: x["score"], reverse=True)[:3]
            }
            topic_list.append(topic_info)
        
        return sorted(topic_list, key=lambda x: x["count"], reverse=True)
    
    def _analyze_user_engagement(
        self, 
        posts: List[Dict[str, Any]], 
        comments: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """分析用户参与度"""
        if not posts:
            return {"engagement_level": "low"}
        
        total_posts = len(posts)
        total_comments = sum(post.get("num_comments", 0) for post in posts)
        total_score = sum(post.get("score", 0) for post in posts)
        avg_upvote_ratio = sum(post.get("upvote_ratio", 0) for post in posts) / total_posts
        
        # 参与度评级
        avg_comments_per_post = total_comments / total_posts
        avg_score_per_post = total_score / total_posts
        
        if avg_comments_per_post >= 20 and avg_score_per_post >= 10:
            engagement_level = "high"
        elif avg_comments_per_post >= 10 and avg_score_per_post >= 5:
            engagement_level = "medium"
        else:
            engagement_level = "low"
        
        return {
            "engagement_level": engagement_level,
            "total_posts": total_posts,
            "total_comments": total_comments,
            "average_comments_per_post": round(avg_comments_per_post, 2),
            "average_score_per_post": round(avg_score_per_post, 2),
            "average_upvote_ratio": round(avg_upvote_ratio, 2)
        }
    
    def _analyze_community_reach(self, subreddit_stats: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析社区覆盖范围"""
        if not subreddit_stats:
            return {"reach_level": "unknown"}
        
        total_subscribers = sum(stat.get("subscribers", 0) for stat in subreddit_stats)
        total_active = sum(stat.get("active_users", 0) for stat in subreddit_stats)
        
        # 覆盖范围评级
        if total_subscribers >= 1000000:
            reach_level = "massive"
        elif total_subscribers >= 100000:
            reach_level = "large"
        elif total_subscribers >= 10000:
            reach_level = "medium"
        else:
            reach_level = "small"
        
        return {
            "reach_level": reach_level,
            "total_subscribers": total_subscribers,
            "total_active_users": total_active,
            "subreddits_analyzed": len(subreddit_stats),
            "top_subreddits": sorted(
                subreddit_stats, 
                key=lambda x: x.get("subscribers", 0), 
                reverse=True
            )[:5]
        }
    
    def _analyze_trending_status(self, trending_discussions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析趋势状态"""
        if not trending_discussions:
            return {"trending_level": "none"}
        
        total_score = sum(d.get("score", 0) for d in trending_discussions)
        total_comments = sum(d.get("num_comments", 0) for d in trending_discussions)
        
        # 趋势评级
        if total_score >= 1000 and total_comments >= 100:
            trending_level = "viral"
        elif total_score >= 500 and total_comments >= 50:
            trending_level = "trending"
        elif total_score >= 100:
            trending_level = "popular"
        else:
            trending_level = "normal"
        
        return {
            "trending_level": trending_level,
            "total_trending_score": total_score,
            "total_trending_comments": total_comments,
            "trending_discussions_count": len(trending_discussions)
        }
    
    async def is_available(self) -> bool:
        """检查Reddit API是否可用"""
        try:
            url = f"{self.base_url}/api/v1/me"
            headers = {"User-Agent": self.user_agent}
            
            # 简单的可用性检查
            response = await self._make_request(url, headers=headers)
            return True  # 如果没有异常，说明API可用
            
        except Exception as e:
            logger.error(f"检查Reddit API可用性失败: {str(e)}")
            return False
