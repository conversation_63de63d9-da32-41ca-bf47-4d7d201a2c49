"""
用户相关的Pydantic模型
包含用户的创建、更新和查询模型
"""
from datetime import datetime
from typing import Optional, List
import re
from typing_extensions import Self
from pydantic import BaseModel, Field, field_validator, model_validator, EmailStr
from .role import Role

class User(BaseModel):
    """用户数据模型"""
    id: str = Field(..., description='用户ID')
    username: str = Field(..., min_length=3, max_length=50, pattern=r'^[a-zA-Z0-9_]+$', description='用户名')
    email: Optional[EmailStr] = Field(..., description='电子邮箱')
    nickname: Optional[str] = Field(None, max_length=50, description='昵称')
    phone: Optional[str] = Field(None, max_length=20, description='手机号')
    hashed_password: Optional[str] = Field(None, min_length=8, exclude=True, max_length=100, description='密码')
    is_active: bool = Field(True, description='是否启用')
    is_superuser: bool = Field(False, description='是否超级管理员')
    needs_email_binding: bool = Field(False, description='是否需要绑定')
    last_login: Optional[datetime] = Field(None, description='最后登录时间')
    roles: List[Role] = Field(default_factory=list, exclude=True, description='角色列表')
    created_at: datetime = Field(..., description='创建时间')
    updated_at: datetime = Field(..., description='更新时间')
    failed_login_attempts: int = Field(0, ge=0, description='失败登录次数')
    locked_until: Optional[datetime] = Field(None, description='锁定时间')
    version: str = Field('1.0.0', description='版本号')

    @field_validator('username')
    def validate_username(cls, v: str) -> str:
        if not v.isalnum() and '_' not in v:
            raise ValueError('用户名只能包含字母、数字和下划线')
        return v

    @model_validator(mode="after")
    def convert_roles(self) -> Self:
        self.roles = [Role.model_validate(role) for role in self.roles]
        return self

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "username": "admin",
                "email": "<EMAIL>",
                "nickname": "管理员",
                "phone": "12345678901",
                "is_active": True,
                "is_superuser": True,
                "last_login": "2024-01-01T12:00:00Z",
                "roles": [],
                "failed_login_attempts": 0,
                "locked_until": None,
                "version": "1.0.0"
            }
        }


class UserCreateRequest(BaseModel):
    """创建用户的请求数据模型"""
    username: str = Field(..., min_length=3, max_length=40, pattern=r'^[a-zA-Z0-9_]+$', description='用户名')
    #email: EmailStr = Field(..., description='电子邮箱')
    email: Optional[str] = Field(None, description='电子邮箱')
    password: str = Field(..., min_length=8, max_length=20, description='密码')
    nickname: Optional[str] = Field(None, min_length=3, max_length=40, description='昵称')
    phone: Optional[str] = Field(None, max_length=20, description='手机号')
    is_active: bool = Field(True, description='是否启用')
    is_superuser: bool = Field(False, description='是否超级管理员')
    needs_email_binding: bool = Field(False, description='是否绑定邮箱')
    role_ids: Optional[List[str]] = Field(None, description='角色ID列表')

    @field_validator('username')
    def validate_username(cls, v: str) -> str:
        if not v.isalnum() and '_' not in v:
            raise ValueError('用户名只能包含字母、数字和下划线')
        if len(v) < 3:
            raise ValueError('用户名长度不能少于4个字符')
        if len(v) > 40:
            raise ValueError('用户名长度不能超过40个字符')

        return v

    @field_validator('nickname')
    def validate_nickname(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            raise ValueError('昵称不能为空')
        if len(v) < 3:
            raise ValueError('昵称长度不能少于3个字符')
        if len(v) > 40:
            raise ValueError('昵称长度不能超过40个字符')
        return v

    @field_validator('password')
    def validate_password(cls, v: str) -> str:
        # 使用正则表达式验证密码要求
        pattern = r'^(?=.*[a-z])(?=.*[A-Z]).*$'
        if not re.match(pattern, v):
            raise ValueError('密码必须同时包含大写字母和小写字母')
        return v

    @field_validator('email')
    def validate_email(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return v

        # 邮箱格式验证（EmailStr已经包含了基本验证）
        # if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', v):
        #     raise ValueError('邮箱格式不正确')

        # 邮箱长度验证
        if len(v) > 254:  # RFC 5321 标准
            raise ValueError('邮箱长度不能超过254个字符')

        # 邮箱域名验证
        # domain = v.split('@')[1]
        # if len(domain) > 255:  # RFC 5321 标准
        #     raise ValueError('邮箱域名长度不能超过255个字符')

        # 邮箱用户名部分验证
        # username = v.split('@')[0]
        # if len(username) > 64:  # RFC 5321 标准
        #     raise ValueError('邮箱用户名部分长度不能超过64个字符')

        return v

    # @field_validator('phone')
    # def validate_phone(cls, v: Optional[str]) -> Optional[str]:
    #     if v is None:
    #         return v
    #
    #     # 手机号格式验证（中国大陆手机号）
    #     if not re.match(r'^1[3-9]\d{9}$', v):
    #         raise ValueError('手机号格式不正确，请输入11位中国大陆手机号')
    #
    #     # 手机号长度验证
    #     if len(v) != 11:
    #         raise ValueError('手机号长度必须为11位')



    class Config:
        json_schema_extra = {
            "example": {
                "username": "admin",
                "email": "<EMAIL>",
                "password": "Admin@123",
                "nickname": "管理员",
                "is_active": True,
                "is_superuser": True,
                "role_ids": ["1"]
            }
        }

class UserUpdateRequest(BaseModel):
    """更新用户的请求数据模型"""
    user_id: str = Field(..., description='用户ID')
    email: Optional[EmailStr] = Field(None, description='电子邮箱')
    phone: Optional[str] = Field(None, description='电话')
    username: Optional[str] = Field(None, min_length=3, max_length=50, pattern=r'^[a-zA-Z0-9_]+$', description='用户名')
    password: Optional[str] = Field(None, min_length=8, max_length=100, description='密码')
    nickname: Optional[str] = Field(None, max_length=50, description='昵称')
    is_active: Optional[bool] = Field(None, description='是否启用')
    role_ids: Optional[List[str]] = Field(None, description='角色ID列表')

    @field_validator('password')
    def validate_password(cls, v: str) -> str:
        # 使用正则表达式验证密码要求
        pattern = r'^(?=.*[a-z])(?=.*[A-Z]).*$'
        if not re.match(pattern, v):
            raise ValueError('密码必须同时包含大写字母和小写字母')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "Admin@123",
                "nickname": "管理员",
                "is_active": True,
                "role_ids": ["1", "2"]
            }
        }


class UserChangePasswordRequest(BaseModel):
    """修改密码的请求数据模型"""
    user_id: Optional[str] = Field(None, description='用户ID')
    current_password: str = Field(..., min_length=8, max_length=100, description='当前密码')
    new_password: str = Field(..., min_length=8, max_length=100, description='新密码')


    @field_validator('new_password')
    def validate_password(cls, v: str) -> str:
        # 使用正则表达式验证密码要求
        pattern = r'^(?=.*[a-z])(?=.*[A-Z]).*$'
        if not re.match(pattern, v):
            raise ValueError('密码必须同时包含大写字母和小写字母')
        return v


class UpdateOauthEmailRequest(BaseModel):
    """修改密码的请求数据模型"""
    validate_code: str = Field(..., description='验证码')
    email: str = Field(..., description='邮箱')
    user_id: str = Field(..., description='user_id')



class UpdateSelfPasswordWithOutValidateRequest(BaseModel):
    """（新）修改密码的请求数据模型"""
    new_password: str = Field(..., min_length=8, max_length=100, description='新密码')
    user_id: str = Field(..., description='用户ID')

class UpdateSelfPasswordRequest(BaseModel):
    """修改密码的请求数据模型"""
    new_password: str = Field(..., min_length=8, max_length=100, description='新密码')
    validate_code: str = Field(..., description='验证码')
    email: str = Field(None, description='邮箱')
    phone: str = Field(None, description='电话')

    @field_validator('new_password')
    def validate_password(cls, v: str) -> str:
        # 使用正则表达式验证密码要求
        pattern = r'^(?=.*[a-z])(?=.*[A-Z]).*$'
        if not re.match(pattern, v):
            raise ValueError('密码必须同时包含大写字母和小写字母')
        return v

    #
    # class Config:
    #     json_schema_extra = {
    #         "example": {
    #             "current_password": "OldPassword@123",
    #             "new_password": "NewPassword@123"
    #         }
    #     }


class CurrentUserUpdateRequest(BaseModel):
    """当前用户更新请求"""
    username: Optional[str] = Field(None, min_length=3, max_length=50, pattern=r'^[a-zA-Z0-9_]+$', description='用户名')
    email: Optional[str] = Field(None, description='电子邮箱')
    password: Optional[str] = Field(None, min_length=8, max_length=100, description='密码')
    nickname: Optional[str] = Field(None, max_length=50, description='昵称')

class UserResetPasswordRequest(BaseModel):
    """重置密码的请求数据模型"""
    user_id: str = Field(..., description='用户ID')
    password: str = Field(..., min_length=8, max_length=100, description='密码')

    @field_validator('password')
    def validate_password(cls, v: str) -> str:
        # 使用正则表达式验证密码要求
        pattern = r'^(?=.*[a-z])(?=.*[A-Z]).*$'
        if not re.match(pattern, v):
            raise ValueError('密码必须同时包含大写字母和小写字母')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "password": "Admin@123"
            }
        }


class UserLoginRequest(BaseModel):
    """用户登录请求"""
    username: str = Field(..., min_length=3, max_length=64, description="账号，可以是用户名、邮箱或手机号")
    password: str = Field(..., min_length=8, max_length=32, description="密码")


class UserLogoutRequest(BaseModel):
    """登出请求模型"""
    user_id: str = Field(..., description='用户ID')
    token: str = Field(..., description='访问令牌')

    class Config:
        json_schema_extra = {
            "example": {
                "user_id": "1",
                "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            }
        }


class UserListResponse(BaseModel):
    """用户列表分页响应"""
    total: int = Field(..., description="总记录数")
    users: List[User] = Field(..., description="用户列表")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(0, description="总页数")

    @model_validator(mode="after")
    def compute_total_pages(self) -> Self:
        """计算总页数"""    
        if self.total and self.page_size and self.total > 0 and self.page_size > 0:
            self.total_pages = (self.total + self.page_size - 1) // self.page_size
        else:
            self.total_pages = 0
        return self

    class Config:
        """配置"""
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


User.model_rebuild()
UserListResponse.model_rebuild()