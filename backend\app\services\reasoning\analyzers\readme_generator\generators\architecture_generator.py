#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
架构章节生成器
"""
import logging
import os
from typing import Any, Dict, List, Optional, Tuple

from langchain.chains import <PERSON><PERSON>hain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser

from . import Generator
from ..section_models import ArchitectureSection, Component, Relationship
from .....ai_agent_core import AgentInput, AnalysisOutputParser
from ......utils.retry import retry_async

logger = logging.getLogger(__name__)


class ArchitectureGenerator(Generator):
    """
    架构章节生成器
    负责生成README文档的架构章节
    """

    def __init__(self, llm: ChatOpenAI):
        """初始化架构章节生成器"""
        super().__init__(llm)

    async def create_chain(self) -> None:
        """初始化处理链"""
        try:
            # 架构章节生成提示模板
            architecture_template = """
            作为精通各种软件架构风格与模式的高级架构师，请基于项目分析数据生成深入、全面且实用的架构设计文档。

            ## 项目基本信息
            项目名称: {{ project_name }}
            项目路径: {{ project_path }}

            {%- if architecture_analysis %}

            ## 架构分析数据
            {%- if architecture_analysis.architecture_style %}
            架构风格: {{ architecture_analysis.architecture_style }}
            {%- endif %}
            {%- if architecture_analysis.main_languages %}
            主要语言: {{ ", ".join(architecture_analysis.main_languages) }}
            {%- endif %}
            {%- if architecture_analysis.architecture_quality %}
            架构质量: 可维护性 {{ architecture_analysis.architecture_quality.maintainability }}/10, 可扩展性 {{ architecture_analysis.architecture_quality.extensibility }}/10, 模块化 {{ architecture_analysis.architecture_quality.modularity }}/10
            {%- endif %}

            {%- if architecture_analysis.architecture_components %}

            ### 核心架构组件:
            {%- for comp in architecture_analysis.architecture_components[:6] %}
            - **{{ comp.name }}** ({{ comp.layer }}): {{ comp.type }}
            {%- endfor %}
            {%- endif %}

            {%- if architecture_analysis.architecture_patterns %}

            ### 架构模式:
            {%- for pattern in architecture_analysis.architecture_patterns[:4] %}
            - **{{ pattern.name }}**: {{ pattern.description }}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if structure_analysis %}

            ## 项目结构分析
            {%- if structure_analysis.core %}

            ### 核心组件:
            {%- for component in structure_analysis.core[:4] %}
            - **{{ component.name }}** ({{ component.component_kind }}): {{ component.description }}
            {%- endfor %}
            {%- endif %}

            {%- if structure_analysis.entries %}

            ### 系统入口点:
            {%- for entry in structure_analysis.entries[:3] %}
            - **{{ entry.name }}** ({{ entry.entry_category }}): {{ entry.description }}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if dependency_analysis and dependency_analysis.tech_stack %}

            ## 技术栈信息
            {%- if dependency_analysis.tech_stack.primary_language %}
            主要语言: {{ dependency_analysis.tech_stack.primary_language }}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.frameworks %}
            架构框架: {%- for framework in dependency_analysis.tech_stack.frameworks[:4] %}{{ framework }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.databases %}
            数据存储: {%- for db in dependency_analysis.tech_stack.databases[:3] %}{{ db }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if module_merger_analyses %}

            ## 模块架构分析
            {%- for group_key, merger_result in module_merger_analyses.items() %}
            {%- if merger_result.ai_analysis and merger_result.ai_analysis.architectural_components %}

            ### {{ group_key }} 架构组件:
            {%- for component in merger_result.ai_analysis.architectural_components[:3] %}
            - **{{ component.name }}** ({{ component.type }}): {{ component.layer }}层级组件
            {%- endfor %}
            {%- endif %}
            {%- endfor %}
            {%- endif %}

            ## 文档生成要求与原则

            ### 核心生成原则
            1. **基于实际数据**：所有架构内容必须基于提供的项目分析数据生成
            2. **架构重点突出**：重点关注架构设计、组件关系和技术选型
            3. **技术准确专业**：使用准确的架构术语，确保技术描述的专业性
            4. **结构清晰完整**：确保架构文档结构完整，层次分明
            5. **实用性导向**：生成对开发者和架构师都有实用价值的内容
            6. **平衡详细度**：在技术细节与高层概述之间保持平衡

            ### 内容生成任务
            请生成一份专业、全面的架构设计文档，包含以下9个部分：

            1. **架构概述**（200-250字）：简明扼要地描述项目的整体架构风格和设计理念，解释架构选择的原因和优势
            2. **架构图**：提供架构图的文字描述，说明图中应包含的主要元素和组件关系
            3. **架构层次**（每层100-150字）：详细描述每个架构层次的职责、边界和交互方式
            4. **核心组件**（每个组件80-120字）：详细描述4-6个最重要的架构组件及其职责、功能和依赖关系
            5. **设计模式与最佳实践**：列出项目中使用的3-5个主要设计模式及其应用场景
            6. **数据流**：描述系统中的主要数据流向和组件间的数据传递方式
            7. **技术选择**：解释关键技术选择的原因及其如何支持架构目标
            8. **扩展性与可维护性**：分析架构的扩展性和可维护性，提供具体扩展例子
            9. **安全考虑**（如适用）：描述架构中的安全设计考虑和保护措施

            ### 输出语言要求
            所有生成的架构文档内容必须使用中文(简体中文)输出。包括所有架构风格描述、架构层级介绍、组件功能、设计模式说明、架构改进建议等内容都必须是中文。代码示例、组件名称等技术术语保持原样。

            ### 输出质量要求
            1. **内容驱动生成**：内容必须基于提供的实际项目数据，不要臆想不存在的架构元素
            2. **术语专业准确**：使用专业而准确的架构术语，同时确保非技术读者也能理解
            3. **技术细节平衡**：平衡技术细节与高层次概述，使不同背景的读者都能获取有用信息
            4. **优势创新突出**：突出架构的优势和创新点，但也诚实地指出潜在的限制或改进空间
            5. **技术栈一致**：确保描述的架构与项目的实际技术栈和功能需求一致
            6. **实用价值导向**：生成的架构章节应既能帮助开发者理解系统技术构成，也能让项目管理者理解系统设计

            {{ format_instructions }}
            """

            # 创建Parser
            architecture_parser = AnalysisOutputParser(pydantic_object=ArchitectureSection)
            parser = OutputFixingParser.from_llm(parser=architecture_parser, llm=self.llm)

            # 获取格式指令
            architecture_format_instructions = architecture_parser.get_format_instructions()

            # 创建提示模板
            architecture_prompt = PromptTemplate.from_template(
                architecture_template,
                template_format="jinja2"
            ).partial(format_instructions=architecture_format_instructions)

            # 创建处理链
            self.chain = (
                architecture_prompt
                | self.llm
                | parser
            )

            logger.info("架构章节生成器处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: AgentInput) -> None:
        """
        准备输入数据并设置self.input_parameters

        Args:
            input_data: 原始输入数据
        """
        # 初始化self.input_parameters，直接使用输入参数
        self.input_parameters = input_data.parameters.copy()

        logger.info("架构章节生成器输入参数准备完成")

    @retry_async(max_retries=3)
    async def process(self) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询

        Returns:
            Tuple[Any, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 调用LLM处理链
            # result = await self.chain.first.ainvoke(self.input_parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(self.input_parameters)

            # 设置章节顺序
            if result:
                result.order = 5  # 架构章节通常是第五个，在API参考之后

            return result, {"generator": "architecture_generator"}
        except Exception as e:
            logger.error(f"架构章节生成失败: {str(e)}")
            raise
