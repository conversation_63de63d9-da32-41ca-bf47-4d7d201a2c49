#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内部数据源

从项目内部提取数据，包括代码结构、配置文件、文档等
严格从现有的分析数据中提取信息，不依赖外部数据源或推测信息
"""

import logging
from typing import Dict, Any, Optional

# 导入工作流模型
from ....workflow.models import AnalysisState

logger = logging.getLogger(__name__)


class InternalDataSource:
    """内部数据源类 - 从现有分析数据中提取信息"""

    def __init__(self, analysis_state: Optional[AnalysisState] = None):
        """
        初始化内部数据源

        Args:
            analysis_state: 分析状态对象，包含现有的分析结果
        """
        if not analysis_state:
            raise ValueError("分析状态对象不能为空")

        self.project_path = analysis_state.project_path
        self.project_name = analysis_state.project_name
        self.analysis_state = analysis_state
        self.data_cache: Dict[str, Any] = {}

    def extract_all_data(self) -> Dict[str, Any]:
        """
        提取所有内部数据

        Returns:
            Dict[str, Any]: 提取的数据字典
        """
        try:
            logger.info(f"开始从分析状态中提取项目 {self.project_name} 的内部数据")

            # 提取各类数据
            extracted_data = {
                "project_metadata": self._extract_project_metadata(),
                "tech_stack": self._extract_tech_stack_info(),
                "dependencies": self._extract_dependency_info(),
                "architecture": self._extract_architecture_info(),
                "structure": self._extract_structure_info(),
                "modules": self._extract_modules_info(),
                "code_quality": self._extract_code_quality_info(),
                "api_design": self._extract_api_design_info(),
                "file_organization": self._extract_file_organization_info()
            }

            # 缓存提取的数据
            self.data_cache.update(extracted_data)

            logger.info(f"成功提取项目 {self.project_name} 的内部数据")
            return extracted_data

        except Exception as e:
            logger.error(f"提取内部数据时发生错误: {str(e)}")
            return {}

    def _extract_project_metadata(self) -> Dict[str, Any]:
        """
        从分析状态中提取项目元数据

        Returns:
            Dict[str, Any]: 项目元数据
        """
        try:
            metadata = {
                "project_name": self.project_name,
                "project_path": self.project_path,
                "analysis_stage": self.analysis_state.stage,
                "total_files": len(self.analysis_state.files),
                "analyzed_modules": len(self.analysis_state.module_analyses),
                "analysis_progress": self.analysis_state.analysis_progress,
                "start_time": self.analysis_state.start_time.isoformat() if self.analysis_state.start_time else None,
                "end_time": self.analysis_state.end_time.isoformat() if self.analysis_state.end_time else None
            }

            # 从项目结构扫描结果中提取基本信息
            if self.analysis_state.project_structure:
                ps = self.analysis_state.project_structure
                metadata.update({
                    "project_type": ps.project_type,
                    "total_directories": ps.total_directories,
                    "total_lines_of_code": ps.total_lines_of_code,
                    "total_size": ps.total_size,
                    "language_summary": ps.language_summary,
                    "file_types": ps.file_types,
                    "created_at": ps.created_at.isoformat() if ps.created_at else None,
                    "last_modified": ps.last_modified.isoformat() if ps.last_modified else None,
                    "average_directory_depth": ps.average_directory_depth,
                    "max_directory_depth": ps.max_directory_depth
                })

            return metadata

        except Exception as e:
            logger.error(f"提取项目元数据时发生错误: {str(e)}")
            return {}

    def _extract_tech_stack_info(self) -> Dict[str, Any]:
        """
        从依赖分析结果中提取技术栈信息

        Returns:
            Dict[str, Any]: 技术栈信息
        """
        try:
            tech_stack_info = {
                "primary_language": "",
                "frameworks": [],
                "databases": [],
                "frontend": [],
                "backend": [],
                "build_tools": [],
                "deployment": []
            }

            # 从依赖分析中提取技术栈
            if self.analysis_state.dependency_analysis:
                dep_analysis = self.analysis_state.dependency_analysis
                if dep_analysis.tech_stack:
                    tech_stack = dep_analysis.tech_stack
                    tech_stack_info.update({
                        "primary_language": tech_stack.primary_language,
                        "frameworks": tech_stack.frameworks,
                        "databases": tech_stack.databases,
                        "frontend": tech_stack.frontend,
                        "backend": tech_stack.backend,
                        "build_tools": tech_stack.build_tools,
                        "deployment": tech_stack.deployment
                    })

            # 从项目结构扫描结果中补充语言信息
            if self.analysis_state.project_structure and not tech_stack_info["primary_language"]:
                ps = self.analysis_state.project_structure
                if ps.language_summary:
                    # 找到代码行数最多的语言作为主要语言
                    primary_lang = max(ps.language_summary.items(), key=lambda x: x[1])[0]
                    tech_stack_info["primary_language"] = primary_lang

            return tech_stack_info

        except Exception as e:
            logger.error(f"提取技术栈信息时发生错误: {str(e)}")
            return {}

    def _extract_dependency_info(self) -> Dict[str, Any]:
        """
        从依赖分析结果中提取依赖关系信息

        Returns:
            Dict[str, Any]: 依赖关系信息
        """
        try:
            dependency_info = {
                "external_dependencies": [],
                "dependency_count": 0,
                "direct_dependencies": 0,
                "dev_dependencies": 0,
                "vulnerable_dependencies": 0,
                "dependency_categories": {},
                "licenses": {}
            }

            if self.analysis_state.dependency_analysis:
                dep_analysis = self.analysis_state.dependency_analysis

                # 提取外部依赖信息
                external_deps = []
                direct_count = 0
                dev_count = 0
                vulnerable_count = 0
                categories = {}
                licenses = {}

                for dep in dep_analysis.external_dependencies:
                    dep_info = {
                        "name": dep.name,
                        "version": dep.version,
                        "purpose": dep.purpose,
                        "is_direct": dep.is_direct,
                        "is_dev_dependency": dep.is_dev_dependency,
                        "license": dep.license,
                        "is_vulnerable": dep.is_vulnerable,
                        "category": dep.dependency_category,
                        "source_url": dep.source_url
                    }
                    external_deps.append(dep_info)

                    # 统计信息
                    if dep.is_direct:
                        direct_count += 1
                    if dep.is_dev_dependency:
                        dev_count += 1
                    if dep.is_vulnerable:
                        vulnerable_count += 1

                    # 分类统计
                    category = dep.dependency_category
                    categories[category] = categories.get(category, 0) + 1

                    # 许可证统计
                    license_name = dep.license
                    if license_name:
                        licenses[license_name] = licenses.get(license_name, 0) + 1

                dependency_info.update({
                    "external_dependencies": external_deps,
                    "dependency_count": len(external_deps),
                    "direct_dependencies": direct_count,
                    "dev_dependencies": dev_count,
                    "vulnerable_dependencies": vulnerable_count,
                    "dependency_categories": categories,
                    "licenses": licenses
                })

            return dependency_info

        except Exception as e:
            logger.error(f"提取依赖信息时发生错误: {str(e)}")
            return {}

    def _extract_architecture_info(self) -> Dict[str, Any]:
        """
        从架构分析结果中提取架构信息

        Returns:
            Dict[str, Any]: 架构信息
        """
        try:
            architecture_info = {
                "architecture_style": "",
                "main_languages": [],
                "layers": [],
                "components": [],
                "patterns": [],
                "anti_patterns": [],
                "quality_metrics": {},
                "recommendations": []
            }

            if self.analysis_state.architecture_analysis:
                arch_analysis = self.analysis_state.architecture_analysis

                architecture_info.update({
                    "architecture_style": arch_analysis.architecture_style,
                    "main_languages": arch_analysis.main_languages
                })

                # 提取架构层次信息
                layers = []
                for layer in arch_analysis.architecture_layers:
                    layer_info = {
                        "name": layer.name,
                        "responsibilities": layer.responsibilities,
                        "components": layer.components
                    }
                    layers.append(layer_info)
                architecture_info["layers"] = layers

                # 提取架构组件信息
                components = []
                for comp in arch_analysis.architecture_components:
                    comp_info = {
                        "name": comp.name,
                        "path": comp.path,
                        "purpose": comp.purpose,
                        "layer": comp.layer
                    }
                    components.append(comp_info)
                architecture_info["components"] = components

                # 提取架构模式信息
                patterns = []
                for pattern in arch_analysis.architecture_patterns:
                    pattern_info = {
                        "name": pattern.name,
                        "description": pattern.description,
                        "applied_at": pattern.applied_at
                    }
                    patterns.append(pattern_info)
                architecture_info["patterns"] = patterns

                # 提取反模式信息
                anti_patterns = []
                for anti in arch_analysis.anti_patterns:
                    anti_info = {
                        "name": anti.name,
                        "description": anti.description,
                        "severity": anti.severity
                    }
                    anti_patterns.append(anti_info)
                architecture_info["anti_patterns"] = anti_patterns

                # 提取质量指标
                quality = arch_analysis.architecture_quality
                architecture_info["quality_metrics"] = {
                    "maintainability": quality.maintainability,
                    "extensibility": quality.extensibility,
                    "modularity": quality.modularity,
                    "overall": quality.overall
                }

                # 提取改进建议
                recommendations = []
                for rec in arch_analysis.recommendations:
                    rec_info = {
                        "target_area": rec.target_area,
                        "recommendation": rec.recommendation,
                        "priority": rec.priority
                    }
                    recommendations.append(rec_info)
                architecture_info["recommendations"] = recommendations

            return architecture_info

        except Exception as e:
            logger.error(f"提取架构信息时发生错误: {str(e)}")
            return {}

    def _extract_structure_info(self) -> Dict[str, Any]:
        """
        从结构分析结果中提取项目组织信息

        Returns:
            Dict[str, Any]: 项目结构信息
        """
        try:
            structure_info = {
                "project_type": "",
                "primary_language": "",
                "core_components": [],
                "config_components": [],
                "dependency_components": [],
                "test_components": [],
                "build_components": [],
                "doc_components": [],
                "entry_points": []
            }

            if self.analysis_state.structure_analysis:
                struct_analysis = self.analysis_state.structure_analysis

                structure_info.update({
                    "project_type": struct_analysis.project_type,
                    "primary_language": struct_analysis.primary_language
                })

                # 提取核心组件
                core_components = []
                for core in struct_analysis.core:
                    core_info = {
                        "name": core.name,
                        "path": core.path,
                        "component_kind": core.component_kind,
                        "business_importance": core.business_importance,
                        "description": core.description
                    }
                    core_components.append(core_info)
                structure_info["core_components"] = core_components

                # 提取配置组件
                config_components = []
                for config in struct_analysis.configs:
                    config_info = {
                        "name": config.name,
                        "path": config.path,
                        "config_format": config.config_format,
                        "config_scope": config.config_scope,
                        "is_required": config.is_required,
                        "config_priority": config.config_priority,
                        "description": config.description
                    }
                    config_components.append(config_info)
                structure_info["config_components"] = config_components

                # 提取依赖管理组件
                dependency_components = []
                for dep in struct_analysis.dependencies:
                    dep_info = {
                        "name": dep.name,
                        "path": dep.path,
                        "package_manager": dep.package_manager,
                        "dependency_category": dep.dependency_category,
                        "has_lockfile": dep.has_lockfile,
                        "supports_versioning": dep.supports_versioning,
                        "is_monorepo": dep.is_monorepo,
                        "description": dep.description
                    }
                    dependency_components.append(dep_info)
                structure_info["dependency_components"] = dependency_components

                # 提取测试组件
                test_components = []
                for test in struct_analysis.tests:
                    test_info = {
                        "name": test.name,
                        "path": test.path,
                        "test_category": test.test_category,
                        "test_framework": test.test_framework,
                        "test_scope": test.test_scope,
                        "coverage_target": test.coverage_target,
                        "is_automated": test.is_automated,
                        "execution_environment": test.execution_environment,
                        "test_priority": test.test_priority,
                        "description": test.description
                    }
                    test_components.append(test_info)
                structure_info["test_components"] = test_components

                # 提取构建部署组件
                build_components = []
                for build in struct_analysis.builds:
                    build_info = {
                        "name": build.name,
                        "path": build.path,
                        "build_tool": build.build_tool,
                        "deployment_stage": build.deployment_stage,
                        "target_platforms": build.target_platforms,
                        "build_stages": build.build_stages,
                        "supports_multi_arch": build.supports_multi_arch,
                        "automation_level": build.automation_level,
                        "description": build.description
                    }
                    build_components.append(build_info)
                structure_info["build_components"] = build_components

                # 提取文档组件
                doc_components = []
                for doc in struct_analysis.docs:
                    doc_info = {
                        "name": doc.name,
                        "path": doc.path,
                        "doc_category": doc.doc_category,
                        "doc_format": doc.doc_format,
                        "target_audience": doc.target_audience,
                        "maintenance_status": doc.maintenance_status,
                        "doc_importance": doc.doc_importance,
                        "completeness_level": doc.completeness_level,
                        "description": doc.description
                    }
                    doc_components.append(doc_info)
                structure_info["doc_components"] = doc_components

                # 提取入口点组件
                entry_points = []
                for entry in struct_analysis.entries:
                    entry_info = {
                        "name": entry.name,
                        "path": entry.path,
                        "entry_category": entry.entry_category,
                        "execution_context": entry.execution_context,
                        "is_primary": entry.is_primary,
                        "startup_priority": entry.startup_priority,
                        "description": entry.description
                    }
                    entry_points.append(entry_info)
                structure_info["entry_points"] = entry_points

            return structure_info

        except Exception as e:
            logger.error(f"提取结构信息时发生错误: {str(e)}")
            return {}

    def _extract_modules_info(self) -> Dict[str, Any]:
        """
        从模块分析结果中提取模块信息

        Returns:
            Dict[str, Any]: 模块信息
        """
        try:
            modules_info = {
                "total_modules": 0,
                "modules_by_language": {},
                "modules_by_layer": {},
                "modules_by_importance": {},
                "high_risk_modules": [],
                "api_modules": [],
                "module_details": []
            }

            if self.analysis_state.module_analyses:
                modules = self.analysis_state.module_analyses
                modules_info["total_modules"] = len(modules)

                # 按语言分组统计
                by_language = {}
                by_layer = {}
                by_importance = {"high": 0, "medium": 0, "low": 0}
                high_risk_modules = []
                api_modules = []
                module_details = []

                for module_path, module_analysis in modules.items():
                    # 语言统计
                    lang = module_analysis.language
                    by_language[lang] = by_language.get(lang, 0) + 1

                    # 层级统计
                    layer = module_analysis.layer
                    if layer:
                        by_layer[layer] = by_layer.get(layer, 0) + 1

                    # 重要性统计
                    importance = module_analysis.importance
                    if importance >= 4.0:
                        by_importance["high"] += 1
                    elif importance >= 2.5:
                        by_importance["medium"] += 1
                    else:
                        by_importance["low"] += 1

                    # 高风险模块
                    if module_analysis.risk_level in ["high", "critical"]:
                        risk_info = {
                            "module_name": module_analysis.module_name,
                            "module_path": module_analysis.module_path,
                            "risk_level": module_analysis.risk_level,
                            "issues": module_analysis.high_priority_issues
                        }
                        high_risk_modules.append(risk_info)

                    # API模块
                    if module_analysis.api_design.is_api_module:
                        api_info = {
                            "module_name": module_analysis.module_name,
                            "module_path": module_analysis.module_path,
                            "api_type": module_analysis.api_design.api_type,
                            "endpoints_count": len(module_analysis.api_design.endpoints),
                            "quality_score": module_analysis.api_design.quality.overall_score
                        }
                        api_modules.append(api_info)

                    # 模块详细信息
                    module_detail = {
                        "module_name": module_analysis.module_name,
                        "module_path": module_analysis.module_path,
                        "purpose": module_analysis.purpose,
                        "language": module_analysis.language,
                        "importance": module_analysis.importance,
                        "role": module_analysis.role,
                        "layer": module_analysis.layer,
                        "responsibilities": module_analysis.responsibilities,
                        "dependency_count": module_analysis.dependency_count,
                        "has_circular_dependencies": module_analysis.has_circular_dependencies,
                        "code_quality_score": module_analysis.code_quality.overall_score,
                        "risk_level": module_analysis.risk_level,
                        "is_api_module": module_analysis.api_design.is_api_module
                    }
                    module_details.append(module_detail)

                modules_info.update({
                    "modules_by_language": by_language,
                    "modules_by_layer": by_layer,
                    "modules_by_importance": by_importance,
                    "high_risk_modules": high_risk_modules,
                    "api_modules": api_modules,
                    "module_details": module_details
                })

            return modules_info

        except Exception as e:
            logger.error(f"提取模块信息时发生错误: {str(e)}")
            return {}

    def _extract_code_quality_info(self) -> Dict[str, Any]:
        """
        从模块分析结果中提取代码质量信息

        Returns:
            Dict[str, Any]: 代码质量信息
        """
        try:
            quality_info = {
                "overall_quality_score": 0.0,
                "total_code_smells": 0,
                "total_technical_debts": 0,
                "complexity_distribution": {},
                "quality_by_severity": {},
                "debt_by_impact": {},
                "quality_trends": []
            }

            if self.analysis_state.module_analyses:
                modules = self.analysis_state.module_analyses
                total_score = 0.0
                total_smells = 0
                total_debts = 0
                complexity_dist = {"low": 0, "medium": 0, "high": 0}
                quality_by_severity = {"low": 0, "medium": 0, "high": 0, "critical": 0}
                debt_by_impact = {"low": 0, "medium": 0, "high": 0, "critical": 0}

                for module_analysis in modules.values():
                    # 质量评分累计
                    total_score += module_analysis.code_quality.overall_score

                    # 代码异味统计
                    for smell in module_analysis.code_quality.code_smells:
                        total_smells += 1
                        severity = smell.severity
                        quality_by_severity[severity] = quality_by_severity.get(severity, 0) + 1

                    # 技术债务统计
                    for debt in module_analysis.code_quality.technical_debts:
                        total_debts += 1
                        impact = debt.impact
                        debt_by_impact[impact] = debt_by_impact.get(impact, 0) + 1

                    # 复杂度分布
                    complexity = module_analysis.code_quality.complexity_metrics.cyclomatic_complexity
                    if complexity <= 5:
                        complexity_dist["low"] += 1
                    elif complexity <= 10:
                        complexity_dist["medium"] += 1
                    else:
                        complexity_dist["high"] += 1

                # 计算平均质量评分
                if len(modules) > 0:
                    overall_score = total_score / len(modules)
                else:
                    overall_score = 0.0

                quality_info.update({
                    "overall_quality_score": overall_score,
                    "total_code_smells": total_smells,
                    "total_technical_debts": total_debts,
                    "complexity_distribution": complexity_dist,
                    "quality_by_severity": quality_by_severity,
                    "debt_by_impact": debt_by_impact
                })

            return quality_info

        except Exception as e:
            logger.error(f"提取代码质量信息时发生错误: {str(e)}")
            return {}

    def _extract_api_design_info(self) -> Dict[str, Any]:
        """
        从模块分析结果中提取API设计信息

        Returns:
            Dict[str, Any]: API设计信息
        """
        try:
            api_info = {
                "total_api_modules": 0,
                "total_endpoints": 0,
                "api_types": [],
                "endpoints_by_type": {},
                "endpoints_by_method": {},
                "authentication_required_count": 0,
                "deprecated_endpoints_count": 0,
                "api_quality_distribution": {},
                "api_modules_details": []
            }

            if self.analysis_state.module_analyses:
                modules = self.analysis_state.module_analyses
                api_modules_count = 0
                total_endpoints = 0
                api_types = set()
                endpoints_by_type = {}
                endpoints_by_method = {}
                auth_required_count = 0
                deprecated_count = 0
                quality_dist = {"excellent": 0, "good": 0, "fair": 0, "poor": 0}
                api_modules_details = []

                for module_analysis in modules.values():
                    if module_analysis.api_design.is_api_module:
                        api_modules_count += 1
                        api_type = module_analysis.api_design.api_type
                        api_types.add(api_type)

                        # API模块详细信息
                        api_module_detail = {
                            "module_name": module_analysis.module_name,
                            "module_path": module_analysis.module_path,
                            "api_type": api_type,
                            "endpoints_count": len(module_analysis.api_design.endpoints),
                            "quality_score": module_analysis.api_design.quality.overall_score,
                            "issues": module_analysis.api_design.quality.issues
                        }
                        api_modules_details.append(api_module_detail)

                        # 端点统计
                        for endpoint in module_analysis.api_design.endpoints:
                            total_endpoints += 1

                            # 按API类型分组
                            if api_type not in endpoints_by_type:
                                endpoints_by_type[api_type] = []
                            endpoints_by_type[api_type].append({
                                "path": endpoint.path,
                                "method": endpoint.method,
                                "function_name": endpoint.function_name,
                                "description": endpoint.description,
                                "authentication_required": endpoint.authentication_required,
                                "deprecated": endpoint.deprecated
                            })

                            # 按HTTP方法分组
                            method = endpoint.method
                            endpoints_by_method[method] = endpoints_by_method.get(method, 0) + 1

                            # 认证和废弃统计
                            if endpoint.authentication_required:
                                auth_required_count += 1
                            if endpoint.deprecated:
                                deprecated_count += 1

                        # 质量分布统计
                        quality_score = module_analysis.api_design.quality.overall_score
                        if quality_score >= 8.0:
                            quality_dist["excellent"] += 1
                        elif quality_score >= 6.0:
                            quality_dist["good"] += 1
                        elif quality_score >= 4.0:
                            quality_dist["fair"] += 1
                        else:
                            quality_dist["poor"] += 1

                api_info.update({
                    "total_api_modules": api_modules_count,
                    "total_endpoints": total_endpoints,
                    "api_types": list(api_types),
                    "endpoints_by_type": endpoints_by_type,
                    "endpoints_by_method": endpoints_by_method,
                    "authentication_required_count": auth_required_count,
                    "deprecated_endpoints_count": deprecated_count,
                    "api_quality_distribution": quality_dist,
                    "api_modules_details": api_modules_details
                })

            return api_info

        except Exception as e:
            logger.error(f"提取API设计信息时发生错误: {str(e)}")
            return {}

    def _extract_file_organization_info(self) -> Dict[str, Any]:
        """
        从项目结构和文件信息中提取文件组织信息

        Returns:
            Dict[str, Any]: 文件组织信息
        """
        try:
            file_org_info = {
                "total_files": 0,
                "files_by_type": {},
                "files_by_language": {},
                "directory_structure": {},
                "large_files": [],
                "recent_files": [],
                "important_files": []
            }

            if self.analysis_state.files:
                files = self.analysis_state.files
                file_org_info["total_files"] = len(files)

                files_by_type = {}
                files_by_language = {}
                large_files = []
                recent_files = []
                important_files = []

                for file_path, file_info in files.items():
                    # 按文件扩展名分类
                    extension = file_info.extension
                    files_by_type[extension] = files_by_type.get(extension, 0) + 1

                    # 按编程语言分类
                    if file_info.language:
                        language = file_info.language
                        files_by_language[language] = files_by_language.get(language, 0) + 1

                    # 大文件识别（超过100KB或1000行代码）
                    if file_info.size > 100 * 1024 or (file_info.lines_of_code and file_info.lines_of_code > 1000):
                        large_file_info = {
                            "name": file_info.name,
                            "path": file_info.path,
                            "size": file_info.size,
                            "lines_of_code": file_info.lines_of_code,
                            "language": file_info.language
                        }
                        large_files.append(large_file_info)

                    # 最近修改的文件（可以根据需要调整时间范围）
                    recent_file_info = {
                        "name": file_info.name,
                        "path": file_info.path,
                        "last_modified": file_info.last_modified.isoformat(),
                        "language": file_info.language,
                        "is_code_file": file_info.is_code_file
                    }
                    recent_files.append(recent_file_info)

                    # 重要文件识别（代码文件且行数较多）
                    if file_info.is_code_file and file_info.lines_of_code and file_info.lines_of_code > 100:
                        important_file_info = {
                            "name": file_info.name,
                            "path": file_info.path,
                            "lines_of_code": file_info.lines_of_code,
                            "language": file_info.language,
                            "size": file_info.size
                        }
                        important_files.append(important_file_info)

                # 按最后修改时间排序最近文件
                recent_files.sort(key=lambda x: x["last_modified"], reverse=True)
                recent_files = recent_files[:20]  # 只保留最近的20个文件

                # 按代码行数排序重要文件
                important_files.sort(key=lambda x: x["lines_of_code"], reverse=True)
                important_files = important_files[:15]  # 只保留前15个重要文件

                file_org_info.update({
                    "files_by_type": files_by_type,
                    "files_by_language": files_by_language,
                    "large_files": large_files,
                    "recent_files": recent_files,
                    "important_files": important_files
                })

            # 从项目结构中提取目录结构信息
            if self.analysis_state.project_structure:
                ps = self.analysis_state.project_structure
                directory_structure = {
                    "total_directories": ps.total_directories,
                    "average_depth": ps.average_directory_depth,
                    "max_depth": ps.max_directory_depth,
                    "directory_details": []
                }

                # 提取目录详细信息
                for directory in ps.directories:
                    dir_info = {
                        "name": directory.name,
                        "path": directory.path,
                        "file_count": directory.file_count,
                        "subdirectory_count": directory.subdirectory_count,
                        "total_size": directory.total_size,
                        "total_lines_of_code": directory.total_lines_of_code,
                        "languages": directory.languages,
                        "description": directory.description
                    }
                    directory_structure["directory_details"].append(dir_info)

                file_org_info["directory_structure"] = directory_structure

            return file_org_info

        except Exception as e:
            logger.error(f"提取文件组织信息时发生错误: {str(e)}")
            return {}

    def get_cached_data(self, data_type: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的数据

        Args:
            data_type: 数据类型

        Returns:
            Optional[Dict[str, Any]]: 缓存的数据，如果不存在则返回None
        """
        return self.data_cache.get(data_type)

    def clear_cache(self):
        """清空数据缓存"""
        self.data_cache.clear()
        logger.info("内部数据源缓存已清空")
