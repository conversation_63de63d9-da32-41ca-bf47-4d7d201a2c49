"""
Git服务相关的请求和响应模型
"""
from datetime import datetime
from typing import Dict, List, Optional, Any

from pydantic import BaseModel, Field

# 通用模型
class GitAuthor(BaseModel):
    """Git作者信息"""
    name: str = Field(..., description="作者姓名")
    email: str = Field(..., description="作者邮箱")

class GitCommit(BaseModel):
    """Git提交信息"""
    id: str = Field(..., description="提交ID")
    short_id: str = Field(..., description="短提交ID")
    message: str = Field(..., description="提交消息")
    author: GitAuthor = Field(..., description="作者信息")
    committed_date: datetime = Field(..., description="提交时间")

class GitCommitStats(BaseModel):
    """Git提交统计信息"""
    additions: int = Field(..., description="添加行数")
    deletions: int = Field(..., description="删除行数")
    files_changed: int = Field(..., description="修改文件数")

class GitCommitWithStats(GitCommit):
    """带统计信息的Git提交"""
    stats: GitCommitStats = Field(..., description="统计信息")

class GitRepoStats(BaseModel):
    """Git仓库统计信息"""
    total_commits: int = Field(..., description="总提交数")
    total_branches: int = Field(..., description="总分支数")
    total_tags: int = Field(..., description="总标签数")

# 请求模型
class GitRepositoryRequest(BaseModel):
    """Git仓库请求"""
    repo_path: str = Field(..., description="仓库路径")

class GitFileRequest(BaseModel):
    """Git文件请求"""
    repo_path: str = Field(..., description="仓库路径")
    file_path: str = Field(..., description="文件路径")
    ref: Optional[str] = Field("HEAD", description="Git引用（分支、标签或提交ID）")

class GitListFilesRequest(BaseModel):
    """Git列出文件请求"""
    repo_path: str = Field(..., description="仓库路径")
    path: Optional[str] = Field("", description="目录路径")
    ref: Optional[str] = Field("HEAD", description="Git引用（分支、标签或提交ID）")

class GitCommitsRequest(BaseModel):
    """Git提交历史请求"""
    repo_path: str = Field(..., description="仓库路径")
    branch: Optional[str] = Field(None, description="分支名称")
    limit: Optional[int] = Field(50, description="返回数量限制")

class GitFileHistoryRequest(BaseModel):
    """Git文件历史请求"""
    repo_path: str = Field(..., description="仓库路径")
    file_path: str = Field(..., description="文件路径")
    limit: Optional[int] = Field(20, description="返回数量限制")

class GitFindRepositoriesRequest(BaseModel):
    """查找Git仓库请求"""
    base_path: str = Field(..., description="基础目录路径")

# 响应模型
class GitRepositoryInfo(BaseModel):
    """Git仓库信息"""
    name: str = Field(..., description="仓库名称")
    path: str = Field(..., description="仓库路径")
    remote_url: str = Field(..., description="远程URL")
    current_branch: str = Field(..., description="当前分支")
    latest_commit: GitCommit = Field(..., description="最新提交")
    stats: GitRepoStats = Field(..., description="统计信息")
    is_dirty: bool = Field(..., description="是否有未提交的更改")

class GitBranchInfo(BaseModel):
    """Git分支信息"""
    name: str = Field(..., description="分支名称")
    is_current: bool = Field(..., description="是否为当前分支")
    commit: GitCommit = Field(..., description="最新提交")

class GitFileInfo(BaseModel):
    """Git文件信息"""
    name: str = Field(..., description="文件名")
    path: str = Field(..., description="文件路径")
    type: str = Field(..., description="类型（file或dir）")
    size: Optional[int] = Field(0, description="文件大小（仅对文件有效）")
    children: List['GitFileInfo'] = Field(default_factory=list, description="子文件")

GitFileInfo.model_rebuild()

class GitFileContent(BaseModel):
    """Git文件内容"""
    name: str = Field(..., description="文件名")
    content: str = Field(..., description="文件内容")
    path: str = Field(..., description="文件路径")

class GitRepositoryListItem(BaseModel):
    """Git仓库列表项"""
    name: str = Field(..., description="仓库名称")
    path: str = Field(..., description="仓库路径")
    remote_url: str = Field(..., description="远程URL")
    current_branch: str = Field(..., description="当前分支")
    latest_commit: Dict[str, Any] = Field(..., description="最新提交简要信息")
