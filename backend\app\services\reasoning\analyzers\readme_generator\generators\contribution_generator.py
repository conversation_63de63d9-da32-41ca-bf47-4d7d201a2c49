#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
贡献指南章节生成器
"""
import logging
import os
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone
from pathlib import Path

from langchain.chains import <PERSON><PERSON>hain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser

from . import Generator
from ..section_models import ContributionSection
from .....ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser
from ......utils.retry import retry_async
from ......utils.file_utils import read_file_content

logger = logging.getLogger(__name__)


class ContributionGenerator(Generator):
    """
    贡献指南章节生成器
    负责生成README文档的贡献指南章节
    """

    def __init__(self, llm: ChatOpenAI):
        """初始化贡献指南章节生成器"""
        super().__init__(llm)

    async def create_chain(self) -> None:
        """初始化处理链"""
        try:
            # 贡献指南章节生成提示模板
            contribution_template = """
            作为经验丰富的开源项目维护者和社区建设专家，请基于项目分析数据生成清晰、完整且易于遵循的贡献指南文档。

            ## 项目基本信息
            项目名称: {{ project_name }}
            项目路径: {{ project_path }}

            {%- if contribution_files %}

            ## 现有贡献指南文档
            {%- for file_path, content in contribution_files.items() %}
            ### {{ file_path }}:
            ```
            {{ content }}
            ```
            {%- endfor %}
            {%- endif %}

            {%- if structure_analysis %}

            ## 开发相关结构分析
            {%- if structure_analysis.primary_language %}
            主要编程语言: {{ structure_analysis.primary_language }}
            {%- endif %}

            {%- if structure_analysis.entries %}

            ### 项目入口点:
            {%- for entry in structure_analysis.entries[:3] %}
            - **{{ entry.name }}**: {{ entry.description }}
            {%- endfor %}
            {%- endif %}

            {%- if structure_analysis.tests %}

            ### 测试体系:
            {%- for test in structure_analysis.tests[:4] %}
            - **{{ test.name }}** ({{ test.test_category }}): {{ test.test_scope }}
            {%- if test.test_framework %}  框架: {{ test.test_framework }}{%- endif %}
            {%- if test.is_automated %}  自动化测试{%- endif %}
            {%- if test.coverage_target %}  覆盖率: {{ (test.coverage_target * 100)|round(1) }}%{%- endif %}
            {%- endfor %}
            {%- endif %}

            {%- if structure_analysis.builds %}

            ### 构建部署:
            {%- for build in structure_analysis.builds[:3] %}
            - **{{ build.build_tool }}**: {{ build.deployment_stage }}
            {%- if build.automation_level %}  自动化: {{ build.automation_level }}{%- endif %}
            {%- if build.target_platforms %}  平台: {{ build.target_platforms|join(", ") }}{%- endif %}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if dependency_analysis %}

            ## 技术栈和开发依赖
            {%- if dependency_analysis.tech_stack %}
            {%- if dependency_analysis.tech_stack.primary_language %}
            主要语言: {{ dependency_analysis.tech_stack.primary_language }}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.frameworks %}
            核心框架: {%- for framework in dependency_analysis.tech_stack.frameworks[:4] %}{{ framework }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.build_tools %}
            构建工具: {%- for tool in dependency_analysis.tech_stack.build_tools[:3] %}{{ tool }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.databases %}
            数据存储: {%- for db in dependency_analysis.tech_stack.databases[:3] %}{{ db }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if dependency_analysis.external_dependencies %}

            ### 开发依赖:
            {%- for dep in dependency_analysis.external_dependencies[:6] %}
            {%- if dep.is_dev_dependency and dep.is_direct %}
            - **{{ dep.name }}** {%- if dep.version %}{{ dep.version }}{%- endif %}: {{ dep.purpose }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if module_merger_analyses %}

            ## 开发相关模块洞察
            {%- for group_key, merger_result in module_merger_analyses.items() %}
            {%- if merger_result.ai_analysis and merger_result.ai_analysis.ai_findings %}

            ### {{ group_key }} 开发指南:
            {%- for finding in merger_result.ai_analysis.ai_findings[:3] %}
            {%- if "contribution" in finding.title.lower() or "贡献" in finding.title.lower() or "develop" in finding.title.lower() or "开发" in finding.title.lower() or "test" in finding.title.lower() or "测试" in finding.title.lower() %}
            - **{{ finding.title }}**: {{ finding.description }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}
            {%- endfor %}
            {%- endif %}

            {%- if architecture_analysis %}

            ## 架构开发建议
            {%- if architecture_analysis.recommendations %}

            ### 开发改进建议:
            {%- for recommendation in architecture_analysis.recommendations[:3] %}
            {%- if "contribution" in recommendation.target_area.lower() or "贡献" in recommendation.target_area.lower() or "develop" in recommendation.target_area.lower() or "开发" in recommendation.target_area.lower() or "workflow" in recommendation.target_area.lower() or "工作流" in recommendation.target_area.lower() %}
            - **{{ recommendation.target_area }}** ({{ recommendation.priority }}): {{ recommendation.recommendation }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            ## 贡献指南生成要求与原则

            ### 核心生成原则
            1. **友好包容**：使用友好、包容且鼓励参与的语言，避免过于正式或生硬的语气
            2. **基于实际**：内容必须基于提供的实际项目数据，特别是现有贡献指南文档内容
            3. **清晰具体**：说明应该清晰、具体且易于遵循，适合不同经验水平的贡献者
            4. **忠实反映**：如果发现贡献指南文档，请优先使用其中的信息，忠实反映内容
            5. **技能包容**：确保指南适合不同技能水平和背景的贡献者，提供适当的指导和支持
            6. **社区导向**：强调社区贡献的重要性，建立积极的贡献文化

            ### 内容生成任务
            请生成一份全面、清晰、易于遵循的贡献指南文档，包含以下9个核心要素：

            1. **欢迎和项目概述**（100-150字）：热情欢迎潜在贡献者、简要说明项目的目标和价值、强调社区贡献的重要性
            2. **贡献类型**：详细列出各种贡献方式（代码、文档、测试、问题报告、功能请求等）、说明每种贡献类型的价值和重要性、鼓励不同技能水平的贡献者参与
            3. **环境设置**（200-250字）：详细的开发环境设置步骤、必要的工具和依赖安装说明、如何获取和构建源代码、如何运行测试套件
            4. **问题和功能请求流程**：如何搜索现有问题、如何创建新问题（包括模板或必要信息）、问题标签系统说明、功能请求的提交和讨论流程
            5. **代码贡献流程**：分支创建和命名约定、提交消息格式和最佳实践、拉取请求流程和模板、代码审查流程和期望、合并策略
            6. **代码风格和标准**：编码约定和风格指南、文档要求、测试覆盖率期望、性能和安全考虑
            7. **社区准则**：行为准则摘要和链接、沟通渠道和最佳实践、决策过程说明
            8. **贡献者认可**：如何获得贡献者认可、贡献者列表维护方式
            9. **帮助和支持**：获取帮助的方式、社区资源和文档链接

            ### 输出语言要求
            所有生成的贡献指南文档内容必须使用中文(简体中文)输出。包括所有贡献流程说明、代码规范、提交规范、开发环境配置等内容都必须是中文。代码示例、命令行命令、文件路径等技术内容保持原样。

            ### 输出质量要求
            1. **实际数据驱动**：内容必须基于提供的实际项目数据，特别是现有贡献指南文档内容
            2. **语言友好包容**：语言应该友好、包容且鼓励参与，保持专业性的同时避免过于正式或生硬
            3. **说明清晰具体**：说明应该清晰、具体且易于遵循，包含详细的操作步骤
            4. **文档忠实性**：如果项目中有现有的贡献指南文件，请优先使用其中的信息，忠实反映内容
            5. **技能包容性**：确保指南适合不同经验水平的贡献者，提供适当的指导和支持
            6. **社区文化建设**：生成的贡献指南章节应该能够帮助新贡献者快速上手，并为项目建立一个积极、包容的贡献文化

            {{ format_instructions }}
            """

            # 创建Parser
            contribution_parser = AnalysisOutputParser(pydantic_object=ContributionSection)
            parser = OutputFixingParser.from_llm(parser=contribution_parser, llm=self.llm)

            # 获取格式指令
            contribution_format_instructions = contribution_parser.get_format_instructions()

            # 创建提示模板
            contribution_prompt = PromptTemplate.from_template(
                contribution_template,
                template_format="jinja2"
            ).partial(format_instructions=contribution_format_instructions)

            # 创建处理链
            self.chain = (
                contribution_prompt
                | self.llm
                | parser
            )

            logger.info("贡献指南章节生成器处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: AgentInput) -> None:
        """
        准备输入数据并设置self.input_parameters

        Args:
            input_data: 原始输入数据
        """
        # 初始化self.input_parameters，直接使用输入参数
        self.input_parameters = input_data.parameters.copy()
        
        # 读取贡献指南相关文档内容
        contribution_files = {}
        project_path = Path(self.input_parameters.get("project_path", ""))
        
        # 获取项目结构分析数据
        structure_analysis = self.input_parameters.get("structure_analysis")
        
        if structure_analysis and structure_analysis.docs:
            for doc in structure_analysis.docs:
                # 重点关注贡献指南相关的文档
                if (doc.doc_category == 'contributing' or 
                    'contributing' in doc.name.lower() or 
                    'contribute' in doc.name.lower() or
                    'development' in doc.name.lower() or
                    'dev-guide' in doc.name.lower() or
                    'developer' in doc.name.lower()):
                    
                    full_path = project_path / doc.path
                    content = read_file_content(str(full_path.resolve()), 15000)  # 限制为15000字符
                    if content:
                        contribution_files[doc.path] = content
                        logger.info(f"读取贡献指南文档: {doc.path}")
        
        # 如果没有找到明确的贡献指南文档，尝试查找常见的贡献指南文件名
        if not contribution_files:
            common_contribution_files = [
                'CONTRIBUTING.md', 'CONTRIBUTING.txt', 'CONTRIBUTING.rst',
                'CONTRIBUTE.md', 'CONTRIBUTE.txt', 'CONTRIBUTE.rst',
                'DEVELOPMENT.md', 'DEVELOPMENT.txt', 'DEV_GUIDE.md',
                'DEVELOPER_GUIDE.md', 'docs/CONTRIBUTING.md', 'docs/contributing.md',
                '.github/CONTRIBUTING.md', 'docs/development.md'
            ]
            
            for filename in common_contribution_files:
                contribution_path = project_path / filename
                if contribution_path.exists() and contribution_path.is_file():
                    content = read_file_content(str(contribution_path.resolve()), 15000)
                    if content:
                        contribution_files[filename] = content
                        logger.info(f"找到并读取贡献指南文件: {filename}")
                        break  # 找到第一个就停止
        
        # 将贡献指南文件内容添加到参数中
        self.input_parameters["contribution_files"] = contribution_files
        
        if contribution_files:
            logger.info(f"成功读取 {len(contribution_files)} 个贡献指南相关文档")
        else:
            logger.warning("未找到任何贡献指南相关文档")
        
        logger.info("贡献指南章节生成器输入参数准备完成")

    @retry_async(max_retries=3)
    async def process(self) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询

        Returns:
            Tuple[Any, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 调用LLM处理链
            # result = await self.chain.first.ainvoke(self.input_parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(self.input_parameters)

            # 设置章节顺序
            if result:
                result.order = 7  # 贡献指南章节通常是第七个

            return result, {"generator": "contribution_generator"}
        except Exception as e:
            logger.error(f"贡献指南章节生成失败: {str(e)}")
            raise
