"""
自定义提供者模块
提供特定于应用的依赖注入提供者
"""
from typing import Optional, Any, AsyncGenerator, Generator
from contextlib import asynccontextmanager, contextmanager
from dependency_injector import providers
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from app.core.database import Database

logger = structlog.get_logger(__name__)

class SessionProvider(providers.Provider):
    """数据库会话提供者
    
    提供数据库会话的上下文管理，确保会话的正确创建和释放
    """
    
    def __init__(self, database: Database):
        """初始化提供者
        
        Args:
            database: 数据库服务实例
        """
        self.database = database
        super().__init__()
        
    @contextmanager
    def __call__(self) -> Generator[Session, None, None]:
        """获取同步数据库会话
        
        Returns:
            Session: 数据库会话
            
        Example:
            with session_provider() as session:
                result = session.execute(query)
                session.commit()
        """
        with self.database(sync=True) as session:
            yield session
        
    def reset(self) -> None:
        """重置提供者状态"""
        pass

class AsyncSessionProvider(providers.Provider):
    """异步数据库会话提供者
    
    提供异步数据库会话的上下文管理，确保会话的正确创建和释放
    """
    
    def __init__(self, database: Database):
        """初始化提供者
        
        Args:
            database: 数据库服务实例
        """
        self.database = database
        super().__init__()
        
    @asynccontextmanager
    async def __call__(self) -> AsyncGenerator[AsyncSession, None]:
        """获取异步数据库会话
        
        Returns:
            AsyncSession: 异步数据库会话
            
        Example:
            async with async_session_provider() as session:
                result = await session.execute(query)
                await session.commit()
        """
        async with self.database(sync=False) as session:
            yield session
        
    def reset(self) -> None:
        """重置提供者状态"""
        pass
