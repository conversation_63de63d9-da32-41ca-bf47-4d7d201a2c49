"""
权限相关模型
包含权限组(PermissionGroupModel)和权限(PermissionModel)
"""
from datetime import datetime, timezone
from sqlalchemy import ForeignKey, UniqueConstraint, String
from sqlalchemy.orm import Mapped, mapped_column, relationship
import enum

from app.models.model_base import ModelBase
from app.models.rbac.associations import role_permission

class PermissionType(str, enum.Enum):
    """权限类型"""
    MENU = "menu"  # 菜单权限
    OPERATION = "operation"  # 操作权限
    DATA = "data"  # 数据权限
    API = "api"  # 接口权限
    BUTTON = "button"  # 按钮权限
    
class PermissionModel(ModelBase):
    """
    权限模型
    定义具体的权限项，例如：创建用户、删除用户等
    每个权限必须属于一个权限组
    """
    __tablename__ = 'permissions'
    __table_args__ = (
        UniqueConstraint('group_id', 'code', name='uq_permission_group_code'),
    )
    
    group_id: Mapped[str] = mapped_column(ForeignKey('permission_groups.id', ondelete='CASCADE'), comment='所属权限组ID')
    name: Mapped[str] = mapped_column(String(100), comment='权限名称')
    code: Mapped[str] = mapped_column(String(100), index=True, comment='权限标识符(小写英文+下划线)')
    description: Mapped[str | None] = mapped_column(String(200), default=None, comment='描述信息')
    sort_order: Mapped[int] = mapped_column(default=0, comment='排序号')
    type: Mapped[PermissionType] = mapped_column(String(20), default=PermissionType.OPERATION, comment='权限类型')
    is_active: Mapped[bool] = mapped_column(default=True, comment='是否启用')

    # 关系定义
    group: Mapped['PermissionGroupModel'] = relationship(back_populates='permissions')
    roles: Mapped[list['RoleModel']] = relationship(
        'RoleModel',
        secondary=role_permission,
        back_populates='permissions'
    )
