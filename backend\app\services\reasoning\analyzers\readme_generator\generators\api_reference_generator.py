#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API参考章节生成器
"""
import logging
import os
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone

from langchain.chains import <PERSON><PERSON>hain
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser

from . import Generator
from ..section_models import ApiReferenceSection, Endpoint, ParamInfo
from .....ai_agent_core import AgentInput, AnalysisOutputParser
from ......utils.retry import retry_async

logger = logging.getLogger(__name__)


class ApiReferenceGenerator(Generator):
    """
    API参考章节生成器
    负责生成README文档的API参考章节
    """

    def __init__(self, llm: ChatOpenAI):
        """初始化API参考章节生成器"""
        super().__init__(llm)

    async def create_chain(self) -> None:
        """初始化处理链"""
        try:
            # API参考章节生成提示模板
            api_reference_template = """
            作为专业的API文档分析与生成专家，请基于项目分析数据创建详尽、专业且实用的API参考文档。

            ## 项目基本信息
            项目名称: {{ project_name }}
            项目路径: {{ project_path }}

            {%- if structure_analysis %}

            ## API相关结构分析
            {%- if structure_analysis.core %}

            ### API核心组件:
            {%- for component in structure_analysis.core[:5] %}
            {%- if "api" in component.name.lower() or "route" in component.name.lower() or "endpoint" in component.name.lower() or "controller" in component.name.lower() or "handler" in component.name.lower() or "service" in component.name.lower() %}
            - **{{ component.name }}** ({{ component.component_kind }}): {{ component.description }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}

            {%- if structure_analysis.entries %}

            ### API入口点:
            {%- for entry in structure_analysis.entries[:3] %}
            {%- if "api" in entry.name.lower() or "server" in entry.name.lower() or "app" in entry.name.lower() or "main" in entry.name.lower() %}
            - **{{ entry.name }}** ({{ entry.entry_category }}): {{ entry.description }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if architecture_analysis %}

            ## API架构分析
            {%- if architecture_analysis.architecture_style %}
            架构风格: {{ architecture_analysis.architecture_style }}
            {%- endif %}

            {%- if architecture_analysis.architecture_components %}

            ### API相关组件:
            {%- for comp in architecture_analysis.architecture_components[:4] %}
            {%- if "api" in comp.name.lower() or "route" in comp.name.lower() or "endpoint" in comp.name.lower() or "controller" in comp.name.lower() or "handler" in comp.name.lower() %}
            - **{{ comp.name }}** ({{ comp.layer }}): {{ comp.purpose }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if dependency_analysis and dependency_analysis.tech_stack %}

            ## API技术栈
            {%- if dependency_analysis.tech_stack.frameworks %}
            核心框架: {%- for framework in dependency_analysis.tech_stack.frameworks[:3] %}{{ framework }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}
            {%- if dependency_analysis.tech_stack.backend %}
            后端技术: {%- for tech in dependency_analysis.tech_stack.backend[:3] %}{{ tech }}{%- if not loop.last %}, {%- endif %}{%- endfor %}
            {%- endif %}

            {%- if dependency_analysis.external_dependencies %}

            ### API相关依赖:
            {%- for dep in dependency_analysis.external_dependencies[:5] %}
            {%- if "api" in dep.purpose.lower() or "http" in dep.purpose.lower() or "rest" in dep.purpose.lower() or "graphql" in dep.purpose.lower() or "路由" in dep.purpose.lower() or "endpoint" in dep.purpose.lower() %}
            - **{{ dep.name }}**: {{ dep.purpose }}
            {%- endif %}
            {%- endfor %}
            {%- endif %}
            {%- endif %}

            {%- if merged_api_analyses %}

            ## API模块分析
            {%- for group_key, api_analysis in merged_api_analyses.items() %}

            ### {{ group_key }} API设计:
            
            #### API统计信息:
            - **API模块数量**: {{ api_analysis.total_api_modules }}
            - **API端点总数**: {{ api_analysis.total_endpoints }}
            {%- if api_analysis.api_types %}
            - **API类型**: {{ api_analysis.api_types|join(', ') }}
            {%- endif %}
            {%- if api_analysis.deprecated_endpoints_count > 0 %}
            - **已废弃端点**: {{ api_analysis.deprecated_endpoints_count }}
            {%- endif %}
            {%- if api_analysis.authenticated_endpoints_count > 0 %}
            - **需要认证的端点**: {{ api_analysis.authenticated_endpoints_count }}
            {%- endif %}

            {%- if api_analysis.endpoints_by_type %}

            #### 按API类型分组的端点:
            {%- for api_type, endpoints in api_analysis.endpoints_by_type.items() %}
            {%- if endpoints %}

            **{{ api_type.upper() }} API** ({{ endpoints|length }}个端点):
            {%- for endpoint in endpoints[:5] %}
            - `{{ endpoint.method }} {{ endpoint.path }}`
              {%- if endpoint.description %} - {{ endpoint.description }}{%- endif %}
              {%- if endpoint.parameters and endpoint.parameters|length > 0 %}
              - **参数**: {{ endpoint.parameters|join(', ') }}
              {%- endif %}
              {%- if endpoint.response_type %}
              - **响应类型**: {{ endpoint.response_type }}
              {%- endif %}
              {%- if endpoint.authentication_required %} 🔒{%- endif %}
              {%- if endpoint.deprecated %} ⚠️{%- endif %}
            {%- endfor %}
            {%- if endpoints|length > 5 %}
            - ... 还有 {{ endpoints|length - 5 }} 个端点
            {%- endif %}
            {%- endif %}
            {%- endfor %}
            {%- endif %}

            {%- if api_analysis.duplicate_paths %}

            #### ⚠️ 重复API路径:
            {%- for path in api_analysis.duplicate_paths[:3] %}
            - `{{ path }}`
            {%- endfor %}
            {%- endif %}

            #### API质量评估:
            - **整体评分**: {{ api_analysis.overall_quality.overall_score }}/10
            {%- if api_analysis.overall_quality.issues %}
            - **主要问题**:
            {%- for issue in api_analysis.overall_quality.issues[:3] %}
              - {{ issue }}
            {%- endfor %}
            {%- endif %}
            {%- endfor %}
            {%- endif %}

            ## 文档生成要求与原则

            ### 核心生成原则
            1. **API导向生成**：重点突出API接口、端点、请求/响应格式的详细信息
            2. **实际数据驱动**：所有API文档内容必须基于提供的项目分析数据
            3. **技术规范准确**：确保API描述的技术准确性和专业性
            4. **使用便利优先**：生成对开发者实际使用有帮助的文档内容
            5. **结构清晰完整**：确保API文档结构完整，分类清晰
            6. **示例实用有效**：提供有效且符合项目实际API的示例

            ### 内容生成任务
            请生成一个全面、精确且结构完善的API参考文档章节，包含以下要素：

            1. **API概述**（200-300字）：基于项目分析数据，概述API架构设计理念、技术选型、功能定位和适用场景
            2. **认证与授权**（如适用）：详细说明API认证机制、认证流程、权限级别和访问控制
            3. **基础URL与请求格式**：明确说明API基础URL、请求格式、内容类型和通用请求头
            4. **API端点分类**：将API端点按功能或资源类型分组，为每个分类提供描述
            5. **详细端点文档**：包含端点URL、HTTP方法、功能描述、参数列表、请求示例、响应状态码、响应示例和错误处理
            6. **数据模型**：详细描述API使用的主要数据结构、字段类型和约束条件
            7. **限流与性能考虑**（如适用）：说明限流策略、性能优化建议和缓存机制
            8. **版本控制与兼容性**：解释版本控制策略、版本差异和迁移指南
            9. **SDK与工具**（如适用）：列出官方SDK、客户端库和有用的第三方工具

            ### 输出语言要求
            所有生成的文档内容必须使用中文(简体中文)输出。包括所有API描述、功能说明、参数解释、示例代码注释等内容都必须是中文。原代码、API路径、参数名称、返回值格式等技术内容保持原样。

            ### 输出质量要求
            1. **内容真实准确**：内容必须基于提供的实际项目数据，不要臆想不存在的API
            2. **示例有效实用**：所有示例必须是有效的，并且符合项目的实际API
            3. **术语专业准确**：使用专业而准确的术语，但同时确保清晰易懂
            4. **风格一致规范**：保持文档风格一致，使用第三人称和现在时态
            5. **信息完整无遗**：确保所有端点文档的完整性，不要遗漏关键信息
            6. **适应项目类型**：如果项目是库而非Web API，请相应调整格式，详细描述公共函数、类和接口
            7. **满足使用需求**：生成的API参考章节应该既能满足初次使用API的开发者需求，也能作为有经验开发者的快速参考

            {{ format_instructions }}
            """

            # 创建Parser
            api_reference_parser = AnalysisOutputParser(pydantic_object=ApiReferenceSection)
            parser = OutputFixingParser.from_llm(parser=api_reference_parser, llm=self.llm)

            # 获取格式指令
            api_reference_format_instructions = api_reference_parser.get_format_instructions()

            # 创建提示模板
            api_reference_prompt = PromptTemplate.from_template(
                api_reference_template,
                template_format="jinja2"
            ).partial(format_instructions=api_reference_format_instructions)

            # 创建处理链
            self.chain = (
                api_reference_prompt
                | self.llm
                | parser
            )

            logger.info("API参考章节生成器处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: AgentInput) -> None:
        """
        准备输入数据并设置self.input_parameters

        Args:
            input_data: 原始输入数据
        """
        # 初始化self.input_parameters，直接使用输入参数
        self.input_parameters = input_data.parameters.copy()
        
        # 预处理API分析数据
        merged_api_analyses = {}
        if "module_merger_analyses" in self.input_parameters:
            for group_key, merger_result in self.input_parameters["module_merger_analyses"].items():
                try:
                    # 提取API分析数据，只有存在API端点时才添加
                    if (merger_result and 
                        merger_result.code_analysis and 
                        merger_result.code_analysis.api_analysis):
                        api_analysis = merger_result.code_analysis.api_analysis

                        if api_analysis.total_endpoints > 0:
                            merged_api_analyses[group_key] = api_analysis
                            logger.info(f"提取 {group_key} 的API分析数据: {api_analysis.total_endpoints}个端点")
                        else:
                            logger.debug(f"跳过 {group_key}，未发现API端点")
                except Exception as e:
                    logger.warning(f"提取 {group_key} API分析数据时出错: {e}")
                    continue
        
        # 将预处理的API数据添加到输入参数中
        self.input_parameters["merged_api_analyses"] = merged_api_analyses

    @retry_async(max_retries=3)
    async def process(self) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询

        Args:
            input_data: 输入数据

        Returns:
            Tuple[Any, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 调用LLM处理链
            # result = await self.chain.first.ainvoke(self.input_parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(self.input_parameters)

            # 设置章节顺序
            if result:
                result.order = 5  # API参考章节通常是第五个

            return result, {"generator": "api_reference_generator"}
        except Exception as e:
            logger.error(f"API参考章节生成失败: {str(e)}")
            raise
