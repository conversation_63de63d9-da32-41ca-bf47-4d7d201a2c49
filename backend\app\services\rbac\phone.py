#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/6/7 14:41
# @File    : phone.py
# @Description: 
"""
"""
手机验证码服务
"""
import random
import string
import datetime
import hashlib
import requests
import json
import base64
import structlog

from datetime import datetime, timedelta
from typing import Optional, Dict, Tuple
import structlog
from app.core.config import settings

logger = structlog.get_logger(__name__)


class PhoneService:
    """手机验证码服务"""

    def __init__(self):
        # 存储不同类型的验证码
        self.verification_codes: Dict[str, Dict[str, str]] = {}  # 注册验证码
        self.change_password_verification_codes: Dict[str, Dict[str, str]] = {}  # 修改密码验证码
        self.change_oauth_phone_codes: Dict[str, Dict[str, str]] = {}  # 更换手机号验证码
        self.fast_auth_phone_codes: Dict[str, Dict[str, str]] = {}  # 更换手机号验证码
        self.bind_change_phone_codes: Dict[str, Dict[str, str]] = {}  # 添加换绑验证码存储
        # 验证码有效期（分钟）
        self.phone_timeout = 10

        # 容联云配置
        self.account_sid = settings.phone.RONGLIAN_ACCOUNT_SID
        self.account_token = settings.phone.RONGLIAN_ACCOUNT_TOKEN
        self.app_id = settings.phone.RONGLIAN_APP_ID
        self.template_id = settings.phone.RONGLIAN_TEMPLATE_ID
        self.base_url = settings.phone.RONGLIAN_BASE_URL


    def _generate_signature(self) -> Tuple[str, str]:
        """生成签名和时间戳"""
        now = datetime.now().strftime("%Y%m%d%H%M%S")
        signature = self.account_sid + self.account_token + now
        signature_bytes = signature.encode('utf-8')
        sig_parameter = hashlib.md5(signature_bytes).hexdigest().upper()
        return sig_parameter, now

    def _generate_authorization(self, timestamp: str) -> str:
        """生成授权信息"""
        authorization = f"{self.account_sid}:{timestamp}"
        return base64.b64encode(authorization.encode('utf-8')).decode('utf-8')

    def _send_sms(self, phone_number: str ,code: str) -> Tuple[bool, str]:
        """发送短信验证码

        Args:
            phone_number: 手机号
            code: 验证码

        Returns:
            Tuple[bool, str]: (是否发送成功, 原因)
        """
        try:

            logger.info(
                "开始发送短信验证码",
                phone=phone_number,
                code=code,
                phone_timeout=self.phone_timeout
            )

            # 生成签名和时间戳
            sig_parameter, timestamp = self._generate_signature()
            # 构建请求URL
            url = f"{self.base_url}/{self.account_sid}/SMS/TemplateSMS?sig={sig_parameter}"

            # 生成授权信息
            authorization = self._generate_authorization(timestamp)

            # 构建请求头
            headers = {
                'content-type': 'application/json;charset=utf-8',
                'accept': 'application/json',
                'Authorization': authorization
            }

            # 构建请求数据
            data = {
                'to': phone_number,
                'appId': self.app_id,
                'templateId': self.template_id,
                'datas': [str(code), str(self.phone_timeout)]  # 验证码和有效期
            }

            # 记录详细的请求参数
            logger.info(
                "短信发送请求参数",
                url=url,
                headers={k: v if k != 'Authorization' else 'Bearer ***' for k, v in headers.items()},  # 隐藏敏感信息
                request_data=data,
                account_sid=self.account_sid,
                app_id=self.app_id,
                template_id=self.template_id,
                timestamp=timestamp,
                base_url=self.base_url
            )

            # 发送请求
            response = requests.post(
                url=url,
                data=json.dumps(data),
                headers=headers
            )

            # 解析响应
            response_data = response.json()

            if response_data['statusCode'] == '000000':
                logger.info(
                    "短信发送成功",
                    phone=phone_number,
                    template_id=self.template_id
                )
                return True, response_data.get('statusMsg', '发送成功')
            else:
                logger.error(
                    "短信发送失败",
                    phone=phone_number,
                    error=response_data.get('statusMsg', '未知错误')
                )
                return False, response_data.get('statusMsg', '发送失败')

        except Exception as e:
            logger.error(
                "短信发送异常",
                phone=phone_number,
                error=str(e)
            )
            return False, f"发送异常: {str(e)}"


    def generate_verification_code(self, length: int = 4) -> str:
        """生成验证码"""
        return ''.join(random.choices(string.digits, k=length))


    def send_bind_change_verification_code(self, phone: str) -> bool:
        """发送更换绑定手机号验证码

        Args:
            phone: 手机号

        Returns:
            bool: 发送成功返回True，否则返回False
        """
        try:
            code = self.generate_verification_code()
            success, message = self._send_sms(phone, code)

            if success:
                self.bind_change_phone_codes[phone] = {
                    'code': code,
                    'expire_time': datetime.now() + timedelta(minutes=self.phone_timeout)
                }
                return True
            else:
                logger.error("发送更换绑定手机号验证码失败", phone=phone, error=message)
                return False

        except Exception as e:
            logger.error("发送更换绑定手机号验证码失败", phone=phone, error=str(e))
            return False

    def verify_bind_change_phone_code(self, phone: str, code: str) -> bool:
        """验证更换绑定手机号验证码

        Args:
            phone: 手机号
            code: 验证码

        Returns:
            bool: 验证成功返回True，否则返回False
        """
        logger.info(
            "开始验证更换绑定手机号验证码",
            phone=phone,
            code=code,
            stored_codes=self.bind_change_phone_codes
        )
        result = self._verify_code_generic(phone, code, self.bind_change_phone_codes)
        logger.info(
            "更换绑定手机号验证码验证结果",
            phone=phone,
            result=result
        )
        return result

    def send_verification_code(self, phone: str) -> bool:
        """发送注册验证码

        Args:
            phone: 手机号

        Returns:
            bool: 发送成功返回True，否则返回False
        """
        try:
            code = self.generate_verification_code()
            success, message = self._send_sms(phone, code)

            if success:
                # 存储验证码
                self.verification_codes[phone] = {
                    'code': code,
                    'expire_time': datetime.now() + timedelta(minutes=self.phone_timeout)
                }
                return True
            else:
                logger.error("发送注册验证码失败", phone=phone, error=message)
                return False

        except Exception as e:
            logger.error("发送手机验证码失败", phone=phone, error=str(e))
            return False

    def send_password_reset_code(self, phone: str) -> bool:
        """发送密码重置验证码

        Args:
            phone: 手机号

        Returns:
            bool: 发送成功返回True，否则返回False
        """
        try:
            code = self.generate_verification_code()
            success, message = self._send_sms(phone, code)

            if success:
                self.change_password_verification_codes[phone] = {
                    'code': code,
                    'expire_time': datetime.now() + timedelta(minutes=self.phone_timeout)
                }
                return True
            else:
                logger.error("发送密码重置验证码失败", phone=phone, error=message)
                return False

        except Exception as e:
            logger.error("发送密码重置验证码失败", phone=phone, error=str(e))
            return False

    def send_self_oauth_phone_verification_code(self, phone: str) -> bool:
        """发送更换手机号验证码

        Args:
            phone: 手机号

        Returns:
            bool: 发送成功返回True，否则返回False
        """
        try:
            code = self.generate_verification_code()
            success, message = self._send_sms(phone, code)

            if success:
                self.change_oauth_phone_codes[phone] = {
                    'code': code,
                    'expire_time': datetime.now() + timedelta(minutes=self.phone_timeout)
                }
                return True
            else:
                logger.error("发送更换手机号验证码失败", phone=phone, error=message)
                return False

        except Exception as e:
            logger.error("发送更换手机号验证码失败", phone=phone, error=str(e))
            return False

    def send_fast_auth_phone_verification_code(self, phone: str) -> bool:
        """发送更换手机号验证码

        Args:
            phone: 手机号

        Returns:
            bool: 发送成功返回True，否则返回False
        """
        try:
            code = self.generate_verification_code()
            success, message = self._send_sms(phone, code)

            if success:
                self.fast_auth_phone_codes[phone] = {
                    'code': code,
                    'expire_time': datetime.now() + timedelta(minutes=self.phone_timeout)
                }
                return True
            else:
                logger.error("发送更换手机号验证码失败", phone=phone, error=message)
                return False

        except Exception as e:
            logger.error("发送更换手机号验证码失败", phone=phone, error=str(e))
            return False

    def _verify_code_generic(self, phone: str, code: str, code_storage: dict) -> bool:
        """通用验证码验证函数

        Args:
            phone: 手机号
            code: 待验证的验证码
            code_storage: 验证码存储字典

        Returns:
            bool: 验证是否通过
        """
        if phone not in code_storage:
            return False

        stored_code = code_storage[phone]

        # 检查验证码是否过期
        if datetime.now() > stored_code['expire_time']:
            del code_storage[phone]  # 只在过期时删除
            return False

        # 验证码匹配时删除并返回True
        if stored_code['code'] == code:
            del code_storage[phone]
            return True

        # 验证码不匹配时只返回False，不删除
        return False

    def verify_code(self, phone: str, code: str) -> bool:
        """验证注册验证码"""
        logger.info(
            "开始验证手机验证码",
            phone=phone,
            code=code,
            stored_codes=self.verification_codes
        )
        result = self._verify_code_generic(phone, code, self.verification_codes)
        logger.info(
            "手机验证码验证结果",
            phone=phone,
            result=result
        )
        return result

    def verify_password_change_code(self, phone: str, code: str) -> bool:
        """验证密码重置验证码"""
        return self._verify_code_generic(phone, code, self.change_password_verification_codes)

    def verify_change_oauth_phone_code(self, phone: str, code: str) -> bool:
        """验证绑定手机号验证码"""
        return self._verify_code_generic(phone, code, self.change_oauth_phone_codes)

    def verify_fast_auth_phone_code(self, phone: str, code: str) -> bool:
        """验证绑定手机号验证码"""
        return self._verify_code_generic(phone, code, self.fast_auth_phone_codes)