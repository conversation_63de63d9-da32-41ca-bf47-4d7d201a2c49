"""提供PackURI值类型。

还包括一些有用的已知包URI字符串，如PACKAGE_URI。
"""

from __future__ import annotations

import posixpath
import re


class PackURI(str):
    """提供对包URI组件的访问，如baseURI和文件名切片。

    其他行为与|str|相同。
    """

    _filename_re = re.compile("([a-zA-Z]+)([1-9][0-9]*)?")

    def __new__(cls, pack_uri_str: str):
        if pack_uri_str[0] != "/":
            tmpl = "PackURI必须以斜杠开头，得到的是'%s'"
            raise ValueError(tmpl % pack_uri_str)
        return str.__new__(cls, pack_uri_str)

    @staticmethod
    def from_rel_ref(baseURI: str, relative_ref: str) -> PackURI:
        """通过将`relative_ref`转换到`baseURI`上形成的绝对PackURI。"""
        joined_uri = posixpath.join(baseURI, relative_ref)
        abs_uri = posixpath.abspath(joined_uri)
        return PackURI(abs_uri)

    @property
    def baseURI(self) -> str:
        """此包URI的基础URI，大致上是目录部分。

        例如，对于``'/ppt/slides/slide1.xml'``是``'/ppt/slides'``。
        对于包伪部件名'/'，baseURI是'/'。
        """
        return posixpath.split(self)[0]

    @property
    def ext(self) -> str:
        """此包URI的扩展名部分，例如对于``'/word/document.xml'``是``'xml'``。

        注意不包含句点。
        """
        # raw_ext是空字符串或以句点开头，例如'.xml'
        raw_ext = posixpath.splitext(self)[1]
        return raw_ext[1:] if raw_ext.startswith(".") else raw_ext

    @property
    def filename(self):
        """此包URI的"文件名"部分，例如对于``'/ppt/slides/slide1.xml'``
        是``'slide1.xml'``。

        对于包伪部件名'/'，filename是''。
        """
        return posixpath.split(self)[1]

    @property
    def idx(self):
        """对于元组部件名返回整数形式的部件名索引，对于单例部件名返回None。
        例如，对于``'/ppt/slides/slide21.xml'``返回``21``，
        对于``'/ppt/presentation.xml'``返回|None|。"""
        filename = self.filename
        if not filename:
            return None
        name_part = posixpath.splitext(filename)[0]  # filename w/ext removed
        match = self._filename_re.match(name_part)
        if match is None:
            return None
        if match.group(2):
            return int(match.group(2))
        return None

    @property
    def membername(self):
        """去掉开头斜杠的包URI，这种形式用作包项目的Zip文件成员名。

        对于包伪部件名'/'返回''。
        """
        return self[1:]

    def relative_ref(self, baseURI: str):
        """返回包含从`baseURI`到包项目的相对引用的字符串。

        例如，对于baseURI '/ppt/slides'，
        PackURI('/ppt/slideLayouts/slideLayout1.xml')将返回
        '../slideLayouts/slideLayout1.xml'。
        """
        # workaround for posixpath bug in 2.6, doesn't generate correct
        # relative path when `start` (second) parameter is root ('/')
        return self[1:] if baseURI == "/" else posixpath.relpath(self, baseURI)

    @property
    def rels_uri(self):
        """当前包URI对应的.rels部分的包URI。

        仅在包URI是部件名或包伪部件名'/'时产生有意义的输出。
        """
        rels_filename = "%s.rels" % self.filename
        rels_uri_str = posixpath.join(self.baseURI, "_rels", rels_filename)
        return PackURI(rels_uri_str)


PACKAGE_URI = PackURI("/")
CONTENT_TYPES_URI = PackURI("/[Content_Types].xml")
