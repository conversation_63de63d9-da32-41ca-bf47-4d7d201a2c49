#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hacker News数据源

通过Hacker News API获取技术新闻和讨论信息
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from urllib.parse import quote_plus
import time

from .external_data_source import ExternalDataSource

logger = logging.getLogger(__name__)


class HackerNewsDataSource(ExternalDataSource):
    """Hacker News数据源类"""
    
    def __init__(self, timeout: int = 30):
        """
        初始化Hacker News数据源
        
        Args:
            timeout: 请求超时时间
        """
        super().__init__(timeout)
        self.base_url = "https://hacker-news.firebaseio.com/v0"
        self.algolia_url = "https://hn.algolia.com/api/v1"
        self.request_delay = 0.1  # API请求间隔（秒）
        
    def get_data_source_name(self) -> str:
        """获取数据源名称"""
        return "HackerNews"
    
    async def fetch_data(
        self, 
        project_name: str = None, 
        keywords: List[str] = None,
        github_url: str = None
    ) -> Dict[str, Any]:
        """
        获取Hacker News数据
        
        Args:
            project_name: 项目名称
            keywords: 搜索关键词
            github_url: GitHub仓库URL
            
        Returns:
            Dict[str, Any]: Hacker News数据
        """
        try:
            if not project_name and not keywords and not github_url:
                logger.warning("缺少搜索参数")
                return {}
            
            logger.info(f"开始获取Hacker News数据: {project_name}")
            
            # 构建搜索查询
            search_queries = self._build_search_queries(project_name, keywords, github_url)
            
            # 获取各种数据
            data = {
                "stories": await self._fetch_stories(search_queries),
                "comments": await self._fetch_comments(search_queries),
                "trending_stories": await self._fetch_trending_stories(),
                "user_submissions": await self._fetch_user_submissions(project_name) if project_name else []
            }
            
            # 分析和汇总数据
            data["analysis"] = self._analyze_data(data)
            
            # 缓存数据
            cache_key = f"{project_name}_{','.join(keywords or [])}_{github_url or ''}"
            self.cache_data(cache_key, data)
            
            logger.info(f"Hacker News数据获取完成: {project_name}")
            return data
            
        except Exception as e:
            logger.error(f"获取Hacker News数据失败: {str(e)}")
            return {}
    
    def _build_search_queries(
        self, 
        project_name: Optional[str], 
        keywords: Optional[List[str]],
        github_url: Optional[str]
    ) -> List[str]:
        """构建搜索查询"""
        queries = []
        
        if project_name:
            queries.append(project_name)
            
            # 项目名称 + 常见技术词汇
            tech_terms = ["release", "launch", "open source", "framework", "library"]
            for term in tech_terms[:2]:
                queries.append(f"{project_name} {term}")
        
        if keywords:
            queries.extend(keywords[:3])
        
        if github_url:
            # 提取GitHub仓库名
            if "github.com" in github_url:
                repo_parts = github_url.split("/")
                if len(repo_parts) >= 2:
                    repo_name = repo_parts[-1] if repo_parts[-1] else repo_parts[-2]
                    queries.append(repo_name)
        
        return queries[:5]  # 限制查询数量
    
    async def _fetch_stories(self, search_queries: List[str]) -> List[Dict[str, Any]]:
        """获取相关故事"""
        try:
            all_stories = []
            
            for query in search_queries:
                await asyncio.sleep(self.request_delay)
                
                # 使用Algolia搜索API
                url = f"{self.algolia_url}/search"
                params = {
                    "query": query,
                    "tags": "story",
                    "hitsPerPage": 10,
                    "attributesToRetrieve": "objectID,title,url,author,points,num_comments,created_at_i"
                }
                
                data = await self._make_request(url, params=params)
                
                if data and "hits" in data:
                    stories = []
                    for hit in data["hits"]:
                        # 获取详细信息
                        story_detail = await self._fetch_story_detail(hit.get("objectID"))
                        
                        story = {
                            "id": hit.get("objectID"),
                            "title": hit.get("title"),
                            "url": hit.get("url"),
                            "author": hit.get("author"),
                            "points": hit.get("points", 0),
                            "num_comments": hit.get("num_comments", 0),
                            "created_at": hit.get("created_at_i"),
                            "hn_url": f"https://news.ycombinator.com/item?id={hit.get('objectID')}",
                            "text": story_detail.get("text", "") if story_detail else "",
                            "type": story_detail.get("type", "story") if story_detail else "story"
                        }
                        stories.append(story)
                        
                        # 添加延迟避免过于频繁的请求
                        await asyncio.sleep(self.request_delay)
                    
                    all_stories.extend(stories)
            
            # 去重和排序
            unique_stories = {}
            for story in all_stories:
                story_id = story["id"]
                if story_id not in unique_stories or story["points"] > unique_stories[story_id]["points"]:
                    unique_stories[story_id] = story
            
            # 按点数排序
            sorted_stories = sorted(
                unique_stories.values(),
                key=lambda x: (x["points"], x["num_comments"]),
                reverse=True
            )
            
            return sorted_stories[:15]
            
        except Exception as e:
            logger.error(f"获取Hacker News故事失败: {str(e)}")
            return []
    
    async def _fetch_story_detail(self, story_id: str) -> Optional[Dict[str, Any]]:
        """获取故事详细信息"""
        try:
            if not story_id:
                return None
            
            url = f"{self.base_url}/item/{story_id}.json"
            data = await self._make_request(url)
            
            return data
            
        except Exception as e:
            logger.error(f"获取故事详情失败 {story_id}: {str(e)}")
            return None
    
    async def _fetch_comments(self, search_queries: List[str]) -> List[Dict[str, Any]]:
        """获取相关评论"""
        try:
            all_comments = []
            
            for query in search_queries[:3]:  # 限制查询数量
                await asyncio.sleep(self.request_delay)
                
                # 搜索评论
                url = f"{self.algolia_url}/search"
                params = {
                    "query": query,
                    "tags": "comment",
                    "hitsPerPage": 5,
                    "attributesToRetrieve": "objectID,comment_text,author,points,created_at_i,parent_id,story_id"
                }
                
                data = await self._make_request(url, params=params)
                
                if data and "hits" in data:
                    comments = []
                    for hit in data["hits"]:
                        comment = {
                            "id": hit.get("objectID"),
                            "text": hit.get("comment_text", "")[:300],  # 限制长度
                            "author": hit.get("author"),
                            "points": hit.get("points", 0),
                            "created_at": hit.get("created_at_i"),
                            "parent_id": hit.get("parent_id"),
                            "story_id": hit.get("story_id"),
                            "hn_url": f"https://news.ycombinator.com/item?id={hit.get('objectID')}"
                        }
                        comments.append(comment)
                    
                    all_comments.extend(comments)
            
            # 按点数排序
            sorted_comments = sorted(
                all_comments,
                key=lambda x: x["points"],
                reverse=True
            )
            
            return sorted_comments[:10]
            
        except Exception as e:
            logger.error(f"获取Hacker News评论失败: {str(e)}")
            return []
    
    async def _fetch_trending_stories(self) -> List[Dict[str, Any]]:
        """获取当前热门故事"""
        try:
            await asyncio.sleep(self.request_delay)
            
            # 获取前页故事ID
            url = f"{self.base_url}/topstories.json"
            story_ids = await self._make_request(url)
            
            if not story_ids:
                return []
            
            # 获取前10个热门故事的详细信息
            trending_stories = []
            for story_id in story_ids[:10]:
                await asyncio.sleep(self.request_delay)
                
                story_detail = await self._fetch_story_detail(str(story_id))
                if story_detail:
                    story = {
                        "id": story_detail.get("id"),
                        "title": story_detail.get("title"),
                        "url": story_detail.get("url"),
                        "author": story_detail.get("by"),
                        "points": story_detail.get("score", 0),
                        "num_comments": story_detail.get("descendants", 0),
                        "created_at": story_detail.get("time"),
                        "hn_url": f"https://news.ycombinator.com/item?id={story_id}",
                        "type": story_detail.get("type", "story")
                    }
                    trending_stories.append(story)
            
            return trending_stories
            
        except Exception as e:
            logger.error(f"获取热门故事失败: {str(e)}")
            return []
    
    async def _fetch_user_submissions(self, project_name: str) -> List[Dict[str, Any]]:
        """获取用户提交的相关内容"""
        try:
            # 搜索可能的项目作者或相关用户
            url = f"{self.algolia_url}/search"
            params = {
                "query": f"{project_name} author",
                "tags": "story",
                "hitsPerPage": 5
            }
            
            data = await self._make_request(url, params=params)
            
            if data and "hits" in data:
                submissions = []
                for hit in data["hits"]:
                    submission = {
                        "id": hit.get("objectID"),
                        "title": hit.get("title"),
                        "author": hit.get("author"),
                        "points": hit.get("points", 0),
                        "num_comments": hit.get("num_comments", 0),
                        "created_at": hit.get("created_at_i"),
                        "url": hit.get("url"),
                        "hn_url": f"https://news.ycombinator.com/item?id={hit.get('objectID')}"
                    }
                    submissions.append(submission)
                
                return submissions
            
            return []
            
        except Exception as e:
            logger.error(f"获取用户提交失败: {str(e)}")
            return []
    
    def _analyze_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分析Hacker News数据"""
        try:
            stories = data.get("stories", [])
            comments = data.get("comments", [])
            trending_stories = data.get("trending_stories", [])
            
            analysis = {
                "visibility": self._analyze_visibility(stories, trending_stories),
                "community_interest": self._analyze_community_interest(stories, comments),
                "discussion_quality": self._analyze_discussion_quality(comments),
                "tech_relevance": self._analyze_tech_relevance(stories),
                "influence_score": self._calculate_influence_score(stories, comments)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析Hacker News数据失败: {str(e)}")
            return {}
    
    def _analyze_visibility(
        self, 
        stories: List[Dict[str, Any]], 
        trending_stories: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """分析项目可见度"""
        if not stories:
            return {"visibility_level": "none", "total_mentions": 0}
        
        total_mentions = len(stories)
        total_points = sum(story.get("points", 0) for story in stories)
        avg_points = total_points / total_mentions if total_mentions > 0 else 0
        
        # 检查是否出现在热门故事中
        is_trending = any(
            story.get("title", "").lower() in [ts.get("title", "").lower() for ts in trending_stories]
            for story in stories
        )
        
        # 可见度评级
        if is_trending or (total_mentions >= 5 and avg_points >= 50):
            visibility_level = "high"
        elif total_mentions >= 3 and avg_points >= 20:
            visibility_level = "medium"
        elif total_mentions >= 1:
            visibility_level = "low"
        else:
            visibility_level = "none"
        
        return {
            "visibility_level": visibility_level,
            "total_mentions": total_mentions,
            "total_points": total_points,
            "average_points": round(avg_points, 2),
            "is_trending": is_trending
        }
    
    def _analyze_community_interest(
        self, 
        stories: List[Dict[str, Any]], 
        comments: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """分析社区兴趣度"""
        if not stories:
            return {"interest_level": "none"}
        
        total_comments = sum(story.get("num_comments", 0) for story in stories)
        total_stories = len(stories)
        avg_comments = total_comments / total_stories if total_stories > 0 else 0
        
        # 兴趣度评级
        if avg_comments >= 50:
            interest_level = "very_high"
        elif avg_comments >= 20:
            interest_level = "high"
        elif avg_comments >= 10:
            interest_level = "medium"
        elif avg_comments >= 5:
            interest_level = "low"
        else:
            interest_level = "very_low"
        
        return {
            "interest_level": interest_level,
            "total_comments": total_comments,
            "average_comments_per_story": round(avg_comments, 2),
            "stories_with_comments": sum(1 for s in stories if s.get("num_comments", 0) > 0)
        }
    
    def _analyze_discussion_quality(self, comments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析讨论质量"""
        if not comments:
            return {"quality_level": "none"}
        
        total_comments = len(comments)
        avg_points = sum(comment.get("points", 0) for comment in comments) / total_comments
        high_quality_comments = sum(1 for c in comments if c.get("points", 0) >= 5)
        
        # 质量评级
        quality_ratio = high_quality_comments / total_comments
        if avg_points >= 10 and quality_ratio >= 0.5:
            quality_level = "high"
        elif avg_points >= 5 and quality_ratio >= 0.3:
            quality_level = "medium"
        else:
            quality_level = "low"
        
        return {
            "quality_level": quality_level,
            "total_comments": total_comments,
            "average_points": round(avg_points, 2),
            "high_quality_ratio": round(quality_ratio, 2)
        }
    
    def _analyze_tech_relevance(self, stories: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析技术相关性"""
        if not stories:
            return {"relevance_level": "none"}
        
        # 技术相关关键词
        tech_keywords = [
            "open source", "github", "api", "framework", "library", "tool",
            "programming", "developer", "code", "software", "tech", "launch"
        ]
        
        relevant_stories = 0
        for story in stories:
            title = story.get("title", "").lower()
            text = story.get("text", "").lower()
            content = title + " " + text
            
            if any(keyword in content for keyword in tech_keywords):
                relevant_stories += 1
        
        relevance_ratio = relevant_stories / len(stories)
        
        # 相关性评级
        if relevance_ratio >= 0.8:
            relevance_level = "very_high"
        elif relevance_ratio >= 0.6:
            relevance_level = "high"
        elif relevance_ratio >= 0.4:
            relevance_level = "medium"
        elif relevance_ratio >= 0.2:
            relevance_level = "low"
        else:
            relevance_level = "very_low"
        
        return {
            "relevance_level": relevance_level,
            "relevant_stories": relevant_stories,
            "total_stories": len(stories),
            "relevance_ratio": round(relevance_ratio, 2)
        }
    
    def _calculate_influence_score(
        self, 
        stories: List[Dict[str, Any]], 
        comments: List[Dict[str, Any]]
    ) -> float:
        """计算影响力评分"""
        if not stories:
            return 0.0
        
        # 基于点数、评论数和故事数量计算影响力
        total_points = sum(story.get("points", 0) for story in stories)
        total_comments = sum(story.get("num_comments", 0) for story in stories)
        story_count = len(stories)
        
        # 影响力公式：(总点数 + 总评论数 * 0.5) * log(故事数量 + 1)
        import math
        influence_score = (total_points + total_comments * 0.5) * math.log(story_count + 1)
        
        # 标准化到0-100范围
        normalized_score = min(100.0, influence_score / 10)
        
        return round(normalized_score, 2)
    
    async def is_available(self) -> bool:
        """检查Hacker News API是否可用"""
        try:
            url = f"{self.base_url}/maxitem.json"
            data = await self._make_request(url)
            return data is not None
            
        except Exception as e:
            logger.error(f"检查Hacker News API可用性失败: {str(e)}")
            return False
