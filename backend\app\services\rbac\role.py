"""
角色服务模块

提供角色管理相关功能
"""
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
import structlog
from sqlalchemy import select, and_, or_, func, delete
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import DatabaseError

from app.core.database import DatabaseError
from app.core.di.providers import SessionProvider, AsyncSessionProvider
from app.models.rbac.role import RoleModel
from app.models.rbac.permission import PermissionModel
from app.models.rbac.permission_group import PermissionGroupModel
from app.models.rbac.user import UserModel

from app.schemas.rbac.role import Role, RoleListResponse, RoleCreateRequest, RoleUpdateRequest
from app.schemas.rbac.permission import Permission
from app.schemas.rbac.permission_group import PermissionGroup
from app.model_converters.rbac.role import RoleConverter

logger = structlog.get_logger(__name__)

class RoleService:
    """角色服务类"""
    
    def __init__(
        self,
        session: SessionProvider,
        async_session: AsyncSessionProvider
    ):
        """初始化角色服务
        
        Args:
            session: 数据库会话提供者
            async_session: 异步数据库会话提供者
        """
        self.session = session
        self.async_session = async_session
        self.role_converter = RoleConverter()
        logger.debug("角色服务初始化完成")


    async def get_role_and_permission_by_id(self, role_id: str) -> Optional[Role]:
        """根据ID获取角色

        Args:
            role_id: 角色ID

        Returns:
            角色信息，不存在返回None
        """
        async with self.async_session() as session:
            stmt = (
                select(RoleModel)
                .options(
                    selectinload(RoleModel.parent),
                    selectinload(RoleModel.children),
                    selectinload(RoleModel.permissions),
                    selectinload(RoleModel.permission_groups),
                    selectinload(RoleModel.users),
                )
                .where(RoleModel.id == role_id)
            )
            result = await session.execute(stmt)
            role_model = result.scalar_one_or_none()
            if not role_model:
                return None
            role_data = self.to_schema_with_permissions(role_model)
            return role_data if role_data else None

    def to_schema_with_permissions(self, role_model: RoleModel) -> Role:
        """将角色模型转换为schema，并包含权限组和权限信息

        Args:
            role_model: 角色模型实例

        Returns:
            包含完整权限信息的角色schema实例
        """
        # 1. 基本属性转换
        role_dict = {
            "id": role_model.id,
            "name": role_model.name,
            "code": role_model.code,
            "description": role_model.description,
#            "parent_id": role_model.parent_id,
            "is_active": role_model.is_active,
            "is_system": role_model.is_system,
            "is_inherit": role_model.is_inherit,
            "sort_order": role_model.sort_order,
            "created_at": role_model.created_at,
            "updated_at": role_model.updated_at,
            "permissions": []
        }

        # 2. 不处理父、子角色、权限组信息

        direct_permissions = []
        for perm in role_model.permissions:
            # perm_dict = {
            #     "id": perm.id,
            #     "code": perm.code,
            #     "name": perm.name,
            #     "description": perm.description,
            #     # 添加验证所需的字段
            #     "type": getattr(perm, "type", "system"),  # 默认为system类型
            #     "group_id": getattr(perm, "group_id", None),
            #     "created_at": getattr(perm, "created_at", role_model.created_at),
            #     "updated_at": getattr(perm, "updated_at", role_model.updated_at)
            #
            # }
            # direct_permissions.append(perm_dict)
            direct_permissions.append(str(perm.code))
        role_dict["permissions"] = direct_permissions
        # 8. 创建并返回角色schema实例
        return role_dict

    async def get_by_id(self, role_id: str) -> Optional[Role]:
        """根据ID获取角色

        Args:
            role_id: 角色ID

        Returns:
            角色信息，不存在返回None
        """
        async with self.async_session() as session:
            stmt = (
                select(RoleModel)
                .options(
                    selectinload(RoleModel.parent),
                    selectinload(RoleModel.children),
                    selectinload(RoleModel.permissions),
                    selectinload(RoleModel.permission_groups),
                    selectinload(RoleModel.users),
                )
                .where(RoleModel.id == role_id)
            )
            result = await session.execute(stmt)
            role = result.scalar_one_or_none()
            return self.role_converter.to_schema(role) if role else None

    async def get_by_code(self, code: str) -> Optional[Role]:
        """根据角色代码获取角色

        Args:
            code: 角色代码

        Returns:
            角色信息，不存在返回None
        """
        async with self.async_session() as session:
            stmt = (
                select(RoleModel)
                .options(
                    selectinload(RoleModel.parent),
                    selectinload(RoleModel.children),
                    selectinload(RoleModel.permissions),
                    selectinload(RoleModel.permission_groups),
                    selectinload(RoleModel.users)
                )
                .where(RoleModel.code == code)
            )
            result = await session.execute(stmt)
            role = result.scalar_one_or_none()
            return self.role_converter.to_schema(role) if role else None

    async def get_list(
        self,
        *,
        page: int = 1,
        page_size: int = 100,
        is_active: Optional[bool] = None,
        is_system: Optional[bool] = None,
        parent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        permission_id: Optional[str] = None,
        permission_group_id: Optional[str] = None,
        search: Optional[str] = None
    ) -> RoleListResponse:
        """获取角色列表

        Args:
            page: 页码
            page_size: 每页记录数
            is_active: 是否启用
            is_system: 是否系统角色
            parent_id: 父角色ID
            user_id: 用户ID
            permission_id: 权限ID
            permission_group_id: 权限组ID
            search: 搜索关键词(代码、名称、描述)

        Returns:
            角色列表分页响应
        """
        async with self.async_session() as session:
            stmt = (
                select(RoleModel)
                .options(
                    selectinload(RoleModel.parent),
                    selectinload(RoleModel.children),
                    selectinload(RoleModel.permissions),
                    selectinload(RoleModel.permission_groups),
                    selectinload(RoleModel.users)
                )
            )
            
            # 构建查询条件
            conditions = []
            
            if is_active is not None:
                conditions.append(RoleModel.is_active == is_active)
                
            if is_system is not None:
                conditions.append(RoleModel.is_system == is_system)
                
            if parent_id is not None:
                conditions.append(RoleModel.parent_id == parent_id)
                
            if user_id is not None:
                stmt = stmt.join(RoleModel.users)
                conditions.append(UserModel.id == user_id)
                
            if permission_id is not None:
                stmt = stmt.join(RoleModel.permissions)
                conditions.append(PermissionModel.id == permission_id)
                
            if permission_group_id is not None:
                stmt = stmt.join(RoleModel.permission_groups)
                conditions.append(PermissionGroupModel.id == permission_group_id)
                
            if search:
                search_condition = or_(
                    RoleModel.code.ilike(f"%{search}%"),
                    RoleModel.name.ilike(f"%{search}%"),
                    RoleModel.description.ilike(f"%{search}%")
                )
                conditions.append(search_condition)
                
            if conditions:
                stmt = stmt.where(and_(*conditions))
                
            # 获取总记录数
            count_stmt = select(func.count()).select_from(stmt.subquery())
            total = await session.scalar(count_stmt)
            
            # 计算分页
            skip = (page - 1) * page_size
            
            # 获取分页数据
            stmt = (
                stmt.order_by(RoleModel.sort_order.asc(), RoleModel.id.desc())
                .offset(skip)
                .limit(page_size)
            )
            result = await session.execute(stmt)
            roles = result.scalars().all()
            
            return RoleListResponse(
                total=total,
                roles=[self.role_converter.to_schema(r) for r in roles],
                page=page,
                page_size=page_size
            )

    async def create(
        self,
        request: RoleCreateRequest
    ) -> Role:
        """创建角色

        Args:
            request: 角色创建请求

        Returns:
            创建的角色信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 角色代码已存在
        """
        async with self.async_session() as session:
            try:
                # 检查角色代码是否存在
                result = await session.execute(
                    select(RoleModel).where(RoleModel.code == request.code)
                )
                if result.scalar_one_or_none():
                    raise ValueError("角色代码已存在")
                
                # 创建角色
                role = RoleModel(
                    code=request.code,
                    name=request.name,
                    description=request.description,
                    is_active=request.is_active,
                    is_inherit=request.is_inherit,
                    parent_id=request.parent_id,
                    sort_order=request.sort_order,
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc)
                )
                
                # 分配权限
                if request.permission_ids:
                    result = await session.execute(
                        select(PermissionModel).where(PermissionModel.id.in_(request.permission_ids))
                    )
                    permissions = result.scalars().all()
                    if len(permissions) != len(request.permission_ids):
                        raise ValueError("部分权限不存在")
                    role.permissions = permissions
                    
                # 分配权限组
                if request.permission_group_ids:
                    result = await session.execute(
                        select(PermissionGroupModel).where(
                            PermissionGroupModel.id.in_(request.permission_group_ids)
                        )
                    )
                    groups = result.scalars().all()
                    if len(groups) != len(request.permission_group_ids):
                        raise ValueError("部分权限组不存在")
                    role.permission_groups = groups
                    
                session.add(role)
                await session.commit()
                # await session.refresh(role)
                # 同login
                role_data = await session.execute(
                    select(RoleModel).options(
                        selectinload(RoleModel.permissions),
                        selectinload(RoleModel.permission_groups),
                        selectinload(RoleModel.parent),
                        selectinload(RoleModel.children)
                    ).where(RoleModel.id == role.id)
                )
                role = role_data.scalar_one()


                logger.info(
                    "角色创建成功",
                    code=request.code,
                    name=request.name,
                    role_id=role.id
                )
                
                return self.role_converter.to_schema(role)
                
            except Exception as e:
                logger.error(
                    "角色创建失败",
                    code=request.code,
                    name=request.name,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("角色创建失败") from e

    async def update(
        self,
        *,
        request: RoleUpdateRequest
    ) -> Role:
        """更新角色信息

        Args:
            request: 角色更新请求

        Returns:
            更新后的角色信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 角色代码已存在
        """
        async with self.async_session() as session:
            try:
                # 获取角色
                stmt = (
                    select(RoleModel)
                    .options(
                        selectinload(RoleModel.parent),
                        selectinload(RoleModel.children),
                        selectinload(RoleModel.permissions),
                        selectinload(RoleModel.permission_groups),
                        selectinload(RoleModel.users)
                    )
                    .where(RoleModel.id == request.role_id)
                )
                result = await session.execute(stmt)
                role = result.scalar_one_or_none()
                if not role:
                    raise ValueError("角色不存在")
                    
                # 检查角色代码是否重复
                if request.code and request.code != role.code:
                    result = await session.execute(
                        select(RoleModel).where(RoleModel.code == request.code)
                    )
                    if result.scalar_one_or_none():
                        raise ValueError("角色代码已存在")
                    role.code = request.code
                    
                # 更新其他字段
                if request.name is not None:
                    role.name = request.name
                if request.description is not None:
                    role.description = request.description
                if request.is_active is not None:
                    role.is_active = request.is_active
                if request.is_inherit is not None:
                    role.is_inherit = request.is_inherit
                if request.parent_id is not None:
                    role.parent_id = request.parent_id
                if request.sort_order is not None:
                    role.sort_order = request.sort_order
                    
                # 更新权限
                if request.permission_codes is not None:
                    result = await session.execute(
                        select(PermissionModel).where(PermissionModel.code.in_(request.permission_codes))
                    )
                    permissions = result.scalars().all()
                    # 暂时不做审查
                    # if len(permissions) != len(request.permission_codes):
                    #     raise ValueError("部分权限不存在")
                    role.permissions = permissions
                #
                # # 更新权限组
                # if request.permission_group_ids is not None:
                #     result = await session.execute(
                #         select(PermissionGroupModel).where(
                #             PermissionGroupModel.id.in_(request.permission_group_ids)
                #         )
                #     )
                #     groups = result.scalars().all()
                #     if len(groups) != len(request.permission_group_ids):
                #         raise ValueError("部分权限组不存在")
                #     role.permission_groups = groups
                    
                role.updated_at = datetime.now(timezone.utc)
                await session.commit()
                await session.refresh(role)
                
                logger.info(
                    "角色更新成功",
                    role_id=request.role_id,
                    code=request.code,
                    name=request.name
                )
                
                return self.role_converter.to_schema(role)
                
            except Exception as e:
                logger.error(
                    "角色更新失败",
                    role_id=request.role_id,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("角色更新失败") from e

    async def add_permissions(
        self,
        *,
        role_id: str,
        permission_ids: List[str]
    ) -> Role:
        """向角色添加权限

        Args:
            role_id: 角色ID
            permission_ids: 权限ID列表

        Returns:
            更新后的角色信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 角色不存在或权限不存在
        """
        async with self.async_session() as session:
            try:
                # 获取角色
                stmt = (
                    select(RoleModel)
                    .options(
                        selectinload(RoleModel.parent),
                        selectinload(RoleModel.children),
                        selectinload(RoleModel.permissions),
                        selectinload(RoleModel.permission_groups),
                        selectinload(RoleModel.users)
                    )
                    .where(RoleModel.id == role_id)
                )
                result = await session.execute(stmt)
                role = result.scalar_one_or_none()
                if not role:
                    raise ValueError("角色不存在")
                    
                # 获取要添加的权限
                result = await session.execute(
                    select(PermissionModel).where(PermissionModel.id.in_(permission_ids))
                )
                permissions = result.scalars().all()
                if len(permissions) != len(permission_ids):
                    raise ValueError("部分权限不存在")
                    
                # 添加权限
                current_permission_ids = {p.id for p in role.permissions}
                for permission in permissions:
                    if permission.id not in current_permission_ids:
                        role.permissions.append(permission)
                        
                role.updated_at = datetime.now(timezone.utc)
                await session.commit()
                await session.refresh(role)
                
                logger.info(
                    "角色添加权限成功",
                    role_id=role_id,
                    permission_ids=permission_ids
                )
                
                return self.role_converter.to_schema(role)
                
            except Exception as e:
                logger.error(
                    "角色添加权限失败",
                    role_id=role_id,
                    permission_ids=permission_ids,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("角色添加权限失败") from e

    async def remove_permissions(
        self,
        *,
        role_id: str,
        permission_ids: List[str]
    ) -> Role:
        """从角色移除权限

        Args:
            role_id: 角色ID
            permission_ids: 权限ID列表

        Returns:
            更新后的角色信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 角色不存在
        """
        async with self.async_session() as session:
            try:
                # 获取角色
                stmt = (
                    select(RoleModel)
                    .options(
                        selectinload(RoleModel.parent),
                        selectinload(RoleModel.children),
                        selectinload(RoleModel.permissions),
                        selectinload(RoleModel.permission_groups),
                        selectinload(RoleModel.users)
                    )
                    .where(RoleModel.id == role_id)
                )
                result = await session.execute(stmt)
                role = result.scalar_one_or_none()
                if not role:
                    raise ValueError("角色不存在")
                
                # 移除权限
                role.permissions = [
                    p for p in role.permissions
                    if p.id not in permission_ids
                ]
                
                role.updated_at = datetime.now(timezone.utc)
                await session.commit()
                await session.refresh(role)
                
                logger.info(
                    "角色移除权限成功",
                    role_id=role_id,
                    permission_ids=permission_ids
                )
                
                return self.role_converter.to_schema(role)
                
            except Exception as e:
                logger.error(
                    "角色移除权限失败",
                    role_id=role_id,
                    permission_ids=permission_ids,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("角色移除权限失败") from e

    async def add_permission_groups(
        self,
        *,
        role_id: str,
        group_ids: List[str]
    ) -> Role:
        """向角色添加权限组

        Args:
            role_id: 角色ID
            group_ids: 权限组ID列表

        Returns:
            更新后的角色信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 角色不存在或权限组不存在
        """
        async with self.async_session() as session:
            try:
                # 获取角色
                stmt = (
                    select(RoleModel)
                    .options(
                        selectinload(RoleModel.parent),
                        selectinload(RoleModel.children),
                        selectinload(RoleModel.permissions),
                        selectinload(RoleModel.permission_groups),
                        selectinload(RoleModel.users)
                    )
                    .where(RoleModel.id == role_id)
                )
                result = await session.execute(stmt)
                role = result.scalar_one_or_none()
                if not role:
                    raise ValueError("角色不存在")
                    
                # 获取要添加的权限组
                result = await session.execute(
                    select(PermissionGroupModel).where(
                        PermissionGroupModel.id.in_(group_ids)
                    )
                )
                groups = result.scalars().all()
                if len(groups) != len(group_ids):
                    raise ValueError("部分权限组不存在")
                    
                # 添加权限组
                current_group_ids = {g.id for g in role.permission_groups}
                for group in groups:
                    if group.id not in current_group_ids:
                        role.permission_groups.append(group)
                        
                role.updated_at = datetime.now(timezone.utc)
                await session.commit()
                await session.refresh(role)
                
                logger.info(
                    "角色添加权限组成功",
                    role_id=role_id,
                    group_ids=group_ids
                )
                
                return self.role_converter.to_schema(role)
                
            except Exception as e:
                logger.error(
                    "角色添加权限组失败",
                    role_id=role_id,
                    group_ids=group_ids,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("角色添加权限组失败") from e

    async def remove_permission_groups(
        self,
        *,
        role_id: str,
        group_ids: List[str]
    ) -> Role:
        """从角色移除权限组

        Args:
            role_id: 角色ID
            group_ids: 权限组ID列表

        Returns:
            更新后的角色信息

        Raises:
            DatabaseError: 数据库错误
            ValueError: 角色不存在
        """
        async with self.async_session() as session:
            try:
                # 获取角色
                stmt = (
                    select(RoleModel)
                    .options(
                        selectinload(RoleModel.parent),
                        selectinload(RoleModel.children),
                        selectinload(RoleModel.permissions),
                        selectinload(RoleModel.permission_groups),
                        selectinload(RoleModel.users)
                    )
                    .where(RoleModel.id == role_id)
                )
                result = await session.execute(stmt)
                role = result.scalar_one_or_none()
                if not role:
                    raise ValueError("角色不存在")
                
                # 移除权限组
                role.permission_groups = [
                    g for g in role.permission_groups
                    if g.id not in group_ids
                ]
                
                role.updated_at = datetime.now(timezone.utc)
                await session.commit()
                await session.refresh(role)
                
                logger.info(
                    "角色移除权限组成功",
                    role_id=role_id,
                    group_ids=group_ids
                )
                
                return self.role_converter.to_schema(role)
                
            except Exception as e:
                logger.error(
                    "角色移除权限组失败",
                    role_id=role_id,
                    group_ids=group_ids,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("角色移除权限组失败") from e

    async def batch_delete(self, role_ids: List[str]) -> Dict[str, Any]:
        """批量删除角色

        Args:
            role_ids: 角色ID列表

        Returns:
            Dict: 包含删除结果的字典
        """
        deleted_count = 0
        failed_count = 0
        errors = {}

        async with self.async_session() as session:
            for role_id in role_ids:
                try:
                    # 查询角色
                    role = await self.get_by_id(role_id)
                    if not role:
                        failed_count += 1
                        errors[role_id] = "角色不存在"
                        continue

                    # 执行删除
                    delete_stmt = delete(RoleModel).where(RoleModel.id == role_id)
                    await session.execute(delete_stmt)
                    deleted_count += 1
                except Exception as e:
                    failed_count += 1
                    errors[role_id] = str(e)

            # 提交事务
            await session.commit()

        return {
            "deleted": deleted_count,
            "failed": failed_count,
            "errors": errors
        }

    async def delete(self, role_id: str) -> bool:
        """删除角色

        Args:
            role_id: 角色ID

        Returns:
            是否删除成功

        Raises:
            DatabaseError: 数据库错误
        """
        async with self.async_session() as session:
            try:
                result = await session.execute(
                    delete(RoleModel).where(RoleModel.id == role_id)
                )
                await session.commit()
                
                deleted = result.rowcount > 0
                if deleted:
                    logger.info("角色删除成功", role_id=role_id)
                else:
                    logger.warning("角色不存在", role_id=role_id)
                    
                return deleted
                
            except Exception as e:
                logger.error(
                    "角色删除失败",
                    role_id=role_id,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("角色删除失败") from e

    async def check_permission(
        self,
        *,
        role_id: str,
        permission_code: str,
        check_parent: bool = True
    ) -> bool:
        """检查角色是否拥有指定权限

        Args:
            role_id: 角色ID
            permission_code: 权限代码
            check_parent: 是否检查父角色权限

        Returns:
            是否拥有权限
        """
        role = await self.get_by_id(role_id)
        if not role or not role.is_active:
            return False
            
        # 检查直接权限
        for permission in role.permissions:
            if permission.code == permission_code and permission.is_active:
                return True
                
        # 检查权限组中的权限
        for group in role.permission_groups:
            if not group.is_active:
                continue
            for permission in group.permissions:
                if permission.code == permission_code and permission.is_active:
                    return True
                    
        # 如果需要检查父角色且角色支持继承
        if check_parent and role.is_inherit and role.parent_id:
            return await self.check_permission(
                role_id=role.parent_id,
                permission_code=permission_code,
                check_parent=True
            )
            
        return False

    async def get_all_permissions(
        self,
        *,
        role_id: str,
        include_parent: bool = True
    ) -> List[Permission]:
        """获取角色的所有权限（包括权限组中的权限）

        Args:
            role_id: 角色ID
            include_parent: 是否包含父角色的权限

        Returns:
            权限列表

        Raises:
            ValueError: 角色不存在
        """
        role = await self.get_role_and_permission_by_id(role_id)
        if not role:
            raise ValueError("角色不存在")


        return role
        # 因为消除了权限组的概念所以权限都是全的，暂时不需要去重
        # permissions = set()
        #
        # # 添加直接权限
        # for permission in role.permissions:
        #     if permission.is_active:
        #         permissions.add(permission)
                
        # 添加权限组中的权限
        # for group in role.permission_groups:
        #     if group.is_active:
        #         for permission in group.permissions:
        #             if permission.is_active:
        #                 permissions.add(permission)
                        
        # 如果需要包含父角色权限且角色支持继承
        # if include_parent and role.is_inherit and role.parent_id:
        #     parent_permissions = await self.get_all_permissions(
        #         role_id=role.parent_id,
        #         include_parent=True
        #     )
        #     permissions.update(parent_permissions)
        
        # # 转换为列表
        # permissions = [Permission.model_validate(p) for p in permissions]
        # return permissions

    async def get_effective_permissions(
        self,
        *,
        role_id: str,
        include_parent: bool = True
    ) -> Dict[str, List[Permission]]:
        """获取角色的有效权限（按来源分类）

        Args:
            role_id: 角色ID
            include_parent: 是否包含父角色的权限

        Returns:
            Dict[str, List[Permission]]: 按来源分类的权限列表
            {
                "direct": [直接分配的权限],
                "groups": [权限组中的权限],
                "inherited": [继承的权限]
            }

        Raises:
            ValueError: 角色不存在
        """
        role = await self.get_by_id(role_id)
        if not role:
            raise ValueError("角色不存在")
            
        result = {
            "direct": [],
            "groups": [],
            "inherited": []
        }
        
        # 添加直接权限
        result["direct"] = [
            Permission.model_validate(p)
            for p in role.permissions
            if p.is_active
        ]
        
        # 添加权限组中的权限
        for group in role.permission_groups:
            if group.is_active:
                result["groups"].extend([
                    Permission.model_validate(p)
                    for p in group.permissions
                    if p.is_active
                ])
                
        # 如果需要包含父角色权限且角色支持继承
        if include_parent and role.is_inherit and role.parent_id:
            parent_permissions = await self.get_all_permissions(
                role_id=role.parent_id,
                include_parent=True
            )
            result["inherited"] = [
                Permission.model_validate(p)
                for p in parent_permissions
            ]
            
        # 返回结果
        return result
