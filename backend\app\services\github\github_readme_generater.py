#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/5/22 15:50
# @File    : github_readme_generater.py
# @Description: 
"""
import structlog
import asyncio
import os
import platform
import subprocess
import shutil
import uuid
from urllib.parse import urlparse

import aiohttp
from typing import Dict, Any, Optional, Tuple, List
import requests
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.models.github.github_project import GitHubProjectModel
from app.services.reasoning.workflow import AnalysisState
from app.services.reasoning.workflow.diagram_generator_graph import generate_project_diagrams
from app.services.reasoning.workflow.diagram_generators import DiagramType, DiagramFormat
from app.services.reasoning.workflow.project_analysis_graph import analyze_project
from app.utils.status_enum import ProjectStatusEnum

logger = structlog.get_logger(__name__)

class GitHubReadmeGenerate:
    """Git项目下载工具类"""

    _instance = None
    _generate_queue = asyncio.Queue()
    _generate_task = []
    _initialized = False
    _active_tasks = 0  # 当前活跃任务数
    _max_concurrent_tasks = 1  # 最大并发任务数
    _generate_stats = {
        "total": 0,
        "success": 0,
        "failed": 0,
        "in_progress": 0
    }

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, session=None, async_session=None):
        if not self._initialized:
            # 检测操作系统
            os_type = platform.system()
            # 从配置中获取基础目录（根据操作系统）
            os_type = platform.system()
            if os_type == "Windows":
                base_dir = settings.github.GITHUB_PROJECTS_README_DIR_WINDOWS
            else:  # Linux 或 Mac
                base_dir = settings.github.GITHUB_PROJECTS_README_DIR_LINUX
            self.base_dir = base_dir
            logger.info(f"使用项目存储目录: {self.base_dir} (操作系统: {os_type})")

            # 创建目录（如果不存在）
            os.makedirs(self.base_dir, exist_ok=True)

            # 保存会话提供者
            self._session = session
            self._async_session = async_session


            # 确保生成任务已启动
            for _ in range(self._max_concurrent_tasks):
                task = asyncio.create_task(self._process_generate_queue())
                GitHubReadmeGenerate._generate_task.append(task)
            self._initialized = True
        logger.info(f"Git README生成器初始化完成，基础目录: {self.base_dir}")

    @classmethod
    async def get_generate_queue_status(cls) -> Dict[str, int]:
        """获取生成队列状态

        Returns:
            Dict[str, int]: 包含队列状态的字典
        """
        return {
            "queue_size": cls._generate_queue.qsize(),
            "active_tasks": cls._active_tasks,
            "max_concurrent": cls._max_concurrent_tasks,
            "total_generations": cls._generate_stats["total"],
            "successful_generations": cls._generate_stats["success"],
            "failed_generations": cls._generate_stats["failed"],
            "in_progress": cls._generate_stats["in_progress"]
        }

    @classmethod
    async def get_generate_queue(cls) -> List[str]:
        """获取当前生成队列中的所有项目ID

        Returns:
            List[str]: 队列中的项目ID列表
        """
        # 创建队列内容的副本
        queue_copy = []
        temp_queue = asyncio.Queue()

        # 获取所有项目ID并保持队列不变
        try:
            while not cls._generate_queue.empty():
                item = await cls._generate_queue.get()
                queue_copy.append(item)
                await temp_queue.put(item)

            # 恢复原队列
            while not temp_queue.empty():
                item = await temp_queue.get()
                await cls._generate_queue.put(item)

            return queue_copy

        except Exception as e:
            logger.error(f"获取生成队列时发生错误: {str(e)}")
            return []

    @classmethod
    async def add_to_generate_queue(cls, project_id: str) -> None:
        """添加项目到下载队列

        Args:
            project_id: 项目ID
            repo_url: 仓库URL
        """
        await cls._generate_queue.put(project_id)
        logger.info(f"项目 {project_id} 已添加到生成队列")

    async def _process_generate_queue(self) -> None:
        """处理下载队列的异步任务"""
        while True:
            try:
                # 从队列获取项目信息
                project_id = await self._generate_queue.get()
                # 使用异步上下文管理器创建新的session 这里真的能行吗
                async with self._async_session() as session:
                    try:
                        # asyncio.create_task(self._process_project(session, project_id))
                        await self._process_project(session, project_id)
                    except Exception as e:
                        logger.error(f"处理项目 {project_id} 时发生错误: {str(e)}")
                        await self._update_project_status(session, project_id, ProjectStatusEnum.GenerateFail.value)
                    finally:
                        self._generate_queue.task_done()

            except Exception as e:
                logger.error(f"下载队列处理发生错误: {str(e)}")
                await asyncio.sleep(5)  # 发生错误时等待一段时间再继续

    async def _process_project(self, session: AsyncSession, project_id: str) -> None:
        """处理单个项目的生成任务，使用信号量限制并发"""
        try:
            # 获取信号量以限制并发数量
            self._generate_stats["in_progress"] += 1
            self._generate_stats["total"] += 1
            self._active_tasks += 1

            # await self._update_project_status(session, project_id, DownloadStatusEnum.Generate.value)
            local_path = await self._get_project_local_path(session, project_id)

            if not local_path:
                logger.error(f"生成README卡片失败，项目 {project_id} 本地路径未找到")
                await self._update_project_status(session, project_id,
                                                  ProjectStatusEnum.GenerateFail.value)
                self._generate_stats["failed"] += 1
                return

            # 生成README
            success, readme_info, architecture, dependency = await self.generate_repository(local_path)

            if success:
                # 更新项目信息
                slogan, description, feature_tags, phone_shared_data = await self._update_project_card_info(session, project_id, readme_info, architecture, dependency)
                await self._update_project_success(session, project_id, architecture, dependency, slogan, description, feature_tags, phone_shared_data)
                self._generate_stats["success"] += 1
                logger.info(f"项目 {project_id} README生成成功")
            else:
                await self._update_project_status(session, project_id, ProjectStatusEnum.GenerateFail.value)
                self._generate_stats["failed"] += 1
                logger.error(f"项目 {project_id} README生成失败")

            logger.info(f"项目 {project_id} 处理完成，程序休息5秒")
            await asyncio.sleep(5)
        except Exception as e:
            logger.error(f"处理项目 {project_id} 时发生错误: {str(e)}")
            self._generate_stats["failed"] += 1
            # 确保当前会话仍然有效
            try:
                await self._update_project_status(session, project_id, ProjectStatusEnum.GenerateFail.value)
            except Exception as session_error:
                logger.error(f"更新项目状态失败: {str(session_error)}")
        finally:
            self._generate_stats["in_progress"] -= 1
            self._active_tasks -= 1


    async def _update_project_status(self, session: AsyncSession, project_id: str, status: str) -> None:

        try:
            project = await session.get(GitHubProjectModel, project_id)
            if project:
                project.project_phase = status
                await session.commit()
        except Exception as e:
            logger.error(f"更新项目状态失败: {str(e)}")
            await session.rollback()

    async def _get_project_local_path(self, session: AsyncSession, project_id):
        try:
            project = await session.get(GitHubProjectModel, project_id)
            if project:
                return project.local_path
        except Exception as e:
            logger.error(f"更新项目信息失败: {str(e)}")
            await session.rollback()

    async def _update_project_card_info(self, session: AsyncSession, project_id: str,
                                        cards_info: AnalysisState,
                                        architecture: str,
                                        dependency: str,
                                        ) -> None:
        """更新项目卡片信息

        Args:
            session: 异步数据库会话feature_tags
            project_id: 项目ID
            project_info: 项目分析状态对象
        """
        try:
            from app.models.github.github_project_card import GitHubProjectCardModel
            from sqlalchemy import delete

            # 先删除该项目所有现有卡片
            delete_stmt = delete(GitHubProjectCardModel).where(GitHubProjectCardModel.project_id == str(project_id))
            await session.execute(delete_stmt)

            # 检查README是否存在
            if not cards_info or not cards_info.readme:
                logger.warning(f"项目 {project_id} 未生成README或README为空")
                return None, None

            # 获取按顺序排序的章节
            ordered_sections = sorted(
                cards_info.readme.sections.values(),
                key=lambda s: s.order
            )
            # logger.info(f"项目 {project_id} 的ordered_sections内容", sections=ordered_sections,
            #             sections_count=len(ordered_sections))

            # 为每个章节创建卡片
            index = 0
            for section in ordered_sections:
                index += 1
                # 调用每个章节的 to_markdown() 方法
                logger.info(f"项目 {project_id} 的section内容", section=section)

                section_markdown = section.to_markdown()

                # 如果markdown内容为空，跳过
                if not section_markdown or not section_markdown.strip():
                    continue
                if index == 2:
                    pass

                title = section.title if hasattr(section, 'title') and section.title else f"章节 {index}"
                # 创建卡片 - 使用markdown内容
                section_card = GitHubProjectCardModel(
                    project_id=str(project_id),
                    title=title,
                    content=section_markdown,  # 使用完整的markdown内容
                    like=0,
                    dislike=0,
                    collect=0,
                    sort_order=index
                )
                session.add(section_card)

                # if index == 2:  # 在第二个卡片之后插入 必定是依赖图解
                #     dependency_card = GitHubProjectCardModel(
                #         project_id=str(project_id),
                #         title="依赖关系图解",
                #         content=dependency,  # 使用传入的dependency参数
                #         like=0,
                #         dislike=0,
                #         collect=0,
                #         sort_order=index + 1
                #     )
                #     session.add(dependency_card)
                #     # 更新后续卡片的排序
                #     index += 1
            await session.commit()
            logger.info(f"项目 {project_id} 卡片信息更新成功，共创建 {len(ordered_sections)} 个卡片")


            # 外部推荐
            slogan = cards_info.readme.sections['introduction'].slogan
            # 内部详情
            description = cards_info.readme.sections['introduction'].project_description
            # 特征标签
            feature_tags = cards_info.readme.sections['introduction'].feature_tags

            # 分享
            phone_shared_data = self._create_share_data(cards_info)

            return slogan, description, feature_tags, phone_shared_data

        except Exception as e:
            logger.error(f"更新项目卡片信息失败: {str(e)}")
            await session.rollback()

    def _create_share_data(self, cards_info: AnalysisState) -> str:
        """创建手机分享数据

        Args:
            cards_info: 项目分析状态对象

        Returns:
            str: JSON格式的分享数据
        """
        import json

        # 初始化分享数据列表
        shared_data = []

        # 获取introduction章节
        intro_section = cards_info.readme.sections.get('introduction')
        if intro_section:
            # 1. 简介 - 使用project_description
            if intro_section.project_description:
                shared_data.append({
                    "key": "简介",
                    "value": intro_section.project_description
                })

            # 2. 功能特性 - 收集所有核心功能的标题
            if intro_section.core_features:
                feature_titles = []
                for feature in intro_section.core_features:
                    if feature.title:
                        feature_titles.append(feature.title)
                if feature_titles:
                    shared_data.append({
                        "key": "功能特性",
                        "value": "、".join(feature_titles)
                    })

            # 3. 适用场景 - 使用use_cases
            if intro_section.use_cases:
                shared_data.append({
                    "key": "适用场景",
                    "value": "、".join(intro_section.use_cases)
                })

            # 4. 项目愿景 - 使用project_vision
            if intro_section.project_vision:
                shared_data.append({
                    "key": "项目愿景",
                    "value": intro_section.project_vision
                })

        # 将数据转换为JSON字符串
        return json.dumps(shared_data, ensure_ascii=False)

    async def _update_project_success(self, session: AsyncSession, project_id: str,
                                    architecture: str,
                                    dependency: str,
                                    slogan: Optional[str] = None,
                                    description: Optional[str] = None,
                                    feature_tags: Optional[str] = None,
                                    phone_shared_data: Optional[str] = None
                                      ) -> None:

        try:
            project = await session.get(GitHubProjectModel, project_id)
            if project:
                # project.local_path = local_path
                # project.image_url = local_image_url
                # project.icon_url = local_image_url
                project.description_recommend = slogan
                project.description_project = description
                project.status = ProjectStatusEnum.TRUE.value
                project.project_phase = ProjectStatusEnum.GenerateSuccess.value
                if architecture is not None:
                    project.architecture_mermaid = architecture
                if dependency is not None:
                    project.dependency_mermaid = dependency
                if feature_tags is not None:
                    project.tags = feature_tags
                if phone_shared_data is not None:
                    project.shared_data = phone_shared_data
                await session.commit()
        except Exception as e:
            logger.error(f"更新项目信息失败: {str(e)}")
            await session.rollback()

    def solve_markdown(self):
        pass

    def generate_md_filename(self,path):
        parent_dir = os.path.basename(os.path.dirname(path))
        current_dir = os.path.basename(path)
        return f"{parent_dir}_{current_dir}.md"

    async def generate_repository(self, project_path: str) -> (bool, AnalysisState, str, str):
        try:
            project_name = os.path.basename(project_path)
            file_name = self.generate_md_filename(project_path)
            generated_path = self.base_dir
            logger.info("开始项目示例:: ", project_path=project_path, project_name=project_name, file_name=file_name, generated_path=generated_path)

            #=======================生成卡片=======================
            analysis_result = await analyze_project(
                file_name=self.generate_md_filename(project_path),
                generated_path=generated_path,
                project_path=project_path,
                project_name=project_name
            )

            # ==================== 详细输出分析结果 ====================
            logger.info("项目分析完成，开始输出所有结果详情", project_name=project_name)

            # 输出 AnalysisState 对象的基本信息
            logger.info(" AnalysisState 对象基本信息",
                        analysis_result_type=type(analysis_result).__name__,
                        analysis_result_str=str(analysis_result),
                        analysis_result_repr=repr(analysis_result))


            #=======================生成图=======================
            # 定义要生成的图表类型
            diagram_types = [
                DiagramType.ARCHITECTURE,  # 架构图
                DiagramType.DEPENDENCY  # 依赖图
            ]

            diagram_formats = [
                DiagramFormat.MERMAID,  # Mermaid 格式
                DiagramFormat.JSON  # JSON 格式
            ]

            # 生成项目架构图和依赖图
            diagram_result = await generate_project_diagrams(
                project_path=project_path,
                project_name=project_name,
                # output_dir=diagrams_dir,
                diagram_types=diagram_types,
                diagram_formats=diagram_formats,
                detail_level=2,  # 中等详细程度
                colorize=True,  # 使用颜色
                interactive=True  # 生成交互式图表
            )
            architecture = diagram_result.get("diagrams", {}).get(DiagramType.ARCHITECTURE, {}).get(DiagramFormat.MERMAID, "")
            dependency = diagram_result.get("diagrams", {}).get(DiagramType.DEPENDENCY, {}).get(DiagramFormat.MERMAID, "")

            return True, analysis_result, architecture, dependency
        # except subprocess.CalledProcessError as e:
        #     logger.error(f"仓库克隆失败: {str(e)}", error=e.stderr.decode() if e.stderr else "")
        #     return False, "", {}
        except Exception as e:
            logger.error(f"分析过程中发生错误: {str(e)}")
            return False, None, None, None


    def _normalize_path(self, path: str) -> str:
        os_type = platform.system()

        if os_type == "Windows":
            # Windows路径处理
            return path.replace('/', '\\')
        else:
            # Linux/Mac路径处理
            return path.replace('\\', '/')

    def _get_repository_info(self, repo_url: str, owner: str, repo_name: str) -> Dict[str, Any]:
        """获取仓库信息

        Args:
            repo_url: 仓库URL
            owner: 仓库所有者
            repo_name: 仓库名称

        Returns:
            Dict[str, Any]: 仓库信息
        """
        # 尝试从GitHub API获取仓库信息
        api_url = f"https://api.github.com/repos/{owner}/{repo_name}"

        try:
            headers = {}
            if settings.github.GITHUB_TOKEN:
                headers["Authorization"] = f"token {settings.github.GITHUB_TOKEN}"

            response = requests.get(api_url, headers=headers)
            response.raise_for_status()

            repo_data = response.json()

            # 提取需要的信息
            tags = []
            if "topics" in repo_data:
                tags = repo_data["topics"]
            elif "language" in repo_data and repo_data["language"]:
                tags = [repo_data["language"]]

            return {
                "repository_url": repo_url,
                "description_recommend": repo_data.get("description", ""),
                "tags": tags,
                "image_url": repo_data.get("owner", {}).get("avatar_url", None),
                "status": "true"
            }

        except Exception as e:
            logger.warning(f"无法从GitHub API获取仓库信息: {str(e)}")
            # 返回基本信息
            return {
                "repository_url": repo_url,
                "tags": [],
                "status": "true"
            }