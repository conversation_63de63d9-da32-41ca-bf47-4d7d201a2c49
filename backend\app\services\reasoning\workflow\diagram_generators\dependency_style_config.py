"""
依赖图样式配置模块
提供基于dependency_category的智能样式分配
"""

import hashlib
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum


class NodeShape(Enum):
    """节点形状枚举"""
    RECTANGLE = "rect"          # 矩形 - 工具类
    CIRCLE = "circle"           # 圆形 - 库类
    HEXAGON = "hexagon"         # 六边形 - 框架类
    DIAMOND = "diamond"         # 菱形 - 数据库类
    ROUNDED_RECT = "round"      # 圆角矩形 - 服务类
    ELLIPSE = "ellipse"         # 椭圆 - 中间件类
    CYLINDER = "cylinder"       # 圆柱 - 存储类
    STADIUM = "stadium"         # 体育场形 - 网络类


@dataclass
class CategoryStyle:
    """分类样式配置"""
    color: str                  # 主色调
    shape: NodeShape           # 节点形状
    border_color: str          # 边框颜色
    text_color: str            # 文字颜色
    icon: Optional[str] = None # 图标（可选）


class DependencyStyleConfig:
    """依赖样式配置管理器"""
    
    def __init__(self):
        # 预定义的分类样式映射
        self.predefined_styles: Dict[str, CategoryStyle] = {
            # 前端相关
            "frontend": CategoryStyle("#61DAFB", NodeShape.HEXAGON, "#21A0C4", "#000000", "🎨"),
            "ui": CategoryStyle("#FF6B6B", NodeShape.HEXAGON, "#E55555", "#FFFFFF", "🎭"),
            "css": CategoryStyle("#1572B6", NodeShape.RECTANGLE, "#0F5A8A", "#FFFFFF", "🎨"),
            
            # 后端相关
            "backend": CategoryStyle("#68D391", NodeShape.ROUNDED_RECT, "#48BB78", "#000000", "⚙️"),
            "api": CategoryStyle("#9F7AEA", NodeShape.ROUNDED_RECT, "#805AD5", "#FFFFFF", "🔌"),
            "server": CategoryStyle("#F6AD55", NodeShape.ROUNDED_RECT, "#ED8936", "#000000", "🖥️"),
            
            # 数据库相关
            "database": CategoryStyle("#4299E1", NodeShape.CYLINDER, "#3182CE", "#FFFFFF", "🗄️"),
            "storage": CategoryStyle("#38B2AC", NodeShape.CYLINDER, "#319795", "#FFFFFF", "💾"),
            "cache": CategoryStyle("#FC8181", NodeShape.DIAMOND, "#F56565", "#FFFFFF", "⚡"),
            
            # 网络相关
            "network": CategoryStyle("#A78BFA", NodeShape.STADIUM, "#8B5CF6", "#FFFFFF", "🌐"),
            "http": CategoryStyle("#FBB6CE", NodeShape.STADIUM, "#F687B3", "#000000", "📡"),
            "websocket": CategoryStyle("#81E6D9", NodeShape.STADIUM, "#4FD1C7", "#000000", "🔗"),
            
            # 工具相关
            "tool": CategoryStyle("#FED7D7", NodeShape.RECTANGLE, "#FC8181", "#000000", "🔧"),
            "build": CategoryStyle("#C6F6D5", NodeShape.RECTANGLE, "#68D391", "#000000", "🏗️"),
            "test": CategoryStyle("#FEEBC8", NodeShape.RECTANGLE, "#F6AD55", "#000000", "🧪"),
            
            # 安全相关
            "security": CategoryStyle("#FEB2B2", NodeShape.DIAMOND, "#F56565", "#FFFFFF", "🔒"),
            "auth": CategoryStyle("#D6BCFA", NodeShape.DIAMOND, "#B794F6", "#FFFFFF", "🔐"),
            
            # 监控相关
            "monitoring": CategoryStyle("#BEE3F8", NodeShape.ELLIPSE, "#90CDF4", "#000000", "📊"),
            "logging": CategoryStyle("#C6F6D5", NodeShape.ELLIPSE, "#9AE6B4", "#000000", "📝"),
            
            # 其他
            "utility": CategoryStyle("#E2E8F0", NodeShape.CIRCLE, "#CBD5E0", "#000000", "🛠️"),
            "other": CategoryStyle("#F7FAFC", NodeShape.CIRCLE, "#E2E8F0", "#000000", "📦"),
        }
        
        # 颜色调色板（用于动态生成）
        self.color_palette = [
            "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
            "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9",
            "#F8C471", "#82E0AA", "#F1948A", "#85C1E9", "#D7BDE2"
        ]
        
        # 形状优先级（按使用频率排序）
        self.shape_priority = [
            NodeShape.CIRCLE, NodeShape.RECTANGLE, NodeShape.HEXAGON,
            NodeShape.ROUNDED_RECT, NodeShape.DIAMOND, NodeShape.ELLIPSE,
            NodeShape.STADIUM, NodeShape.CYLINDER
        ]
    
    def get_category_style(self, category: str, is_dev_dependency: bool = False) -> CategoryStyle:
        """
        获取分类样式
        
        Args:
            category: 依赖分类
            is_dev_dependency: 是否为开发依赖
            
        Returns:
            CategoryStyle: 分类样式配置
        """
        # 标准化分类名称
        normalized_category = self._normalize_category(category)
        
        # 检查预定义样式
        if normalized_category in self.predefined_styles:
            style = self.predefined_styles[normalized_category]
        else:
            # 动态生成样式
            style = self._generate_dynamic_style(normalized_category)
        
        # 如果是开发依赖，调整样式
        if is_dev_dependency:
            style = self._adjust_for_dev_dependency(style)
        
        return style
    
    def _normalize_category(self, category: str) -> str:
        """标准化分类名称"""
        if not category:
            return "other"
        
        # 转换为小写并移除特殊字符
        normalized = category.lower().strip()
        
        # 处理常见的分类别名
        category_aliases = {
            "web": "frontend",
            "framework": "backend",
            "db": "database",
            "testing": "test",
            "dev-tool": "tool",
            "development": "tool",
            "middleware": "backend",
            "orm": "database",
            "cli": "tool",
            "parser": "utility",
            "validator": "utility",
            "serializer": "utility",
        }
        
        return category_aliases.get(normalized, normalized)
    
    def _generate_dynamic_style(self, category: str) -> CategoryStyle:
        """动态生成样式"""
        # 使用哈希算法确保相同分类始终生成相同样式
        hash_value = int(hashlib.md5(category.encode()).hexdigest(), 16)
        
        # 选择颜色
        color_index = hash_value % len(self.color_palette)
        color = self.color_palette[color_index]
        
        # 选择形状
        shape_index = (hash_value // len(self.color_palette)) % len(self.shape_priority)
        shape = self.shape_priority[shape_index]
        
        # 生成边框颜色（比主色调深一些）
        border_color = self._darken_color(color)
        
        # 选择文字颜色（基于背景亮度）
        text_color = self._get_text_color(color)
        
        return CategoryStyle(
            color=color,
            shape=shape,
            border_color=border_color,
            text_color=text_color,
            icon="📦"  # 默认图标
        )
    
    def _adjust_for_dev_dependency(self, style: CategoryStyle) -> CategoryStyle:
        """为开发依赖调整样式"""
        # 开发依赖使用虚线边框和稍微透明的颜色
        return CategoryStyle(
            color=self._lighten_color(style.color),
            shape=style.shape,
            border_color=style.border_color,
            text_color=style.text_color,
            icon=style.icon
        )
    
    def _darken_color(self, hex_color: str) -> str:
        """使颜色变深"""
        # 简单的颜色变深算法
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        darkened_rgb = tuple(max(0, int(c * 0.7)) for c in rgb)
        return f"#{darkened_rgb[0]:02x}{darkened_rgb[1]:02x}{darkened_rgb[2]:02x}"
    
    def _lighten_color(self, hex_color: str) -> str:
        """使颜色变浅"""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        lightened_rgb = tuple(min(255, int(c + (255 - c) * 0.3)) for c in rgb)
        return f"#{lightened_rgb[0]:02x}{lightened_rgb[1]:02x}{lightened_rgb[2]:02x}"
    
    def _get_text_color(self, hex_color: str) -> str:
        """根据背景颜色选择文字颜色"""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        # 计算亮度
        brightness = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000
        return "#000000" if brightness > 128 else "#FFFFFF"
    
    def get_all_categories(self) -> List[str]:
        """获取所有预定义分类"""
        return list(self.predefined_styles.keys())
    
    def add_custom_style(self, category: str, style: CategoryStyle):
        """添加自定义样式"""
        self.predefined_styles[category] = style
