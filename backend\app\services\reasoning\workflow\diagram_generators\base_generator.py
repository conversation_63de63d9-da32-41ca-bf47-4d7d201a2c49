#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础图生成器类
"""
import structlog
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod
from .models import DiagramState, DiagramConfig, DiagramType, DiagramFormat

logger = structlog.get_logger(__name__)


class BaseDiagramGenerator(ABC):
    """基础图生成器抽象类"""

    def __init__(self, config: DiagramConfig):
        """
        初始化基础图生成器

        Args:
            config: 图表生成配置
        """
        self.config = config
        self.diagram_type = ""  # 子类需要设置具体图表类型

    @staticmethod
    def create_diagram_state_from_analysis(analysis_state: Any, config: DiagramConfig) -> DiagramState:
        """
        从AnalysisState创建DiagramState
        
        Args:
            analysis_state: 分析状态，包含各阶段生成的数据
            config: 图表生成配置
            
        Returns:
            用于图表生成的状态对象
        """
        # 创建基础DiagramState对象
        diagram_state = DiagramState(
            project_path=analysis_state.project_path,
            project_name=analysis_state.project_name,
            config=config
        )
        
        # 映射分析数据
        diagram_state.structure_analysis = analysis_state.structure_analysis
        diagram_state.dependency_analysis = analysis_state.dependency_analysis
        diagram_state.architecture_analysis = analysis_state.architecture_analysis
        
        # 初始化图表结果字典
        for diagram_type in config.diagram_types:
            if diagram_type not in diagram_state.diagrams:
                diagram_state.diagrams[diagram_type] = {}
                for format_type in config.diagram_format:
                    diagram_state.diagrams[diagram_type][format_type] = ""
                    
        return diagram_state

    @abstractmethod
    async def generate(self, state: DiagramState) -> DiagramState:
        """
        生成图表

        Args:
            state: 当前图表生成状态

        Returns:
            更新后的图表生成状态
        """
        pass
    
    async def validate_required_data(self, state: DiagramState) -> Dict[str, bool]:
        """
        验证生成图表所需的数据是否完整
        
        Args:
            state: 当前图表生成状态
            
        Returns:
            数据验证结果字典
        """
        return {"valid": True, "message": "数据验证通过"}
