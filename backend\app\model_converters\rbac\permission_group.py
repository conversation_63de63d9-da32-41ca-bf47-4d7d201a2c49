"""
权限组模型转换器
"""
from typing import Optional
from app.models.rbac.permission_group import PermissionGroupModel
from app.schemas.rbac.permission_group import PermissionGroup
from ..base import BaseConverter
from .permission import PermissionConverter

class PermissionGroupConverter(BaseConverter[PermissionGroupModel, PermissionGroup]):
    """权限组模型转换器"""
    
    def __init__(self):
        """初始化转换器"""
        super().__init__()
        self.permission_converter = PermissionConverter()
    
    def to_schema(self, model: PermissionGroupModel) -> PermissionGroup:
        """将权限组模型转换为schema
        
        Args:
            model: 权限组模型实例
            
        Returns:
            权限组schema实例
        """
        # 临时存储permissions和roles避免循环引用
        self._store_temp_attr(model, 'permissions', [])
        self._store_temp_attr(model, 'roles', [])
        
        # 转换基本属性
        permission_group = PermissionGroup.model_validate(model)
        
        # 恢复但不转换，避免循环引用
        self._restore_temp_attr(model, 'permissions')
        self._restore_temp_attr(model, 'roles')
        
        return permission_group
    
    def to_model(self, schema: PermissionGroup) -> PermissionGroupModel:
        """将权限组schema转换为模型
        
        Args:
            schema: 权限组schema实例
            
        Returns:
            权限组模型实例
        """
        # 转换基本属性
        permission_group = PermissionGroupModel()
        for field in schema.model_fields:
            if field not in ['permissions', 'roles'] and hasattr(schema, field):
                value = getattr(schema, field)
                if value is not None:
                    setattr(permission_group, field, value)
        
        # 转换permissions
        if schema.permissions:
            permission_group.permissions = [
                self.permission_converter.to_model(permission)
                for permission in schema.permissions
            ]
        
        return permission_group
