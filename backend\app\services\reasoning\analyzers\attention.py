#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注意力机制实现
包含注意力单元和注意力上下文的核心类
"""
import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Literal
import uuid

from pydantic import BaseModel, Field, field_validator

logger = logging.getLogger(__name__)


class AttentionUnit(BaseModel):
    """注意力单元，表示关注的代码单元"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="注意力单元ID")
    name: str = Field(..., description="单元名称")
    type: Literal["function", "class", "module", "package", "project"] = Field(..., description="单元类型")
    content: str = Field(..., description="代码内容")
    path: str = Field(..., description="代码路径")
    importance_score: float = Field(default=0.5, description="重要性分数")
    context_relevance: float = Field(default=0.5, description="上下文相关性")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="创建时间")
    
    @field_validator("importance_score", "context_relevance")
    def validate_score(cls, v):
        if v < 0.0 or v > 1.0:
            raise ValueError("分数必须在0.0到1.0之间")
        return v


class AttentionContext(BaseModel):
    """注意力上下文，管理多个注意力单元"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="注意力上下文ID")
    units: List[AttentionUnit] = Field(default_factory=list, description="注意力单元列表")
    focus_level: Literal["function", "module", "project"] = Field(default="module", description="关注级别")
    focus_weights: Dict[str, float] = Field(
        default_factory=lambda: {"function": 0.7, "module": 0.2, "project": 0.1},
        description="各级别权重"
    )
    current_focus: Optional[str] = Field(default=None, description="当前关注的单元ID")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="创建时间")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="更新时间")
    
    def add_unit(self, unit: AttentionUnit) -> None:
        """添加注意力单元"""
        self.units.append(unit)
        self.updated_at = datetime.now(timezone.utc)
    
    def remove_unit(self, unit_id: str) -> bool:
        """移除注意力单元"""
        for i, unit in enumerate(self.units):
            if unit.id == unit_id:
                self.units.pop(i)
                self.updated_at = datetime.now(timezone.utc)
                return True
        logger.warning(f"未找到要移除的注意力单元: {unit_id}")
        return False
    
    def set_focus(self, unit_id: str) -> bool:
        """设置当前关注点"""
        for unit in self.units:
            if unit.id == unit_id:
                self.current_focus = unit_id
                self.updated_at = datetime.now(timezone.utc)
                return True
        logger.warning(f"未找到要关注的注意力单元: {unit_id}")
        return False
    
    def get_current_focus(self) -> Optional[AttentionUnit]:
        """获取当前关注的单元"""
        if not self.current_focus:
            return None
        
        for unit in self.units:
            if unit.id == self.current_focus:
                return unit
                
        logger.warning(f"当前关注的单元不存在: {self.current_focus}")
        return None
    
    def adjust_weights(self, weights: Dict[str, float]) -> None:
        """调整关注级别权重"""
        # 验证权重总和为1
        total = sum(weights.values())
        if abs(total - 1.0) > 0.01:
            normalized_weights = {k: v/total for k, v in weights.items()}
            logger.warning(f"权重总和不为1.0，已自动归一化: {weights} -> {normalized_weights}")
            self.focus_weights = normalized_weights
        else:
            self.focus_weights = weights
            
        self.updated_at = datetime.now(timezone.utc)
    
    def get_weighted_units(self, top_n: int = 5) -> List[AttentionUnit]:
        """获取加权排序后的注意力单元"""
        if not self.units:
            return []
            
        # 计算综合分数
        scored_units = []
        for unit in self.units:
            # 根据单元类型获取权重
            type_weight = self.focus_weights.get(unit.type, 0.1)
            
            # 计算综合分数 = 重要性 * 0.6 + 相关性 * 0.4 * 类型权重
            score = (
                unit.importance_score * 0.6 + 
                unit.context_relevance * 0.4
            ) * type_weight
            
            scored_units.append((unit, score))
            
        # 按分数降序排序
        sorted_units = sorted(scored_units, key=lambda x: x[1], reverse=True)
        
        # 取前top_n个单元
        return [unit for unit, _ in sorted_units[:top_n]]
