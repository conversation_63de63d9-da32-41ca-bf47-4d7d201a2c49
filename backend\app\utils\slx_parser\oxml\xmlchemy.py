# pyright: reportImportCycles=false

"""支持声明式定义lxml自定义元素类。"""

from __future__ import annotations

import re
from typing import (
    TYPE_CHECKING,
    Any,
    Callable,
    Dict,
    List,
    Sequence,
    Tuple,
    Type,
    TypeVar,
)

from lxml import etree
from lxml.etree import ElementBase, _Element  # pyright: ignore[reportPrivateUsage]

from app.utils.slx_parser.oxml.exceptions import InvalidXmlError
from app.utils.slx_parser.oxml.ns import NamespacePrefixedTag, nsmap, qn
from app.utils.slx_parser.opc.shared import lazyproperty

if TYPE_CHECKING:
    from app.utils.slx_parser.enum.base import BaseXmlEnum
    from app.utils.slx_parser.oxml.simpletypes import BaseSimpleType


def serialize_for_reading(element: ElementBase):
    """将`element`序列化为适合测试的人类可读XML。
    
    不包含XML声明。
    """
    xml = etree.tostring(element, encoding="unicode", pretty_print=True)
    return XmlString(xml)


class XmlString(str):
    """提供适用于序列化XML的字符串比较重写，这对测试很有用。"""

    # '    <w:xyz xmlns:a="http://ns/decl/a" attr_name="val">text</w:xyz>'
    # |          |                                          ||           |
    # +----------+------------------------------------------++-----------+
    #  front      attrs                                     | text
    #                                                     close

    _xml_elm_line_patt = re.compile(r"( *</?[\w:]+)(.*?)(/?>)([^<]*</[\w:]+>)?$")

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, str):
            return False
        lines = self.splitlines()
        lines_other = other.splitlines()
        if len(lines) != len(lines_other):
            return False
        for line, line_other in zip(lines, lines_other):
            if not self._eq_elm_strs(line, line_other):
                return False
        return True

    def __ne__(self, other: object) -> bool:
        return not self.__eq__(other)

    def _attr_seq(self, attrs: str) -> List[str]:
        """返回从`attrs`解析出的属性字符串序列。
        
        每个属性字符串都会去除两端的空白。
        """
        attrs = attrs.strip()
        attr_lst = attrs.split()
        return sorted(attr_lst)

    def _eq_elm_strs(self, line: str, line_2: str):
        """如果`line_2`中的元素在XML上等价于`line`中的元素，则返回True。"""
        front, attrs, close, text = self._parse_line(line)
        front_2, attrs_2, close_2, text_2 = self._parse_line(line_2)
        if front != front_2:
            return False
        if self._attr_seq(attrs) != self._attr_seq(attrs_2):
            return False
        if close != close_2:
            return False
        if text != text_2:
            return False
        return True

    @classmethod
    def _parse_line(cls, line: str) -> Tuple[str, str, str, str]:
        """解析XML元素`line`返回的(前部分, 属性, 结束标签, 文本)4元组。"""
        match = cls._xml_elm_line_patt.match(line)
        if match is None:
            return "", "", "", ""
        front, attrs, close, text = [match.group(n) for n in range(1, 5)]
        return front, attrs, close, text


_T = TypeVar("_T")


class MetaOxmlElement(type):
    """MetaOxmlElement的元类。"""
    def __init__(cls, clsname: str, bases: Tuple[type, ...], namespace: Dict[str, Any]):
        dispatchable = (
            OneAndOnlyOne,
            OneOrMore,
            OptionalAttribute,
            RequiredAttribute,
            ZeroOrMore,
            ZeroOrOne,
            ZeroOrOneChoice,
        )
        for key, value in namespace.items():
            if isinstance(value, dispatchable):
                value.populate_class_members(cls, key)


class BaseAttribute:
    """BaseAttribute和RequiredAttribute的基类。
    
    提供通用方法。
    """

    def __init__(self, attr_name: str, simple_type: Type[BaseXmlEnum] | Type[BaseSimpleType]):
        super(BaseAttribute, self).__init__()
        self._attr_name = attr_name
        self._simple_type = simple_type

    def populate_class_members(self, element_cls: MetaOxmlElement, prop_name: str) -> None:
        """向element_cls添加适当的方法。"""
        self._element_cls = element_cls
        self._prop_name = prop_name

        self._add_attr_property()

    def _add_attr_property(self):
        """向元素类添加一个读写.{prop_name}属性。
        
        该属性在访问时返回此属性的解释值，在赋值时将属性值更改为其ST_*对应值。
        """
        property_ = property(self._getter, self._setter, None)
        # -- 赋值不加条件以覆盖元素名称定义 --
        setattr(self._element_cls, self._prop_name, property_)

    @property
    def _clark_name(self):
        if ":" in self._attr_name:
            return qn(self._attr_name)
        return self._attr_name

    @property
    def _getter(self) -> Callable[[BaseOxmlElement], Any | None]: ...

    @property
    def _setter(
        self,
    ) -> Callable[[BaseOxmlElement, Any | None], None]: ...


class OptionalAttribute(BaseAttribute):
    """定义自定义元素类上的可选属性。
    
    可选属性在读取时返回默认值，如果没有指定则返回None。当赋值为None时，属性会被删除，但在读取时仍会返回默认值。
    """

    def __init__(
        self,
        attr_name: str,
        simple_type: Type[BaseXmlEnum] | Type[BaseSimpleType],
        default: BaseXmlEnum | BaseSimpleType | str | bool | None = None,
    ):
        super(OptionalAttribute, self).__init__(attr_name, simple_type)
        self._default = default

    @property
    def _docstring(self):
        """属性属性描述符的__doc__属性使用的字符串。"""
        return (
            f"{self._simple_type.__name__} 类型转换的 ``{self._attr_name}`` 属性值，"
            f" 或 |None| (或指定的默认值) 如果没有出现。"
            f" 将默认值赋给属性会导致属性从元素中删除。"
        )

    @property
    def _getter(
        self,
    ) -> Callable[[BaseOxmlElement], Any | None]:
        """属性属性描述符的get方法。"""

        def get_attr_value(
            obj: BaseOxmlElement,
        ) -> Any | None:
            attr_str_value = obj.get(self._clark_name)
            if attr_str_value is None:
                return self._default
            return self._simple_type.from_xml(attr_str_value)

        get_attr_value.__doc__ = self._docstring
        return get_attr_value

    @property
    def _setter(self) -> Callable[[BaseOxmlElement, Any], None]:
        """属性属性描述符的set方法。"""

        def set_attr_value(obj: BaseOxmlElement, value: Any | None):
            if value is None or value == self._default:
                if self._clark_name in obj.attrib:
                    del obj.attrib[self._clark_name]
                return
            str_value = self._simple_type.to_xml(value)
            if str_value is None:
                if self._clark_name in obj.attrib:
                    del obj.attrib[self._clark_name]
                return
            obj.set(self._clark_name, str_value)

        return set_attr_value


class RequiredAttribute(BaseAttribute):
    """定义自定义元素类上的必需属性。
    
    必需属性在读取时假定存在，因此没有默认值；它的实际值始终使用。如果缺失，则引发InvalidXmlError。它还不会在赋值为None时删除属性；将None赋给它会引发TypeError或ValueError，具体取决于属性的简单类型。
    """

    @property
    def _docstring(self):
        """属性属性描述符的__doc__属性使用的字符串。"""
        return "%s 类型转换的 ``%s`` 属性值。" % (
            self._simple_type.__name__,
            self._attr_name,
        )

    @property
    def _getter(self) -> Callable[[BaseOxmlElement], Any]:
        """属性属性描述符的get方法。"""

        def get_attr_value(obj: BaseOxmlElement) -> Any | None:
            attr_str_value = obj.get(self._clark_name)
            if attr_str_value is None:
                raise InvalidXmlError(
                    "必需的 '%s' 属性未出现在元素 %s 上" % (self._attr_name, obj.tag)
                )
            return self._simple_type.from_xml(attr_str_value)

        get_attr_value.__doc__ = self._docstring
        return get_attr_value

    @property
    def _setter(self) -> Callable[[BaseOxmlElement, Any], None]:
        """属性属性描述符的set方法。"""

        def set_attr_value(obj: BaseOxmlElement, value: Any):
            str_value = self._simple_type.to_xml(value)
            if str_value is None:
                raise ValueError(f"无法将 {value} 赋给此必需属性")
            obj.set(self._clark_name, str_value)

        return set_attr_value


class _BaseChildElement:
    """子元素类的基类。
    
    子元素子类对应于不同的基数，如ZeroOrOne和ZeroOrMore。
    """

    def __init__(self, nsptagname: str, successors: Tuple[str, ...] = ()):
        super(_BaseChildElement, self).__init__()
        self._nsptagname = nsptagname
        self._successors = successors

    def populate_class_members(self, element_cls: MetaOxmlElement, prop_name: str) -> None:
        """向element_cls添加适当的方法。"""
        self._element_cls = element_cls
        self._prop_name = prop_name

    def _add_adder(self):
        """向元素类添加一个_add_x()方法以添加子元素。"""
        def _add_child(obj: BaseOxmlElement, **attrs: Any):
            new_method = getattr(obj, self._new_method_name)
            child = new_method()
            for key, value in attrs.items():
                setattr(child, key, value)
            insert_method = getattr(obj, self._insert_method_name)
            insert_method(child)
            return child

        _add_child.__doc__ = (
            "无条件地添加一个新的 ``<%s>`` 子元素，插入到正确的顺序中。" % self._nsptagname
        )
        self._add_to_class(self._add_method_name, _add_child)

    def _add_creator(self):
        """向元素类添加一个_new_{prop_name}()方法以创建一个新的空元素。"""
        creator = self._creator
        creator.__doc__ = (
            '返回一个 "松散" 的新创建的 ``<%s>`` 元素，没有属性、文本或子元素。' % self._nsptagname
        )
        self._add_to_class(self._new_method_name, creator)

    def _add_getter(self):
        """向元素类添加一个只读{prop_name}属性以获取子元素。"""
        property_ = property(self._getter, None, None)
        # -- 赋值不加条件以覆盖元素名称定义 --
        setattr(self._element_cls, self._prop_name, property_)

    def _add_inserter(self):
        """向元素类添加一个_insert_x()方法以插入子元素。"""
        def _insert_child(obj: BaseOxmlElement, child: BaseOxmlElement):
            obj.insert_element_before(child, *self._successors)
            return child

        _insert_child.__doc__ = (
            "返回传递的 ``<%s>`` 元素，作为子元素插入到正确的顺序中。" % self._nsptagname
        )
        self._add_to_class(self._insert_method_name, _insert_child)

    def _add_list_getter(self):
        """向元素类添加一个只读{prop_name}_lst属性以获取子元素列表。"""
        prop_name = "%s_lst" % self._prop_name
        property_ = property(self._list_getter, None, None)
        setattr(self._element_cls, prop_name, property_)

    @lazyproperty
    def _add_method_name(self):
        return "_add_%s" % self._prop_name

    def _add_public_adder(self):
        """向元素类添加一个公共add_x()方法以添加子元素。"""
        def add_child(obj: BaseOxmlElement):
            private_add_method = getattr(obj, self._add_method_name)
            child = private_add_method()
            return child

        add_child.__doc__ = (
            "无条件地添加一个新的 ``<%s>`` 子元素，插入到正确的顺序中。" % self._nsptagname
        )
        self._add_to_class(self._public_add_method_name, add_child)

    def _add_to_class(self, name: str, method: Callable[..., Any]):
        """向目标类添加方法，除非该类已经定义了该名称。"""
        if hasattr(self._element_cls, name):
            return
        setattr(self._element_cls, name, method)

    @property
    def _creator(self) -> Callable[[BaseOxmlElement], BaseOxmlElement]:
        """创建一个空元素的可调用对象。"""
        from slx_parser.oxml.parser import OxmlElement

        def new_child_element(obj: BaseOxmlElement):
            return OxmlElement(self._nsptagname)

        return new_child_element

    @property
    def _getter(self):
        """属性属性描述符的get方法。"""
        def get_child_element(obj: BaseOxmlElement):
            return obj.find(qn(self._nsptagname))

        get_child_element.__doc__ = (
            "``<%s>`` 子元素或 |None| 如果没有出现。" % self._nsptagname
        )
        return get_child_element

    @lazyproperty
    def _insert_method_name(self):
        return "_insert_%s" % self._prop_name

    @property
    def _list_getter(self):
        """属性属性描述符的get方法。"""
        def get_child_element_list(obj: BaseOxmlElement):
            return obj.findall(qn(self._nsptagname))

        get_child_element_list.__doc__ = (
            "一个包含每个 ``<%s>`` 子元素的列表，按出现顺序排列。" % self._nsptagname
        )
        return get_child_element_list

    @lazyproperty
    def _public_add_method_name(self):
        """add_childElement()是公共API，用于添加新元素到序列中。"""
        return "add_%s" % self._prop_name

    @lazyproperty
    def _remove_method_name(self):
        return "_remove_%s" % self._prop_name

    @lazyproperty
    def _new_method_name(self):
        return "_new_%s" % self._prop_name


class Choice(_BaseChildElement):
    """定义一个子元素，属于一个组，只有一个成员可以作为子元素出现。"""

    @property
    def nsptagname(self):
        return self._nsptagname

    def populate_class_members(  # pyright: ignore[reportIncompatibleMethodOverride]
        self,
        element_cls: MetaOxmlElement,
        group_prop_name: str,
        successors: Tuple[str, ...],
    ) -> None:
        """向element_cls添加适当的方法。"""
        self._element_cls = element_cls
        self._group_prop_name = group_prop_name
        self._successors = successors

        self._add_getter()
        self._add_creator()
        self._add_inserter()
        self._add_adder()
        self._add_get_or_change_to_method()

    def _add_get_or_change_to_method(self):
        """向元素类添加一个get_or_change_to_x()方法以获取或更改子元素。"""
        def get_or_change_to_child(obj: BaseOxmlElement):
            child = getattr(obj, self._prop_name)
            if child is not None:
                return child
            remove_group_method = getattr(obj, self._remove_group_method_name)
            remove_group_method()
            add_method = getattr(obj, self._add_method_name)
            child = add_method()
            return child

        get_or_change_to_child.__doc__ = (
            "返回 ``<%s>`` 子元素，替换任何其他组元素，如果找到。" % self._nsptagname
        )
        self._add_to_class(self._get_or_change_to_method_name, get_or_change_to_child)

    @property
    def _prop_name(self):
        """属性名称，计算自标签名，例如a:schemeClr -> schemeClr。"""
        start = self._nsptagname.index(":") + 1 if ":" in self._nsptagname else 0
        return self._nsptagname[start:]

    @lazyproperty
    def _get_or_change_to_method_name(self):
        return "get_or_change_to_%s" % self._prop_name

    @lazyproperty
    def _remove_group_method_name(self):
        return "_remove_%s" % self._group_prop_name


class OneAndOnlyOne(_BaseChildElement):
    """定义一个必需的子元素。"""

    def __init__(self, nsptagname: str):
        super(OneAndOnlyOne, self).__init__(nsptagname, ())

    def populate_class_members(self, element_cls: MetaOxmlElement, prop_name: str) -> None:
        """向element_cls添加适当的方法。"""
        super(OneAndOnlyOne, self).populate_class_members(element_cls, prop_name)
        self._add_getter()

    @property
    def _getter(self):
        """属性属性描述符的get方法。"""
        def get_child_element(obj: BaseOxmlElement):
            child = obj.find(qn(self._nsptagname))
            if child is None:
                raise InvalidXmlError(
                    "必需的 ``<%s>`` 子元素未出现" % self._nsptagname
                )
            return child

        get_child_element.__doc__ = "必需的 ``<%s>`` 子元素。" % self._nsptagname
        return get_child_element


class OneOrMore(_BaseChildElement):
    """定义一个重复的子元素，必须至少出现一次。"""
    def populate_class_members(self, element_cls: MetaOxmlElement, prop_name: str) -> None:
        """向element_cls添加适当的方法。"""
        super(OneOrMore, self).populate_class_members(element_cls, prop_name)
        self._add_list_getter()
        self._add_creator()
        self._add_inserter()
        self._add_adder()
        self._add_public_adder()
        delattr(element_cls, prop_name)


class ZeroOrMore(_BaseChildElement):
    """定义一个可选的重复子元素。"""
    def populate_class_members(self, element_cls: MetaOxmlElement, prop_name: str) -> None:
        """向element_cls添加适当的方法。"""
        super(ZeroOrMore, self).populate_class_members(element_cls, prop_name)
        self._add_list_getter()
        self._add_creator()
        self._add_inserter()
        self._add_adder()
        self._add_public_adder()
        delattr(element_cls, prop_name)


class ZeroOrOne(_BaseChildElement):
    """定义一个可选的子元素。"""
    def populate_class_members(self, element_cls: MetaOxmlElement, prop_name: str) -> None:
        """向element_cls添加适当的方法。"""
        super(ZeroOrOne, self).populate_class_members(element_cls, prop_name)
        self._add_getter()
        self._add_creator()
        self._add_inserter()
        self._add_adder()
        self._add_get_or_adder()
        self._add_remover()

    def _add_get_or_adder(self):
        """向元素类添加一个get_or_add_x()方法以获取或添加子元素。"""
        def get_or_add_child(obj: BaseOxmlElement):
            child = getattr(obj, self._prop_name)
            if child is None:
                add_method = getattr(obj, self._add_method_name)
                child = add_method()
            return child

        get_or_add_child.__doc__ = (
            "返回 ``<%s>`` 子元素，新添加如果没有出现。" % self._nsptagname
        )
        self._add_to_class(self._get_or_add_method_name, get_or_add_child)

    def _add_remover(self):
        """向元素类添加一个_remove_x()方法以删除子元素。"""
        def _remove_child(obj: BaseOxmlElement):
            obj.remove_all(self._nsptagname)

        _remove_child.__doc__ = ("删除所有 ``<%s>`` 子元素。" % self._nsptagname)
        self._add_to_class(self._remove_method_name, _remove_child)

    @lazyproperty
    def _get_or_add_method_name(self):
        return "get_or_add_%s" % self._prop_name


class ZeroOrOneChoice(_BaseChildElement):
    """对应于一个EG_*元素组，其中最多一个成员可以作为子元素出现。"""
    def __init__(self, choices: Sequence[Choice], successors: Tuple[str, ...] = ()):
        self._choices = choices
        self._successors = successors

    def populate_class_members(self, element_cls: MetaOxmlElement, prop_name: str) -> None:
        """向element_cls添加适当的方法。"""
        super(ZeroOrOneChoice, self).populate_class_members(element_cls, prop_name)
        self._add_choice_getter()
        for choice in self._choices:
            choice.populate_class_members(element_cls, self._prop_name, self._successors)
        self._add_group_remover()

    def _add_choice_getter(self):
        """向元素类添加一个只读{prop_name}属性以获取组成员。"""
        property_ = property(self._choice_getter, None, None)
        # assign unconditionally to overwrite element name definition
        setattr(self._element_cls, self._prop_name, property_)

    def _add_group_remover(self):
        """向元素类添加一个_remove_eg_x()方法以删除组子元素。"""
        def _remove_choice_group(obj: BaseOxmlElement):
            for tagname in self._member_nsptagnames:
                obj.remove_all(tagname)

        _remove_choice_group.__doc__ = "删除当前组子元素，如果存在。"
        self._add_to_class(self._remove_choice_group_method_name, _remove_choice_group)

    @property
    def _choice_getter(self):
        """属性属性描述符的get方法。"""
        def get_group_member_element(obj: BaseOxmlElement):
            return obj.first_child_found_in(*self._member_nsptagnames)

        get_group_member_element.__doc__ = (
            "返回组成员子元素，或 |None| 如果没有出现。"
        )
        return get_group_member_element

    @lazyproperty
    def _member_nsptagnames(self):
        """组成员的命名空间前缀标签名序列。"""
        return [choice.nsptagname for choice in self._choices]

    @lazyproperty
    def _remove_choice_group_method_name(self):
        return "_remove_%s" % self._prop_name


# -- lxml类型不是完全正确的，只需忽略此错误即可 --
class BaseOxmlElement(etree.ElementBase, metaclass=MetaOxmlElement):
    """所有自定义元素类的有效基类。
    
    在一个地方添加标准化的行为。
    """
    def __repr__(self):
        return "<%s '<%s>' at 0x%0x>" % (
            self.__class__.__name__,
            self._nsptag,
            id(self),
        )

    def first_child_found_in(self, *tagnames: str) -> _Element | None:
        """第一个子元素的标签在tagnames中，或者如果没有找到则为None。"""
        for tagname in tagnames:
            child = self.find(qn(tagname))
            if child is not None:
                return child
        return None

    def insert_element_before(self, elm: ElementBase, *tagnames: str):
        successor = self.first_child_found_in(*tagnames)
        if successor is not None:
            successor.addprevious(elm)
        else:
            self.append(elm)
        return elm

    def remove_all(self, *tagnames: str) -> None:
        """删除具有标签名（例如"a:p"）的子元素。"""
        for tagname in tagnames:
            matching = self.findall(qn(tagname))
            for child in matching:
                self.remove(child)

    @property
    def xml(self) -> str:
        """此元素的XML字符串，适合测试目的。
        
        美化打印以提高可读性，没有顶部的XML声明。
        """
        return serialize_for_reading(self)

    def xpath(self, xpath_str: str) -> Any:  # pyright: ignore[reportIncompatibleMethodOverride]

        """重写lxml的_Element.xpath()方法。
        
        提供标准Open XML命名空间映射（nsmap）在中央位置。
        """
        return super().xpath(xpath_str, namespaces=nsmap)

    @property
    def _nsptag(self) -> str:
        return NamespacePrefixedTag.from_clark_name(self.tag)
