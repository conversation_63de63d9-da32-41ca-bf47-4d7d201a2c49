"""
刷新令牌模型模块
"""
from datetime import datetime, timezone
from typing import Optional

from sqlalchemy import String, Integer, DateTime, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.model_base import ModelBase


class TokenModel(ModelBase):
    """用户令牌模型"""
    __tablename__ = "user_tokens"
    
    # 令牌信息  
    token_type: Mapped[str] = mapped_column(String(64), nullable=False)
    access_token: Mapped[str] = mapped_column(String(512), nullable=False, unique=True)
    refresh_token: Mapped[str] = mapped_column(String(512), nullable=False, unique=True)
    user_id: Mapped[str] = mapped_column(String(64), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    expires_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    
    # 关系
    user: Mapped["UserModel"] = relationship(
        "UserModel",
        back_populates="tokens",
        lazy="joined"
    )
    
    def __repr__(self) -> str:
        return f"<Token(id={self.id}, access_token={self.access_token}, refresh_token={self.refresh_token}, user_id={self.user_id})>"
