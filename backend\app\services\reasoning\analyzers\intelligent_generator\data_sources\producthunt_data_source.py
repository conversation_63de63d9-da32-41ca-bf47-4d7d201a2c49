#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Product Hunt数据源

通过Product Hunt API获取产品发布和用户评价信息
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from urllib.parse import quote_plus
import time

from .external_data_source import ExternalDataSource

logger = logging.getLogger(__name__)


class ProductHuntDataSource(ExternalDataSource):
    """Product Hunt数据源类"""
    
    def __init__(self, access_token: Optional[str] = None, timeout: int = 30):
        """
        初始化Product Hunt数据源
        
        Args:
            access_token: Product Hunt API访问令牌
            timeout: 请求超时时间
        """
        super().__init__(timeout)
        self.access_token = access_token
        self.base_url = "https://api.producthunt.com/v2/api/graphql"
        self.request_delay = 1.0  # API请求间隔（秒）
        
    def get_data_source_name(self) -> str:
        """获取数据源名称"""
        return "ProductHunt"
    
    async def fetch_data(
        self, 
        project_name: str = None, 
        keywords: List[str] = None,
        website_url: str = None
    ) -> Dict[str, Any]:
        """
        获取Product Hunt数据
        
        Args:
            project_name: 项目名称
            keywords: 搜索关键词
            website_url: 项目网站URL
            
        Returns:
            Dict[str, Any]: Product Hunt数据
        """
        try:
            if not project_name and not keywords:
                logger.warning("缺少搜索参数")
                return {}
            
            if not self.access_token:
                logger.warning("缺少Product Hunt访问令牌")
                return {}
            
            logger.info(f"开始获取Product Hunt数据: {project_name}")
            
            # 构建搜索查询
            search_queries = self._build_search_queries(project_name, keywords)
            
            # 获取各种数据
            data = {
                "products": await self._fetch_products(search_queries),
                "posts": await self._fetch_posts(search_queries),
                "trending_products": await self._fetch_trending_products(),
                "collections": await self._fetch_collections(search_queries)
            }
            
            # 分析和汇总数据
            data["analysis"] = self._analyze_data(data)
            
            # 缓存数据
            cache_key = f"{project_name}_{','.join(keywords or [])}_{website_url or ''}"
            self.cache_data(cache_key, data)
            
            logger.info(f"Product Hunt数据获取完成: {project_name}")
            return data
            
        except Exception as e:
            logger.error(f"获取Product Hunt数据失败: {str(e)}")
            return {}
    
    def _build_search_queries(
        self, 
        project_name: Optional[str], 
        keywords: Optional[List[str]]
    ) -> List[str]:
        """构建搜索查询"""
        queries = []
        
        if project_name:
            queries.append(project_name)
        
        if keywords:
            queries.extend(keywords[:3])
        
        return queries[:4]  # 限制查询数量
    
    async def _fetch_products(self, search_queries: List[str]) -> List[Dict[str, Any]]:
        """获取相关产品"""
        try:
            all_products = []
            
            for query in search_queries:
                await asyncio.sleep(self.request_delay)
                
                # GraphQL查询
                graphql_query = """
                query SearchProducts($query: String!, $first: Int!) {
                  posts(query: $query, first: $first) {
                    edges {
                      node {
                        id
                        name
                        tagline
                        description
                        url
                        website
                        votesCount
                        commentsCount
                        createdAt
                        featuredAt
                        user {
                          name
                          username
                        }
                        makers {
                          name
                          username
                        }
                        topics {
                          name
                          slug
                        }
                        thumbnail {
                          url
                        }
                        gallery {
                          images {
                            url
                          }
                        }
                      }
                    }
                  }
                }
                """
                
                variables = {
                    "query": query,
                    "first": 10
                }
                
                headers = {
                    "Authorization": f"Bearer {self.access_token}",
                    "Content-Type": "application/json"
                }
                
                payload = {
                    "query": graphql_query,
                    "variables": variables
                }
                
                data = await self._make_request(
                    self.base_url, 
                    method="POST", 
                    headers=headers, 
                    json=payload
                )
                
                if data and "data" in data and "posts" in data["data"]:
                    posts = data["data"]["posts"]["edges"]
                    
                    for edge in posts:
                        node = edge["node"]
                        product = {
                            "id": node.get("id"),
                            "name": node.get("name"),
                            "tagline": node.get("tagline"),
                            "description": node.get("description", "")[:500],
                            "url": node.get("url"),
                            "website": node.get("website"),
                            "votes_count": node.get("votesCount", 0),
                            "comments_count": node.get("commentsCount", 0),
                            "created_at": node.get("createdAt"),
                            "featured_at": node.get("featuredAt"),
                            "user": {
                                "name": node.get("user", {}).get("name"),
                                "username": node.get("user", {}).get("username")
                            },
                            "makers": [
                                {
                                    "name": maker.get("name"),
                                    "username": maker.get("username")
                                }
                                for maker in node.get("makers", [])
                            ],
                            "topics": [
                                {
                                    "name": topic.get("name"),
                                    "slug": topic.get("slug")
                                }
                                for topic in node.get("topics", [])
                            ],
                            "thumbnail_url": node.get("thumbnail", {}).get("url"),
                            "gallery_images": [
                                img.get("url") 
                                for img in node.get("gallery", {}).get("images", [])
                            ]
                        }
                        all_products.append(product)
            
            # 去重和排序
            unique_products = {}
            for product in all_products:
                product_id = product["id"]
                if product_id not in unique_products:
                    unique_products[product_id] = product
            
            # 按投票数排序
            sorted_products = sorted(
                unique_products.values(),
                key=lambda x: (x["votes_count"], x["comments_count"]),
                reverse=True
            )
            
            return sorted_products[:15]
            
        except Exception as e:
            logger.error(f"获取Product Hunt产品失败: {str(e)}")
            return []
    
    async def _fetch_posts(self, search_queries: List[str]) -> List[Dict[str, Any]]:
        """获取相关帖子"""
        try:
            # Product Hunt的帖子通常就是产品发布，这里可以获取更详细的帖子信息
            all_posts = []
            
            for query in search_queries[:2]:  # 限制查询数量
                await asyncio.sleep(self.request_delay)
                
                graphql_query = """
                query SearchPosts($query: String!, $first: Int!) {
                  posts(query: $query, first: $first) {
                    edges {
                      node {
                        id
                        name
                        tagline
                        votesCount
                        commentsCount
                        createdAt
                        featuredAt
                        reviewsCount
                        reviewsRating
                        isCollected
                        user {
                          name
                          username
                          profileImage
                        }
                      }
                    }
                  }
                }
                """
                
                variables = {
                    "query": query,
                    "first": 5
                }
                
                headers = {
                    "Authorization": f"Bearer {self.access_token}",
                    "Content-Type": "application/json"
                }
                
                payload = {
                    "query": graphql_query,
                    "variables": variables
                }
                
                data = await self._make_request(
                    self.base_url, 
                    method="POST", 
                    headers=headers, 
                    json=payload
                )
                
                if data and "data" in data and "posts" in data["data"]:
                    posts = data["data"]["posts"]["edges"]
                    
                    for edge in posts:
                        node = edge["node"]
                        post = {
                            "id": node.get("id"),
                            "name": node.get("name"),
                            "tagline": node.get("tagline"),
                            "votes_count": node.get("votesCount", 0),
                            "comments_count": node.get("commentsCount", 0),
                            "reviews_count": node.get("reviewsCount", 0),
                            "reviews_rating": node.get("reviewsRating", 0),
                            "created_at": node.get("createdAt"),
                            "featured_at": node.get("featuredAt"),
                            "is_collected": node.get("isCollected", False),
                            "user": {
                                "name": node.get("user", {}).get("name"),
                                "username": node.get("user", {}).get("username"),
                                "profile_image": node.get("user", {}).get("profileImage")
                            }
                        }
                        all_posts.append(post)
            
            return all_posts
            
        except Exception as e:
            logger.error(f"获取Product Hunt帖子失败: {str(e)}")
            return []
    
    async def _fetch_trending_products(self) -> List[Dict[str, Any]]:
        """获取热门产品"""
        try:
            await asyncio.sleep(self.request_delay)
            
            graphql_query = """
            query TrendingProducts($first: Int!) {
              posts(first: $first, order: VOTES) {
                edges {
                  node {
                    id
                    name
                    tagline
                    votesCount
                    commentsCount
                    createdAt
                    featuredAt
                    topics {
                      name
                      slug
                    }
                  }
                }
              }
            }
            """
            
            variables = {
                "first": 10
            }
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "query": graphql_query,
                "variables": variables
            }
            
            data = await self._make_request(
                self.base_url, 
                method="POST", 
                headers=headers, 
                json=payload
            )
            
            if data and "data" in data and "posts" in data["data"]:
                posts = data["data"]["posts"]["edges"]
                
                trending_products = []
                for edge in posts:
                    node = edge["node"]
                    product = {
                        "id": node.get("id"),
                        "name": node.get("name"),
                        "tagline": node.get("tagline"),
                        "votes_count": node.get("votesCount", 0),
                        "comments_count": node.get("commentsCount", 0),
                        "created_at": node.get("createdAt"),
                        "featured_at": node.get("featuredAt"),
                        "topics": [
                            topic.get("name") 
                            for topic in node.get("topics", [])
                        ]
                    }
                    trending_products.append(product)
                
                return trending_products
            
            return []
            
        except Exception as e:
            logger.error(f"获取热门产品失败: {str(e)}")
            return []
    
    async def _fetch_collections(self, search_queries: List[str]) -> List[Dict[str, Any]]:
        """获取相关收藏集"""
        try:
            all_collections = []
            
            for query in search_queries[:2]:  # 限制查询数量
                await asyncio.sleep(self.request_delay)
                
                graphql_query = """
                query SearchCollections($query: String!, $first: Int!) {
                  collections(query: $query, first: $first) {
                    edges {
                      node {
                        id
                        name
                        description
                        url
                        postsCount
                        followersCount
                        createdAt
                        user {
                          name
                          username
                        }
                      }
                    }
                  }
                }
                """
                
                variables = {
                    "query": query,
                    "first": 5
                }
                
                headers = {
                    "Authorization": f"Bearer {self.access_token}",
                    "Content-Type": "application/json"
                }
                
                payload = {
                    "query": graphql_query,
                    "variables": variables
                }
                
                data = await self._make_request(
                    self.base_url, 
                    method="POST", 
                    headers=headers, 
                    json=payload
                )
                
                if data and "data" in data and "collections" in data["data"]:
                    collections = data["data"]["collections"]["edges"]
                    
                    for edge in collections:
                        node = edge["node"]
                        collection = {
                            "id": node.get("id"),
                            "name": node.get("name"),
                            "description": node.get("description", "")[:300],
                            "url": node.get("url"),
                            "posts_count": node.get("postsCount", 0),
                            "followers_count": node.get("followersCount", 0),
                            "created_at": node.get("createdAt"),
                            "user": {
                                "name": node.get("user", {}).get("name"),
                                "username": node.get("user", {}).get("username")
                            }
                        }
                        all_collections.append(collection)
            
            return all_collections
            
        except Exception as e:
            logger.error(f"获取收藏集失败: {str(e)}")
            return []
    
    def _analyze_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分析Product Hunt数据"""
        try:
            products = data.get("products", [])
            posts = data.get("posts", [])
            trending_products = data.get("trending_products", [])
            collections = data.get("collections", [])
            
            analysis = {
                "product_presence": self._analyze_product_presence(products),
                "market_reception": self._analyze_market_reception(products, posts),
                "community_interest": self._analyze_community_interest(products, collections),
                "trending_status": self._analyze_trending_status(trending_products),
                "launch_performance": self._analyze_launch_performance(products)
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析Product Hunt数据失败: {str(e)}")
            return {}
    
    def _analyze_product_presence(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析产品存在度"""
        if not products:
            return {"presence_level": "none", "total_products": 0}
        
        total_products = len(products)
        total_votes = sum(product.get("votes_count", 0) for product in products)
        avg_votes = total_votes / total_products if total_products > 0 else 0
        
        # 存在度评级
        if total_products >= 3 and avg_votes >= 100:
            presence_level = "strong"
        elif total_products >= 2 and avg_votes >= 50:
            presence_level = "moderate"
        elif total_products >= 1 and avg_votes >= 10:
            presence_level = "weak"
        else:
            presence_level = "minimal"
        
        return {
            "presence_level": presence_level,
            "total_products": total_products,
            "total_votes": total_votes,
            "average_votes": round(avg_votes, 2)
        }
    
    def _analyze_market_reception(
        self, 
        products: List[Dict[str, Any]], 
        posts: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """分析市场接受度"""
        all_items = products + posts
        
        if not all_items:
            return {"reception_level": "unknown"}
        
        total_votes = sum(item.get("votes_count", 0) for item in all_items)
        total_comments = sum(item.get("comments_count", 0) for item in all_items)
        total_reviews = sum(item.get("reviews_count", 0) for item in all_items if "reviews_count" in item)
        avg_rating = sum(item.get("reviews_rating", 0) for item in all_items if "reviews_rating" in item)
        
        # 接受度评级
        if total_votes >= 500 and total_comments >= 50:
            reception_level = "excellent"
        elif total_votes >= 200 and total_comments >= 20:
            reception_level = "good"
        elif total_votes >= 50 and total_comments >= 5:
            reception_level = "fair"
        elif total_votes >= 10:
            reception_level = "poor"
        else:
            reception_level = "very_poor"
        
        return {
            "reception_level": reception_level,
            "total_votes": total_votes,
            "total_comments": total_comments,
            "total_reviews": total_reviews,
            "average_rating": round(avg_rating / len(all_items), 2) if all_items else 0
        }
    
    def _analyze_community_interest(
        self, 
        products: List[Dict[str, Any]], 
        collections: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """分析社区兴趣度"""
        total_comments = sum(product.get("comments_count", 0) for product in products)
        total_collections = len(collections)
        collection_followers = sum(col.get("followers_count", 0) for col in collections)
        
        # 兴趣度评级
        if total_comments >= 100 and total_collections >= 3:
            interest_level = "very_high"
        elif total_comments >= 50 and total_collections >= 2:
            interest_level = "high"
        elif total_comments >= 20 and total_collections >= 1:
            interest_level = "medium"
        elif total_comments >= 5:
            interest_level = "low"
        else:
            interest_level = "very_low"
        
        return {
            "interest_level": interest_level,
            "total_comments": total_comments,
            "collections_count": total_collections,
            "collection_followers": collection_followers
        }
    
    def _analyze_trending_status(self, trending_products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析趋势状态"""
        if not trending_products:
            return {"trending_level": "none"}
        
        # 检查是否有产品在热门列表中
        trending_count = len(trending_products)
        avg_votes = sum(p.get("votes_count", 0) for p in trending_products) / trending_count
        
        # 趋势评级
        if trending_count >= 3 and avg_votes >= 200:
            trending_level = "viral"
        elif trending_count >= 2 and avg_votes >= 100:
            trending_level = "trending"
        elif trending_count >= 1 and avg_votes >= 50:
            trending_level = "popular"
        else:
            trending_level = "normal"
        
        return {
            "trending_level": trending_level,
            "trending_products_count": trending_count,
            "average_trending_votes": round(avg_votes, 2) if trending_count > 0 else 0
        }
    
    def _analyze_launch_performance(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析发布表现"""
        if not products:
            return {"performance_level": "none"}
        
        featured_products = sum(1 for p in products if p.get("featured_at"))
        total_products = len(products)
        featured_ratio = featured_products / total_products
        
        avg_votes = sum(p.get("votes_count", 0) for p in products) / total_products
        avg_comments = sum(p.get("comments_count", 0) for p in products) / total_products
        
        # 表现评级
        if featured_ratio >= 0.5 and avg_votes >= 100:
            performance_level = "outstanding"
        elif featured_ratio >= 0.3 and avg_votes >= 50:
            performance_level = "good"
        elif featured_ratio >= 0.1 and avg_votes >= 20:
            performance_level = "average"
        elif avg_votes >= 5:
            performance_level = "below_average"
        else:
            performance_level = "poor"
        
        return {
            "performance_level": performance_level,
            "featured_products": featured_products,
            "total_products": total_products,
            "featured_ratio": round(featured_ratio, 2),
            "average_votes": round(avg_votes, 2),
            "average_comments": round(avg_comments, 2)
        }
    
    async def is_available(self) -> bool:
        """检查Product Hunt API是否可用"""
        try:
            if not self.access_token:
                return False
            
            # 简单的GraphQL查询测试
            graphql_query = """
            query TestQuery {
              viewer {
                user {
                  id
                }
              }
            }
            """
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "query": graphql_query
            }
            
            data = await self._make_request(
                self.base_url, 
                method="POST", 
                headers=headers, 
                json=payload
            )
            
            return data is not None and "data" in data
            
        except Exception as e:
            logger.error(f"检查Product Hunt API可用性失败: {str(e)}")
            return False
