#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
架构图生成器模型定义
"""
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timezone
from pydantic import Field, BaseModel

from ...analyzers.project_structure_models import ProjectStructureAnalysis
from ...analyzers.project_dependency_models import DependencyAnalysis
from ...analyzers.project_architecture_models import ArchitectureAnalysis
from ...analyzers.module_models import ModuleAnalysis
from ...analyzers.module_merger_models import ModuleMergerAnalysisResult


class DiagramFormat(str):
    """架构图格式"""
    MERMAID = "mermaid"
    JSON = "json"


class DiagramType(str):
    """图表类型"""
    ARCHITECTURE = "architecture"  # 架构图
    DEPENDENCY = "dependency"  # 依赖图


class DiagramConfig(BaseModel):
    """图表生成配置"""
    project_path: str = Field(..., description="项目路径")
    project_name: str = Field(None, description="项目名称")
    output_dir: str = Field("./diagrams", description="输出目录")
    diagram_format: List[str] = Field([DiagramFormat.MERMAID, DiagramFormat.JSON], description="输出格式")
    diagram_types: List[str] = Field([DiagramType.ARCHITECTURE], description="生成的图类型")
    include_external_dependencies: bool = Field(True, description="是否包含外部依赖")
    detail_level: int = Field(2, description="详细程度 (1-3)，1最简单，3最详细")
    exclude_patterns: List[str] = Field([], description="排除的文件/目录模式")
    focus_components: List[str] = Field([], description="重点关注的组件")
    colorize: bool = Field(True, description="是否使用颜色区分组件")
    group_by_module: bool = Field(True, description="是否按模块分组")
    include_description: bool = Field(True, description="是否包含组件描述")
    max_depth: int = Field(3, description="组件嵌套的最大深度")
    theme: str = Field("default", description="图表主题")
    show_relationships: bool = Field(True, description="是否显示组件间关系")
    show_metrics: bool = Field(True, description="是否显示架构评估指标")
    interactive: bool = Field(False, description="是否生成交互式图表")
    include_code_examples: bool = Field(False, description="是否包含代码示例")


class DiagramState(BaseModel):
    """图表生成状态"""
    # 基本信息
    project_path: str = Field(..., description="项目路径")
    project_name: str = Field(..., description="项目名称")
    start_time: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")

    # 配置
    config: DiagramConfig = Field(None, description="图表生成配置")

    # 分析数据
    structure_analysis: Optional[ProjectStructureAnalysis] = Field(None, description="项目结构分析结果")
    dependency_analysis: Optional[DependencyAnalysis] = Field(None, description="依赖分析结果")
    architecture_analysis: Optional[ArchitectureAnalysis] = Field(None, description="架构分析结果")
    module_analyses: Dict[str, ModuleAnalysis] = Field(default_factory=dict, description="模块分析结果")
    module_merger_analyses: Dict[str, ModuleMergerAnalysisResult] = Field(default_factory=dict, description="模块合并分析结果")

    # 生成结果
    diagrams: Dict[str, Dict[str, Any]] = Field(default_factory=dict, description="生成的图表，格式为 {类型: {格式: 内容}}")

    # 状态跟踪
    current_step: str = Field("", description="当前执行的步骤")
    errors: Dict[str, str] = Field(default_factory=dict, description="错误信息")
