#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/4/27 13:18
# @File    : statistics.py
# @Description: 
"""

from datetime import datetime, timezone
from sqlalchemy import String, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.model_base import ModelBase
from app.models.rbac.associations import user_role
from app.utils.security import get_password_hash


class StatisticsModel(ModelBase):
    """
    暂时不做按ip按区域划分/日期时间段划分/唯一访问用户数划分
    """
    __tablename__ = 'statistic'

    name: Mapped[str] = mapped_column(String(32), comment='统计值')
    count: Mapped[str] = mapped_column(String(4), comment='数量')
