"""
权限服务模块

提供权限管理相关功能
"""
from datetime import datetime, timezone
from typing import Optional, List
import structlog
from sqlalchemy import select, and_, or_, func, delete, true
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import DatabaseError

from app.core.database import DatabaseError
from app.core.di.providers import SessionProvider, AsyncSessionProvider
from app.models.rbac.permission import PermissionModel, PermissionType
from app.models.rbac.permission_group import PermissionGroupModel
from app.models.rbac.role import RoleModel

from app.schemas.rbac.permission import Permission, PermissionListResponse, PermissionCreateRequest, PermissionUpdateRequest
from app.schemas.rbac.permission_group import PermissionGroup
from app.schemas.rbac.role import Role
from app.model_converters.rbac.permission import PermissionConverter

logger = structlog.get_logger(__name__)

class PermissionService:
    """权限服务类"""
    
    def __init__(
        self,
        session: SessionProvider,
        async_session: AsyncSessionProvider
    ):
        """初始化权限服务
        
        Args:
            session: 数据库会话提供者
            async_session: 异步数据库会话提供者
        """
        self.session = session
        self.async_session = async_session
        self.permission_converter = PermissionConverter()
        logger.debug("权限服务初始化完成")

    async def get_by_id(self, permission_id: str) -> Optional[Permission]:
        """根据ID获取权限

        Args:
            permission_id: 权限ID

        Returns:
            权限信息，不存在返回None
        """
        async with self.async_session() as session:
            stmt = (
                select(PermissionModel)
                .options(
                    selectinload(PermissionModel.roles),
                    selectinload(PermissionModel.group)
                )
                .where(PermissionModel.id == permission_id)
            )
            result = await session.execute(stmt)
            permission = result.scalar_one_or_none()
            return self.permission_converter.to_schema(permission) if permission else None

    async def get_by_code(self, code: str, group_id: Optional[str] = None) -> Optional[Permission]:
        """根据权限代码获取权限

        Args:
            code: 权限代码
            group_id: 权限组ID

        Returns:
            权限信息，不存在返回None
        """
        async with self.async_session() as session:
            stmt = (
                select(PermissionModel)
                .options(
                    selectinload(PermissionModel.roles),
                    selectinload(PermissionModel.group)
                )
                .where(
                    and_(
                        PermissionModel.code == code,
                        PermissionModel.group_id == group_id if group_id else true()
                    )
                )
            )
            result = await session.execute(stmt)
            permission = result.scalar_one_or_none()
            return self.permission_converter.to_schema(permission) if permission else None


    async def get_list(
        self,
        *,
        page: int = 1,
        page_size: int = 100,
        is_active: Optional[bool] = None,
        role_id: Optional[str] = None,
        group_id: Optional[str] = None,
        permission_type: Optional[PermissionType] = None,
        search: Optional[str] = None
    ) -> PermissionListResponse:
        """获取权限列表

        Args:
            page: 页码
            page_size: 每页记录数
            is_active: 是否启用
            role_id: 角色ID
            group_id: 权限组ID
            permission_type: 权限类型
            search: 搜索关键词(代码、名称、描述)

        Returns:
            权限列表分页响应
        """
        async with self.async_session() as session:
            stmt = (
                select(PermissionModel)
                .options(
                    selectinload(PermissionModel.roles),
                    selectinload(PermissionModel.group)
                )
            )
            
            # 构建查询条件
            conditions = []
            
            if is_active is not None:
                conditions.append(PermissionModel.is_active == is_active)
                
            if role_id is not None:
                stmt = stmt.join(PermissionModel.roles)
                conditions.append(RoleModel.id == role_id)
                
            if group_id is not None:
                conditions.append(PermissionModel.group_id == group_id)
                
            if permission_type is not None:
                conditions.append(PermissionModel.type == permission_type)
                
            if search:
                search_condition = or_(
                    PermissionModel.code.ilike(f"%{search}%"),
                    PermissionModel.name.ilike(f"%{search}%"),
                    PermissionModel.description.ilike(f"%{search}%")
                )
                conditions.append(search_condition)
                
            if conditions:
                stmt = stmt.where(and_(*conditions))
                
            # 获取总记录数
            count_stmt = select(func.count()).select_from(stmt.subquery())
            total = await session.scalar(count_stmt)
            
            # 计算分页
            skip = (page - 1) * page_size
            
            # 获取分页数据
            stmt = (
                stmt.order_by(PermissionModel.sort_order.asc(), PermissionModel.id.desc())
                .offset(skip)
                .limit(page_size)
            )
            result = await session.execute(stmt)
            permissions = result.scalars().all()
            
            return PermissionListResponse(
                total=total,
                permissions=[self.permission_converter.to_schema(p) for p in permissions],
                page=page,
                page_size=page_size
            )

    async def create(
        self,
        request: PermissionCreateRequest
    ) -> Permission:
        """创建权限

        Args:
            request: 创建权限请求

        Returns:
            权限信息

        Raises:
            ValueError: 参数错误
            DatabaseError: 数据库错误
        """
        async with self.async_session() as session:
            # 检查权限代码是否存在
            stmt = (
                select(PermissionModel)
                .options(
                    selectinload(PermissionModel.group),
                    selectinload(PermissionModel.roles)
                )
                .where(
                    and_(
                        PermissionModel.code == request.code,
                        PermissionModel.group_id == request.group_id
                    )
                )
            )
            result = await session.execute(stmt)
            if result.scalar_one_or_none():
                raise ValueError("权限代码已存在")

            try:
                # 创建权限
                permission = PermissionModel(
                    name=request.name,
                    code=request.code,
                    description=request.description,
                    group_id=request.group_id,
                    type=request.type,
                    sort_order=request.sort_order,
                    is_active=request.is_active
                )
                session.add(permission)
                await session.commit()
                await session.refresh(permission)

                return self.permission_converter.to_schema(permission)
            except Exception as e:
                logger.error(
                    "创建权限失败",
                    request=request,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("创建权限失败") from e

    async def update(
        self,
        request: PermissionUpdateRequest
    ) -> Permission:
        """更新权限

        Args:
            request: 更新权限请求

        Returns:
            权限信息

        Raises:
            ValueError: 参数错误
            DatabaseError: 数据库错误
        """
        async with self.async_session() as session:
            # 获取权限
            stmt = (
                select(PermissionModel)
                .options(
                    selectinload(PermissionModel.group),
                    selectinload(PermissionModel.roles)
                )
                .where(PermissionModel.id == request.permission_id)
            )
            result = await session.execute(stmt)
            permission = result.scalar_one_or_none()
            
            if not permission:
                raise ValueError("权限不存在")

            try:
                # 如果修改了权限代码，检查是否存在
                if request.code and request.code != permission.code:
                    stmt = (
                        select(PermissionModel)
                        .where(
                            and_(
                                PermissionModel.code == request.code,
                                PermissionModel.group_id == permission.group_id,
                                PermissionModel.id != request.permission_id
                            )
                        )
                    )
                    result = await session.execute(stmt)
                    if result.scalar_one_or_none():
                        raise ValueError("权限代码已存在")

                # 更新权限
                if request.name is not None:
                    permission.name = request.name
                if request.code is not None:
                    permission.code = request.code
                if request.description is not None:
                    permission.description = request.description
                if request.type is not None:
                    permission.type = request.type
                if request.sort_order is not None:
                    permission.sort_order = request.sort_order
                if request.is_active is not None:
                    permission.is_active = request.is_active

                await session.commit()
                await session.refresh(permission)

                return self.permission_converter.to_schema(permission)
            except ValueError as e:
                await session.rollback()
                raise e
            except Exception as e:
                logger.error(
                    "更新权限失败",
                    permission_id=request.permission_id,
                    request=request,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("更新权限失败") from e

    async def delete(self, permission_id: str) -> bool:
        """删除权限

        Args:
            permission_id: 权限ID

        Returns:
            是否删除成功

        Raises:
            DatabaseError: 数据库错误
        """
        async with self.async_session() as session:
            try:
                stmt = delete(PermissionModel).where(PermissionModel.id == permission_id)
                result = await session.execute(stmt)
                await session.commit()
                
                deleted = result.rowcount > 0
                if deleted:
                    logger.info("权限删除成功", permission_id=permission_id)
                else:
                    logger.warning("权限不存在", permission_id=permission_id)
                    
                return deleted
                
            except Exception as e:
                logger.error(
                    "权限删除失败",
                    permission_id=permission_id,
                    error=str(e)
                )
                await session.rollback()
                raise DatabaseError("权限删除失败") from e
