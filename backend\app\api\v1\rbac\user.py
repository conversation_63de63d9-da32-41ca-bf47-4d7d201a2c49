"""
用户相关的API处理器
"""
import secrets
from datetime import datetime, timedelta
from http import HTTPStatus
from typing import Optional, List

import structlog
from dependency_injector.wiring import inject, Provide
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.core.config import settings
from app.core.di.containers import Container
from app.services.rbac.phone import PhoneService
from app.services.rbac.user import UserService
from app.services.rbac.email import EmailService
from app.core.di.containers import Container
from app.services.rbac.user import UserService
from app.services.rbac.auth import AuthService
from app.services.rbac.email import EmailService

from app.schemas.rbac.user import (
    UserCreateRequest,
    UserUpdateRequest,
    UserChangePasswordRequest,
    UserResetPasswordRequest,
    User, UserLoginRequest, UpdateSelfPasswordRequest, UpdateOauthEmailRequest, UpdateSelfPasswordWithOutValidateRequest
)
from app.schemas.rbac.password_reset import (
    PasswordResetRequest,
    PasswordResetTokenVerify,
    PasswordResetComplete
)

from app.core.middleware import auth_middleware, require_auth
from app.utils.password_reset_token_manager import PasswordResetTokenManager
from app.utils.security import get_current_user_id

logger = structlog.get_logger(__name__)

class UserHandler(BaseHandler):
    """用户管理处理器"""
    
    @inject
    def initialize(
        self,
        user_service: UserService = Provide[Container.user_service]
    ):
        """初始化处理器
        
        Args:
            user_service: 用户服务
        """
        super().initialize()
        self.user_service: UserService = user_service
    
    @require_auth(required=True, permissions=["system:user:list"])
    async def get(self):
        """获取用户列表"""
        try:
            # 获取查询参数
            page = int(self.get_argument("page", "1"))
            page_size = int(self.get_argument("page_size", "10"))
            # search = self.get_argument("search", None)
            name = self.get_argument("name", None)
            phone = self.get_argument("phone", None)
            status = self.get_argument("status", None)
            created_at_begin = self.get_argument("created_at_begin", None)
            created_at_end = self.get_argument("created_at_end", None)

            # 获取用户列表
            result = await self.user_service.get_list(
                page=page,
                page_size=page_size,
                name=name,
                phone=phone,
                status=status,
                created_at_begin=created_at_begin,
                created_at_end=created_at_end
            )

            def serialize_users_with_roles(users_result):
                return {
                    "total": users_result.total,
                    "page": users_result.page,
                    "page_size": users_result.page_size,
                    "total_pages": users_result.total_pages,
                    "users": [
                        {
                            **user.model_dump(exclude={"roles"}),
                            "roles": [role.model_dump() for role in user.roles]
                        }
                        for user in users_result.users
                    ]
                }

            self.success_response(serialize_users_with_roles(result))
            #self.success_response(result.model_dump())
            
        except ValueError as e:
            logger.warning("获取用户列表参数无效", error=str(e))
            raise HTTPError(400, "无效的请求参数")
            
        except HTTPError:
            raise
            
        except Exception as e:
            logger.error("获取用户列表时发生错误", error=str(e))
            raise HTTPError(500, "服务器内部错误")

    # 权限相同system:user:delete
    @require_auth(required=True, permissions=["system:user:delete"])
    async def delete(self) -> None:
        """批量删除用户"""
        try:
            # 验证请求数据
            if not self.json_body or "user_ids" not in self.json_body:
                self.write_error(500, error_message="请求必须包含 user_ids 字段")
            user_ids = self.json_body.get("user_ids", [])
            if not isinstance(user_ids, list) or not user_ids:
                self.write_error(500, error_message="user_ids 必须是非空数组")

            # 调用服务批量删除用户
            result = await self.user_service.batch_delete(user_ids)

            self.success_response({
                "total": len(user_ids),
                "deleted": result.get("deleted", 0),
                "failed": result.get("failed", 0),
                "errors": result.get("errors", {})
            })
        except ValueError as e:
            logger.error("批量删除用户参数错误", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("批量删除用户失败", error=str(e))
            self.write_error(500, error_message="批量删除用户失败 " + str(e))

    @require_auth(required=True, permissions=["system:user:add"])
    async def post(self) -> None:
        """创建新用户（管理员操作）"""
        try:
            # 验证请求数据
            data = UserCreateRequest(**self.json_body)

            # 调用用户服务创建用户
            user = await self.user_service.create(data)

            # 返回创建的用户信息
            self.success_response(user.model_dump())

        except ValueError as e:
            logger.error("创建用户参数错误", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("创建用户失败", error=str(e))
            self.write_error(500, error_message="创建用户失败 " + str(e))


class PasswordResetRequestHandler(BaseHandler):
    """密码重置请求处理器"""

    @inject
    def initialize(
            self,
            user_service: UserService = Provide[Container.user_service],
            email_service: EmailService = Provide[Container.email_service]
    ):
        """初始化处理器

        Args:
            user_service: 用户服务
            email_service: 邮件服务
        """
        super().initialize()
        self.user_service = user_service
        self.email_service = email_service

    async def post(self) -> None:
        """处理密码重置请求"""
        try:
            # 验证请求数据
            data = PasswordResetRequest(**self.json_body)

            # 检查用户是否存在
            user = await self.user_service.get_by_email(data.email)
            if not user:
                # 即使用户不存在，也返回成功响应，避免泄露用户信息
                logger.info("请求重置密码的邮箱不存在", email=data.email)
                self.success_response(message="如果该邮箱存在，我们已发送重置链接")
                return

            # 生成密码重置令牌
            token = PasswordResetTokenManager.create_token(data.email)

            # 构建重置链接
            reset_url = f"{data.reset_url}?token={token}"
            logger.info("发送密码重置邮件:: " + str(data.email) + " " +str(token) )
            # 发送重置邮件
            success = self.email_service.send_password_reset_email(
                email=data.email,
                reset_url=reset_url
            )

            if not success:
                logger.error("发送密码重置邮件失败", email=data.email)
                raise HTTPError(500, "发送密码重置邮件失败")

            # 返回成功响应
            self.success_response(message="如果该邮箱存在，我们已发送重置链接")

        except ValueError as e:
            logger.warning("密码重置请求数据无效", error=str(e))
            self.write_error(400, error_message=str(e))
        except Exception as e:
            logger.error("处理密码重置请求时发生错误", error=str(e))
            self.write_error(500, error_message="处理密码重置请求时发生错误")


class PasswordResetVerifyHandler(BaseHandler):
    """密码重置令牌验证处理器"""

    async def post(self) -> None:
        """验证密码重置令牌"""
        try:
            # 验证请求数据
            data = PasswordResetTokenVerify(**self.json_body)

            # 验证令牌
            email = PasswordResetTokenManager.verify_token(data.token)

            if email:
                self.success_response(message="令牌有效", data={"email": email})
            else:
                self.write_error(400, error_message="无效或已过期的令牌")

        except ValueError as e:
            logger.warning("密码重置验证请求数据无效", error=str(e))
            self.write_error(400, error_message=str(e))
        except Exception as e:
            logger.error("验证密码重置令牌时发生错误", error=str(e))
            self.write_error(500, error_message="验证密码重置令牌时发生错误")


class PasswordResetCompleteHandler(BaseHandler):
    """完成密码重置处理器"""

    @inject
    def initialize(
            self,
            user_service: UserService = Provide[Container.user_service]
    ):
        """初始化处理器

        Args:
            user_service: 用户服务
        """
        super().initialize()
        self.user_service = user_service

    async def post(self) -> None:
        """完成密码重置"""
        try:
            # 验证请求数据
            data = PasswordResetComplete(**self.json_body)

            # 验证令牌
            email = PasswordResetTokenManager.verify_token(data.token)

            if not email:
                self.write_error(400, error_message="无效或已过期的令牌")
                return

            # 更新用户密码
            success = await self.user_service.repick_password(
                email=email,
                new_password=data.new_password
            )

            if success:
                # 使令牌失效，确保它不能被再次使用
                PasswordResetTokenManager.invalidate_token(data.token)
                self.success_response(message="密码重置成功")
            else:
                self.write_error(500, error_message="重置密码失败，用户可能不存在")

        except ValueError as e:
            logger.warning("密码重置请求数据无效", error=str(e))
            self.write_error(500, error_message="密码重置请求数据无效" + str(e))
        except Exception as e:
            logger.error("完成密码重置时发生错误", error=str(e))
            self.write_error(500, error_message="完成密码重置时发生错误")




class RegisterHandler(BaseHandler):
    """用户注册处理器"""
    
    @inject
    def initialize(
        self,
        user_service: UserService = Provide[Container.user_service],
        email_service: EmailService = Provide[Container.email_service],
        auth_service: AuthService = Provide[Container.auth_service],
        phone_service: PhoneService = Provide[Container.phone_service]
    ):
        self.user_service = user_service
        self.email_service = email_service
        self.auth_service = auth_service
        self.phone_service = phone_service


    async def post(self) -> None:
        """用户注册"""
        try:
            # 获取请求数据
            request_data = self.json_body
            logger.info("接收到的注册请求数据", data=request_data)

            # 记录验证开始
            email = request_data.get('email')
            phone = request_data.get('phone')
            verification_code = request_data.get('verification_code')
            
            logger.info(
                "解析的注册数据",
                email=email,
                phone=phone,
                verification_code=verification_code
            )
            
            if not email and not phone:
                self.write_error(500, error_message="邮箱和手机号至少需要提供一个")
                return
            if email and phone:
                self.write_error(500, error_message="邮箱和手机号不能同时填写")
                return

            # 创建用户
            logger.info("验证前的请求数据", data=request_data)
            user_data = UserCreateRequest.model_validate(request_data)
            logger.info("验证后的用户数据", data=user_data.model_dump())
            logger.info(
                "开始验证码验证",
                extra={
                    "email": email,
                    "phone": phone,
                    "verification_code": verification_code,
                }
            )

            # 根据提供的联系方式验证验证码
            if email:
                if not self.email_service.verify_code(email, verification_code):
                    logger.warning("邮箱验证码验证失败", email=email)
                    self.write_error(500, error_message="邮箱验证码错误或已过期")
                    return
            elif phone:
                if not self.phone_service.verify_code(phone, verification_code):
                    logger.warning("手机验证码验证失败", phone=phone)
                    self.write_error(500, error_message="手机验证码错误或已过期")
                    return

            user_info = await self.user_service.create(user_data)

            if user_info:
                # 注册成功后直接登录
                # 邮箱或者手机注册
                login_username = user_data.email if user_data.email else user_data.phone
                login_request = UserLoginRequest(
                    username=login_username,
                    password=user_data.password
                )

                user, token = await self.auth_service.login(
                    login_request
                )

                # 返回用户信息和token
                response_data = {
                    "user": user.model_dump(),
                    "token": token.model_dump()
                }
                self.success_response(data=response_data)
            else:
                self.success_response("注册失败")

        except ValueError as e:
            error_msg = "注册请求数据无效"
            logger.warning(error_msg, error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("处理注册请求时发生错误", error=str(e))
            self.write_error(500, error_message="处理注册请求时发生错误" + str(e))


class UpdateSelfPasswordVerificationCodeHandler(BaseHandler):
    """修改密码 更新验证码"""

    @inject
    def initialize(
            self,
            email_service: EmailService = Provide[Container.email_service],
            user_service: UserService = Provide[Container.user_service],
    ):
        self.email_service = email_service
        self.user_service = user_service

    async def post(self) -> None:
        """发送验证码"""
        try:
            email = self.get_argument("email")
            # ans = await self.user_service.check_email_exists(email)
            # if ans:
                # self.write_error(500, error_message="该邮箱已注册过无法发送验证码")
            if not self.email_service.send_self_update_password_verification_code(email):
                self.write_error(500, error_message="发送验证码失败")
            self.success_response()
        except Exception as e:
            logger.error("发送验证码失败", error=str(e))
            self.write_error(500, error_message="发送验证码失败")

    async def get(self) -> None:
        """处理GET请求"""
        self.write_error(405, error_message="请使用POST方法请求验证码")


class UpdateOauthEmailVerificationCodeHandler(BaseHandler):
    """绑定邮箱/手机 更新验证码"""

    @inject
    def initialize(
            self,
            email_service: EmailService = Provide[Container.email_service],
            phone_service: PhoneService = Provide[Container.phone_service],
            user_service: UserService = Provide[Container.user_service],
    ):
        self.email_service = email_service
        self.user_service = user_service
        self.phone_service = phone_service


    async def post(self) -> None:
        """发送验证码"""
        try:
            email = self.get_argument("email", None)
            phone = self.get_argument("phone", None)

            if not email and not phone:
                self.write_error(400, error_message="请提供邮箱或手机号")
                return

            success = True
            error_messages = []

            # 处理邮箱验证码
            if email:
                # 检查邮箱是否已注册
                email_exists = await self.user_service.check_email_exists(email)
                if email_exists:
                    error_messages.append("该邮箱已注册过无法发送验证码")
                else:
                    # 发送邮箱验证码
                    if not self.email_service.send_self_oauth_email_verification_code(email):
                        error_messages.append("邮箱验证码发送失败")
                        success = False
                    else:
                        logger.info("邮箱验证码发送成功", email=email)

            # 处理手机验证码
            if phone:
                # 检查手机号是否已注册
                phone_exists = await self.user_service.check_phone_exists(phone)
                if phone_exists:
                    error_messages.append("该手机号已注册过无法发送验证码绑定")
                    success = False
                else:
                    if not self.phone_service.send_self_oauth_phone_verification_code(phone):
                        error_messages.append("手机验证码发送失败")
                        success = False

            if not success:
                self.write_error(500, error_message="; ".join(error_messages))
                return


        except Exception as e:
            logger.error("发送验证码失败", error=str(e))
            self.write_error(500, error_message="发送验证码失败")

    async def get(self) -> None:
        """处理GET请求"""
        self.write_error(405, error_message="请使用POST方法请求验证码")


class VerificationCodeHandler(BaseHandler):
    """验证码处理器"""
    
    @inject
    def initialize(
        self,
        email_service: EmailService = Provide[Container.email_service],
        user_service: UserService = Provide[Container.user_service],
        phone_service: PhoneService = Provide[Container.phone_service],
    ):
        self.email_service = email_service
        self.user_service = user_service
        self.phone_service = phone_service

    async def post(self) -> None:
        """发送验证码"""
        try:
            email = self.get_argument("email", None)
            phone = self.get_argument("phone", None)

            if not email and not phone:
                self.write_error(400, error_message="请提供邮箱或手机号")
                return

            success = True
            error_messages = []

            # 处理邮箱验证码
            if email:
                # 检查邮箱是否已注册
                email_exists = await self.user_service.check_email_exists(email)
                if email_exists:
                    error_messages.append("该邮箱已注册过无法发送验证码")
                else:
                    # 发送邮箱验证码
                    if not self.email_service.send_verification_code(email):
                        error_messages.append("邮箱验证码发送失败")
                        success = False
                    else:
                        logger.info("邮箱验证码发送成功", email=email)

            # 处理手机验证码
            if phone:
                # 检查手机号是否已注册
                phone_exists = await self.user_service.check_phone_exists(phone)
                if phone_exists:
                    error_messages.append("该手机号已注册过无法发送验证码")
                    success = False
                else:
                    if not self.phone_service.send_verification_code(phone):
                        error_messages.append("手机验证码发送失败")
                        success = False

            if not success:
                self.write_error(500, error_message="; ".join(error_messages))
                return

            self.success_response()

        except Exception as e:
            logger.error("发送验证码失败", error=str(e))
            self.write_error(500, error_message="发送验证码失败")


    async def get(self) -> None:
        """处理GET请求"""
        self.write_error(405, error_message="请使用POST方法请求验证码")


class VerificationCodeHandler(BaseHandler):
    """验证码处理器"""

    @inject
    def initialize(
            self,
            email_service: EmailService = Provide[Container.email_service],
            user_service: UserService = Provide[Container.user_service],
            phone_service: PhoneService = Provide[Container.phone_service],
    ):
        self.email_service = email_service
        self.user_service = user_service
        self.phone_service = phone_service

    async def post(self) -> None:
        """发送验证码"""
        try:
            email = self.get_argument("email", None)
            phone = self.get_argument("phone", None)

            if not email and not phone:
                self.write_error(400, error_message="请提供邮箱或手机号")
                return

            success = True
            error_messages = []

            # 处理邮箱验证码
            if email:
                # 检查邮箱是否已注册
                email_exists = await self.user_service.check_email_exists(email)
                if email_exists:
                    error_messages.append("该邮箱已注册过无法发送验证码")
                else:
                    # 发送邮箱验证码
                    if not self.email_service.send_verification_code(email):
                        error_messages.append("邮箱验证码发送失败")
                        success = False
                    else:
                        logger.info("邮箱验证码发送成功", email=email)

            # 处理手机验证码
            if phone:
                # 检查手机号是否已注册
                phone_exists = await self.user_service.check_phone_exists(phone)
                if phone_exists:
                    error_messages.append("该手机号已注册过无法发送验证码")
                    success = False
                else:
                    if not self.phone_service.send_verification_code(phone):
                        error_messages.append("手机验证码发送失败")
                        success = False

            if not success:
                self.write_error(500, error_message="; ".join(error_messages))
                return

            self.success_response()

        except Exception as e:
            logger.error("发送验证码失败", error=str(e))
            self.write_error(500, error_message="发送验证码失败")

    async def get(self) -> None:
        """处理GET请求"""
        self.write_error(405, error_message="请使用POST方法请求验证码")



class UserDetailHandler(BaseHandler):
    """用户详情处理器"""
    
    @inject
    def initialize(
        self,
        user_service: UserService = Provide[Container.user_service]
    ):
        """初始化处理器
        
        Args:
            user_service: 用户服务
        """
        super().initialize()
        self.user_service = user_service
    
    @require_auth(required=True, permissions=["system:user:list"])
    async def get(self, user_id: str):
        """获取用户详情
        
        Args:
            user_id: 用户ID
        """
        try:
            # 获取用户信息
            # user_info = await self.user_service.get_by_id(user_id)
            user_info = await self.user_service.get_user_with_roles(user_id)
            if not user_info:
                self.write_error(500, "用户不存在")
            self.success_response(user_info)

        except Exception as e:
            logger.error("获取用户详情时发生错误", error=str(e))
            self.write_error(500, "服务器内部错误" + str(e))

    @require_auth(required=True, permissions=["system:user:edit"])
    async def put(self, user_id: str):
        """更新用户信息
        
        Args:
            user_id: 用户ID
        """
        try:
            # 验证请求数据
            data = UserUpdateRequest.model_validate(self.json_body)
            # 更新用户信息
            user_info = await self.user_service.update(
                data
            )
            
            # if error_msg:
            #     self.write_error(500, str(error_msg))
            self.success_response(user_info.model_dump())
        except ValueError as e:
            logger.warning("更新用户信息请求数据无效", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("更新用户信息时发生错误", error=str(e))
            self.write_error(500, error_message="更新用户信息时发生错误 "+str(e))
    
    @require_auth(required=True, permissions=["system:user:delete"])
    async def delete(self, user_id: str):
        """删除用户
        
        Args:
            user_id: 用户ID
        """
        try:
            # 删除用户
            ans = await self.user_service.delete(user_id)
            self.success_response(ans)
        except Exception as e:
            logger.error("删除用户时发生错误", error=str(e))
            self.write_error(500, error_message="删除用户时发生错误 "+str(e))

#@auth_middleware(required=True, permissions=["user:change_password"])
class ChangePasswordHandler(BaseHandler):
    """修改密码处理器"""
    
    @inject
    def initialize(
        self,
        user_service: UserService = Provide[Container.user_service]
    ):
        """初始化处理器
        
        Args:
            user_service: 用户服务
        """
        super().initialize()
        self.user_service = user_service
    
    async def post(self):
        """处理修改密码请求"""
        try:
            # 验证请求数据
            data = UserChangePasswordRequest.model_validate(self.json_body)
            # 修改密码
            error_msg = await self.user_service.change_password(
                data
            )
            if error_msg:
                self.write_error(500, str(error_msg))
            self.success_response()
            
        except ValueError as e:
            logger.warning("修改密码请求数据无效", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("修改密码时发生错误", error=str(e))
            self.write_error(500, error_message="修改密码时发生错误 "+str(e))


class UpdateOauthEmailHandler(BaseHandler):
    """绑定邮箱处理器（用户操作）"""

    @inject
    def initialize(
            self,
            user_service: UserService = Provide[Container.user_service],
            email_service: EmailService = Provide[Container.email_service],
            auth_service: AuthService = Provide[Container.auth_service],
            phone_service: PhoneService = Provide[Container.phone_service],
    ):
        """初始化处理器

        Args:
            user_service: 用户服务
        """
        super().initialize()
        self.user_service = user_service
        self.email_service = email_service
        self.auth_service = auth_service
        self.phone_service = phone_service

    async def post(self):
        """处理用户自己更新密码的请求"""
        try:
            # 验证请求数据
            data = self.json_body
            email = data.get('email')
            phone = data.get('phone')
            verification_code = data.get('validate_code')

            # 验证请求数据
            if not email and not phone:
                self.write_error(400, error_message="请提供邮箱或手机号")
                return

            if email and phone:
                self.write_error(400, error_message="不能同时绑定邮箱和手机号")
                return

            # 记录验证开始
            logger.info(
                "开始验证码验证",
                extra={
                    "email": email,
                    "phone": phone,
                    "verification_code": verification_code,
                }
            )
            # 验证 验证码
            verification_success = False
            if email:
                verification_success = self.email_service.verify_oauth_mail_codes(email, verification_code)
            elif phone:
                verification_success = self.phone_service.verify_change_oauth_phone_code(phone, verification_code)

            if not verification_success:
                self.write_error(500, error_message="验证码错误或已过期")
                return

            # 更新邮箱
            from app.schemas.rbac.user import UserUpdateRequest
            data = UserUpdateRequest.model_validate(self.json_body)
            is_bild = False
            # 处理结尾为unauth的临时未绑定账号
            solve_unauth_email = True
            user = await self.user_service.update(data, is_bild=is_bild, solve_unauth_email = True)

            access_token = self.auth_service.create_access_token(
                data={"sub": user.id, "is_superuser": user.is_superuser}
            )
            refresh_token = self.auth_service.create_refresh_token(
                data={"sub": user.id}
            )

            # 创建令牌
            token = await self.user_service.create_token(user_id=user.id, access_token=access_token,
                                                         refresh_token=refresh_token,
                                                         expires_at=datetime.now() + timedelta(
                                                             minutes=settings.security.JWT_ACCESS_TOKEN_EXPIRE_MINUTES))
            response_data = {
                "access_token": token.access_token,
                "refresh_token": token.refresh_token,
                "token_type": "bearer",
                "user": {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                }
            }

            # 直接登陆
            self.success_response(response_data)

        except ValueError as e:
            logger.warning("修改密码请求数据无效", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("修改密码时发生错误", error=str(e))
            self.write_error(500, error_message="绑定邮箱时发生错误 " + str(e))



class UpdateSelfPasswordHandler(BaseHandler):
    """用户修改密码处理器（用户操作）"""

    @inject
    def initialize(
            self,
            user_service: UserService = Provide[Container.user_service],
            email_service: EmailService = Provide[Container.email_service],
            phone_service: PhoneService = Provide[Container.phone_service]
    ):
        super().initialize()
        self.user_service = user_service
        self.email_service = email_service
        self.phone_service = phone_service

    async def post(self):
        """处理用户自己更新密码的请求"""
        try:

            # 验证请求数据
            # data = self.json_body
            # email = data.get('email')
            # phone = data.get('phone')
            # verification_code = data.get('validate_code')
            #
            # # 验证请求数据
            # if not email and not phone:
            #     self.write_error(400, error_message="请提供邮箱或手机号")
            #     return
            #
            # if email and phone:
            #     self.write_error(400, error_message="不能同时验证邮箱和手机号")
            #     return
            #
            # # 记录验证开始
            # logger.info(
            #     "开始验证码验证",
            #     extra={
            #         "email": email,
            #         "phone": phone,
            #         "verification_code": verification_code,
            #     }
            # )
            # # 验证 验证码
            # verification_success = False
            # if email:
            #     verification_success = self.email_service.verify_change_password_codes(email, verification_code)
            # elif phone:
            #     verification_success = self.phone_service.verify_password_change_code(phone, verification_code)
            #
            # if not verification_success:
            #     self.write_error(500, error_message="验证码错误或已过期")
            #     return
            #
            # # 验证请求数据
            # self_password_request = UpdateSelfPasswordRequest.model_validate(self.json_body)
            #
            #
            # # 修改密码
            # error_msg = await self.user_service.change_password(
            #     self_password_request
            # )

            # 6.9修改密码不再需要验证码
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)
            self.json_body['user_id'] = user_id
            self_password_request = UpdateSelfPasswordWithOutValidateRequest.model_validate(self.json_body)

            # 修改密码
            error_msg = await self.user_service.change_password(
                self_password_request
            )

            if error_msg:
                self.write_error(500, str(error_msg))
            self.success_response()

        except ValueError as e:
            logger.warning("修改密码请求数据无效", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("修改密码时发生错误", error=str(e))
            self.write_error(500, error_message="修改密码时发生错误 " + str(e))


# @auth_middleware(required=True, permissions=["user:reset_password"])
class ResetPasswordHandler(BaseHandler):
    """重置密码处理器（管理员操作）"""
    
    @inject
    def initialize(
        self,
        user_service: UserService = Provide[Container.user_service]
    ):
        """初始化处理器
        
        Args:
            user_service: 用户服务
        """
        super().initialize()
        self.user_service = user_service
    
    async def post(self, user_id: str):
        """处理重置密码请求
        
        Args:
            user_id: 用户ID
        """
        try:
            # 验证请求数据
            data = UserResetPasswordRequest.model_validate(self.json_body)
            data.user_id = user_id
            
            # 重置密码
            ret = await self.user_service.reset_password(
                data
            )
            
            # if error_msg:
            #     self.write_error(500, str(error_msg))
            self.success_response()
            
        except ValueError as e:
            logger.warning("重置密码请求数据无效", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("重置密码时发生错误", error=str(e))
            self.write_error(500, error_message="重置密码时发生错误 "+str(e))


@auth_middleware(required=True, permissions=["user:change_status"])
class UserStatusHandler(BaseHandler):
    """用户状态处理器（管理员操作）"""
    
    @inject
    def initialize(
        self,
        user_service: UserService = Provide[Container.user_service]
    ):
        """初始化处理器
        
        Args:
            user_service: 用户服务
        """
        super().initialize()
        self.user_service = user_service
    
    async def post(self, user_id: str, action: str):
        """处理用户状态变更请求
        
        Args:
            user_id: 用户ID
            action: 操作类型（activate/deactivate）
        """
        try:
            # 执行状态变更
            if action == "activate":
                await self.user_service.activate(user_id)
                message = "用户已激活"
            elif action == "deactivate":
                await self.user_service.deactivate(user_id)
                message = "用户已停用"
            else:
                self.write_error(500, error_message=str("无效的操作类型"))
            self.success_response()
        except Exception as e:
            logger.error("处理用户状态变更时发生错误", error=str(e))
            self.write_error(500, error_message="处理用户状态变更时发生错误 "+str(e))

@auth_middleware(required=True, permissions=["user:change_permissions"])
class UserPermissionsHandler(BaseHandler):
    """用户权限处理器"""
    
    @inject
    def initialize(
        self,
        auth_service: AuthService = Provide[Container.auth_service]
    ):
        """初始化处理器
        
        Args:
            auth_service: 认证服务
        """
        super().initialize()
        self.auth_service = auth_service
    
    async def get(self, user_id: Optional[str] = None):
        """获取用户权限列表
        
        Args:
            user_id: 用户ID，如果不提供则获取当前用户权限
        """
        try:
            # 如果未提供用户ID，则使用当前用户ID
            target_id = user_id or self.current_user.id
            
            # 如果请求其他用户权限，需要管理员权限
            if target_id != self.current_user.id and not self.current_user.is_superuser:
                self.write_error(500, str("需要管理员权限"))
            # 获取用户权限
            permissions = await self.auth_service.get_user_permissions(target_id)
            self.success_response({"permissions": permissions})
        except Exception as e:
            logger.error("获取用户权限时发生错误", error=str(e))
            self.write_error(500, error_message="获取用户权限时发生错误 "+str(e))

