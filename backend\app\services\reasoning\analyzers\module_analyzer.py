#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块级代码分析器
"""
import logging
import os
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone
from pathlib import Path

from langchain.chains import <PERSON><PERSON>hain
from langchain.prompts import PromptTemplate
from langchain.output_parsers.fix import OutputFixingParser
from langchain_openai import ChatOpenAI
from ....utils.retry import retry_async
from ....utils.file_utils import read_file_content

from ...ai_agent_core import AgentInput, AgentOutput, BaseAgent, AgentManager, AnalysisOutputParser
from .common_models import AttentionPointInfo
from .module_models import (
    ModuleAnalysis
)

logger = logging.getLogger(__name__)


@AgentManager.register("ModuleAnalyzerAgent")
class ModuleAnalyzer(BaseAgent):
    """
    模块级代码分析器
    分析模块级别的代码，识别公共API、类定义、导入依赖、核心组件
    """

    def __init__(self):
        """初始化模块级分析器"""
        super().__init__()
        self.name = "ModuleAnalyzer"
        self.chain = None

    async def _initialize(self) -> None:
        """初始化分析器特定资源"""
        logger.info("初始化模块级代码分析器特定资源")
        # 在这里可以添加特定于模块分析器的初始化逻辑

    async def _create_chain(self) -> None:
        """初始化处理链"""
        try:

            # 模块分析提示模板 - 优化版本
            module_analysis_template = """
            # 模块代码分析 - 深度内容分析

            作为专业的模块代码分析专家，请基于实际文件内容对模块进行深度分析。

            ## 模块信息
            模块名称: {{ module_name }}
            模块路径: {{ module_path }}
            编程语言: {{ language }}

            ## 模块内容
            ```
            {{ module_content }}
            ```

            ## 深度分析框架

            请严格按照以下框架进行深度分析，所有分析必须基于实际文件内容：

            ### 1. 基本信息识别
            基于文件内容准确识别模块的核心属性：
            - module_name: 模块名称（基于文件名和内容）
            - module_path: 模块路径（完整相对路径）
            - purpose: 主要功能目的（基于类、函数、注释分析）
            - language: 编程语言（基于文件扩展名和语法特征）
            - importance: 重要性评分（1.0-5.0，基于代码复杂度、依赖关系、功能范围）

            ### 2. 架构上下文分析
            基于实际代码结构识别模块在项目中的定位：
            - role: 项目角色识别
              * controller: 控制器层，处理请求路由和业务调用
              * service: 服务层，核心业务逻辑实现
              * model: 数据模型层，数据结构和持久化
              * utility: 工具模块，提供辅助功能
              * middleware: 中间件，处理横切关注点
              * config: 配置模块，管理配置信息
              * client: 客户端模块，外部服务调用
              * handler: 处理器模块，特定业务处理
            - layer: 架构层级（基于导入关系和调用模式）
            - responsibilities: 具体职责列表（基于函数和类的功能分析）

            ### 3. 核心功能特性分析 (features)
            基于实际代码内容识别功能特性：
            - name: 功能名称（基于函数、类、方法名称）
            - description: 详细功能描述（基于代码逻辑和注释）
            - use_cases: 使用场景列表（基于调用模式分析）
            - importance: 功能重要性（1.0-5.0，基于调用频率和业务价值）

            ### 4. 依赖关系分析 (dependency_graph)
            基于导入语句和代码调用进行精确依赖分析：
            
            **节点分析 (nodes)**：
            - name: 模块名称
            - module_path: 模块文件路径
            - importance: 节点重要性（1.0-5.0）
            - role: 模块角色
            - dependency_scope: 依赖范围
              * internal: 内部节点（项目内部模块）
              * external: 外部节点（第三方库、包或外部服务）

            **边分析 (edges)**：
            - source: 源模块
            - target: 目标模块（依赖目标）
            - relation_type: 依赖类型
              * import: 标准导入依赖
              * from_import: from...import依赖
              * inheritance: 继承依赖
              * composition: 组合依赖
              * use: 使用依赖
              * call: 调用依赖
            - dependency_scope: 依赖范围
              * internal: 内部依赖（项目内部模块间的依赖）
              * external: 外部依赖（第三方库、包或外部服务）
            - strength: 关系强度（0.0-5.0，基于使用频率和重要性）
            - description: 依赖关系描述
            - usage_locations: 使用位置（行号或函数名）
            - is_circular: 是否可能形成循环依赖
            - is_direct: 是否直接依赖
            
            **循环依赖检测 (circular_paths)**：
            - 潜在循环依赖路径（模块名称列表的列表）

            ### 5. 代码质量分析 (code_quality)
            基于实际代码内容进行质量评估：

            **复杂度指标 (complexity_metrics)**：
            - cyclomatic_complexity: 圈复杂度（基于条件分支数量）
            - cognitive_complexity: 认知复杂度（基于代码理解难度）
            - nesting_depth: 最大嵌套深度（基于代码层级结构）

            **代码异味 (code_smells)**：
            - smell_type: 异味类型
              * long_method: 过长方法
              * large_class: 过大类
              * feature_envy: 特性依恋
              * data_clumps: 数据泥团
              * duplicate_code: 重复代码
              * dead_code: 死代码
              * god_class: 上帝类
              * other: 其他
            - location: 位置信息（行号或函数名）
            - severity: 严重程度（low/medium/high/critical）
            - description: 详细描述

            **技术债务 (technical_debts)**：
            - debt_type: 债务类型（design/code/test/documentation/architecture/performance/other）
            - title: 债务标题
            - description: 详细描述
            - impact: 影响程度（low/medium/high/critical）
            - priority: 优先级（1-5，5为最高）

            **总体质量评分 (overall_score)**：
            - 基于复杂度、异味、债务综合评估的质量评分（1.0-10.0）

            ### 6. API设计分析 (api_design)
            如果模块包含API相关代码，进行API设计分析：

            **基本信息**：
            - is_api_module: 是否为API模块（基于路由、装饰器、HTTP相关代码判断）
            - api_type: API类型（rest/graphql/grpc/websocket/other/none）

            **端点信息 (endpoints)**：
            - path: API路径（基于路由定义）
            - method: HTTP方法（GET/POST/PUT/DELETE/PATCH/OPTIONS/HEAD）
            - function_name: 对应的函数名
            - description: 端点功能描述
            - parameters: 参数列表（基于函数参数分析）
            - response_type: 响应类型（基于返回值分析）
            - authentication_required: 是否需要认证（基于装饰器或中间件）
            - deprecated: 是否已废弃（基于注释或装饰器）

            **质量评估 (quality)**：
            - overall_score: 总体质量评分（1.0-10.0）
            - issues: 发现的问题列表

            ### 7. 关键发现识别 (findings)
            基于代码质量和架构模式进行问题识别：
            - title: 发现标题
            - description: 详细描述
            - severity: 严重程度
              * info: 信息性发现
              * warning: 需要注意的问题
              * critical: 严重问题需要修复

            ## 分析要求与原则

            ### 核心分析原则
            1. **基于实际内容**：所有分析必须基于提供的实际文件内容
            2. **禁止内容推测**：严禁推测未在代码中明确体现的功能或依赖
            3. **精确依赖识别**：依赖关系必须基于实际的import语句和代码调用
            4. **代码质量评估**：基于实际代码模式识别潜在问题
            5. **功能准确描述**：功能描述必须与实际代码逻辑一致
            6. **避免过度解读**：不要添加代码中不存在的功能或特性
            7. **路径准确性**：所有路径都是相对路径，保持与输入一致
            8. **质量分析客观**：代码质量评估必须基于可观察的代码特征
            9. **API识别准确**：仅在存在明确API相关代码时标记为API模块

            ### 输出语言要求
            所有分析结果使用中文(简体中文)输出，技术名称和代码保持原文。

            ### 输出质量要求
            1. **内容驱动分析**：分析结果必须能从实际代码内容中找到依据
            2. **依赖关系精确**：准确识别所有导入和调用关系
            3. **功能描述准确**：功能描述与实际代码逻辑完全匹配
            4. **问题识别有效**：发现的问题必须是实际存在的代码问题
            5. **重要性评估合理**：基于代码复杂度和业务逻辑进行评估
            6. **质量评估客观**：复杂度、异味、债务评估基于可量化的代码特征
            7. **API分析准确**：仅在确实存在API相关代码时进行API分析

            ### JSON输出格式要求
            1. **字符串引号**：所有字符串值必须用引号包裹
            2. **列表元素引号**：列表中的每个字符串元素都必须有引号
            3. **布尔值规范**：使用标准JSON布尔值（true/false，不加引号）
            4. **数值格式**：数值类型不加引号，保持原始格式
            5. **嵌套结构**：确保嵌套对象和数组格式正确

            ### 必需输出组件分析
            请确保输出包含以下7个方面的完整分析：

            1. **基本信息 (basic_info)**: 模块核心属性和重要性
            2. **架构上下文 (architecture_context)**: 项目定位和职责
            3. **核心功能 (features)**: 具体功能特性和使用场景
            4. **依赖关系 (dependency_graph)**: 精确的依赖分析和循环检测
            5. **代码质量 (code_quality)**: 复杂度、异味、技术债务和质量评分
            6. **API设计 (api_design)**: API相关分析（如果适用）
            7. **关键发现 (findings)**: 代码质量问题和改进建议

            **注意**：如果某个方面在模块中不明显或不存在，请基于实际情况提供合理的空值或默认值，不要创造不存在的信息。

            {{ format_instructions }}
            """
            # 创建解析器
            module_parser = AnalysisOutputParser(pydantic_object=ModuleAnalysis)
            parser = OutputFixingParser.from_llm(parser=module_parser, llm=self.llm)

            # 获取格式指令
            module_format_instructions = module_parser.get_format_instructions()

            # 创建提示模板
            module_prompt = PromptTemplate.from_template(
                module_analysis_template,
                template_format="jinja2"
            ).partial(format_instructions=module_format_instructions)

            # 创建处理链
            self.chain = (
                module_prompt
                | self.llm
                | parser
            )

            logger.info("模块级代码分析处理链初始化完成")
        except Exception as e:
            logger.error(f"初始化处理链失败: {str(e)}")
            raise

    async def prepare_input(self, input_data: Dict[str, Any]) -> AgentInput:
        """
        准备输入数据

        Args:
            input_data: 原始输入数据

        Returns:
            AgentInput: 准备后的输入数据
        """
        input_data: AgentInput = await super().prepare_input(input_data)
        project_path = input_data.parameters["project_path"]
        module_path = input_data.parameters["module_path"]
        file_path = Path(project_path) / module_path

        # 使用file_utils读取文件内容
        content = read_file_content(str(file_path.resolve()))
        
        if content is None:
            raise ValueError(f"无法读取模块文件: {file_path}")
        
        input_data.parameters["module_content"] = content
        return input_data

    @retry_async(max_retries=3)
    async def _process(
        self,
        input_data: AgentInput
    ) -> Tuple[Any, Dict[str, Any]]:
        """
        处理查询

        Args:
            input_data: 输入数据

        Returns:
            Tuple[Any, Dict[str, Any]]: (分析结果, 元数据)
        """
        try:
            # 调用处理链
            # result = await self.chain.first.ainvoke(input_data.parameters)
            # logger.info(result.text)
            result = await self.chain.ainvoke(input_data.parameters)

            return result, {"analyzer": "module"}
        except Exception as e:
            logger.error(f"模块级分析失败: {str(e)}")
            raise

    async def _shutdown(self) -> None:
        """关闭分析器资源"""
        logger.info("关闭模块级代码分析器资源")
        # 释放可能的资源
        self.chain = None
        self.llm = None
