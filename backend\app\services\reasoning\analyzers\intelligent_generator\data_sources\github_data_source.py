#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub数据源

从GitHub API获取项目相关信息
"""

import logging
import re
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse

from .external_data_source import ExternalDataSource

logger = logging.getLogger(__name__)


class GitHubDataSource(ExternalDataSource):
    """GitHub数据源类"""
    
    def __init__(self, api_token: Optional[str] = None, timeout: int = 30):
        """
        初始化GitHub数据源
        
        Args:
            api_token: GitHub API令牌（可选，用于提高请求限制）
            timeout: 请求超时时间
        """
        super().__init__(timeout)
        self.api_token = api_token
        self.base_url = "https://api.github.com"
        
    def get_data_source_name(self) -> str:
        """获取数据源名称"""
        return "GitHub"
    
    async def fetch_data(self, repository_url: str = None, owner: str = None, repo: str = None) -> Dict[str, Any]:
        """
        获取GitHub数据
        
        Args:
            repository_url: 仓库URL
            owner: 仓库所有者
            repo: 仓库名称
            
        Returns:
            Dict[str, Any]: GitHub数据
        """
        try:
            # 解析仓库信息
            if repository_url:
                owner, repo = self._parse_repository_url(repository_url)
            
            if not owner or not repo:
                logger.warning("缺少仓库所有者或仓库名称")
                return {}
            
            logger.info(f"开始获取GitHub数据: {owner}/{repo}")
            
            # 获取各种数据
            data = {
                "repository_info": await self._fetch_repository_info(owner, repo),
                "releases": await self._fetch_releases(owner, repo),
                "contributors": await self._fetch_contributors(owner, repo),
                "languages": await self._fetch_languages(owner, repo),
                "topics": await self._fetch_topics(owner, repo),
                "readme": await self._fetch_readme(owner, repo),
                "license": await self._fetch_license(owner, repo),
                "issues_stats": await self._fetch_issues_stats(owner, repo),
                "pull_requests_stats": await self._fetch_pull_requests_stats(owner, repo)
            }
            
            # 缓存数据
            self.cache_data(f"{owner}/{repo}", data)
            
            logger.info(f"GitHub数据获取完成: {owner}/{repo}")
            return data
            
        except Exception as e:
            logger.error(f"获取GitHub数据失败: {str(e)}")
            return {}
    
    def _parse_repository_url(self, url: str) -> tuple:
        """
        解析仓库URL
        
        Args:
            url: 仓库URL
            
        Returns:
            tuple: (owner, repo)
        """
        try:
            # 支持多种URL格式
            patterns = [
                r'github\.com[:/]([^/]+)/([^/]+?)(?:\.git)?/?$',
                r'github\.com/([^/]+)/([^/]+)',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    owner, repo = match.groups()
                    # 移除.git后缀
                    if repo.endswith('.git'):
                        repo = repo[:-4]
                    return owner, repo
            
            logger.warning(f"无法解析GitHub URL: {url}")
            return None, None
            
        except Exception as e:
            logger.error(f"解析GitHub URL失败: {str(e)}")
            return None, None
    
    async def _fetch_repository_info(self, owner: str, repo: str) -> Optional[Dict[str, Any]]:
        """获取仓库基本信息"""
        url = f"{self.base_url}/repos/{owner}/{repo}"
        headers = self._get_headers()
        
        data = await self._make_request(url, headers=headers)
        if data:
            return {
                "name": data.get("name"),
                "full_name": data.get("full_name"),
                "description": data.get("description"),
                "homepage": data.get("homepage"),
                "language": data.get("language"),
                "size": data.get("size"),
                "stargazers_count": data.get("stargazers_count"),
                "watchers_count": data.get("watchers_count"),
                "forks_count": data.get("forks_count"),
                "open_issues_count": data.get("open_issues_count"),
                "created_at": data.get("created_at"),
                "updated_at": data.get("updated_at"),
                "pushed_at": data.get("pushed_at"),
                "clone_url": data.get("clone_url"),
                "ssh_url": data.get("ssh_url"),
                "default_branch": data.get("default_branch"),
                "topics": data.get("topics", []),
                "license": data.get("license", {}).get("name") if data.get("license") else None,
                "has_issues": data.get("has_issues"),
                "has_projects": data.get("has_projects"),
                "has_wiki": data.get("has_wiki"),
                "has_pages": data.get("has_pages"),
                "archived": data.get("archived"),
                "disabled": data.get("disabled")
            }
        return None
    
    async def _fetch_releases(self, owner: str, repo: str) -> Optional[List[Dict[str, Any]]]:
        """获取发布信息"""
        url = f"{self.base_url}/repos/{owner}/{repo}/releases"
        headers = self._get_headers()
        params = {"per_page": 10}  # 限制数量
        
        data = await self._make_request(url, headers=headers, params=params)
        if data and isinstance(data, list):
            releases = []
            for release in data:
                releases.append({
                    "tag_name": release.get("tag_name"),
                    "name": release.get("name"),
                    "body": release.get("body"),
                    "published_at": release.get("published_at"),
                    "prerelease": release.get("prerelease"),
                    "draft": release.get("draft"),
                    "download_count": sum(asset.get("download_count", 0) for asset in release.get("assets", []))
                })
            return releases
        return None
    
    async def _fetch_contributors(self, owner: str, repo: str) -> Optional[List[Dict[str, Any]]]:
        """获取贡献者信息"""
        url = f"{self.base_url}/repos/{owner}/{repo}/contributors"
        headers = self._get_headers()
        params = {"per_page": 20}  # 限制数量
        
        data = await self._make_request(url, headers=headers, params=params)
        if data and isinstance(data, list):
            contributors = []
            for contributor in data:
                contributors.append({
                    "login": contributor.get("login"),
                    "contributions": contributor.get("contributions"),
                    "avatar_url": contributor.get("avatar_url"),
                    "html_url": contributor.get("html_url")
                })
            return contributors
        return None
    
    async def _fetch_languages(self, owner: str, repo: str) -> Optional[Dict[str, int]]:
        """获取编程语言统计"""
        url = f"{self.base_url}/repos/{owner}/{repo}/languages"
        headers = self._get_headers()
        
        return await self._make_request(url, headers=headers)
    
    async def _fetch_topics(self, owner: str, repo: str) -> Optional[List[str]]:
        """获取仓库主题"""
        url = f"{self.base_url}/repos/{owner}/{repo}/topics"
        headers = self._get_headers()
        headers["Accept"] = "application/vnd.github.mercy-preview+json"  # 主题API需要特殊Accept头
        
        data = await self._make_request(url, headers=headers)
        if data:
            return data.get("names", [])
        return None
    
    async def _fetch_readme(self, owner: str, repo: str) -> Optional[Dict[str, Any]]:
        """获取README文件信息"""
        url = f"{self.base_url}/repos/{owner}/{repo}/readme"
        headers = self._get_headers()
        
        data = await self._make_request(url, headers=headers)
        if data:
            return {
                "name": data.get("name"),
                "path": data.get("path"),
                "size": data.get("size"),
                "download_url": data.get("download_url"),
                "html_url": data.get("html_url")
            }
        return None
    
    async def _fetch_license(self, owner: str, repo: str) -> Optional[Dict[str, Any]]:
        """获取许可证信息"""
        url = f"{self.base_url}/repos/{owner}/{repo}/license"
        headers = self._get_headers()
        
        data = await self._make_request(url, headers=headers)
        if data:
            license_info = data.get("license", {})
            return {
                "name": license_info.get("name"),
                "spdx_id": license_info.get("spdx_id"),
                "key": license_info.get("key"),
                "url": license_info.get("url"),
                "html_url": data.get("html_url"),
                "download_url": data.get("download_url")
            }
        return None
    
    async def _fetch_issues_stats(self, owner: str, repo: str) -> Optional[Dict[str, Any]]:
        """获取Issues统计"""
        # 获取开放的Issues
        open_url = f"{self.base_url}/repos/{owner}/{repo}/issues"
        headers = self._get_headers()
        params = {"state": "open", "per_page": 1}
        
        open_data = await self._make_request(open_url, headers=headers, params=params)
        
        # 获取关闭的Issues（最近的一些）
        closed_params = {"state": "closed", "per_page": 1}
        closed_data = await self._make_request(open_url, headers=headers, params=closed_params)
        
        if open_data is not None or closed_data is not None:
            return {
                "has_open_issues": len(open_data) > 0 if open_data else False,
                "has_closed_issues": len(closed_data) > 0 if closed_data else False
            }
        return None
    
    async def _fetch_pull_requests_stats(self, owner: str, repo: str) -> Optional[Dict[str, Any]]:
        """获取Pull Requests统计"""
        url = f"{self.base_url}/repos/{owner}/{repo}/pulls"
        headers = self._get_headers()
        params = {"state": "all", "per_page": 1}
        
        data = await self._make_request(url, headers=headers, params=params)
        if data is not None:
            return {
                "has_pull_requests": len(data) > 0 if data else False
            }
        return None
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "IntelligentReadmeGenerator/1.0"
        }
        
        if self.api_token:
            headers["Authorization"] = f"token {self.api_token}"
        
        return headers
    
    async def is_available(self) -> bool:
        """检查GitHub API是否可用"""
        try:
            url = f"{self.base_url}/rate_limit"
            headers = self._get_headers()
            
            data = await self._make_request(url, headers=headers)
            return data is not None
            
        except Exception as e:
            logger.error(f"检查GitHub API可用性失败: {str(e)}")
            return False
