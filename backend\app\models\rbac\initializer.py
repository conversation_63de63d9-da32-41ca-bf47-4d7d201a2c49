"""
RBAC系统初始化模块
用于初始化系统必需的权限组、权限、角色和超级管理员用户
"""
from datetime import datetime, timezone
from typing import Dict, List, Optional, Set
import structlog
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.rbac.permission import PermissionModel, PermissionType
from app.models.rbac.permission_group import PermissionGroupModel
from app.models.rbac.role import RoleModel
from app.models.rbac.user import UserModel

logger = structlog.get_logger(__name__)

class RBACInitializer:
    """RBAC系统初始化器"""

    def __init__(self, async_session: AsyncSession) -> None:
        """初始化

        Args:
            async_session: 异步数据库会话
        """
        self.async_session = async_session
        self.now = datetime.now(timezone.utc)

    async def initialize(self) -> None:
        """执行初始化"""
        try:
            # 初始化权限组
            await self._init_permission_groups()
            # 初始化权限
            await self._init_permissions()
            # 初始化角色
            await self._init_roles()
            # 初始化超级管理员
            await self._init_superuser()
            # 初始化普通用户和宾客用户
            await self._init_normal_users()
            # 添加诊断
            # await self.diagnose_user_permissions()

            logger.info("RBAC系统初始化完成")
        except Exception as e:
            logger.error("RBAC系统初始化失败", error=str(e))
            raise

    async def _init_permission_groups(self) -> None:
        """初始化权限组"""
        # 基础权限组
        groups = [
            {
                "code": "system",
                "name": "系统管理",
                "description": "系统管理相关权限",
                "sort_order": 0
            },
            {
                "code": "user",
                "name": "用户管理",
                "description": "用户管理相关权限",
                "sort_order": 1
            },
            {
                "code": "role",
                "name": "角色管理",
                "description": "角色管理相关权限",
                "sort_order": 2
            },
            {
                "code": "permission",
                "name": "权限管理",
                "description": "权限管理相关权限",
                "sort_order": 3
            },
            {
                "code": "permission_group",
                "name": "权限组管理",
                "description": "权限组管理相关权限",
                "sort_order": 4
            },
            {
                "code": "api",
                "name": "接口管理",
                "description": "API接口相关权限",
                "sort_order": 5
            },
            {
                "code": "menu",
                "name": "菜单管理",
                "description": "菜单相关权限",
                "sort_order": 6
            },
            {
                "code": "broadcast",
                "name": "咕咕播报",
                "description": "咕咕播报相关权限",
                "sort_order": 7
            }
        ]

        # groups = [
        #     {
        #         "code": "default",
        #         "name": "默认权限",
        #         "description": "默认权限",
        #         "sort_order": 8
        #     }
        # ]

        # 获取已存在的权限组
        result = await self.async_session.execute(
            select(PermissionGroupModel.code)
        )
        existing_codes = {row[0] for row in result.all()}

        # 创建不存在的权限组
        for group in groups:
            if group["code"] not in existing_codes:
                group_model = PermissionGroupModel(
                    code=group["code"],
                    name=group["name"],
                    description=group["description"],
                    sort_order=group["sort_order"],
                    is_active=True,
                    created_at=self.now,
                    updated_at=self.now
                )
                self.async_session.add(group_model)

        await self.async_session.commit()
        logger.info("权限组初始化完成")

    async def _init_permissions(self) -> None:
        """初始化权限"""
        # 获取权限组
        result = await self.async_session.execute(
            select(PermissionGroupModel)
        )
        groups = {group.code: group for group in result.scalars().all()}

        # 定义系统基础权限
        permissions = [
            # 系统管理权限
            {
                "group_code": "system",
                "code": "system:config",
                "name": "系统配置",
                "description": "系统配置管理",
                "type": PermissionType.MENU,
                "sort_order": 0
            },
            {
                "group_code": "system",
                "code": "system:monitor",
                "name": "系统监控",
                "description": "系统监控管理",
                "type": PermissionType.MENU,
                "sort_order": 1
            },
            # 用户管理权限
            {
                "group_code": "user",
                "code": "user:list",
                "name": "用户列表",
                "description": "查看用户列表",
                "type": PermissionType.API,
                "sort_order": 0
            },
            {
                "group_code": "user",
                "code": "user:detail",
                "name": "用户详情",
                "description": "查看用户详情",
                "type": PermissionType.API,
                "sort_order": 1
            },
            {
                "group_code": "user",
                "code": "system:user:add",
                "name": "创建用户",
                "description": "创建新用户",
                "type": PermissionType.API,
                "sort_order": 2
            },
            {
                "group_code": "user",
                "code": "system:user:edit",
                "name": "更新用户",
                "description": "更新用户信息",
                "type": PermissionType.API,
                "sort_order": 3
            },
            {
                "group_code": "user",
                "code": "system:user:delete",
                "name": "删除用户",
                "description": "删除用户",
                "type": PermissionType.API,
                "sort_order": 4
            },
            {
                "group_code": "user",
                "code": "user:change_permissions",
                "name": "获取用户权限",
                "description": "获取用户权限",
                "type": PermissionType.API,
                "sort_order": 5
            },
            # 角色管理权限
            {
                "group_code": "role",
                "code": "system:role:list",
                "name": "角色列表",
                "description": "查看角色列表",
                "type": PermissionType.API,
                "sort_order": 0
            },
            {
                "group_code": "role",
                "code": "role:detail",
                "name": "角色详情",
                "description": "查看角色详情",
                "type": PermissionType.API,
                "sort_order": 1
            },
            {
                "group_code": "role",
                "code": "system:role:add",
                "name": "创建角色",
                "description": "创建新角色",
                "type": PermissionType.API,
                "sort_order": 2
            },
            {
                "group_code": "role",
                "code": "system:role:edit_batch",
                "name": "更新角色",
                "description": "更新角色信息",
                "type": PermissionType.API,
                "sort_order": 3
            },
            {
                "group_code": "role",
                "code": "system:role:delete",
                "name": "删除角色",
                "description": "删除角色",
                "type": PermissionType.API,
                "sort_order": 4
            },
            {
                "group_code": "role",
                "code": "role:add_permission",
                "name": "添加角色权限",
                "description": "添加角色权限",
                "type": PermissionType.API,
                "sort_order": 5
            },
            {
                "group_code": "role",
                "code": "role:remove_permission",
                "name": "移除角色权限",
                "description": "移除角色权限",
                "type": PermissionType.API,
                "sort_order": 6
            },
            {
                "group_code": "role",
                "code": "role:add_permission_group",
                "name": "添加角色权限组",
                "description": "添加角色权限组",
                "type": PermissionType.API,
                "sort_order": 7
            },
            {
                "group_code": "role",
                "code": "role:remove_permission_group",
                "name": "移除角色权限组",
                "description": "移除角色权限组",
                "type": PermissionType.API,
                "sort_order": 8
            },
            # 权限管理权限
            {
                "group_code": "permission",
                "code": "permission:list",
                "name": "权限列表",
                "description": "查看权限列表",
                "type": PermissionType.API,
                "sort_order": 0
            },
            {
                "group_code": "permission",
                "code": "permission:detail",
                "name": "权限详情",
                "description": "查看权限详情",
                "type": PermissionType.API,
                "sort_order": 1
            },
            {
                "group_code": "permission",
                "code": "permission:create",
                "name": "创建权限",
                "description": "创建新权限",
                "type": PermissionType.API,
                "sort_order": 2
            },
            {
                "group_code": "permission",
                "code": "permission:update",
                "name": "更新权限",
                "description": "更新权限信息",
                "type": PermissionType.API,
                "sort_order": 3
            },
            {
                "group_code": "permission",
                "code": "permission:delete",
                "name": "删除权限",
                "description": "删除权限",
                "type": PermissionType.API,
                "sort_order": 4
            },
            # 权限组管理权限
            {
                "group_code": "permission_group",
                "code": "permission_group:list",
                "name": "权限组列表",
                "description": "查看权限组列表",
                "type": PermissionType.API,
                "sort_order": 0
            },
            {
                "group_code": "permission_group",
                "code": "permission_group:detail",
                "name": "权限组详情",
                "description": "查看权限组详情",
                "type": PermissionType.API,
                "sort_order": 1
            },
            {
                "group_code": "permission_group",
                "code": "permission_group:create",
                "name": "创建权限组",
                "description": "创建新权限组",
                "type": PermissionType.API,
                "sort_order": 2
            },
            {
                "group_code": "permission_group",
                "code": "permission_group:update",
                "name": "更新权限组",
                "description": "更新权限组信息",
                "type": PermissionType.API,
                "sort_order": 3
            },
            {
                "group_code": "permission_group",
                "code": "permission_group:delete",
                "name": "删除权限组",
                "description": "删除权限组",
                "type": PermissionType.API,
                "sort_order": 4
            },
            # 权限组权限管理权限
            {
                "group_code": "permission_group",
                "code": "permission_group:add_permission",
                "name": "添加权限",
                "description": "添加权限",
                "type": PermissionType.API,
                "sort_order": 5
            },
            {
                "group_code": "permission_group",
                "code": "permission_group:remove_permission",
                "name": "删除权限",
                "description": "删除权限",
                "type": PermissionType.API,
                "sort_order": 6
            },

            #只服务于前端的按钮API
            # 前端用户管理按钮权限
            {
                "group_code": "menu",
                "code": "system:user:list",
                "name": "用户查询",
                "description": "用户查询",
                "type": PermissionType.BUTTON,
                "sort_order": 1
            },
            {
                "group_code": "menu",
                "code": "system:user:add",
                "name": "用户新增",
                "description": "用户新增",
                "type": PermissionType.BUTTON,
                "sort_order": 2
            },
            {
                "group_code": "menu",
                "code": "system:user:edit",
                "name": "用户修改",
                "description": "用户修改",
                "type": PermissionType.BUTTON,
                "sort_order": 3
            },
            {
                "group_code": "menu",
                "code": "system:user:delete",
                "name": "用户删除",
                "description": "用户删除",
                "type": PermissionType.BUTTON,
                "sort_order": 4
            },
            {
                "group_code": "menu",
                "code": "system:user:reset_pwd",
                "name": "重置密码",
                "description": "重置密码",
                "type": PermissionType.BUTTON,
                "sort_order": 5
            },

            # 前端角色管理按钮权限
            {
                "group_code": "menu",
                "code": "system:role:list",
                "name": "角色查询",
                "description": "角色查询",
                "type": PermissionType.BUTTON,
                "sort_order": 6
            },
            {
                "group_code": "menu",
                "code": "system:role:add",
                "name": "角色新增",
                "description": "角色新增",
                "type": PermissionType.BUTTON,
                "sort_order": 7
            },
            {
                "group_code": "menu",
                "code": "system:role:edit_batch",
                "name": "角色修改",
                "description": "角色修改",
                "type": PermissionType.BUTTON,
                "sort_order": 8
            },
            {
                "group_code": "menu",
                "code": "system:role:delete",
                "name": "角色删除",
                "description": "角色删除",
                "type": PermissionType.BUTTON,
                "sort_order": 9
            },
            {
                "group_code": "menu",
                "code": "system:role:data_scope",
                "name": "分配权限",
                "description": "分配权限",
                "type": PermissionType.BUTTON,
                "sort_order": 10
            },

            # 咕咕播报权限
            {
                "group_code": "broadcast",
                "code": "broadcastmanage:content:list",
                "name": "内容查询",
                "description": "查看内容列表",
                "type": PermissionType.BUTTON,
                "sort_order": 0
            },
            {
                "group_code": "broadcast",
                "code": "broadcastmanage:content:add",
                "name": "内容新增",
                "description": "新增内容",
                "type": PermissionType.BUTTON,
                "sort_order": 1
            },
            {
                "group_code": "broadcast",
                "code": "broadcastmanage:content:edit",
                "name": "内容修改",
                "description": "修改内容",
                "type": PermissionType.BUTTON,
                "sort_order": 2
            },
            {
                "group_code": "broadcast",
                "code": "broadcastmanage:content:delete",
                "name": "内容删除",
                "description": "删除内容",
                "type": PermissionType.BUTTON,
                "sort_order": 3
            },

            # 咕咕播报API权限
            {
                "group_code": "broadcast",
                "code": "broadcast:content:list",
                "name": "内容列表",
                "description": "获取内容列表",
                "type": PermissionType.API,
                "sort_order": 4
            },
            {
                "group_code": "broadcast",
                "code": "broadcast:content:detail",
                "name": "内容详情",
                "description": "获取内容详情",
                "type": PermissionType.API,
                "sort_order": 5
            },
            {
                "group_code": "broadcast",
                "code": "broadcast:content:create",
                "name": "创建内容",
                "description": "创建新内容",
                "type": PermissionType.API,
                "sort_order": 6
            },
            {
                "group_code": "broadcast",
                "code": "broadcast:content:update",
                "name": "更新内容",
                "description": "更新内容信息",
                "type": PermissionType.API,
                "sort_order": 7
            },
            {
                "group_code": "broadcast",
                "code": "broadcast:content:delete",
                "name": "删除内容",
                "description": "删除内容",
                "type": PermissionType.API,
                "sort_order": 8
            }
        ]

        # 获取已存在的权限
        result = await self.async_session.execute(
            select(PermissionModel.code)
        )
        existing_codes = {row[0] for row in result.all()}

        # 创建不存在的权限
        for perm in permissions:
            if perm["code"] not in existing_codes:
                group = groups.get(perm["group_code"])
                if not group:
                    logger.warning(f"权限组不存在: {perm['group_code']}")
                    continue

                perm_model = PermissionModel(
                    group_id=group.id,
                    code=perm["code"],
                    name=perm["name"],
                    description=perm["description"],
                    type=perm["type"],
                    sort_order=perm["sort_order"],
                    is_active=True,
                    created_at=self.now,
                    updated_at=self.now
                )
                self.async_session.add(perm_model)

        await self.async_session.commit()
        logger.info("权限初始化完成")

    async def _init_roles(self) -> None:
        """初始化角色"""
        # 获取所有权限
        result = await self.async_session.execute(
            select(PermissionModel)
        )
        permissions = list(result.scalars().all())

        # 获取所有权限组
        result = await self.async_session.execute(
            select(PermissionGroupModel)
        )
        permission_groups = list(result.scalars().all())

        # 检查超级管理员角色是否存在
        result = await self.async_session.execute(
            select(RoleModel).where(RoleModel.code == "admin")
        )
        admin_role = result.scalar_one_or_none()

        if not admin_role:
            # 创建超级管理员角色
            admin_role = RoleModel(
                code="admin",
                name="超级管理员",
                description="系统超级管理员，拥有所有权限",
                is_active=True,
                is_system=True,
                is_inherit=False,
                sort_order=0,
                created_at=self.now,
                updated_at=self.now
            )
            # 分配所有权限和权限组
            # 'role:detail'？ system:role:add ?
            admin_role.permissions = permissions
            admin_role.permission_groups = permission_groups
            self.async_session.add(admin_role)
            await self.async_session.commit()
        
        # 检查普通用户角色是否存在
        result = await self.async_session.execute(
            select(RoleModel).where(RoleModel.code == "user")
        )
        user_role = result.scalar_one_or_none()
        
        if not user_role:
            # 创建普通用户角色
            user_role = RoleModel(
                code="user",
                name="普通用户",
                description="系统普通用户，拥有基本操作权限",
                is_active=True,
                is_system=True,
                is_inherit=False,
                sort_order=1,
                created_at=self.now,
                updated_at=self.now
            )
            
            # 为普通用户分配基本权限
            # 筛选出用户自身信息管理相关的权限
            user_permissions = [
                p for p in permissions 
                if p.code.startswith("user:self") or 
                p.code.startswith("workspace:") or
                p.code.startswith("project:") or
                p.code.startswith("document:")
            ]
            user_role.permissions = user_permissions
            
            self.async_session.add(user_role)
            await self.async_session.commit()
            
        # 检查宾客角色是否存在
        result = await self.async_session.execute(
            select(RoleModel).where(RoleModel.code == "guest")
        )
        guest_role = result.scalar_one_or_none()
        
        if not guest_role:
            # 创建宾客角色
            guest_role = RoleModel(
                code="guest",
                name="宾客",
                description="系统宾客用户，仅拥有只读权限",
                is_active=True,
                is_system=True,
                is_inherit=False,
                sort_order=2,
                created_at=self.now,
                updated_at=self.now
            )
            
            # 为宾客分配只读权限
            # 筛选出只读相关的权限
            guest_permissions = [
                p for p in permissions 
                if p.code.endswith(":list") or 
                p.code.endswith(":detail") or
                p.code.endswith(":read")
            ]
            guest_role.permissions = guest_permissions
            
            self.async_session.add(guest_role)
            await self.async_session.commit()

        logger.info("角色初始化完成")

    async def _init_superuser(self) -> None:
        """初始化超级管理员用户"""

        # 先查询所有需要的数据
        # admin_query = select(UserModel).where(UserModel.username == "admin")
        # role_query = select(RoleModel).where(RoleModel.code == "admin")
        #
        # result = await self.async_session.execute(admin_query)
        # admin_user = result.scalar_one_or_none()
        #
        # result = await self.async_session.execute(role_query)
        # admin_role = result.scalar_one()

        # 检查超级管理员用户是否存在
        result = await self.async_session.execute(
            select(UserModel).where(UserModel.username == "admin")
        )
        admin_user = result.scalar_one_or_none()

        if not admin_user:
            # 获取超级管理员角色
            # result = await self.async_session.execute(
            #     select(RoleModel).where(RoleModel.code == "admin")
            # )

            result = await self.async_session.execute(
                select(RoleModel)
                    .where(RoleModel.code == "admin")
                    .options(selectinload(RoleModel.permissions))  # 预加载权限
            )

            admin_role = result.scalar_one()

            # 创建超级管理员用户
            admin_user = UserModel(
                username="admin",
                nickname="超级管理员",
                email="<EMAIL>",
                is_active=True,
                is_superuser=True,
                created_at=self.now,
                updated_at=self.now
            )
            # 设置密码
            admin_user.set_password("Admin@123")
            # 分配超级管理员角色
            admin_user.roles = [admin_role]


            logger.info(
                "超级管理员角色权限分配",
                role_id=admin_role.id,
                permission_count=len(admin_role.permissions)
            )

            self.async_session.add(admin_user)
            await self.async_session.commit()



        logger.info("超级管理员用户初始化完成")



    async def _init_normal_users(self) -> None:
        """初始化普通用户和宾客用户"""
        # 获取普通用户角色
        result = await self.async_session.execute(
            select(RoleModel).where(RoleModel.code == "user")
        )
        user_role = result.scalar_one_or_none()
        
        if not user_role:
            logger.error("普通用户角色不存在，无法创建普通用户")
            return
            
        # 获取宾客角色
        result = await self.async_session.execute(
            select(RoleModel).where(RoleModel.code == "guest")
        )
        guest_role = result.scalar_one_or_none()
        
        if not guest_role:
            logger.error("宾客角色不存在，无法创建宾客用户")
            return
            
        # 检查普通用户是否存在
        result = await self.async_session.execute(
            select(UserModel).where(UserModel.username == "user")
        )
        normal_user = result.scalar_one_or_none()
        
        if not normal_user:
            # 创建普通用户
            normal_user = UserModel(
                username="user",
                nickname="普通用户",
                email="<EMAIL>",
                is_active=True,
                is_superuser=False,
                created_at=self.now,
                updated_at=self.now
            )
            # 设置密码
            normal_user.set_password("User@123")
            # 分配普通用户角色
            normal_user.roles = [user_role]
            self.async_session.add(normal_user)
            await self.async_session.commit()
            logger.info("普通用户初始化完成")
            
        # 检查宾客用户是否存在
        result = await self.async_session.execute(
            select(UserModel).where(UserModel.username == "guest")
        )
        guest_user = result.scalar_one_or_none()
        
        if not guest_user:
            # 创建宾客用户
            guest_user = UserModel(
                username="guest",
                nickname="宾客用户",
                email="<EMAIL>",
                is_active=True,
                is_superuser=False,
                created_at=self.now,
                updated_at=self.now
            )
            # 设置密码
            guest_user.set_password("Guest@123")
            # 分配宾客角色
            guest_user.roles = [guest_role]
            self.async_session.add(guest_user)
            await self.async_session.commit()
            logger.info("宾客用户初始化完成")