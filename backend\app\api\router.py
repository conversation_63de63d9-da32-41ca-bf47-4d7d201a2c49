"""
API路由配置
"""
import os
from typing import List, Tuple, Union, Type
from tornado.web import URLSpec, RequestHandler

from app.api.base import CORSStaticFileHandler
from app.api.v1.github.github_image import GitHubImageUpload<PERSON><PERSON><PERSON>, GitHubImageListHandler
from app.api.v1.github.github_project import GitHubProjectDownloadHandler, GitHubProjectHandler, \
    GitHubProjectCardGenerateHandler
from app.api.v1.github.github_project_card import GitHubCardHandler, GitHubCardInteractionHandler, \
    GitHubCardDetailHandler, GitHubCardBatchHandler, GitHubDownloadStatusHandler, GitHubGenerateStatusHandler, \
    GitHubAutoFinderHandler, GitHubSolveDataHandler
from app.api.v1.github.github_project_detail import GitHubProjectDetailHandler
from app.api.v1.github.github_project_file import GitHubProjectFileHandler
from app.api.v1.github.github_project_scan_workflow import ProjectScanHandler
from app.api.v1.github.github_project_share import GitHubShareHandler, GitHubPhoneShareSolveHandler
from app.api.v1.health import HealthHandler
from app.api.v1.rbac.auth import (
    LoginHandler,
    LogoutHandler,
    RefreshTokenHandler, FastAuthHandler, FastAuthVerificationCodeHandler, BindChangeAuthHandler,
    BindChangeAuthVerificationCodeHandler, BindChangeOriginalAuthHandler, BindChangeAuthVerificationCodeOriginalHandler
)
from app.api.v1.rbac.user import (
    UserHandler,
    UserDetailHandler,
    RegisterHandler,
    ChangePasswordHandler,
    ResetPasswordHandler,
    UserStatusHandler,
    UserPermissionsHandler, VerificationCodeHandler, UpdateSelfPasswordHandler,
    UpdateSelfPasswordVerificationCodeHandler, UpdateOauthEmailVerificationCodeHandler, UpdateOauthEmailHandler
)
from app.api.v1.rbac.role import RoleHandler, RoleDetailHandler, RolePermissionGroupHandler, RolePermissionHandler, \
    UserRoleHandler
from app.api.v1.rbac.permission import PermissionHandler, PermissionDetailHandler
from app.api.v1.rbac.permission_group import PermissionGroupHandler, PermissionGroupDetailHandler
from app.api.v1.rbac.oauth import (
    OAuthProvidersHandler,
    OAuthAuthorizeHandler,
    OAuthGithubCallbackHandler,
    OAuthAccountsHandler,
    OAuthAccountDeleteHandler, OAuthGiteeCallbackHandler,
)
from app.api.v1.rbac.current_user import CurrentUserHandler, CurrentUserChangePasswordHandler
from app.api.v1.git import (
    GitRepositoryHandler,
    GitBranchesHandler,
    GitCommitsHandler,
    GitFilesHandler,
    GitFileContentHandler,
    GitFileHistoryHandler,
    GitFindRepositoriesHandler
)
from app.api.v1.rbac.user import (
    PasswordResetRequestHandler,
    PasswordResetVerifyHandler,
    PasswordResetCompleteHandler
)
from app.api.v1.github.github_project_tree import GitHubProjectTreeHandler
from app.api.v1.github.github_search import ProjectSearchHandler, RepoUrlValidationHandler
from app.api.v1.statistics import StatisticsHandler, StatisticsAdminHandler


def setup_routes() -> List[URLSpec]:
    """
    设置API路由
    
    Returns:
        List[URLSpec]: 路由处理器列表
    """
    # v1版本API路由
    v1_prefix = "/api/v1"
    v1_routes = [
        # 健康检查
        (f"{v1_prefix}/health", HealthHandler),
        
        # 认证相关
        (f"{v1_prefix}/auth/login", LoginHandler),
        (f"{v1_prefix}/auth/refresh", RefreshTokenHandler),
        (f"{v1_prefix}/auth/logout", LogoutHandler),
        (f"{v1_prefix}/users/verification-code", VerificationCodeHandler),

        (f"{v1_prefix}/auth/fastauth", FastAuthHandler),
        (f"{v1_prefix}/auth/fastauth-verification-code", FastAuthVerificationCodeHandler),

        (f"{v1_prefix}/auth/bind-change", BindChangeAuthHandler),
        (f"{v1_prefix}/auth/bind-change-verification-code", BindChangeAuthVerificationCodeHandler),

        (f"{v1_prefix}/auth/bind-change-original", BindChangeOriginalAuthHandler),
        (f"{v1_prefix}/auth/bind-change-verification-code-original", BindChangeAuthVerificationCodeOriginalHandler),

        # OAuth认证
        (f"{v1_prefix}/oauth/providers", OAuthProvidersHandler),
        (f"{v1_prefix}/oauth/authorize", OAuthAuthorizeHandler),
        (f"{v1_prefix}/oauth/callback/github", OAuthGithubCallbackHandler),
        (f"{v1_prefix}/oauth/callback/gitee", OAuthGiteeCallbackHandler),
        (f"{v1_prefix}/oauth/accounts", OAuthAccountsHandler),
        (f"{v1_prefix}/oauth/accounts/([^/]+)", OAuthAccountDeleteHandler),
 
        # 用户管理 注意users如果没有二级地址会统一适配成UserDetailHandler
        (f"{v1_prefix}/users", UserHandler),
        (f"{v1_prefix}/users/register", RegisterHandler),
        (f"{v1_prefix}/users/verification-code", VerificationCodeHandler),
        (f"{v1_prefix}/users/([^/]+)", UserDetailHandler),
        # (f"{v1_prefix}/users/([^/]+)/change-password", ChangePasswordHandler),
        (f"{v1_prefix}/users/([^/]+)/reset-password", ResetPasswordHandler),
        (f"{v1_prefix}/users/([^/]+)/status/([^/]+)", UserStatusHandler),
        (f"{v1_prefix}/users/([^/]+)/permissions", UserPermissionsHandler),

        # 为什么这里要重新实现一次？
        (f"{v1_prefix}/users/password-reset/request", PasswordResetRequestHandler),
        (f"{v1_prefix}/users/password-reset/complete", PasswordResetCompleteHandler),
        (f"{v1_prefix}/users/update-self-password/verification-code", UpdateSelfPasswordVerificationCodeHandler),
        (f"{v1_prefix}/users/update-self-password/complete", UpdateSelfPasswordHandler),
        (f"{v1_prefix}/users/update-oauth-email/verification-code", UpdateOauthEmailVerificationCodeHandler),
        (f"{v1_prefix}/users/update-oauth-email/complete", UpdateOauthEmailHandler),

        # 角色管理
        (f"{v1_prefix}/roles", RoleHandler),
        (f"{v1_prefix}/user-roles", UserRoleHandler),
        (f"{v1_prefix}/roles/([^/]+)", RoleDetailHandler),
        (f"{v1_prefix}/roles/([^/]+)/permission-groups", RolePermissionGroupHandler),
        (f"{v1_prefix}/roles/([^/]+)/permissions", RolePermissionHandler),
        
        # 权限管理
        (f"{v1_prefix}/permissions", PermissionHandler),
        (f"{v1_prefix}/permissions/([^/]+)", PermissionDetailHandler),
        
        # 权限组管理
        (f"{v1_prefix}/permission-groups", PermissionGroupHandler),
        (f"{v1_prefix}/permission-groups/([^/]+)", PermissionGroupDetailHandler),
        
        # 当前用户相关
        (f"{v1_prefix}/current-user", CurrentUserHandler),
        (f"{v1_prefix}/current-user/change-password", CurrentUserChangePasswordHandler),
        
        # Git仓库管理
        (f"{v1_prefix}/git/repository", GitRepositoryHandler),
        (f"{v1_prefix}/git/branches", GitBranchesHandler),
        (f"{v1_prefix}/git/commits", GitCommitsHandler),
        (f"{v1_prefix}/git/files", GitFilesHandler),
        (f"{v1_prefix}/git/file-content", GitFileContentHandler),
        (f"{v1_prefix}/git/file-history", GitFileHistoryHandler),
        (f"{v1_prefix}/git/find-repositories", GitFindRepositoriesHandler),

        # github下载和转换

        (f"{v1_prefix}/github/project", GitHubProjectDetailHandler),
        (f"{v1_prefix}/github/projects", GitHubProjectHandler),
        (f"{v1_prefix}/github/projects/generate", GitHubProjectCardGenerateHandler),
        (f"{v1_prefix}/github/projects/download", GitHubProjectDownloadHandler),
        (f"{v1_prefix}/github/projects/generate-cards", GitHubProjectCardGenerateHandler),
        (f"{v1_prefix}/github/projects/trees", GitHubProjectTreeHandler),
        (f"{v1_prefix}/github/projects/cards", GitHubCardHandler),
        (f"{v1_prefix}/github/projects/cards/detail", GitHubCardDetailHandler),
        (f"{v1_prefix}/github/projects/cards/batch", GitHubCardBatchHandler),  # 批量修改 先删再加
        (f"{v1_prefix}/github/projects/file", GitHubProjectFileHandler),
        (f"{v1_prefix}/github/projects/cards/action", GitHubCardInteractionHandler),
        (f"{v1_prefix}/github/upload/image", GitHubImageUploadHandler),
        (f"{v1_prefix}/github/projects/images", GitHubImageListHandler),



        # GitHub下载/AI生成统计API
        (f"{v1_prefix}/github/projects/dowloadstatus", GitHubDownloadStatusHandler),
        (f"{v1_prefix}/github/projects/generatestatus", GitHubGenerateStatusHandler),
        (f"{v1_prefix}/github/projects/needgenerateadder", GitHubAutoFinderHandler),
        (f"{v1_prefix}/github/projects/githubsolvedata", GitHubSolveDataHandler),
        # GitHub搜索API
        (f"{v1_prefix}/github/search", ProjectSearchHandler),
        (f"{v1_prefix}/github/validate-repo", RepoUrlValidationHandler),
        (f"{v1_prefix}/statistics", StatisticsHandler),
        (f"{v1_prefix}/statistics/admin", StatisticsAdminHandler),

        # github 分享api

        # 统计+1
        (f"{v1_prefix}/github/projects/share/statistics", GitHubShareHandler),
        # post修改 get获取
        (f"{v1_prefix}/github/projects/share/phone", GitHubPhoneShareSolveHandler),

        # 扫描项目流程
        (f"{v1_prefix}/github/projects/scan", ProjectScanHandler),

    ]



    upload_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'static', 'images')
    # 添加静态文件处理路由
    static_routes = [
        # 使用StaticFileHandler处理图片文件
        (r"/static/images/(.*)", CORSStaticFileHandler, {"path": upload_dir}),
    ]

    # 未来可以添加其他版本的API路由
    routes = [
        *v1_routes,
    ]

    routes.extend(static_routes)

    # add 3/2
    urlspecs = []
    for route in routes:
        if len(route) == 2:
            pattern, handler = route
            urlspecs.append(URLSpec(pattern, handler))
        elif len(route) == 3:
            pattern, handler, kwargs = route
            urlspecs.append(URLSpec(pattern, handler, kwargs=kwargs))

    return urlspecs

    # 转换为URLSpec
    # return [URLSpec(pattern, handler) for pattern, handler in routes]
