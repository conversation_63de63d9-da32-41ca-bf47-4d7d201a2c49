#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块分析处理器
负责分析项目中的各个模块
"""
import os
import json
import hashlib
import time
import psutil
import asyncio
from typing import Dict, List, Any, Optional, Tuple

import structlog
from ...analyzers.module_analyzer import ModuleAnalyzer
from ...analyzers.module_models import ModuleAnalysis
from ....ai_agent_core import agent_manager
from ...project_scanning.project_info_models import FileInfo
from ..models import AnalysisConfig, AnalysisState
from .base_handler import BaseAnalysisHandler
from ..cache_manager import CacheManager
from .....utils.batch_processing_utils import calculate_batch_size, record_performance

logger = structlog.get_logger(__name__)

class ModuleAnalysisHandler(BaseAnalysisHandler):
    """
    模块分析处理器
    负责分析项目中的各个模块
    """

    def __init__(self, config: AnalysisConfig, cache_dir: str = '.cache'):
        """
        初始化模块分析处理器

        Args:
            config: 分析配置
            cache_dir: 缓存目录
        """
        self.module_analyzer: Optional[ModuleAnalyzer] = None
        super().__init__(config, cache_dir)

    def initialize(self) -> None:
        """
        初始化模块分析器
        """
        try:
            self.module_analyzer = agent_manager.load("ModuleAnalyzerAgent")
            logger.info("模块分析器初始化完成")
        except Exception as e:
            logger.error(f"模块分析器初始化失败: {str(e)}")
            raise

    async def process(self, state: AnalysisState) -> AnalysisState:
        """
        处理模块分析

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        # 1. 从缓存获取状态
        cached_state = self.get_cached_state()

        # 2. 执行模块分析
        try:
            # 检查是否需要执行模块分析
            if not self.config.analyze_modules:
                logger.info("配置指定不分析模块，跳过模块分析")
                return state

            # 检查是否可以使用缓存
            if cached_state and cached_state.module_analyses and state.is_incremental_analysis:
                # 检查是否有模块变化
                has_module_changes = (state.changed_modules or state.new_modules or state.deleted_modules)

                if not has_module_changes:
                    logger.info("没有模块变化，使用缓存的模块分析结果")
                    state.module_analyses = cached_state.module_analyses
                    state.completed_modules = cached_state.completed_modules
                    state.failed_modules = cached_state.failed_modules
                    state.analysis_progress = 0.5  # 模块分析完成，进度50%
                    return state

            # 执行模块分析
            state = await self._analyze_modules(state)

            logger.info("模块分析完成")
        except Exception as e:
            logger.error(f"模块分析失败: {str(e)}")
            state.errors["module_analysis"] = str(e)
            raise

        # 3. 设置缓存
        self.set_cached_state(state)

        # 4. 返回分析结果
        return state

    async def _analyze_modules(self, state: AnalysisState) -> AnalysisState:
        """
        分析所有模块 - 修改为一次性处理所有待分析模块，而非依赖递归调用
        支持增量分析，只处理新增或修改的模块
        从module_analysis_orchestrator.py迁移的核心分析逻辑

        Args:
            state: 当前分析状态

        Returns:
            更新后的分析状态
        """
        # 如果是增量分析，确保待分析模块列表只包含需要分析的模块
        if state.is_incremental_analysis:
            # 清除已删除模块的缓存
            self._clear_deleted_modules_cache(state)

            # 检查待分析模块列表和已完成模块列表
            logger.info(f"增量分析模式: 待分析 {len(state.pending_modules)} 个模块, 已完成 {len(state.completed_modules)} 个模块")
            logger.info(f"增量分析模式: 新增 {len(state.new_modules)} 个模块, 修改 {len(state.changed_modules)} 个模块, 删除 {len(state.deleted_modules)} 个模块")

            # 确保待分析模块列表中不包含已完成的模块
            if state.pending_modules and state.completed_modules:
                original_count = len(state.pending_modules)
                state.pending_modules = [m for m in state.pending_modules if m not in state.completed_modules]
                if len(state.pending_modules) != original_count:
                    logger.info(f"增量分析模式: 移除已完成的模块后，待分析模块从 {original_count} 个减少到 {len(state.pending_modules)} 个")

        if not state.pending_modules:
            logger.info("没有待分析的模块")
            return state

        logger.info(f"开始分析模块，共 {len(state.pending_modules)} 个待处理模块")
        logger.info("使用智能批处理工具进行自适应批处理大小优化和性能数据收集")

        # 对模块进行优先级排序
        all_modules = self._prioritize_modules(state)

        # 记录总体开始时间
        total_start_time = time.time()

        # 统计信息初始化
        successful_count = 0
        total_batches = 0
        overall_batch_durations = []
        batch_size = self.config.parallel_workers

        # 循环直到处理完所有模块
        while state.pending_modules:
            total_batches += 1

            # 标记正在进行并行分析
            state.parallel_analysis_in_progress = True

            # 重新排序剩余模块（因为优先级可能在处理过程中变化）
            if total_batches > 1:
                all_modules = self._prioritize_modules(state)

            # 使用批处理工具函数计算最优批处理大小
            batch_size = calculate_batch_size(
                max_workers=self.config.parallel_workers,
                task_key="module_analysis"
            )
            modules_to_analyze = all_modules[:batch_size]

            logger.info(f"批次 {total_batches}：开始并行分析 {len(modules_to_analyze)} 个模块，当前批处理大小: {batch_size} ")

            # 记录批处理开始时间
            batch_start_time = time.time()

            # 创建并行分析任务
            tasks = []
            for module_path in modules_to_analyze:
                if module_path not in state.files:
                    logger.warning(f"模块 {module_path} 信息不存在，跳过分析")
                    continue

                module_info = state.files[module_path]
                task = asyncio.create_task(self._analyze_single_module(module_info, state))
                tasks.append(task)

            batch_successful_count = 0
            total_processing_time = 0

            # 处理已完成的任务
            for completed_task in asyncio.as_completed(tasks):
                try:
                    module_path, module_analysis, error = await completed_task
                    task_time = time.time() - batch_start_time
                    total_processing_time += task_time

                    if error:
                        state.errors[f"module_{module_path}"] = error
                        state.failed_modules.append(module_path)
                    else:
                        state.module_analyses[module_path] = module_analysis
                        state.completed_modules.append(module_path)
                        batch_successful_count += 1
                        successful_count += 1

                    # 从待分析列表中移除已处理的模块
                    if module_path in state.pending_modules:
                        state.pending_modules.remove(module_path)

                except Exception as e:
                    logger.error(f"处理模块结果时发生错误: {str(e)}")
                    state.errors[f"module_{module_path}_result"] = str(e)

            # 记录批处理性能统计
            batch_end_time = time.time()
            batch_duration = batch_end_time - batch_start_time
            overall_batch_durations.append(batch_duration)

            if batch_successful_count > 0:
                # 使用批处理工具函数记录性能数据
                record_performance(
                    task_key="module_analysis",
                    batch_size=batch_size,
                    items_processed=batch_successful_count,
                    total_time=batch_duration
                )

                logger.info(f"批次 {total_batches} 性能: 大小={batch_size}, 总时间={batch_duration:.2f}秒, "
                          f"完成={batch_successful_count}个, 剩余={len(state.pending_modules)}个")

            # 更新分析进度
            total_modules = len(state.completed_modules) + len(state.pending_modules)
            if total_modules > 0:
                # 模块分析占整体进度的40%
                progress = 0.1 + (0.4 * (len(state.completed_modules) / total_modules))
                state.analysis_progress = min(progress, 0.5)

        state.parallel_analysis_in_progress = False
        # 所有模块处理完毕
        total_duration = time.time() - total_start_time
        avg_batch_time = sum(overall_batch_durations) / len(overall_batch_durations) if overall_batch_durations else 0

        logger.info(f"完成所有 {len(state.completed_modules)} 个模块的分析，共用时 {total_duration:.2f}秒")
        logger.info(f"处理了 {total_batches} 个批次，平均每批次用时 {avg_batch_time:.2f}秒")

        return state

    async def _analyze_single_module(self, file_info: FileInfo, state: AnalysisState = None) -> Tuple[str, Optional[ModuleAnalysis], Optional[str]]:
        """
        分析单个模块的辅助方法

        Args:
            file_info: 模块信息
            state: 当前分析状态，用于检查增量分析模式

        Returns:
            包含(模块路径, 分析结果, 错误信息)的元组
        """
        # 使用文件路径作为缓存键
        cache_key = self._generate_cache_key(state, file_info.path)

        # 检查是否在增量分析模式下需要强制刷新
        force_refresh = False
        if state and state.is_incremental_analysis:
            # 如果模块在变化或新增列表中，则强制刷新
            if file_info.path in state.changed_modules or file_info.path in state.new_modules:
                force_refresh = True
                logger.info(f"增量分析模式: 强制刷新模块 {file_info.path} 的分析结果")
            # 如果模块在已完成列表中，则使用缓存
            elif file_info.path in state.completed_modules:
                logger.info(f"增量分析模式: 模块 {file_info.path} 已完成分析，将使用缓存")

        # 如果不需要强制刷新，尝试从缓存中获取分析结果
        if not force_refresh:
            cached_result = self.cache_manager.get(cache_key)
            if cached_result:
                try:
                    # 尝试反序列化缓存的分析结果
                    module_analysis = ModuleAnalysis.model_validate_json(cached_result)
                    logger.info(f"从缓存中加载模块 {file_info.path} 的分析结果")
                    return file_info.path, module_analysis, None
                except Exception as e:
                    logger.warning(f"反序列化缓存的模块分析结果失败: {str(e)}")
                    # 缓存反序列化失败，继续正常分析

        logger.info(f"开始分析模块: {file_info.path}")

        try:
            # 创建输入数据
            input_data = {
                "parameters": {
                    "module_name": file_info.name,
                    "module_path": file_info.path,
                    "project_path": state.project_path,
                    "analyze_functions": True,  # 指示需要分析内部函数
                    "language": file_info.language
                }
            }

            # 调用模块分析器
            agent_input = await self.module_analyzer.prepare_input(input_data)
            output = await self.module_analyzer.process(agent_input)
            module_analysis = output.response.result

            # 如果分析成功，将结果缓存
            if module_analysis and not output.response.has_error:
                try:
                    # 将分析结果序列化为JSON并缓存
                    module_analysis_json = module_analysis.model_dump_json()
                    self.cache_manager.set(cache_key, module_analysis_json)
                    logger.info(f"模块 {file_info.path} 分析结果已缓存")
                except Exception as e:
                    logger.warning(f"缓存模块分析结果失败: {str(e)}")

            logger.info(f"模块 {file_info.path} 分析完成")
            if output.response.has_error:
                return file_info.path, None, output.response.error_message
            return file_info.path, module_analysis, None

        except Exception as e:
            logger.error(f"模块 {file_info.path} 分析失败: {str(e)}")
            return file_info.path, None, str(e)

    def _prioritize_modules(self, state: AnalysisState) -> List[str]:
        """
        对待分析的模块进行优先级排序

        Args:
            state: 当前分析状态

        Returns:
            排序后的模块列表
        """
        if not state.pending_modules:
            return []

        # 预设的重要模块关键词
        important_keywords = ["main", "core", "app", "api", "model", "config", "service", "util"]

        # 计算模块优先级
        priority_dict = {}
        for module_path in state.pending_modules:
            # 初始优先级为0
            priority = 0

            # 1. 检查是否在优先模块列表中
            if module_path in state.priority_modules:
                priority += 10

            # 2. 检查是否已有预设优先级
            if module_path in state.module_priorities:
                priority += state.module_priorities[module_path]

            # 3. 根据模块名包含的关键词增加优先级
            for i, keyword in enumerate(important_keywords):
                if keyword in module_path.lower():
                    priority += 5 - min(i, 4)  # 前面的关键词重要性更高

            # 4. 如果是__init__.py文件，提高优先级
            if module_path.endswith("__init__.py"):
                priority += 3

            # 5. 检查文件大小，小文件优先处理
            if module_path in state.files:
                file_size = state.files[module_path].size or 0
                # 小文件优先级稍高
                if file_size < 5000:  # 小于5KB
                    priority += 2
                elif file_size > 100000:  # 大于100KB
                    priority -= 2  # 大文件降低优先级

            priority_dict[module_path] = priority

        # 更新状态中的模块优先级字典
        state.module_priorities.update(priority_dict)

        # 根据优先级排序模块
        sorted_modules = sorted(state.pending_modules, key=lambda m: priority_dict.get(m, 0), reverse=True)

        return sorted_modules

    def _clear_deleted_modules_cache(self, state: AnalysisState) -> None:
        """
        清除已删除模块的缓存

        Args:
            state: 分析状态
        """
        if not state.deleted_modules:
            return

        logger.info(f"清除 {len(state.deleted_modules)} 个已删除模块的缓存")
        for module_path in state.deleted_modules:
            cache_key = self._generate_cache_key(state, module_path)
            self.cache_manager.delete(cache_key)

    def _generate_cache_key(self,
                         state: AnalysisState,
                         module_path: str) -> str:
        """
        生成缓存键

        Args:
            state: 当前分析状态
            module_path: 模块路径

        Returns:
            缓存键
        """
        # 创建包含所有信息的字典
        cache_info = {
            "project_name": state.project_name,
            "project_path": state.project_path,
            "module_path": module_path,
        }

        # 将字典转换为JSON字符串并计算哈希值
        try:
            cache_str = json.dumps(cache_info, sort_keys=True)
            cache_hash = hashlib.md5(cache_str.encode()).hexdigest()
            return f"module_analysis_{module_path}_{cache_hash}"
        except TypeError as e:
            # 如果JSON序列化失败，记录错误并使用备用方法
            logger.warning(f"缓存键生成失败: {str(e)}，使用备用方法")
            return f"module_analysis_{state.project_path}_{module_path}"


