#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件操作工具模块
提供文件读取功能
"""
import os
import logging
from typing import Optional

logger = logging.getLogger(__name__)


def read_file_content(file_path: str, max_length: int = 50000) -> Optional[str]:
    """
    读取文件内容，如果内容过长则截断

    Args:
        file_path: 文件路径
        max_length: 最大内容长度，默认50000字符

    Returns:
        str: 文件内容（可能被截断），如果读取失败返回None
    """
    try:
        if os.path.exists(file_path) and os.path.isfile(file_path):
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

                # 如果内容过长，进行截断
                if len(content) > max_length:
                    truncated_content = content[:max_length]
                    # 尝试在合适的位置截断（避免截断到行中间）
                    last_newline = truncated_content.rfind('\n')
                    if last_newline > max_length * 0.8:  # 如果最后一个换行符位置合理
                        truncated_content = truncated_content[:last_newline]

                    # 添加截断提示
                    truncated_content += f"\n\n... [文件内容过长，已截断。原文件大小: {len(content)} 字符，显示前 {len(truncated_content)} 字符]"
                    logger.info(f"文件 {file_path} 内容过长({len(content)} 字符)，已截断到 {len(truncated_content)} 字符")
                    return truncated_content

                return content
        return None
    except Exception as e:
        logger.warning(f"读取文件 {file_path} 失败: {str(e)}")
        return None
