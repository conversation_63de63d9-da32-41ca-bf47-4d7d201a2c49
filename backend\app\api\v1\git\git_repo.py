"""
Git API处理器
"""
import json
import os
from typing import Optional, Dict, Any, List

import structlog
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.exceptions.git import GitError
from app.services.git import GitService
from app.schemas.git import (
    GitRepositoryInfo,
    GitBranchInfo,
    GitFileInfo,
    GitFileContent,
    GitCommitWithStats,
    GitRepositoryListItem
)

logger = structlog.get_logger(__name__)


class GitRepositoryHandler(BaseHandler):
    """Git仓库处理器"""
    
    async def get(self, repo_path: Optional[str] = None) -> None:
        """获取仓库信息
        
        Args:
            repo_path: 仓库路径（URL编码）
        """
        try:
            if not repo_path:
                # 如果没有提供仓库路径，则返回400错误
                raise HTTPError(400, "缺少仓库路径参数")
            
            # URL解码仓库路径
            repo_path = self.get_argument("repo_path", None)
            if not repo_path:
                raise HTTPError(400, "缺少仓库路径参数")
            
            # 检查路径是否存在
            if not os.path.exists(repo_path):
                raise HTTPError(404, f"仓库路径不存在: {repo_path}")
            
            # 检查是否为Git仓库
            if not GitService.is_git_repository(repo_path):
                raise HTTPError(400, f"无效的Git仓库: {repo_path}")
            
            # 获取仓库信息
            git_service = GitService(repo_path)
            repo_info = git_service.get_repository_info()
            
            self.success_response(repo_info)
        except GitError as e:
            logger.error("获取仓库信息失败", error=str(e))
            raise HTTPError(e.status_code, str(e))
        except HTTPError:
            raise
        except Exception as e:
            logger.error("获取仓库信息失败", error=str(e))
            raise HTTPError(500, f"获取仓库信息失败: {str(e)}")


class GitBranchesHandler(BaseHandler):
    """Git分支处理器"""
    
    async def get(self) -> None:
        """获取仓库分支列表"""
        try:
            # 获取仓库路径
            repo_path = self.get_argument("repo_path", None)
            if not repo_path:
                raise HTTPError(400, "缺少仓库路径参数")
            
            # 检查路径是否存在
            if not os.path.exists(repo_path):
                raise HTTPError(404, f"仓库路径不存在: {repo_path}")
            
            # 检查是否为Git仓库
            if not GitService.is_git_repository(repo_path):
                raise HTTPError(400, f"无效的Git仓库: {repo_path}")
            
            # 获取分支列表
            git_service = GitService(repo_path)
            branches = git_service.get_branches()
            
            self.success_response(branches)
        except GitError as e:
            logger.error("获取分支列表失败", error=str(e))
            raise HTTPError(e.status_code, str(e))
        except HTTPError:
            raise
        except Exception as e:
            logger.error("获取分支列表失败", error=str(e))
            raise HTTPError(500, f"获取分支列表失败: {str(e)}")


class GitCommitsHandler(BaseHandler):
    """Git提交历史处理器"""
    
    async def get(self) -> None:
        """获取仓库提交历史"""
        try:
            # 获取仓库路径
            repo_path = self.get_argument("repo_path", None)
            if not repo_path:
                raise HTTPError(400, "缺少仓库路径参数")
            
            # 获取可选参数
            branch = self.get_argument("branch", None)
            limit = int(self.get_argument("limit", "50"))
            
            # 检查路径是否存在
            if not os.path.exists(repo_path):
                raise HTTPError(404, f"仓库路径不存在: {repo_path}")
            
            # 检查是否为Git仓库
            if not GitService.is_git_repository(repo_path):
                raise HTTPError(400, f"无效的Git仓库: {repo_path}")
            
            # 获取提交历史
            git_service = GitService(repo_path)
            commits = git_service.get_commits(branch, limit)
            
            self.success_response(commits)
        except GitError as e:
            logger.error("获取提交历史失败", error=str(e))
            raise HTTPError(e.status_code, str(e))
        except HTTPError:
            raise
        except Exception as e:
            logger.error("获取提交历史失败", error=str(e))
            raise HTTPError(500, f"获取提交历史失败: {str(e)}")


class GitFilesHandler(BaseHandler):
    """Git文件列表处理器"""
    
    async def get(self) -> None:
        """获取仓库文件列表"""
        try:
            # 获取仓库路径
            repo_path = self.get_argument("repo_path", None)
            if not repo_path:
                raise HTTPError(400, "缺少仓库路径参数")
            
            # 获取可选参数
            path = self.get_argument("path", "")
            ref = self.get_argument("ref", "HEAD")
            
            # 检查路径是否存在
            if not os.path.exists(repo_path):
                raise HTTPError(404, f"仓库路径不存在: {repo_path}")
            
            # 检查是否为Git仓库
            if not GitService.is_git_repository(repo_path):
                raise HTTPError(400, f"无效的Git仓库: {repo_path}")
            
            # 获取文件列表
            git_service = GitService(repo_path)
            files = git_service.list_repository_files(path, ref)
            
            self.success_response(files)
        except GitError as e:
            logger.error("获取文件列表失败", error=str(e))
            raise HTTPError(e.status_code, str(e))
        except HTTPError:
            raise
        except Exception as e:
            logger.error("获取文件列表失败", error=str(e))
            raise HTTPError(500, f"获取文件列表失败: {str(e)}")


class GitFileContentHandler(BaseHandler):
    """Git文件内容处理器"""
    
    async def get(self) -> None:
        """获取文件内容"""
        try:
            # 获取仓库路径
            repo_path = self.get_argument("repo_path", None)
            if not repo_path:
                raise HTTPError(400, "缺少仓库路径参数")
            
            # 获取文件路径
            file_path = self.get_argument("file_path", None)
            if not file_path:
                raise HTTPError(400, "缺少文件路径参数")
            
            # 获取可选参数
            ref = self.get_argument("ref", "HEAD")
            
            # 检查路径是否存在
            if not os.path.exists(repo_path):
                raise HTTPError(404, f"仓库路径不存在: {repo_path}")
            
            # 检查是否为Git仓库
            if not GitService.is_git_repository(repo_path):
                raise HTTPError(400, f"无效的Git仓库: {repo_path}")
            
            # 获取文件内容
            git_service = GitService(repo_path)
            content, name = git_service.get_file_content(file_path, ref)
            
            result = {
                "name": name,
                "content": content,
                "path": file_path
            }
            
            self.success_response(result)
        except GitError as e:
            logger.error("获取文件内容失败", error=str(e))
            raise HTTPError(e.status_code, str(e))
        except HTTPError:
            raise
        except Exception as e:
            logger.error("获取文件内容失败", error=str(e))
            raise HTTPError(500, f"获取文件内容失败: {str(e)}")


class GitFileHistoryHandler(BaseHandler):
    """Git文件历史处理器"""
    
    async def get(self) -> None:
        """获取文件提交历史"""
        try:
            # 获取仓库路径
            repo_path = self.get_argument("repo_path", None)
            if not repo_path:
                raise HTTPError(400, "缺少仓库路径参数")
            
            # 获取文件路径
            file_path = self.get_argument("file_path", None)
            if not file_path:
                raise HTTPError(400, "缺少文件路径参数")
            
            # 获取可选参数
            limit = int(self.get_argument("limit", "20"))
            
            # 检查路径是否存在
            if not os.path.exists(repo_path):
                raise HTTPError(404, f"仓库路径不存在: {repo_path}")
            
            # 检查是否为Git仓库
            if not GitService.is_git_repository(repo_path):
                raise HTTPError(400, f"无效的Git仓库: {repo_path}")
            
            # 获取文件历史
            git_service = GitService(repo_path)
            history = git_service.get_file_history(file_path, limit)
            
            self.success_response(history)
        except GitError as e:
            logger.error("获取文件历史失败", error=str(e))
            raise HTTPError(e.status_code, str(e))
        except HTTPError:
            raise
        except Exception as e:
            logger.error("获取文件历史失败", error=str(e))
            raise HTTPError(500, f"获取文件历史失败: {str(e)}")


class GitFindRepositoriesHandler(BaseHandler):
    """查找Git仓库处理器"""
    
    async def get(self) -> None:
        """查找Git仓库"""
        try:
            # 获取基础目录路径
            base_path = self.get_argument("base_path", None)
            if not base_path:
                raise HTTPError(400, "缺少基础目录路径参数")
            
            # 检查路径是否存在
            if not os.path.exists(base_path):
                raise HTTPError(404, f"基础目录不存在: {base_path}")
            
            # 检查是否为目录
            if not os.path.isdir(base_path):
                raise HTTPError(400, f"路径不是目录: {base_path}")
            
            # 查找仓库
            repos = GitService.find_repositories(base_path)
            
            self.success_response(repos)
        except HTTPError:
            raise
        except Exception as e:
            logger.error("查找Git仓库失败", error=str(e))
            raise HTTPError(500, f"查找Git仓库失败: {str(e)}")
